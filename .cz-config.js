module.exports = {
  types: [
    { value: 'feat', name: 'feat:     新增功能' },
    { value: 'fix', name: 'fix:      修复bug' },
    { value: 'docs', name: 'docs:     文档变更' },
    { value: 'style', name: 'style:    代码格式，不影响功能' },
    { value: 'refactor', name: 'refactor: 重构代码' },
    { value: 'perf', name: 'perf:     性能优化' },
    { value: 'test', name: 'test:     增加测试' },
    { value: 'chore', name: 'chore:    构建过程或辅助工具的变动' },
    { value: 'revert', name: 'revert:   回退' },
  ],

  skipQuestions: ['scope', 'body', 'breaking', 'issues', 'footer'],

  // 自定义问题提示
  messages: {
    type: '选择提交类型:',
    subject: '描述:\n',
    confirmCommit: '确认提交?',
  },

  // subject 文字长度限制
  subjectLimit: 100,

  allowEmptyScope: true,
  allowEmptyBody: true,
  allowEmptyBreaking: true,
  allowEmptyIssues: true,
};
