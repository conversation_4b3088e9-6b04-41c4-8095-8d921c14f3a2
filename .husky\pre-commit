#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "代码提交规范检查"
# 运行 lint 检查，并将输出保存到变量
lint_output=$(npm run lint 2>&1)
lint_status=$?

# 如果 lint 检查失败
if [ $lint_status -ne 0 ]; then
  echo "----------------------------------------------------"
  echo "❌ Lint 检查失败！详细信息如下："
  # 显示 lint 检查的详细输出
  echo "$lint_output"
  echo "----------------------------------------------------"
  echo "请根据上面的提示修复代码格式和潜在问题后再提交。"
  echo "您可以通过运行 'npm run lint' 命令在本地查看详细错误。"
  echo "注意：部分问题可能无法自动修复，需要手动处理。"
  echo "----------------------------------------------------"
  exit $lint_status
fi

exit 0
