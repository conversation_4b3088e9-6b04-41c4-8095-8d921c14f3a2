{
  // 设置编辑器的Tab大小为2个空格
  "editor.tabSize": 2,
  // 配置编辑器在每次保存时自动格式化代码
  "editor.formatOnSave": true,
  // 设置默认的代码检查工具为eslint
  "eslint.enable": true,
  // 设置默认的代码格式化工具为prettier
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // 配置编辑器在保存时执行的代码动作，包括应用所有明确的修复
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "eslint.codeActionsOnSave.rules": null,
  
  // i18n-ally完全重新配置
  "i18n-ally.localesPaths": ["src/i18n"],
  "i18n-ally.enabledParsers": ["js"],
  "i18n-ally.keystyle": "flat",
  "i18n-ally.sourceLanguage": "zh-CN",
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.namespace": true,
  "i18n-ally.pathMatcher": "{locale}/{namespace}.js",
  "i18n-ally.extract.autoDetect": true,
  "i18n-ally.keysInUse": ["*"],
  "i18n-ally.translate.engines": ["google-cn"],
  "i18n-ally.extract.keygenStyle": "camelCase",
  "i18n-ally.enabledFrameworks": ["vue", "vue-sfc"],
  "i18n-ally.dirStructure": "file",
  "i18n-ally.sortKeys": true,
  "i18n-ally.fullReloadOnChanged": true
}
