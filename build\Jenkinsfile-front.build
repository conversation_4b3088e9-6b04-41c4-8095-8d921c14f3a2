import groovy.json.*
import java.io.*
import java.lang.*

podTemplate(
    cloud: 'openshift',
    nodeSelector: 'kubernetes.io/hostname=rhocpwa5.ocpuat.pccw.com',
    containers: [
        containerTemplate(
            name: 'jnlp',
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/ose-jenkins-agent-nodejs-12-rhel8"
        ),
        containerTemplate(
            name: 'node',
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/nodejs-14:1-102",
            envVars: [
                envVar(key: 'HOME', value: '.'),
                envVar(key: 'NG_CLI_ANALYTICS', value: 'ci'),
                envVar(key: 'NODE_EXTRA_CA_CERTS', value: './HKT_Signing_Authority.crt')
            ],
            ttyEnabled: true,
            command: 'cat'
        ),
        containerTemplate(
            name: 'sonar-scanner',
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/sonar-scanner-cli:latest",
            envVars: [
                envVar(key: 'SONAR_HOST_URL', value: 'https://rcicd-sonarqube.hkt.com'),
                envVar(key: 'SONARQUBE_TOKEN', value: '$SONARQUBE_TOKEN'),
                envVar(key: 'SONAR_SCANNER_OPTS', value: "-Djavax.net.ssl.trustStore=cacerts -Djavax.net.ssl.trustStorePassword=$CAPWD")
            ],
            ttyEnabled: true,
            command: 'cat'
        )
    ],
    volumes: [
    persistentVolumeClaim(mountPath: '/tmp/cache', claimName: 'sonarqube-plugincache')
]
){
    try {
        timeout(time: 20, unit: 'MINUTES') {
            node(POD_LABEL) {
                stage('Pre-checkout'){
                    container('jnlp') {
                        sh 'git config --global http.sslVerify false'
                    }
                }

                stage('Checkout'){
                    container('jnlp') {
                        checkout scm: [
                            $class: 'GitSCM',
                            branches: [[name: '$BRANCH_NAME']],
                            doGenerateSubmoduleConfigurations: false,
                            extensions: [[$class: 'LocalBranch', localBranch: '**']],
                            submoduleCfg: [],
                            userRemoteConfigs: scm.userRemoteConfigs
                        ]
                        sh '''
                           GIT_COMMIT_HASH=\$(git rev-parse HEAD)
                           echo \$GIT_COMMIT_HASH > ${SERVICE_PATH}/git_commit_hash.txt
                           '''
                    }
                }


                stage('Build') {
                    container('node') {
                        withCredentials([file(credentialsId: 'hkt_cacerts', variable: 'HKT_CACERTS'),
                                         file(credentialsId: 'hkt_auth_crt', variable: 'HKT_AUTH_CRT')]) {
                            sh '''
                            mv ./.m2/.npmrc.ci-install .npmrc
                            cp  \$HKT_AUTH_CRT ./HKT_Signing_Authority.crt
                            cp  \$HKT_CACERTS ./cacerts
                            npm install
                            npm run build
                            npm run build:outer
                            PACKAGE_NAME=\$(npm run env | grep npm_package_name | cut -d '=' -f 2)
                            echo \$PACKAGE_NAME > package_name.txt
                            
                            cd ../
                            pwd
                            
                            '''
                    }
                }
                }

                stage('SonarQube Analysis'){
                    container('sonar-scanner') {
                        sh '''
                        export SONAR_USER_HOME='/tmp'
                        echo 'sonar-scanner \$SONAR_SCANNER_OPTS -Dsonar.login=\$SONARQUBE_TOKEN -Dsonar.projectKey=\$(cat package_name.txt) -Dsonar.projectVersion=\$(git rev-parse HEAD) -Dsonar.sources=src' > code_scan.sh
                        cat code_scan.sh
                        chmod +x code_scan.sh
                        ./code_scan.sh
                        '''
                    }
                }


                stage('Verify Quality Gate'){
                    container('jnlp') {
                        sh '''
                        pwd
                        ls
                        ls  ${SERVICE_PATH}
                        echo 'curl --cacert \$SERVICE_PATH/HKT_Signing_Authority.crt -u \$SONARQUBE_TOKEN: \$(cat \$SERVICE_PATH/.scannerwork/report-task.txt | grep ceTaskUrl| cut -c 11-)' >> report_task.sh

                        cat report_task.sh
                        chmod +x report_task.sh
                        echo 'curl --cacert \$SERVICE_PATH/HKT_Signing_Authority.crt -u \$SONARQUBE_TOKEN: https://rcicd-sonarqube.hkt.com/api/qualitygates/project_status?analysisId=$ANALYSIS_ID' >> quality_gate.sh
                        cat quality_gate.sh
                        chmod +x quality_gate.sh
                        '''
                        script{
                            def analysisId = null;
                            while (analysisId == null) {
                                def response = sh(script: 'pwd', returnStdout: true).trim()
                                println("Current working directory: ${response}")
                                response = sh(script: './report_task.sh', returnStdout: true).trim();
                                println(response);
                                def task = new JsonSlurperClassic().parseText(response).task;
                                def status = task.status;
                                if (status == 'SUCCESS') {
                                    analysisId = task.analysisId;
                                    break;
                                }
                                if (status == 'FAILED' || status == 'CANCELED') {
                                    throw new Exception('SonarQube quality gate failed!');
                                }

                                sleep(5);
                            }

                            def response = sh(script: "export ANALYSIS_ID=$analysisId && ./quality_gate.sh", returnStdout: true).trim();
                            println(response);
                            def status = new JsonSlurperClassic().parseText(response).projectStatus.status;
                            if (status == 'ERROR') {
                                throw new Exception('SonarQube quality gate failed!');
                            }
                        }
                    }
                }

                stage('Package'){
                    container('node') {
                        withCredentials([string(credentialsId: 'nexus-jenkins-npm-token', variable: 'NEXUS_NPM_TOKEN')]) {
                          sh '''
                            mv .npmrc .npmrc.ci-install
                            mv ./.m2/.npmrc.ci-publish .npmrc
                            npm --no-git-tag-version version \$(npm run env | grep npm_package_version | cut -d '=' -f 2)-\$(cat ./git_commit_hash.txt)
                            ls -lrt
                            npm publish
                          '''
                        }
                    }
                }
            }
        }
    } catch (err) {
       echo 'in catch block'
       echo "Caught: $err"
       currentBuild.result = 'FAILURE'
       throw err
    }
}
