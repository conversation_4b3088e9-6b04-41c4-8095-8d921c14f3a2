import groovy.json.*
import java.io.*
import java.lang.*

podTemplate(
    cloud: 'openshift',
    nodeSelector: 'beta.kubernetes.io/os=linux',
    serviceAccount: 'jenkins',
    containers: [
        containerTemplate(
            name: 'jnlp', 
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/origin-jenkins-agent-maven",
        ),       
    ]
){
    try {
        timeout(time: 200, unit: 'MINUTES') {
            node(POD_LABEL) {
                stage('Pre-checkout'){
                    sh 'git config --global http.sslVerify false'
                }

                stage('Checkout'){
                    checkout scm: [
                        $class: 'GitSCM',
                        branches: [[name: '$BRANCH_NAME']],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [[$class: 'LocalBranch', localBranch: '**']],
                        submoduleCfg: [], 
                        userRemoteConfigs: [[
                            credentialsId: 'CIMFrontend',
                            url: '$GIT_URL'
                        ]]
                    ]
                }

                stage('Deploy'){
                    openshift.withCluster() {
					
                        sh '''
                        sed -i "s/\\\$IMAGE_TAG/$IMAGE_VERSION_TAG/" oc-templates/front_templates-$RUN_ENV/Deployment.yaml
                        oc process -f oc-templates/front_templates-$RUN_ENV/Deployment.yaml --param-file=$ENV | oc apply -f -
                        '''
                        openshift.withProject('$NAMESPACE') {
                            def dc = openshift.selector('deployment', '$APP_NAME')
                            dc.rollout().status()
                        }
						
                    }
                }


                if ("$DR_FLAG" == 'true') {
                    sh '''
                    echo $DR_FLAG
                    echo "Deploying App to DR Site......"
                    '''
                    stage('Deploy to DR') {
                        withKubeCredentials(kubectlCredentials: [[caCertificate: '', clusterName: 'openshift-dr', contextName: '', credentialsId: 'jenkins-dr', namespace: '$NAMESPACE', serverUrl: '$DRAPI']]) {
                    	    dir("${env.WORKSPACE}") {
                                sh '''
                                oc project $NAMESPACE
                                sed -i "s/\\\$IMAGE_TAG/$IMAGE_VERSION_TAG/" oc-templates/front_templates-$RUN_ENV/Deployment.yaml
                                oc process -f oc-templates/front_templates-$RUN_ENV/Deployment.yaml --param-file=$ENV | oc apply -f -
                                oc rollout status deployment/$APP_NAME --watch
                                '''
                            }
                        }
                    }

                    stage('Scaledown DR') {
                        sh '''
                        echo $DR_FLAG
                        echo "Scaling down App from DR Site......"
                        '''
                        withKubeCredentials(kubectlCredentials: [[caCertificate: '', clusterName: 'openshift-dr', contextName: '', credentialsId: 'jenkins-dr', namespace: '$NAMESPACE', serverUrl: '$DRAPI']]) {
                            sh '''
                            oc project $NAMESPACE
                            oc scale deployment/$APP_NAME --replicas=0
                            oc rollout status deployment/$APP_NAME --watch
                            echo 'Shutdown DR Success.'
                            '''
                        }
                    }
                }
            }
        }
    } catch (err) {
        echo 'in catch block'
        echo "Caught: $err"
        currentBuild.result = 'FAILURE'
        throw err
    }
}

