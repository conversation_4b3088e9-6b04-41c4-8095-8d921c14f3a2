podTemplate(
    cloud: 'openshift',
    serviceAccount: 'jenkins',
    containers: [
        containerTemplate(
            name: 'jnlp',
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/ose-jenkins-agent-nodejs-12-rhel8",
            envVars: [
                envVar(key: 'NODE_EXTRA_CA_CERTS', value: './HKT_Signing_Authority.crt')
            ],
        )
    ]
){
    try {
        timeout(time: 60, unit: 'MINUTES') {
            node(POD_LABEL) {
                stage('Pre-checkout'){
                    sh 'git config --global http.sslVerify false'
                }

                stage('Checkout'){
                    checkout scm: [
                        $class: 'GitSCM',
                        branches: [[name: '$BRANCH_NAME']],
                        doGenerateSubmoduleConfigurations: false,
                        extensions: [[$class: 'LocalBranch', localBranch: '**']],
                        submoduleCfg: [],
                        userRemoteConfigs: scm.userRemoteConfigs
                    ]
                }
//              export NODE_EXTRA_CA_CERTS=\${pwd}/HKT_Signing_Authority.crt
                stage('Build Docker Image'){
                    withCredentials([file(credentialsId: 'hkt_cacerts', variable: 'HKT_CACERTS'),
                                     file(credentialsId: 'hkt_auth_crt', variable: 'HKT_AUTH_CRT')]) {
                        sh '''
                        cp  \$HKT_AUTH_CRT ./HKT_Signing_Authority.crt
                        mv ./.m2/.npmrc.ci-install .npmrc
                        PACKAGE_NAME=\$(npm run env | grep npm_package_name | cut -d '=' -f 2)
                        mv package.json package.json.bak
                        npm install \$PACKAGE_NAME@$PACKAGE_VERSION
                        ls -lrt
                        ls -lrt node_modules/
                        ls -lrt node_modules/\$PACKAGE_NAME
                        mv node_modules/\$PACKAGE_NAME/\$DIST_NAME dist
                        '''
                        openshift.withCluster() {
                            sh '''
                            pwd
                            ls
                            sed -i "s/\\\$IMAGE_TAG/$PACKAGE_VERSION/" oc-templates/front_templates-$RUN_ENV/BuildConfig.yaml
                            oc process -f oc-templates/front_templates-$RUN_ENV/ImageStream.yaml --param-file=$ENV | oc apply -f -
                            oc process -f oc-templates/front_templates-$RUN_ENV/BuildConfig.yaml --param-file=$ENV | oc apply -f -
                            
                            '''
                            openshift.withProject('$NAMESPACE') {
                                openshift.selector('bc', '$APP_NAME').startBuild('--from-dir=${SERVICE_PATH}/dist --follow=true --wait=true')
                            }
                        }
                }
            }

                stage('Deploy'){
                    openshift.withCluster() {
                        sh '''
                        sed -i "s/\\\$IMAGE_TAG/$PACKAGE_VERSION/" oc-templates/front_templates-$RUN_ENV/Deployment-$APP_NAME.yaml
                        oc process -f oc-templates/front_templates-$RUN_ENV/Deployment-$APP_NAME.yaml --param-file=$ENV | oc apply -f -
                        '''
                        openshift.withProject('$NAMESPACE') {
                            def dc = openshift.selector('deployment', '$APP_NAME')
                            dc.rollout().status()
                        }
                    }
                }
            }
        }
    } catch (err) {
        echo 'in catch block'
        echo "Caught: ${err}"
        currentBuild.result = 'FAILURE'
        throw err
    }
}
