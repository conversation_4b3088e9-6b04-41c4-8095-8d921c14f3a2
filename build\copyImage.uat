podTemplate(
    cloud: 'openshift',
    nodeSelector: 'beta.kubernetes.io/os=linux',
    serviceAccount: 'jenkins',
    containers: [
        containerTemplate(
            name: 'jnlp', 
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/origin-jenkins-agent-maven",
            envVars: [
                envVar(key: 'MAVEN_OPTS', value: '-Dmaven.repo.local=.m2/repository'),
                envVar(key: 'MAVEN_CLI_OPTS', value: '-s .m2/settings.xml --batch-mode')
            ]
        ),
        containerTemplate(
            name: 'skopeo', 
            image: "${OCP4_INTL_IMG_REPO_URI}/${IMG_NAMESPACE}/skopeo-ca:9.0.0-13",
            envVars: [
                envVar(key: 'http_proxy', value: 'http://app-proxy.pccw.com:8080'),
                envVar(key: 'https_proxy', value: 'http://app-proxy.pccw.com:8080'),
                envVar(key: 'no_proxy', value: 'default-route-openshift-image-registry.apps.ocpuat.pccw.com,default-route-openshift-image-registry.apps.ocpprd1.pccw.com'),
                envVar(key: 'XDG_RUNTIME_DIR', value: '/tmp')
            ],
            ttyEnabled: true, 
            command: 'tail -f /dev/null'           
        )        
    ]
){
    try {
        timeout(time: 200, unit: 'MINUTES') {
            node(POD_LABEL) {
                stage('copyImage'){
                    try{
					container('skopeo') {
                        withCredentials([[
                            $class: 'UsernamePasswordMultiBinding',
                            credentialsId: 'CRM-user-credentials',
                            usernameVariable: 'OCP4_UAT_USERNAME',
                            passwordVariable: 'OCP4_UAT_TOKEN'
                        ]]){                     
						
                            sh '''
                            skopeo login -u$OCP4_UAT_USERNAME -p$OCP4_UAT_TOKEN --tls-verify=false $OCP_SIT_REPOSITORY
                            skopeo copy --authfile=/tmp/containers/auth.json --src-tls-verify=false --dest-tls-verify=false docker://$OCP_SIT_REPOSITORY/$IMG_NAMESPACE_SIT/$APP_NAME:$IMAGE_TAG  docker://$OCP_UAT_REPOSITORY/$IMG_NAMESPACE_UAT/$APP_NAME:$IMAGE_TAG
                            '''
							}
					}}catch(error) {
							echo "Copying image failed, let's retry...."
							retry(5) {
                            container('skopeo') {
                                withCredentials([[
                                    $class: 'UsernamePasswordMultiBinding',
                                    credentialsId: 'ocp4-ut-user-credentials',
                                    usernameVariable: 'OCP4_UAT_USERNAME',
                                    passwordVariable: 'OCP4_UAT_TOKEN'
                                ]]){  
					        		

                                        sh '''
                                        skopeo login -u$OCP4_UAT_USERNAME -p$OCP4_UAT_TOKEN --tls-verify=false $OCP_SIT_REPOSITORY
                                        skopeo copy --authfile=/tmp/containers/auth.json --src-tls-verify=false --dest-tls-verify=false docker://$OCP_SIT_REPOSITORY/$IMG_NAMESPACE_SIT/$APP_NAME:$IMAGE_TAG docker://$OCP_UAT_REPOSITORY/$IMG_NAMESPACE_UAT/$APP_NAME:$IMAGE_TAG
                                        '''
					        	}
					        		}
					        }
                            }
              }
            }
        }
    } catch (err) {
        echo 'in catch block'
        echo "Caught: $err"
        currentBuild.result = 'FAILURE'
        throw err
    }
}
