apiVersion: template.openshift.io/v1
kind: Template
metadata:
  name: ${NAME}-bc-template
objects:
  - apiVersion: build.openshift.io/v1
    kind: BuildConfig
    metadata:
      name: ${NAME}
      namespace: ${NAMESPACE}
    spec:
      failedBuildsHistoryLimit: 5
      output:
        to:
          kind: ImageStreamTag
          name: '${NAME}:$IMAGE_TAG'
      runPolicy: Serial
      source:
        binary: {}
        dockerfile: |-
          FROM ${NAMESPACE}/nginx:1.20.1
          COPY ./ /usr/local/nginx/html/dist/
          RUN chmod -R 777 /var/cache/nginx && \
            chmod -R 777 /var/log/nginx && \
            chmod -R 777 /etc/nginx/conf.d &&\
            chmod -R 777 /usr/local/nginx/html/
          RUN touch /var/run/nginx.pid && \
            chmod -R 777 /var/run/nginx.pid
          #设置环境变量
          ENV TZ=Asia/Shanghai
          ENV LANG C.UTF-8
          ENTRYPOINT nginx -g "daemon off;"

        type: Binary
      strategy:
        dockerStrategy:
          from:
            kind: ImageStreamTag
            name: 'nginx:1.20.1'
        type: Docker
      successfulBuildsHistoryLimit: 5
parameters:
  - name: NAME
  - name: NAMESPACE
  - name: PORT
  - name: HOSTNAME
  - name: EXTERNAL_PORT
  - name: PATH
