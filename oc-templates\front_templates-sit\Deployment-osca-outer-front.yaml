apiVersion: template.openshift.io/v1
kind: Template
metadata:
  name: ${NAME}-dc-template
objects:
  - apiVersion: apps/v1
    kind: Deployment
    metadata:
      labels:
        app: ${NAME}
      name: ${NAME}
      namespace: ${NAMESPACE}
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: ${NAME}
      template:
        metadata:
          labels:
            app: ${NAME}
        spec:
          volumes:
            - name: nginx-config-volume
              configMap:
                name: ${NAME}-nginx-config
                defaultMode: 420
          containers:
            - image: >-
                image-registry.openshift-image-registry.svc:5000/${NAMESPACE}/${NAME}:$IMAGE_TAG
              imagePullPolicy: IfNotPresent
              name: ${NAME}
              ports:
                - containerPort: ${{PORT}}
              volumeMounts:
                - name: nginx-config-volume
                  mountPath: /etc/nginx/conf.d/default.conf
                  subPath: default.conf
              readinessProbe:
                tcpSocket:
                  port: ${{PORT}}
                initialDelaySeconds: 10
                periodSeconds: 10
                failureThreshold: 5
                successThreshold: 1
                timeoutSeconds: 1
              livenessProbe:
                tcpSocket:
                  port: ${{PORT}}
                initialDelaySeconds: 60
                periodSeconds: 60
                failureThreshold: 2
                successThreshold: 1
                timeoutSeconds: 1
              resources:
                limits:
                  cpu: 2000m
                  memory: 2Gi
                requests:
                  cpu: 100m
                  memory: 128Mi
              lifecycle:
                preStop:
                  exec:
                    command:
                      - sh
                      - '-c'
                      - sleep 10
parameters:
  - name: NAME
  - name: NAMESPACE
  - name: PORT