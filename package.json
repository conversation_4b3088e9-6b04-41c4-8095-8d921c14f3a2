{"name": "hkt-osca", "version": "0.1.0", "license": "MIT", "sideEffects": ["*.css", "*.less"], "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:outer": "vue-cli-service build --mode outer", "lint": "vue-cli-service lint", "test": "echo \"No test specified\"", "prepare": "husky install", "c": "cz"}, "dependencies": {"ant-design-vue": "^1.7.8", "axios": "^1.3.5", "core-js": "^3.8.3", "html2canvas": "^1.4.1", "mddir": "^1.1.1", "moment": "^2.30.1", "path-browserify": "^1.0.1", "prettier": "^2.4.1", "print-js": "^1.6.0", "uuid": "^11.1.0", "vue": "^2.7.14", "vue-i18n": "^8.28.2", "vue-pdf": "^4.3.0", "vue-router": "^3.6.5", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@commitlint/cli": "^16.2.4", "@commitlint/config-conventional": "^16.2.4", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.6", "clean-webpack-plugin": "^4.0.0", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "husky": "^8.0.3", "less": "^3.9.0", "less-loader": "^6.0.0", "null-loader": "^4.0.1", "terser-webpack-plugin": "^5.3.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie <11"], "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "config": {"commitizen": {"path": "cz-customizable"}}, "files": ["dist", "dist-outer"]}