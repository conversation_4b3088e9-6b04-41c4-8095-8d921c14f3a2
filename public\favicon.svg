<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_1" data-name="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" viewBox="0 0 28 28">
  <!-- Generator: Adobe Illustrator 29.3.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 151)  -->
  <defs>
    <style>
      .st0 {
        mask: url(#mask);
      }

      .st1 {
        fill: url(#_未命名的渐变);
      }

      .st2 {
        fill: #fff;
      }
    </style>
    <mask id="mask" x="0" y="0" width="28" height="28" maskUnits="userSpaceOnUse">
      <g id="mask-2">
        <rect id="path-1" class="st2" width="28" height="28"/>
      </g>
    </mask>
    <linearGradient id="_未命名的渐变" data-name="未命名的渐变" x1="-479.2" y1="505.7" x2="-478.3" y2="506" gradientTransform="translate(13419 12155.9) scale(28 -24)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e02020"/>
      <stop offset=".2" stop-color="#fa6400"/>
      <stop offset=".3" stop-color="#f7b500"/>
      <stop offset=".5" stop-color="#6dd400"/>
      <stop offset=".7" stop-color="#0091ff"/>
      <stop offset=".8" stop-color="#6236ff"/>
      <stop offset="1" stop-color="#b620e0"/>
    </linearGradient>
  </defs>
  <g id="_页面-1" data-name="页面-1">
    <g id="_形状结合" data-name="形状结合">
      <g class="st0">
        <path class="st1" d="M14.1,18l2.1,3.8c.6,1.2.4,2.6-.5,3.6-.9.9-2.3.9-3.2,0-.9-.9-1.1-2.4-.5-3.6l2.1-3.8ZM12.9,3.4l1.1,1.1,1.1-1.1c1.9-1.8,4.8-1.9,6.7-.1l.2.2,4.5,4.5c1.9,1.9,1.9,5,0,6.9l-6.5,6.4c-.2.2-.5.2-.7,0l-5.4-5.4-5.4,5.4c-.2.2-.5.2-.7,0L1.5,14.9c-1.9-1.9-1.9-5,0-6.9L5.9,3.4c1.9-1.9,5-1.9,6.9,0ZM7.4,4.7h0c0,0-4.5,4.6-4.5,4.6-1.1,1.2-1.1,3,0,4.2l5.5,5.4,4.3-4.3-4-4c-.2-.2-.2-.4,0-.6h0c0,0,4-4,4-4l-1.1-1.1c-1.1-1.1-2.9-1.1-4.1,0ZM20.6,4.7c-1.1-1.1-2.9-1-4.1,0l-1.1,1.1,4,4c.2.2.2.5,0,.7l-4,4,4.3,4.3,5.5-5.4c1.2-1.1,1.2-3,0-4.2l-4.5-4.5h0ZM14,7.3l-3,2.9,3,3,3-3-3-2.9Z"/>
      </g>
    </g>
  </g>
</svg>