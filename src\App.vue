<template>
  <a-configProvider :locale="locale">
    <BasicLayoutVue v-if="getEnv('VUE_APP_SHOW_NAVIGATION')" />
    <!-- 外网项目头 -->
    <OuterHeader v-if="getEnv('VUE_APP_MODE') === 'outer'" />
    <b-router-view />
  </a-configProvider>
</template>

<script>
  import en_GB from 'ant-design-vue/es/locale/en_US'; // 导入美式英语语言包
  import zh_CN from 'ant-design-vue/es/locale/zh_CN';
  import zh_TW from 'ant-design-vue/es/locale/zh_TW'; // 导入中文繁体语言包
  import BasicLayoutVue from './layouts/BasicLayout.vue';
  import OuterHeader from '@/layouts/OuterHeader/index.vue';
  import bRouterView from '@/layouts/bRouterView.vue';
  import tool from '@/utils/tool';

  const localeMap = {
    'zh-CN': zh_CN,
    'zh-HK': zh_TW,
    'en-US': en_GB,
  };

  export default {
    name: 'App',
    components: { BasicLayoutVue, OuterHeader, bRouterView },
    data() {
      return {
        locale: localeMap[this.$tool.local.get('accept-language') || 'zh-CN'],
      };
    },
    computed: {
      env() {
        return process.env;
      },
    },
    mounted() {
      const userInfo = JSON.parse(tool.local.get('userInfo') || '{}');
      this.$store.dispatch('app/userInfoAction', userInfo);
    },
    methods: {
      getEnv(key) {
        return process.env[key];
      },
    },
  };
</script>

<style lang="less" scoped></style>
