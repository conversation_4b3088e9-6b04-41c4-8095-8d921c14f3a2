// 账户选择
import request from '@/utils/request';

// 查询账户列表
export const qryAccountList = (data = {}, options) => {
  return request({
    url: '/cim/account/qryAccountList',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 创建新账户
export const addAccount = (data = {}, options) => {
  return request({
    url: '/cim/account/addAccount',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 部门账单
// 部门账单查询
export const queryDepartmentBill = (data = {}, options) => {
  return request({
    url: '/cim/customer/department/bill/queryDepartmentBill',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 部门账单新增
export const addDepartmentBill = (data = {}, options) => {
  return request({
    url: '/cim/customer/department/bill/addDepartmentBill',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 枚举查询
export const queryCreateCustomerEnum = (data = {}, options) => {
  return request({
    url: '/cim/customer/queryCreateCustomerEnum',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 查询客户信息
export const queryByCustId = (data = {}, options) => {
  return request({
    url: '/cim/customer/queryByCustId',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** getCreditCardTokenUrl - 获取tokenURL */
export const getCreditCardTokenUrl = (data = {}, options) => {
  return request({
    url: '/cim//account/getCreditCardTokenUrl',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** getCreditCardToken - 获取token*/
export const getCreditCardToken = (data = {}, options) => {
  return request({
    url: '/cim/account/getCreditCardToken',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 账户创建联系人枚举值查询 */
export const queryParticipantsType = (data = {}, options) => {
  return request({
    url: '/cim/account/queryParticipantsType',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 账户枚举值查询 */
export const qryStaticList = (data = {}, options) => {
  return request({
    url: '/cim/account/qryStaticList',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 获取账户相关信息
export const queryAccountInfo = (data = {}, options) => {
  return request({
    url: '/cim/account/qry360BasicInfo',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 税率
export const queryCppRatePlanApi = (data = {}, options) => {
  return request({
    url: '/order/receive/idd/ccp/queryCppRatePlan',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 查询已选择税率
export const querySelectedCppRatePlanApi = (data = {}, options) => {
  return request({
    url: '/cim/user/idd/ccp/querySelectedCppRatePlan',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
export const qryUserItemFixedApi = (data = {}, options) => {
  return request({
    url: '/cim/user/qryUserItemFixed',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 新建账户需要选择纸质账单
export const virtualUser = (data = {}, options) => {
  return request({
    url: '/order/receive/paperbill/virtualUser',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 改单时，查询ccp税率接口
export const querySelectedCppRatePlanByOrderApi = (data = {}, options) => {
  return request({
    url: '/order/receive/idd/ccp/querySelectedCppRatePlan',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
