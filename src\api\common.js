import request from '@/utils/request';
import { getApiPrefix } from '@/utils/request.js';

/** 员工基本信息查询接口 */
export const staffQuery = (data = {}, options) => {
  return request({
    url: '/auths/query/staff/roleright/auth/privilege/userightinfo/querySalesCodeInfo',
    method: 'post',
    data,
    ...options,
  });
};

/** 产品线参数查询 */
export const lobQuery = (data = {}, options) => {
  return request({
    url: '/prods/query/commpara/lobQuery',
    method: 'post',
    data,
    ...options,
  });
};

// 字典查询
export const queryParamList = (data = {}, options) => {
  return request({
    url: '/osca/order/osca/common/query/param',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** productFamily查询 */
export const productFamilyQuery = (data = {}, options) => {
  return request({
    url: '/prods/query/commpara/productFamilyQuery',
    method: 'post',
    data,
    ...options,
  });
};

/** 切割地址 */
export const splitAddress = (data = {}, options) => {
  return request({
    url: 'order/receive/addrMapping/splitAddress',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 地址模糊查询 */
export const searchAddress = (data = {}, options) => {
  return request({
    url: 'order/receive/addrMapping/searchAddress',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** SB地址列表查询 */
export const querySbList = (data = {}, options) => {
  return request({
    url: 'order/receive/addrMapping/querySbList',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 楼层下拉框查询 */
export const searchFloor = (data = {}, options) => {
  return request({
    url: 'order/receive/addrMapping/searchFloor',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 室下拉框查询 */
export const searchFlat = (data = {}, options) => {
  return request({
    url: 'order/receive/addrMapping/searchFlat',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 一次性付费 */
export const productTypeQueryByScene = (data = {}, options) => {
  return request({
    url: '/prods/query/commpara/productTypeQueryByScene',
    method: 'post',
    data,
    ...options,
  });
};

// 获取当前时间
export const qryCurrentTime = (data = {}, options) => {
  return request({
    url: '/order/receive/queryTime/getFormatTime',
    method: 'get',
    data,
    ...options,
  });
};

// 获取salesCode和salesName信息
export const infoQuery = (data = {}, options) => {
  return request({
    url: '/order/receive/orderAgent/infoQuery',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 上传文件 */
export const uploadFile = (data, options) => {
  return request({
    url: '/osca/file/file-deal/upload',
    method: 'post',
    data,
    ...options,
  });
};
// 文件下载
export const downloadFile = data => {
  return getApiPrefix('osca', '/file/file-deal/download' + data);
};
