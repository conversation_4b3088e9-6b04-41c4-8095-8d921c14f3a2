import request from '@/utils/request';

/* 客户查询 */
export const getCustListQryList = (data = {}, options) => {
  return request({
    url: '/cim/customer/custListQry',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* 不良信用指标查询 */
export const qryBadPaymentIndicator = (data = {}, options) => {
  return request({
    url: '/cim/customer/qryBadPaymentIndicator',
    method: 'post',
    data,
    ...options,
  });
};

/* documentType下拉查询 */
export const queryDocumentType = (data = {}, options) => {
  return request({
    url: '/cim/customer/queryDocumentType',
    method: 'get',
    data,
    ...options,
  });
};

/* productFamily下拉查询 */
export const productFamilyQuery = (data = {}, options) => {
  return request({
    url: '/prods/query/commpara/productFamilyQuery',
    method: 'post',
    data,
    ...options,
  });
};

/* productType下拉查询 */
export const productTypeOperateQuery = (data = {}, options) => {
  return request({
    url: '/prods/query/commpara/productTypeOperateQuery',
    method: 'post',
    data,
    ...options,
  });
};

/* productType下拉查询 */
export const getProductAll = (data = {}, options) => {
  return request({
    url: '/prods/highsearch/all',
    method: 'post',
    data,
    ...options,
  });
};

/** 地址模糊查询 */
export const searchAddress = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/searchAddress',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 楼层下拉框查询 */
export const searchFloor = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/searchFloor',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** 室下拉框查询 */
export const searchFlat = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/searchFlat',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/** SB地址列表查询 */
export const querySbList = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/querySbList',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* Region下拉框数据查询 */
export const searchRegion = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/searchRegion',
    method: 'post',
    data,
    ...options,
  });
};

/* District下拉框数据查询 */
export const searchDistrict = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/searchDistrict',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* 主产品检索 */
export const searchMainProduct = (data = {}, options) => {
  return request({
    url: '/prods/highsearch/all',
    method: 'post',
    data,
    ...options,
  });
};

/* 主产品下包元素 */
export const defprodandmutex = (data = {}, options) => {
  return request({
    url: '/prods/defprodandmutex/defprodandmutex',
    method: 'post',
    data,
    ...options,
  });
};

/* 场景配置 */
export const getElements = (data = {}, params, options) => {
  return request({
    url: `/prods/light/elements`,
    method: 'post',
    data,
    params,
    options,
  });
};

/* Service No. Type下拉检索 */
export const queryStaticData = (data = {}, options) => {
  return request({
    url: '/order/receive/paramStaticData/queryStaticData',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* 空闲号码查询 */
export const querySpareDnByType = (data = {}, options) => {
  return request({
    url: '/order/receive/dnSelection/querySpareDnByType',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* ServiceGroup\Service下拉查询 */
export const querySpecialServiceGroup = (data = {}, options) => {
  return request({
    url: '/order/receive/dnSelection/querySpecialServiceGroup',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* ServiceGroup\Service有值是号码查询 */
export const preAssignSsgOrReservedDn = (data = {}, options) => {
  return request({
    url: '/order/receive/dnSelection/preAssignSsgOrReservedDn',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 销售单收单接口
export const saleOrderReceive = (data = {}, options) => {
  return request({
    url: '/order/receive/receive/saleOrderReceive',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 订单查询接口
export const queryOrder = (data = {}, options) => {
  return request({
    url: '/cim/user/userInfoList',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

/* 融合成员主产品检索 */
export const searchCompmembaseprod = (data = {}, options) => {
  return request({
    url: '/prods/highsearch/compmembaseprod',
    method: 'post',
    data,
    ...options,
  });
};

/* 附加产品检索 */
export const searchAddition = (data = {}, options) => {
  return request({
    url: '/prods/highsearch/addition',
    method: 'post',
    data,
    ...options,
  });
};

/* 查询客户下的DDI用户号段信息 */
export const qryDdiUserSegmentInfo = (data = {}, options) => {
  return request({
    url: '/cim/user/qryDdiUserSegmentInfo',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

/* 已完工的citinet群组查询 */
export const qryCitinetUserGroupInfo = (data = {}, options) => {
  return request({
    url: '/cim/user/qryCitinetUserGroupInfo',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

/* 查询群组用户的成员列表服务 */
export const qryGroupUserMemberList = (data = {}, options) => {
  return request({
    url: '/cim/user/qryGroupUserMemberList',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

/* 客户开户在途citinet群组查询 */
export const qryOpenCitinetGroupUser = (data = {}, options) => {
  return request({
    url: '/order/receive/queryOrder/qryOpenCitinetGroupUser',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

/* 通用预占号码服务 */
export const preAssignDnByType = (data = {}, options) => {
  return request({
    url: '/order/receive/dnSelection/preAssignDnByType',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

// 查询 workingNumber
export const queryWorkingNumber = (data = {}, options) => {
  return request({
    url: '/cim/user/queryWorkingNum',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};
// 拆机 根据产品类型查询
export const ptypecode = (data = {}, options) => {
  return request({
    url: 'prods/query/detailprodbypid/ptypecode',
    method: 'post',
    needREQ: false,
    data,
    ...options,
  });
};

// 查询客户已有散号
export const queryScatteredNum = (data = {}, options) => {
  return request({
    url: '/cim/user/queryScatteredNum',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

// 选址-根据SB No或者Service No查询回填Address
export const queryAddressByNo = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/querySbAddress',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

// 选址-查询master地址
export const detailAddress = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/detailAddress',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

// 选址-根据serviceNo条件查询
export const queryAddressByServiceNo = (data = {}, options) => {
  return request({
    url: '/order/receive/addrMapping/queryAddressByServiceNo',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};

// 群主idap-add ddi
export const querypreAssignSsgOrReservedDn = (data = {}, options) => {
  return request({
    url: '/order/receive/dnSelection/preAssignSsgOrReservedDn',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};
// 群主idap-add workingnumber-校验
export const checkWorkingNumbers = (data = {}, options) => {
  return request({
    url: '/cim/user/qryWorkingNumbers',
    method: 'post',
    needREQ: true,
    data,
    ...options,
  });
};
// 根据接口判断当前选择的产品是否需要勾选EX-Directory(黄页)
export const directory = (data = {}, options) => {
  return request({
    url: '/order/receive/ex/directory',
    method: 'post',
    data,
    ...options,
  });
};
