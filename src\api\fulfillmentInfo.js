// 新装履约信息接口
import request from '@/utils/request';

// 枚举查询
export const queryCreateCustomerEnum = (data = {}, options) => {
  return request({
    url: '/cim/customer/queryCreateCustomerEnum',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 客户联系人查询服务
export const qryCustContactInfo = (data = {}, options) => {
  return request({
    url: '/cim/customer/qryCustContactInfo',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

const targetArr = {
  // IN: '新安装', DC: '拆机', CO: '改产品', CN: '改号',
  // IR: '内移', ER: '外移',  TR: '过户',  RC: '续约',
  // 操作类型映射
  'IN': ['10000', '10002'],
  //拆机
  'DC': [
    // 单产品
    '19200', // √
    '127000', // √
    '192001', // √
    // 群组
    '19201', // √
  ],
  //改产品
  'CO': [
    // 单产品
    '12700', // √
    // 群组
    '11000', // √
    '34001', // √
    '34002', // √
    '12701', // √
    '12704', // √
    '12703',
    '34000', // √
    '34003', // √
    '34004',
    '34005',
  ],
  //改号
  'CN': [
    // 单产品
    '27900', // √
    // 群组
    // '34005',
    '34006',
    '34007',

    '27901', // √
    '11200',
  ],
  //内移
  'IR': ['34101'], // √
  //外移
  'ER': ['34100'], // √
  //过户
  'TR': ['10100', '70000', '16000'], // √
  //续约
  'RC': ['12800'], // √
};

/**
 * 对字符串数组按字母顺序排序并去重
 * @param {string[]} arr - 待处理的字符串数组
 * @returns {string[]} 排序并去重后的新数组
 */
const sortArrayAlphabetically = arr => {
  // 使用Set去重，然后转为数组
  const uniqueArray = [...new Set(arr)];

  // 按字母顺序排序
  return uniqueArray.sort((a, b) => a.localeCompare(b));
};

function findOperationType(sceneType) {
  // 遍历所有操作类型
  for (const [operation, codes] of Object.entries(targetArr)) {
    // 检查当前场景类型是否在操作类型对应的数组中
    if (codes.includes(sceneType)) {
      return operation; // 返回匹配的操作类型
    }
  }

  return null; // 未找到匹配类型
}

// 施工预约查询服务
export const makeAppointment = (data = {}, options) => {
  if (data.params_transfer_arr) {
    const transfer_arr = data.params_transfer_arr;

    // 存储的类型
    let arr = [];
    transfer_arr.forEach(item => {
      arr.push(findOperationType(item) || item);
    });
    console.log('arr', arr);

    if (arr && Array.isArray(arr)) {
      data.ORDER_TYPE = sortArrayAlphabetically(arr).join('|');
    }
    delete data.params_transfer_arr;
  }
  console.log('施工预约查询服务 接口', data);

  return request({
    url: '/order/receive/install/makeAppointment',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 施工预约结果查询
export const queryAppointmentResult = (data = {}, options) => {
  return request({
    url: '/order/receive/install/queryAppointmentResult',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
