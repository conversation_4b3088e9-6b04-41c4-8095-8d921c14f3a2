import request from '@/utils/request';

export const getAFOrderInfo = (data = {}, options) => {
  return request({
    url: '/osca/order/order/query/aFOrderInfo',
    method: 'post',
    data,
    // needREQ: true,
    ...options,
  });
};

// af单 - 获取Endorsement
export const queryAfEndorsement = (data = {}, options) => {
  return request({
    url: '/osca/order/order/query/afEndorsement',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// af单 - 获取orderInformation
export const queryAfOrderInformation = (data = {}, options) => {
  return request({
    url: '/osca/order/order/query/afOrderInformation',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// af单 - endorsement - 保存订单备注及附件记录
export const saveAfOrderRemark = (data = {}, options) => {
  return request({
    url: '/osca/order/af/order/save/orderRemark',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
