import request from '@/utils/request';

// 报价单列表查询
export const queryQuotationList = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/query/orderInfo',
    method: 'post',
    data,
    // needREQ: true,
    ...options,
  });
};
// 报价单详情查询
export const queryOrderDetail = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/query/orderDetail',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 报价单新增/编辑 提交
export const oscaOrderReceive = (data = {}, options) => {
  return request({
    url: '/osca/order/oscaReceive/oscaOrderReceive',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 详情里查询订单下产品信息
export const qryAmendProduct = (data = {}, options) => {
  return request({
    url: '/osca/order/amendOrder/qryAmendProduct',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 订单轨迹查询
export const orderTrackRecord = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/track/record',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 复制报价单
export const oscaOrderCopy = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/copy',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 取消报价单
export const oscaOrderCancel = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/cancel',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};

// 提交报价单审核
export const submitApproval = (data = {}, options) => {
  return request({
    url: '/osca/order/quotation/order/submitApproval',
    method: 'post',
    data,
    needREQ: true,
    ...options,
  });
};
// 查询主产品接口
export const queryAllProductName = (data = {}, options) => {
  return request({
    url: '/osca/order/highsearch/allname',
    method: 'post',
    data,
    ...options,
  });
};

// 查询礼品信息接口
export const queryAdditionPremium = (data = {}, options) => {
  return request({
    url: '/osca/order/highsearch/additionpre',
    method: 'post',
    data,
    ...options,
  });
};
