/**
 * 部署后的外网项目脱离了框架，需要单独的单点登陆
 */
import request from '@/utils/request';

export function toSSO() {
  window.location.href = `${window.location.origin}/asccrm/sso/client/login`;
}

export function getStaffId(nonce) {
  return request({
    url: 'auth/asccrm/sso/client/staffid',
    method: 'get',
    params: { nonce },
  });
}

export function getStaffFromId(params) {
  return request({
    url: `auths/query/staff/roleright/staff/userightinfo/inside/staffid/${params}`,
    method: 'get',
  });
}

// 获取菜单的目的： 获取菜单的目的是为了获取菜单的权限，然后根据权限来判断用户是否有权限访问外网审批页面
export function getMenu(STAFF_ID, PROVINCE_CODE, EPARCHY_CODE, DEPART_ID) {
  const data = {
    STAFF_ID,
    PROVINCE_CODE,
    EPARCHY_CODE,
    DEPART_ID,
    SUBSYS_CODE: 'CRM',
    ZQ_STAFF_ID: '',
    LOGIN_TYPE: 'NORMAL_LOGIN',
  };
  return request({
    url: 'auths/verify/comp/menus/querymenusnew',
    method: 'post',
    data,
  });
}
