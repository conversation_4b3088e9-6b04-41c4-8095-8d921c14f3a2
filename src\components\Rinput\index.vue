/* 输入框 readonly 可点击 回显 */
<template>
  <!-- 
    使用方法 : 引入、注册
    import Rinput from '@/components/Rinput'
    <Rinput v-model="NewAccountFirst.test111" :readonly="true" @click='regionalSetting'/> 

    注：
    1、使用传入值来修改显示的样式 输入框的placeholder兼容性不行；
    2、disabled 禁用输入，输入框变色已经按钮变色；
    3、readonly 禁止输入，但是按钮可以点击；
    4、searchOrBtn  将 img 替换 成按钮，同时利用其修改按钮样式；
    5、btnLabel 按钮国际化
  -->

  <div
    :class="[
      'readonlyInputBox',
      localValue == $t('common.inputPlaceholder') ? 'nullValue' : 'hasValue',
      disabled ? 'forbiddenDiv' : '',
      searchOrBtn === 'btn' ? 'btn_padding' : '',
    ]"
  >
    <input
      style="width: 100%"
      v-model="localValue"
      :placeholder="$t('common.inputPlaceholder')"
      :readonly="readonly"
      class="readonlyInput"
    />
    <a-button v-if="searchOrBtn === 'btn'" type="primary" @click="onClick">
      {{ btnLabel }}
    </a-button>
    <img
      v-else
      src="@/assets/images/search.svg"
      class="searchIcon"
      :class="[disabled ? 'forbiddenIcon' : 'notFobiddenIcon']"
      @click="onClick"
      alt=""
    />
  </div>
</template>

<script>
  export default {
    name: 'Rinput',
    props: {
      value: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      readonly: {
        type: Boolean,
        default: false,
      },
      searchOrBtn: {
        type: String,
        default: 'search',
      },
      btnLabel: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        localValue: this.value,
      };
    },
    watch: {
      value: {
        // 监听如果传入的值是否为空
        handler(newValue) {
          if (!newValue) {
            this.localValue = null;
          } else {
            this.localValue = newValue;
          }
        },
        immediate: true,
      },
    },
    methods: {
      onClick() {
        this.$emit('click');
      },
    },
  };
</script>
<style scoped lang="less">
  .readonlyInputBox {
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 2px;
    height: 32px;
    padding: 4px 11px;
    line-height: 1.5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #bfbfbf;
    .readonlyInput {
      flex: 1;
      all: unset;
      appearance: none;
      -webkit-appearance: none;

      padding-right: 10px;

      height: 32px !important;
      font-size: 14px !important;
      letter-spacing: 0;
      text-align: left;
      font-weight: 400;
      line-height: 32px !important;
    }
    .input-placeholder {
    }
    .searchIcon {
      width: 16px;
      height: 16px;
    }
  }
  input::placeholder {
    color: #bfbfbf;
  }
  .hasValue {
    // color: rgba(0, 0, 0, 0.65);
    color: #333333 !important;
  }
  .nullValue {
    color: #bfbfbf;
  }
  .forbiddenDiv {
    color: #bfbfbf;
    background-color: #f5f5f5;
    cursor: not-allowed;
  }
  .forbiddenIcon {
    color: #bfbfbf !important;
    cursor: not-allowed;
  }
  .notFobiddenIcon {
    cursor: pointer;
  }
  .btn_padding {
    padding: 4px 0px 4px 11px;
  }
</style>
