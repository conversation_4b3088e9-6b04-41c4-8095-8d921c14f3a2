/* 点击二次确认框 */
<template>
  <a-popover :placement="placement" trigger="click" v-model="visible">
    <template slot="content">
      <div class="common-popover-content">
        <p class="icon-title">
          <a-icon slot="icon" type="info-circle" style="color: #01408e; font-size: 17px" />
          <span class="title">{{ title || $t('common.prompt') }}</span>
        </p>
        <p class="description" :style="{ maxWidth }">
          {{ content || $t('common.confirmSubmission') }}
        </p>
        <div>
          <a-button class="popoverBtn" @click="onCancel" size="small">{{
            $t('common.no')
          }}</a-button>
          <a-button type="primary" class="popoverBtn" @click="onConfirm" size="small">{{
            $t('common.yes')
          }}</a-button>
        </div>
      </div>
    </template>
    <slot name="button"></slot>
  </a-popover>
</template>

<script>
  export default {
    name: 'Rpopover',
    props: {
      title: {
        type: String,
        default: '',
      },
      content: {
        type: String,
        default: '',
      },
      placement: {
        type: String,
        default: 'topRight',
      },
      maxWidth: {
        type: String,
      },
    },
    data() {
      return {
        visible: false,
      };
    },
    methods: {
      onCancel() {
        this.visible = false;
      },
      onConfirm() {
        this.$emit('onConfirm');
      },
    },
  };
</script>
<style lang="less" scoped>
  .common-popover-content {
    .icon-title {
      display: flex;
      align-items: center;
      .title {
        font-weight: 600;
        color: #333;
        margin-left: 5px;
      }
    }
    .description {
      margin-left: 20px;
      margin-bottom: 30px;
      color: #333;
    }
    .popoverBtn {
      width: 60px;
      height: 22px;
      margin-left: 20px;
    }
  }
</style>
