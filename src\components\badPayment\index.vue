/* 查询客户不良状态 */
<template>
  <div class="badPayment">
    <a-popover placement="bottom">
      <!-- 图标展示 -->
      <span :class="['iconfont', obj.IS_BAD_PAYMENT ? 'icon-shuoming1' : 'icon-shuoming']"></span>

      <!-- 不良客户 -->
      <template slot="content" v-if="obj.IS_BAD_PAYMENT">
        <div class="item fixedLine">FixedLine:Customer payment overdue,as follow:</div>
        <div class="item">Total Overdue Amount:${{ obj.TOTAL_OVERDUE_AMOUNT }}</div>
        <div class="item">List of Account Number(s)</div>
        <div class="item" v-for="(iitem, index) in obj.OVERDUE_ACCOUNT_LIST || []" :key="index">
          {{ iitem.ACCOUNT_NO }}-${{ iitem.OUTSTANDING_BALANCE }}
        </div>
      </template>

      <!-- 正常客户 -->
      <template slot="content" v-else>
        <div style="font-weight: 550">FixedLine:OK</div>
      </template>
    </a-popover>
  </div>
</template>

<script>
  import { qryBadPaymentIndicator } from '@/api/customerVerify';
  export default {
    name: 'BadPayment',
    props: {
      CUST_ID: {
        type: [String, Number],
        default: '',
      },
    },
    data() {
      return {
        obj: {},
      };
    },
    watch: {
      CUST_ID() {
        this.getDataList();
      },
    },
    mounted() {},
    methods: {
      async getDataList() {
        if (!this.CUST_ID) return;
        const res = await qryBadPaymentIndicator({
          REQ: {
            'CUSTOMER_NO': this.CUST_ID,
          },
        });
        this.obj = res.DATA?.[0] || {};
      },
    },
  };
</script>
<style lang="less" scoped>
  .badPayment {
    .icon-shuoming1 {
      color: #d81c1c;
    }
    .icon-shuoming {
      color: #9e9e9e;
    }
  }
  .item {
    margin-bottom: 5px;
  }
  .fixedLine {
    font-weight: 550;
  }
</style>
