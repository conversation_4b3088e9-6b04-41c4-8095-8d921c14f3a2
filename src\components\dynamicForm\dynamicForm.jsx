import './styles.less';

const DynamicForm = {
  name: 'DynamicForm',
  props: {
    comboData: {
      type: Object,
      required: true,
      default: {},
    },
    formConfig: {
      type: Object,
      default: () => {
        return {
          layout: 'horizontal',
          labelCol: { span: 4 },
          wrapperCol: { span: 14 },
          colon: false,
        };
      },
    },
    rowConfig: {
      type: Object,
      default: () => {
        return {
          justify: 'start',
          type: 'flex',
          gutter: 24,
        };
      },
    },
    colConfig: {
      type: Object,
      default: () => {
        return {
          span: '24',
          flex: 'flex-start',
        };
      },
    },
    MultiColumn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {},
      rules: {
        input: [{ required: true, message: '请输入', trigger: 'blur' }],
      },
    };
  },
  watch: {
    formData: {
      handler(val) {
        console.log('表单数据', val);
      },
      deep: true,
    },
  },
  created() {
    this.comboData.interfaceElement.forEach(item => {
      this.$set(this.formData, item.elementCode, item.intfElementInitValue);
    });
  },
  methods: {
    /* 根据对应属性名返回数据 */
    getSelectionOptions(type) {
      return this.comboData.selectInitialData[type];
    },
    getFieldComponent(val) {
      // 在JSX中直接返回JSX元素
      return this.renderFormElement(val);
    },
    /* 通用的表单元素渲染函数 */
    renderFormElement(val) {
      const { intfElementTypeCode, type } = val;
      const { slot, soltName } = val.elementConfig;
      if (slot) {
        return this.$slots[soltName];
      }
      if (intfElementTypeCode == '0' || type == 'input') {
        return this.InputElement(val);
      } else if (intfElementTypeCode == ('1' || '4' || '5' || '6') || type == 'select') {
        return this.SelectElement(val);
      } else if (intfElementTypeCode == '2' || type == 'datePicker') {
        return this.DatePicker(val);
      } else if (type == 'radioGroup') {
        return this.RadioGroup(val);
      } else if (intfElementTypeCode == '11' || type == 'checkBox') {
        return this.CheckBox(val);
      }
      this.$message.error(`暂不支持该${val.intfElementLabel}类型表单元素`);
    },
    InputElement(val) {
      return (
        <a-input
          value={this.formData[val.elementCode]}
          placeholder="Please Enter"
          onChange={e => {
            this.formData[val.elementCode] = e.target.value;
          }}
          attrs={val.elementConfig}
        />
      );
    },
    SelectElement(val) {
      return (
        <a-select
          value={this.formData[val.elementCode]}
          placeholder={this.$t('common.selectPlaceholder')}
          onChange={e => {
            this.formData[val.elementCode] = e;
          }}
          attrs={val.elementConfig}
        >
          {this.getSelectionOptions(val.elementCode).map(item => {
            return (
              <a-select-option value={item.value} key={item.key}>
                {item.label}
              </a-select-option>
            );
          })}
        </a-select>
      );
    },
    DatePicker(val) {
      return (
        <a-date-picker
          show-time
          value={this.formData[val.elementCode]}
          onChange={e => {
            this.formData[val.elementCode] = e;
          }}
          dateRender={current => {
            return <div class={'ant-calendar-date'}>{current.date()}</div>;
          }}
          attrs={val.elementConfig}
        />
      );
    },
    RadioGroup(val) {
      return (
        <a-radio-group
          name="radioGroup"
          value={this.formData[val.elementCode]}
          onChange={e => {
            this.formData[val.elementCode] = e.target.value;
          }}
          attrs={val.elementConfig}
        >
          {this.getSelectionOptions(val.elementCode).map(item => {
            return <a-radio value={item.value}>{item.label}</a-radio>;
          })}
        </a-radio-group>
      );
    },
    CheckBox(val) {
      return (
        <a-checkbox-group
          value={this.formData[val.elementCode]}
          name="checkboxgroup"
          options={this.getSelectionOptions(val.elementCode)}
          onChange={e => {
            this.formData[val.elementCode] = e;
          }}
          attrs={val.elementConfig}
        />
      );
    },
  },
  render() {
    const { rules, ...rest } = this.formConfig;
    // 使用JSX来定义组件的结构
    return (
      <div class={'DynamicFormOutermostContainer'}>
        <a-form-model
          ref="ruleForm"
          onInput={() => {}}
          props={{ model: this.formData, rules: rules }}
          attrs={rest}
        >
          {this.comboData.interfaceElement?.map((item, index) => {
            return (
              <a-row key={index} attrs={{ ...this.rowConfig, ...item.rowConfig }}>
                {this.MultiColumn ? (
                  item.map(item => {
                    return (
                      <a-col attrs={{ ...this.colConfig, ...item.colConfig }}>
                        {item.slot ? (
                          this.$slots[item.soltName]
                        ) : (
                          <a-form-model-item
                            prop={item.elementCode}
                            label={item.intfElementLabel}
                            attrs={item.elementConfig}
                          >
                            {this.getFieldComponent(item)}
                          </a-form-model-item>
                        )}
                      </a-col>
                    );
                  })
                ) : (
                  <a-col attrs={{ ...this.colConfig, ...item.colConfig }}>
                    {item.slot ? (
                      this.$slots[item.soltName]
                    ) : (
                      <a-form-model-item
                        prop={item.elementCode}
                        label={item.intfElementLabel}
                        attrs={item.elementConfig}
                      >
                        {this.getFieldComponent(item)}
                      </a-form-model-item>
                    )}
                  </a-col>
                )}
              </a-row>
            );
          })}
        </a-form-model>
      </div>
    );
  },
};

export default DynamicForm;
