# DynamicForm 组件注解

## 组件名称：

DynamicForm

## 属性 (Props)：

- `omboData`：对象的类型，必须提供，默认值为空对象。该属性用于传递表单的数据接口信息。

- `formConfig`：对象的类型，默认值为一个包含layout、labelCol、wrapperCol和colon属性的对象。该属性用于配置表单的布局和样式。

- `rowConfig`：对象的类型，默认值为一个包含justify、type和gutter属性的对象。该属性用于配置行的样式和布局。

- `colConfig`：对象的类型，默认值为一个包含span和flex属性的对象。该属性用于配置列的样式和布局。

- `MultiColumn`：布尔类型，默认值为false。该属性用于决定表单是否以多列方式显示。

## 数据 (Data)：

- `formData`：对象的类型，用于存储表单的数据。

- `rules`：对象的类型，用于存储表单验证的规则。

## 监听器 (Watch)：

- `formData`：当formData发生改变时，会触发一个处理函数，并在控制台打印出表单数据。
方法 (Methods)：

## 方法（Methods）

- `getSelectionOptions(type)`：根据提供的类型返回对应的数据选项。

- `getFieldComponent(val)`：返回一个表单元素的组件。

- `renderFormElement(val)`：通用的表单元素渲染函数，根据提供的值返回相应的组件。

- `InputElement(val)`：返回一个a-input组件。

- `SelectElement(val)`：返回一个a-select组件。

- `DatePicker(val)`：返回一个a-date-picker组件。

- `RadioGroup(val)`：返回一个a-radio-group组件。

- `CheckBox(val)`：返回一个a-checkbox-group组件。

## 渲染函数 (Render)：

- `render`：该函数定义了组件的结构，使用JSX语法来创建组件的DOM结构。
注意事项：

## 注意事项

组件的comboData属性是必须的，且应提供一个对象。

formConfig、rowConfig和colConfig属性提供了表单的配置信息，可以根据需要进行自定义。

MultiColumn属性决定了是否多列显示表单元素。

## 示例用法

```jsx
<DynamicForm
  ref="dynamicForm"
  comboData={yourComboData}
  formConfig={yourFormConfig}
  rowConfig={yourRowConfig}
  colConfig={yourColConfig}
  MultiColumn={yourMultiColumn}
/>

````
/* 
INTF_ELEMENT_TYPE_CODE：组件类型，
    0为普通输入框，
    1、4、5、6为下拉框，
    2为日期组件，
    11为复选框组件
*/
const comboData1 = {   // 组件数据--单列
    interfaceElement: [
        {
            intfElementTypeCode: "0",  // 组件类型
            intfElementLabel: "输入框", // 组件名称
            INTF_ELEMENT_INIT_VALUE: undefined,   // 初始值
            intfElementParam: "input",  //接口元素参数
            formModelConfig: {},  // 表单模型配置
            rowConfig: {},   // 行配置
            colConfig: {},  // 列配置
            type: 'input',  // 类型 
            elementConfig: {  // 组件配置
                slot: true,  // 是否插槽，开启后可自定义标签后的内容
                soltName: 'renderInput',  // 插槽名称
                ...elementConfig   // 其余组件配置
            }
        },
        {
            intfElementLabel: "插槽",
            INTF_ELEMENT_INIT_VALUE: undefined,
            intfElementParam: "slot", 
            slot: true,  // 是否插槽，开启后可自定义当前行的插槽内容
            soltName: 'content',
            rowConfig: {},
            colConfig: {},
            formModelConfig: {},
            type: 'slot',
        },
    ],
    selectInitialData: {
        "checkbox": [
            { label: 'Apple', value: 'Apple' },
            { label: 'Pear', value: 'Pear' },
            { label: 'Orange', value: 'Orange' },
        ]
    }
}

const comboData2 = {  // 组件数据--多列
    interfaceElement: [
        [
            {
                intfElementTypeCode: "0",
                intfElementLabel: "输入框",
                INTF_ELEMENT_INIT_VALUE: undefined,
                intfElementParam: "input",
                fieIds: 'input',
                rowConfig: {},
                colConfig: {},
                formModelConfig: {},
                type: 'input',
                elementConfig: {
                    soltName: 'renderInput',
                    slot: true,
                }
            },
            {
                intfElementTypeCode: "1",
                intfElementLabel: "下拉框",
                INTF_ELEMENT_INIT_VALUE: undefined,
                intfElementParam: "select",
                fieIds: 'select',
                rowConfig: {},
                colConfig: {},
                formModelConfig: {},
                type: 'select',
                elementConfig: {}
            },
            {
                intfElementTypeCode: "2",
                intfElementLabel: "日期选择框",
                INTF_ELEMENT_INIT_VALUE: undefined,
                intfElementParam: "datePicker",
                fieIds: 'datePicker',
                rowConfig: {},
                colConfig: {},
                formModelConfig: {},
                type: 'datePicker',
                elementConfig: {
                    showTime: false
                }

            },
        ],
        [
            {
                intfElementLabel: "插槽",
                INTF_ELEMENT_INIT_VALUE: undefined,
                intfElementParam: "slot",
                slot: true,
                soltName: 'content',
                fieIds: 'slot',
                rowConfig: {},
                colConfig: {
                    span: 24
                },
                formModelConfig: {},
                type: 'slot',    
            },
        ]
    ],
    selectInitialData: {
        /* 结构不变，同上 */
    }
}
````