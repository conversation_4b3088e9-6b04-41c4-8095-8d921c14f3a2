.DynamicFormOutermostContainer {
  box-sizing: border-box;
  // padding: 10px 10px 0 10px;
  // min-height: 90vh;

  /* 表单输入框label垂直居中调整 */
  // .ant-form-item-label {
  //     line-height: 32px;
  // }
}

/* 以下样式用于覆盖原有组件库样式，匹配hkt标准 */
.ant-calendar-selected-day .ant-calendar-date {
  background: #0072ff;
  color: #ffffff;
  border-radius: 50%;
}

.ant-calendar-date {
  border-radius: 50%;
  border: none;
  line-height: 24px;
}

.ant-calendar-date:hover {
  border-radius: 50%;
}
