/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* BEM support Func
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
/* Break-points
 -------------------------- */
/* Scrollbar
 -------------------------- */
/* Placeholder
 -------------------------- */
/* BEM
 -------------------------- */
/* Element Chalk Variables */
/* Transition
-------------------------- */
/* Color
-------------------------- */
/* 53a8ff */
/* 66b1ff */
/* 79bbff */
/* 8cc5ff */
/* a0cfff */
/* b3d8ff */
/* c6e2ff */
/* d9ecff */
/* ecf5ff */
/* Link
-------------------------- */
/* Border
-------------------------- */
/* Fill
-------------------------- */
/* Typography
-------------------------- */
/* Size
-------------------------- */
/* z-index
-------------------------- */
/* Disable base
-------------------------- */
/* Icon
-------------------------- */
/* Checkbox
-------------------------- */
/* Radio
-------------------------- */
/* Select
-------------------------- */
/* Alert
-------------------------- */
/* MessageBox
-------------------------- */
/* Message
-------------------------- */
/* Notification
-------------------------- */
/* Input
-------------------------- */
/* Cascader
-------------------------- */
/* Group
-------------------------- */
/* Tab
-------------------------- */
/* Button
-------------------------- */
/* cascader
-------------------------- */
/* Switch
-------------------------- */
/* Dialog
-------------------------- */
/* Table
-------------------------- */
/* Pagination
-------------------------- */
/* Popup
-------------------------- */
/* Popover
-------------------------- */
/* Tooltip
-------------------------- */
/* Tag
-------------------------- */
/* Tree
-------------------------- */
/* Dropdown
-------------------------- */
/* Badge
-------------------------- */
/* Card
--------------------------*/
/* Slider
--------------------------*/
/* Steps
--------------------------*/
/* Menu
--------------------------*/
/* Rate
--------------------------*/
/* DatePicker
--------------------------*/
/* Loading
--------------------------*/
/* Scrollbar
--------------------------*/
/* Carousel
--------------------------*/
/* Collapse
--------------------------*/
/* Transfer
--------------------------*/
/* Header
  --------------------------*/
/* Footer
--------------------------*/
/* Main
--------------------------*/
/* Timeline
--------------------------*/
/* Backtop
--------------------------*/
/* Link
--------------------------*/
/* Calendar
--------------------------*/
/* Form
-------------------------- */
/* Avatar
--------------------------*/
/* Empty
-------------------------- */
/* Descriptions
-------------------------- */
/* Skeleton 
--------------------------*/
/* Svg
--------------- */
/* Result
-------------------------- */
/* Break-point
--------------------------*/
.el-textarea {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: bottom;
  font-size: 14px;
}
.el-textarea__inner {
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.el-textarea__inner::placeholder {
  color: #c0c4cc;
}
.el-textarea__inner:hover {
  border-color: #c0c4cc;
}
.el-textarea__inner:focus {
  outline: none;
  border-color: #409eff;
}

.el-textarea .el-input__count {
  color: #909399;
  background: #ffffff;
  position: absolute;
  font-size: 12px;
  bottom: 5px;
  right: 10px;
}
.el-textarea.is-disabled .el-textarea__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
.el-textarea.is-disabled .el-textarea__inner::placeholder {
  color: #c0c4cc;
}

.el-textarea.is-exceed .el-textarea__inner {
  border-color: #f56c6c;
}
.el-textarea.is-exceed .el-input__count {
  color: #f56c6c;
}

.el-input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;
}
.el-input::-webkit-scrollbar {
  z-index: 11;
  width: 6px;
}
.el-input::-webkit-scrollbar:horizontal {
  height: 6px;
}
.el-input::-webkit-scrollbar-thumb {
  border-radius: 5px;
  width: 6px;
  background: #b4bccc;
}
.el-input::-webkit-scrollbar-corner {
  background: #fff;
}
.el-input::-webkit-scrollbar-track {
  background: #fff;
}
.el-input::-webkit-scrollbar-track-piece {
  background: #fff;
  width: 6px;
}
.el-input .el-input__clear {
  color: #c0c4cc;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.el-input .el-input__clear:hover {
  color: #909399;
}
.el-input .el-input__count {
  height: 100%;
  display: inline-flex;
  align-items: center;
  color: #909399;
  font-size: 12px;
}
.el-input .el-input__count .el-input__count-inner {
  background: #ffffff;
  line-height: initial;
  display: inline-block;
  padding: 0 5px;
}
.el-input__inner {
  -webkit-appearance: none;
  background-color: #ffffff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  font-size: inherit;
  height: 40px;
  line-height: 40px;
  outline: none;
  padding: 0 15px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
.el-input__inner::-ms-reveal {
  display: none;
}
.el-input__inner::placeholder {
  color: #c0c4cc;
}
.el-input__inner:hover {
  border-color: #c0c4cc;
}
.el-input__inner:focus {
  outline: none;
  border-color: #409eff;
}

.el-input__suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0;
  text-align: center;
  color: #c0c4cc;
  transition: all 0.3s;
  pointer-events: none;
}

.el-input__suffix-inner {
  pointer-events: all;
}

.el-input__prefix {
  position: absolute;
  height: 100%;
  left: 5px;
  top: 0;
  text-align: center;
  color: #c0c4cc;
  transition: all 0.3s;
}

.el-input__icon {
  height: 100%;
  width: 25px;
  text-align: center;
  transition: all 0.3s;
  line-height: 40px;
}
.el-input__icon:after {
  content: '';
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle;
}

.el-input__validateIcon {
  pointer-events: none;
}

.el-input.is-active .el-input__inner {
  outline: none;
  border-color: #409eff;
}

.el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
.el-input.is-disabled .el-input__inner::placeholder {
  color: #c0c4cc;
}
.el-input.is-disabled .el-input__icon {
  cursor: not-allowed;
}

.el-input.is-exceed .el-input__inner {
  border-color: #f56c6c;
}
.el-input.is-exceed .el-input__suffix .el-input__count {
  color: #f56c6c;
}

.el-input--suffix .el-input__inner {
  padding-right: 30px;
}

.el-input--prefix .el-input__inner {
  padding-left: 30px;
}

.el-input--medium {
  font-size: 14px;
}
.el-input--medium .el-input__inner {
  height: 36px;
  line-height: 36px;
}

.el-input--medium .el-input__icon {
  line-height: 36px;
}

.el-input--small {
  font-size: 13px;
}
.el-input--small .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.el-input--small .el-input__icon {
  line-height: 32px;
}

.el-input--mini {
  font-size: 12px;
}
.el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input--mini .el-input__icon {
  line-height: 28px;
}

.el-input-group {
  line-height: normal;
  display: inline-table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}
.el-input-group > .el-input__inner {
  vertical-align: middle;
  display: table-cell;
}
.el-input-group__append,
.el-input-group__prepend {
  background-color: #f5f7fa;
  color: #909399;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 20px;
  width: 1px;
  white-space: nowrap;
}
.el-input-group__append:focus,
.el-input-group__prepend:focus {
  outline: none;
}
.el-input-group__append .el-select,
.el-input-group__append .el-button,
.el-input-group__prepend .el-select,
.el-input-group__prepend .el-button {
  display: inline-block;
  margin: -10px -20px;
}
.el-input-group__append button.el-button,
.el-input-group__append div.el-select .el-input__inner,
.el-input-group__append div.el-select:hover .el-input__inner,
.el-input-group__prepend button.el-button,
.el-input-group__prepend div.el-select .el-input__inner,
.el-input-group__prepend div.el-select:hover .el-input__inner {
  border-color: transparent;
  background-color: transparent;
  color: inherit;
  border-top: 0;
  border-bottom: 0;
}
.el-input-group__append .el-button,
.el-input-group__append .el-input,
.el-input-group__prepend .el-button,
.el-input-group__prepend .el-input {
  font-size: inherit;
}

.el-input-group__prepend {
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.el-input-group__append {
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.el-input-group--prepend .el-input__inner {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.el-input-group--prepend .el-select .el-input.is-focus .el-input__inner {
  border-color: transparent;
}

.el-input-group--append .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.el-input-group--append .el-select .el-input.is-focus .el-input__inner {
  border-color: transparent;
}

/** disalbe default clear on IE */
.el-input__inner::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}

.el-input-number {
  position: relative;
  display: inline-block;
  width: 180px;
  line-height: 38px;
}
.el-input-number .el-input {
  display: block;
}
.el-input-number .el-input__inner {
  -webkit-appearance: none;
  padding-left: 50px;
  padding-right: 50px;
  text-align: center;
}
.el-input-number__increase,
.el-input-number__decrease {
  position: absolute;
  z-index: 1;
  top: 1px;
  width: 40px;
  height: auto;
  text-align: center;
  background: #f5f7fa;
  color: #606266;
  cursor: pointer;
  font-size: 13px;
}
.el-input-number__increase:hover,
.el-input-number__decrease:hover {
  color: #409eff;
}
.el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
.el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
  border-color: #409eff;
}
.el-input-number__increase.is-disabled,
.el-input-number__decrease.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.el-input-number__increase {
  right: 1px;
  border-radius: 0 4px 4px 0;
  border-left: 1px solid #dcdfe6;
}

.el-input-number__decrease {
  left: 1px;
  border-radius: 4px 0 0 4px;
  border-right: 1px solid #dcdfe6;
}

.el-input-number.is-disabled .el-input-number__increase,
.el-input-number.is-disabled .el-input-number__decrease {
  border-color: #e4e7ed;
  color: #e4e7ed;
}
.el-input-number.is-disabled .el-input-number__increase:hover,
.el-input-number.is-disabled .el-input-number__decrease:hover {
  color: #e4e7ed;
  cursor: not-allowed;
}

.el-input-number--medium {
  width: 200px;
  line-height: 34px;
}
.el-input-number--medium .el-input-number__increase,
.el-input-number--medium .el-input-number__decrease {
  width: 36px;
  font-size: 14px;
}

.el-input-number--medium .el-input__inner {
  padding-left: 43px;
  padding-right: 43px;
}

.el-input-number--small {
  width: 130px;
  line-height: 30px;
}
.el-input-number--small .el-input-number__increase,
.el-input-number--small .el-input-number__decrease {
  width: 32px;
  font-size: 13px;
}
.el-input-number--small .el-input-number__increase [class*='el-icon'],
.el-input-number--small .el-input-number__decrease [class*='el-icon'] {
  transform: scale(0.9);
}

.el-input-number--small .el-input__inner {
  padding-left: 39px;
  padding-right: 39px;
}

.el-input-number--mini {
  width: 130px;
  line-height: 26px;
}
.el-input-number--mini .el-input-number__increase,
.el-input-number--mini .el-input-number__decrease {
  width: 28px;
  font-size: 12px;
}
.el-input-number--mini .el-input-number__increase [class*='el-icon'],
.el-input-number--mini .el-input-number__decrease [class*='el-icon'] {
  transform: scale(0.8);
}

.el-input-number--mini .el-input__inner {
  padding-left: 35px;
  padding-right: 35px;
}

.el-input-number.is-without-controls .el-input__inner {
  padding-left: 15px;
  padding-right: 15px;
}

.el-input-number.is-controls-right .el-input__inner {
  padding-left: 15px;
  padding-right: 50px;
}
.el-input-number.is-controls-right .el-input-number__increase,
.el-input-number.is-controls-right .el-input-number__decrease {
  height: auto;
  line-height: 19px;
}
.el-input-number.is-controls-right .el-input-number__increase [class*='el-icon'],
.el-input-number.is-controls-right .el-input-number__decrease [class*='el-icon'] {
  transform: scale(0.8);
}

.el-input-number.is-controls-right .el-input-number__increase {
  border-radius: 0 4px 0 0;
  border-bottom: 1px solid #dcdfe6;
}

.el-input-number.is-controls-right .el-input-number__decrease {
  right: 1px;
  bottom: 1px;
  top: auto;
  left: auto;
  border-right: none;
  border-left: 1px solid #dcdfe6;
  border-radius: 0 0 4px 0;
}

.el-input-number.is-controls-right[class*='medium'] [class*='increase'],
.el-input-number.is-controls-right[class*='medium'] [class*='decrease'] {
  line-height: 17px;
}
.el-input-number.is-controls-right[class*='small'] [class*='increase'],
.el-input-number.is-controls-right[class*='small'] [class*='decrease'] {
  line-height: 15px;
}
.el-input-number.is-controls-right[class*='mini'] [class*='increase'],
.el-input-number.is-controls-right[class*='mini'] [class*='decrease'] {
  line-height: 13px;
}

/*# sourceMappingURL=input-number.css.map */
