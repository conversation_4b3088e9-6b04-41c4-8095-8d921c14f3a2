<!-- 闪烁点 -->
<template>
  <span class="status-dot">
    <span class="status-dot-inner" :style="{ backgroundColor: color }"></span>
  </span>
</template>

<script>
  export default {
    name: 'Flicker',
    props: {
      color: {
        type: String,
        default: '#999999',
      },
    },
  };
</script>

<style lang="less" scoped>
  .status-dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 8px;
    vertical-align: middle;
    position: relative;

    .status-dot-inner {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: inherit;
        animation: status-progress 1s ease-in-out infinite;
      }
    }
  }

  @keyframes status-progress {
    0% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    100% {
      transform: scale(2.4);
      opacity: 0;
    }
  }
</style>
