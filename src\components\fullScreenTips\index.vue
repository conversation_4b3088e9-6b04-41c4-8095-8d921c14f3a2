<template>
  <div class="archived-page">
    <a-spin :spinning="spinning" tip="正在跳转huntingForCitinet...">
      <div class="secondLevel-header-title">{{ pageTitle }}</div>
      <div class="tipMainPosition">
        <div v-if="result == 'SUCCESS'" class="modal-container1">
          <div class="modal-container1">
            <div class="icon-container">
              <i class="iconfont icon-wancheng" style="color: #2dcb31"></i>
            </div>
            <div class="title">{{ title }}</div>
            <div class="content">{{ text }}</div>
            <div class="content">{{ number }}</div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
  export default {
    name: 'FullScreenTips',
    data() {
      return {
        confirmLoading: false,
      };
    },
    props: {
      text: {
        type: String,
        default: '',
      },
      title: {
        type: String,
        default: () => {
          return this.$t('orderSummary.orderResult');
        },
      },
      number: {
        type: String,
        default: '',
      },
      pageTitle: {
        type: String,
        default: '',
      },
      result: {
        type: String,
        default: 'SUCCESS',
      },
      spinning: {
        type: Boolean,
        default: false,
      },
    },
  };
</script>

<style scoped lang="less">
  .archived-page {
    position: absolute;
    top: 0;
    bottom: 50px; // TODO底部高度样式变量
    left: 0;
    right: 0;
    padding: 10px 15px 15px 15px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    z-index: 99;
    .tipMainPosition {
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80vh;
      .modal-container1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        .icon-container {
          width: 60px;
          height: 60px;
          i {
            font-size: 60px;
          }
        }
        .title {
          height: 33px;
          font-family: PingFang-SC-Heavy;
          color: #000000;
          font-weight: 500;
          font-size: 24px;
          letter-spacing: 0;
          margin: 20px;
        }
        .content {
          height: 22px;
          font-family: PingFangSC-Regular;
          color: #666666;
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0;
          margin-bottom: 5px;
        }
      }
    }
  }
  /deep/ .ant-spin-nested-loading > div > .ant-spin {
    top: 100%;
  }
</style>
