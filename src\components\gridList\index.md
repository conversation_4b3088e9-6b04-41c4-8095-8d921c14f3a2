# GridList 组件

## 简介

`GridList` 组件是一个用于展示网格列表的 Vue 组件。它可以根据传入的数据动态生成网格项，并支持选择、全选以及自定义图标点击事件等功能。

## 属性

### list


- **类型**：`Array`

- **默认值**：`[]`

- **描述**：用于渲染网格列表的数据源，数组中的每个对象代表一个网格项。

### SelectAll


- **类型**：`Array`

- **默认值**：`[]`

- **描述**：用于表示全选状态的数组，包含所有被选中的网格项的键。

### checkTitle


- **类型**：`String`

- **默认值**："Select/Unselect all Service No. in current page."

- **描述**：全选/取消全选按钮的提示文本。

### Allchecked


- **类型**：`Boolean`

- **默认值**：`false`

- **描述**：表示是否全选当前页的所有网格项。

### handleRuleSelect


- **类型**：`Function`

- **默认值**：`() => {}`

- **描述**：点击网格项时触发的回调函数，用于处理选择事件。

### showIcon


- **类型**：`Boolean`

- **默认值**：`false`

- **描述**：是否显示网格项右侧的图标。

## 事件

### handleRuleSelect(item)


- **参数**：`item`（被点击的网格项的数据对象）

- **描述**：当点击网格项时触发该事件，传递被点击的网格项的数据对象作为参数。

### iconClick(item)


- **参数**：`item`（被点击的网格项的数据对象）

- **描述**：当点击网格项右侧的图标时触发该事件，传递被点击的网格项的数据对象作为参数。

## 插槽

### footer


- **描述**：用于在网格列表底部插入自定义内容的插槽。

## 样式

组件内部使用了 Less 作为样式预处理器，你可以通过修改样式来自定义组件的外观。主要样式类包括 `.grid`、`.list`、`.rule-attribute-container` 等。具体样式细节请参考组件的源代码。