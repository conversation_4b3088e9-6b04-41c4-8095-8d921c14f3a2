<template>
  <div class="grid" ref="gridRef">
    <!-- 
      type的类型有：sort（拖动排序），show（仅作展示），init(可选择可编辑)
    -->
    <!-- 可拖拽版 -->
    <div v-if="type == 'sort'">
      <a-spin :spinning="isLoading">
        <draggable
          v-model="initDatas"
          :class="['bodyRightdraggable', className]"
          @sort="onDraggableUpdate"
          animation="300"
          chosenClass="chosen"
          :disabled="sortDatas.length > initDatas.length"
        >
          <transition-group v-for="(item, index) in initDatas" :key="item.key">
            <div class="tabs triangleSelectionStyle" :key="item.key">
              <div
                class="shapeOutline"
                :class="{
                  isHunting: item.IS_HUNTING == 'Y',
                  select:
                    (!item.IS_HUNTING || item.IS_HUNTING == 'N') &&
                    SelectAll.some(item1 => item1.key == item.key),
                }"
                @click="index => $emit('handleRuleSelect', item)"
              >
                <span
                  class="sortNum"
                  :class="{
                    sortNumSelect: SelectAll.some(item1 => item1.key == item.key),
                  }"
                  >{{ item.serialNumber ?? '' }}</span
                >
                <div class="rule-attribute">
                  {{ item.value }}
                </div>
              </div>
              <span
                style="margin-left: 5px"
                v-if="showIcon"
                :style="{ cursor: item.IS_HUNTING == 'Y' ? 'not-allowed' : '' }"
                @click="() => $emit('iconClick', item)"
              >
                <img src="@/assets/images/update.svg" alt="" />
              </span>
            </div>
          </transition-group>
        </draggable>
      </a-spin>
    </div>

    <!-- 初始化版 -->
    <div class="list" v-else-if="type == 'init'">
      <div v-for="item in list" :key="item.key" class="rule-attribute-container">
        <div
          :class="{
            'rule-attribute': true,
            select: Allchecked || SelectAll.some(item1 => item1.key == item.key),
          }"
          @click="index => $emit('handleRuleSelect', item)"
        >
          {{ item.value }}
        </div>
        <span v-if="showIcon" @click="() => $emit('iconClick', item)">
          <img src="@/assets/images/update.svg" alt="" />
        </span>
        <span v-if="showSeq" class="edit-button" @click="handleEditSeq(item)">
          <img src="@/assets/images/xiugai.svg" />
        </span>
      </div>
    </div>

    <!-- 仅作展示版 -->
    <div class="list" v-else-if="type == 'show'">
      <div class="tabs" v-for="(item, index) in list" :key="item.key">
        <div class="shapeOutline not-rightSelect">
          <span class="sortNum sortNumSelect">{{ index + 1 }}</span>
          <div class="rule-attribute">
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>

    <!-- 可编辑版 -->
    <div class="list" v-else-if="type == 'edit'">
      <div class="tabs" v-for="(item, index) in list" :key="item.key">
        <div
          class="shapeOutline"
          :class="{
            select: Allchecked || SelectAll.some(item1 => item1.key == item.key),
          }"
          @click="index => $emit('handleRuleSelect', item)"
        >
          <span
            class="sortNum"
            :class="{
              sortNumSelect: Allchecked || SelectAll.some(item1 => item1.key == item.key),
            }"
          >
            <a-input
              class="input-style"
              v-containsSqlInjection
              v-model.trim="item.sequence"
              @blur="e => handleSequence(e, item)"
            />
          </span>
          <div class="rule-attribute">
            {{ item.SERVICE_NO }}
          </div>
        </div>
        <span
          style="margin-left: 5px"
          v-if="showIcon"
          :style="{ cursor: item.IS_HUNTING == 'Y' ? 'not-allowed' : '' }"
          @click="() => $emit('iconClick', item)"
        >
          <img src="@/assets/images/update.svg" alt="" />
        </span>
      </div>
    </div>
    <!-- 可编辑版带有样式的 -->
    <div class="list" v-else-if="type == 'editAndStyle'">
      <div class="tabs" v-for="(item, index) in list" :key="item.key">
        <div
          class="shapeOutline"
          :class="{
            selectByWorkNumber:
              item.ServiceNoType?.includes('Working Number') &&
              (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
            select:
              !item.ServiceNoType?.includes('Working Number') &&
              (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
          }"
          @click="index => $emit('handleRuleSelect', item)"
        >
          <div v-if="item.ServiceNoType?.includes('Working Number')">
            <!-- 新增浮动标签 -->
            <div
              v-if="item.BID == selectedOrderList[0].BID"
              :class="{
                bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'IR' }}
            </div>
            <div
              v-else
              :class="{
                bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'ER' }}
            </div>
            <div
              v-if="item.CUST_ID != custId"
              :class="{
                bidTag1: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'TOO' }}
            </div>
          </div>

          <span
            class="sortNum"
            :class="{
              sortNumSelect: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              sortNumSelectByWorkNumber:
                (Allchecked || SelectAll.some(item1 => item1.key == item.key)) &&
                item.ServiceNoType?.includes('Working Number'),
            }"
          >
            <a-input
              class="input-style"
              :class="{
                inputStyleByWorkingNumber:
                  item.ServiceNoType?.includes('Working Number') &&
                  (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
              }"
              v-containsSqlInjection
              v-model.trim="item.sequence"
              @blur="e => handleSequence(e, item)"
            />
          </span>
          <div class="rule-attribute">
            {{ item.SERVICE_NO }}
          </div>
        </div>
        <span
          style="margin-left: 5px"
          v-if="showIcon"
          :style="{ cursor: item.IS_HUNTING == 'Y' ? 'not-allowed' : '' }"
          @click="() => $emit('iconClick', item)"
        >
          <img src="@/assets/images/update.svg" alt="" />
        </span>
      </div>
    </div>
    <!-- 初始化版 -->
    <div class="list" v-else-if="type == 'initAndStyle'">
      <div v-for="item in list" :key="item.key" class="rule-attribute-container">
        <div
          :class="{
            'rule-attribute': true,
            selectByWorkNumber:
              item.ServiceNoType?.includes('Working Number') &&
              (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
            select:
              !item.ServiceNoType?.includes('Working Number') &&
              (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
          }"
          @click="index => $emit('handleRuleSelect', item)"
        >
          {{ item.value }}
          <div v-if="item.ServiceNoType?.includes('Working Number')">
            <!-- 新增浮动标签 -->
            <div
              v-if="item.BID == item?.addressInfo?.BID || item?.addressInfo?.keepCurrentAddress"
              :class="{
                bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'IR' }}
            </div>
            <div
              v-if="!item?.addressInfo?.keepCurrentAddress"
              :class="{
                bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'ER' }}
            </div>
            <div
              v-if="item.CUST_ID != custId"
              :class="{
                bidTag1: Allchecked || SelectAll.some(item1 => item1.key == item.key),
              }"
            >
              {{ 'TOO' }}
            </div>
          </div>
        </div>
        <span v-if="showIcon" @click="() => $emit('iconClick', item)">
          <img src="@/assets/images/update.svg" alt="" />
        </span>
        <span v-if="showSeq" class="edit-button" @click="handleEditSeq(item)">
          <img src="@/assets/images/xiugai.svg" />
        </span>
      </div>
    </div>
    <!-- 可拖拽版 -->
    <div v-if="type == 'sortAndStyle'">
      <a-spin :spinning="isLoading">
        <draggable
          v-model="initDatas"
          :class="['bodyRightdraggable', className]"
          @sort="onDraggableUpdate"
          animation="300"
          chosenClass="chosen"
          :disabled="sortDatas.length > initDatas.length"
        >
          <transition-group v-for="(item, index) in initDatas" :key="item.key">
            <div class="tabs triangleSelectionStyle" :key="item.key">
              <div
                class="shapeOutline"
                :class="{
                  isHunting: item.IS_HUNTING == 'Y',

                  select:
                    (!item.IS_HUNTING || item.IS_HUNTING == 'N') &&
                    (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
                  selectByWorkNumber:
                    item.ServiceNoType?.includes('Working Number') &&
                    (Allchecked || SelectAll.some(item1 => item1.key == item.key)),
                }"
                @click="index => $emit('handleRuleSelect', item)"
              >
                <div v-if="item.ServiceNoType?.includes('Working Number')">
                  <!-- 新增浮动标签 -->
                  <div
                    v-if="item.BID == item?.addressInfo?.BID"
                    :class="{
                      bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
                    }"
                  >
                    {{ 'IR' }}
                  </div>
                  <div
                    v-else
                    :class="{
                      bidTag: Allchecked || SelectAll.some(item1 => item1.key == item.key),
                    }"
                  >
                    {{ 'ER' }}
                  </div>
                  <div
                    v-if="item.CUST_ID != custId"
                    :class="{
                      bidTag1: Allchecked || SelectAll.some(item1 => item1.key == item.key),
                    }"
                  >
                    {{ 'TOO' }}
                  </div>
                </div>
                <span
                  class="sortNum"
                  :class="{
                    sortNumSelect: Allchecked || SelectAll.some(item1 => item1.key == item.key),
                    sortNumSelectByWorkNumber:
                      (Allchecked || SelectAll.some(item1 => item1.key == item.key)) &&
                      item.ServiceNoType?.includes('Working Number'),
                  }"
                  >{{ item.serialNumber ?? '' }}</span
                >
                <div class="rule-attribute">
                  {{ item.value }}
                </div>
              </div>
              <span
                style="margin-left: 5px"
                v-if="showIcon"
                :style="{ cursor: item.IS_HUNTING == 'Y' ? 'not-allowed' : '' }"
                @click="() => $emit('iconClick', item)"
              >
                <img src="@/assets/images/update.svg" alt="" />
              </span>
            </div>
          </transition-group>
        </draggable>
      </a-spin>
    </div>

    <slot name="footer"></slot>
    <a-modal
      :title="$t('orderList.edit')"
      :visible="visible"
      class="edit-modal"
      width="27%"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form-model :model="formData" :colon="false" layout="vertical" ref="form">
        <!-- Select Type 不同类型对应不同表单 -->
        <a-form-model-item :label="$t('orderList.oldServiceNo')">
          <a-input
            v-containsSqlInjection
            v-model.trim="formData.serviceNo"
            :placeholder="$t('common.inputPlaceholder')"
            disabled
          />
        </a-form-model-item>
        <a-form-model-item :label="$t('orderList.serviceNoSeq')">
          <a-input
            v-containsSqlInjection
            v-model.trim="formData.seq"
            type="number"
            :placeholder="$t('common.inputPlaceholder')"
          />
        </a-form-model-item>
      </a-form-model>
      <template slot="footer">
        <a-button class="modal-button-cancel button" @click="handleCancel">{{
          $t('common.buttonCancel')
        }}</a-button>
        <a-button class="moadl-button-Ok button" type="primary" @click="handleOk">{{
          $t('common.buttonConfirm')
        }}</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script>
  import draggable from 'vuedraggable';
  import { mapState } from 'vuex';
  export default {
    name: 'gridList',
    components: { draggable },
    data() {
      return {
        initDatas: [],
        isLoading: false,
        // bodyStyle: {height: '500px', overflow: 'auto'},
        visible: false,
        formData: {
          serviceNo: undefined,
          seq: 0,
        },
      };
    },
    props: {
      type: {
        type: String,
        default: 'init',
      },
      list: {
        type: Array,
        required: true,
        default() {
          return [];
        },
      },
      SelectAll: {
        type: Array,
        default() {
          return [];
        },
      },
      checkTitle: {
        type: String,
        default() {
          return this.$t('customerVerigy.Allchecked');
        },
      },
      Allchecked: {
        type: Boolean,
        default() {
          return false;
        },
      },
      handleRuleSelect: {
        type: Function,
        default() {
          return () => {};
        },
      },
      showIcon: {
        type: Boolean,
        default() {
          return false;
        },
      },
      disabled: {
        type: Boolean,
        default() {
          return false;
        },
      },
      className: {
        type: String,
        default() {
          return '';
        },
      },
      showSeq: {
        type: Boolean,
        default: false,
      },
      sortDatas: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      list: {
        handler(newVal, oldVal) {
          if (newVal) {
            this.initDatas = newVal;
            this.$nextTick(() => {
              this.handleResetNumberList();
            });
          }
        },
        deep: true,
        immediate: true,
      },
    },
    computed: {
      // ...mapState('macd', {
      //   selectedOrderList: state => state.selectedOrderList,
      // }),
      ...mapState('customerVerify', {
        custId: state => state.custId,
      }),
    },
    mounted() {
      console.log('this.SelectAll', this.SelectAll);
      console.log('this.selectedOrderList', this.selectedOrderList);

      this.handleResetNumberList();
      if (this.type === 'init') {
        this.$refs.gridRef.style.setProperty('--gridColumsWidth', '88px');
      } else if (this.type === 'show') {
        this.$refs.gridRef.style.setProperty('--gridColumsWidth', '118px');
      }
    },
    methods: {
      //编辑修改顺序
      handleSequence(e, item) {
        console.log(e, item, 'e,item');
        if (this.SelectAll.length) {
          return;
        }
        this.SelectAll?.map(selectItem => {
          if (selectItem.SERVICE_NO == item.SERVICE_NO) {
            selectItem.sequence = e.target.value;
          }
        });

        this.$emit('handleSequence', e.target.value, item);
      },
      // 根据号码数量动态设置行数和列数
      handleResetNumberList() {
        const length = Math.ceil(this.list.length / 10);
        this.$refs.gridRef.style.setProperty('--maxColumnsLength', length);

        if (this.list.length >= 10) {
          this.$refs.gridRef.style.setProperty('--maxRowLength', 10);
        } else {
          this.$refs.gridRef.style.setProperty('--maxRowLength', this.list.length);
        }
      },

      //拖拽动作   ***.第三步 拖拽完成事件
      onDraggableUpdate(e) {
        this.isLoading = true;
        setTimeout(() => {
          this.isLoading = false;
          //老位置
          // const oldIndex = e.oldIndex;
          // //新位置
          // const newIndex = e.newIndex;
          // const newSort = this.initDatas[e.newIndex].sort;

          // this.initDatas[e.newIndex].sort = this.initDatas[e.oldIndex].sort;
          // this.initDatas[e.oldIndex].sort = newSort;
          this.$message.success('顺序变更成功!');
          // 更新修改后的数据
          // this.initDatas.forEach((item, index) => {
          //   item.serialNumber = index + 1;
          // });
          this.$emit('returnNewListFunc', this.initDatas);
        }, 1000);
      },
      handleEditSeq(item) {
        this.formData.serviceNo = item.value;
        this.formData.seq = item.seq;
        this.visible = true;
      },
      handleOk() {
        const item = this.list.find(item => item.value == this.formData.serviceNo);
        item.seq = this.formData.seq;
        this.visible = false;
      },
      handleCancel() {
        this.formData.seq = undefined;
        this.formData.serviceNo = undefined;
        this.visible = false;
      },
    },
  };
</script>

<style scoped lang="less">
  @import '@/styles/mixin.less';

  .grid {
    .list {
      display: grid;
      grid-template-columns: repeat(var(--maxColumnsLength), var(--gridColumsWidth));
      // overflow: auto;
      grid-template-rows: repeat(
        var(--maxRowLength),
        1fr
      ); /* 号码多于10个，固定十行，少于10个固定多少行 */
      grid-auto-flow: column;
      grid-gap: 5px;
      grid-column-gap: 10px;
      overflow-x: auto;

      .input-style {
        border: 0;
        width: 40px;
        text-align: center;
        color: #01408e;
      }
      .inputStyleByWorkingNumber {
        border: 0;
        width: 40px;
        text-align: center;
        color: #e60017;
        background-color: #fef6f7;
      }
      .rule-attribute-container :hover {
        cursor: pointer;
      }

      .rule-attribute-container {
        display: flex;
        align-items: center;
        height: 35px;
        font-size: 16px;
        margin-right: 8px;
        margin-top: 10px;
        margin-left: 10px;
        span {
          height: 25px;
          margin-left: 5px;
          img {
            width: 16px;
          }
        }

        .rule-attribute {
          width: 120px;
          height: 30px;
          padding: 10px;
          // padding: 5px 0;
          font-size: 12px;
          // line-height: 25px;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 1px dashed #d9d9d9;
          user-select: none; /* 标准语法 */
          -webkit-user-select: none; /* Safari */
          -moz-user-select: none; /* Firefox */
          -ms-user-select: none; /* IE/Edge */
        }
        .inputStyleByWorkingNumber {
          border: 0;
          width: 40px;
          text-align: center;
          color: #e60017;
          background-color: #fef6f7;
        }
        .selectByWorkNumber {
          position: relative;
          text-align: left;
          background-color: #fef6f7; /* 半透明背景防止遮挡文字 */
          color: #e71025;
          border-radius: 2px;
          border: 1px solid #fef6f7;
          width: 120px;
          height: 30px;
        }

        .selectByWorkNumber:before {
          content: '';
          position: absolute;
          right: 0;
          bottom: 0;
          border: 5px solid #e71025;
          border-top-color: transparent;
          border-left-color: transparent;
        }
        .selectByWorkNumber:after {
          content: '';
          width: 3px;
          height: 6px;
          position: absolute;
          right: 1px;
          bottom: 1px;
          border: 1px solid #fff;
          border-top-color: transparent;
          border-left-color: transparent;
          transform: rotate(35deg);
        }
        .bidTag {
          position: absolute;
          top: -10px;
          left: -10px;
          background-color: #fce5e7; /* 半透明背景防止遮挡文字 */
          color: #e60017;
          font-size: 10px;
          width: 30px;
          height: 16px;
          z-index: 2;
          white-space: nowrap;
          border-radius: 8px;
          font-family: 'PingFang SC';
          text-align: center;
        }
        .bidTag1 {
          position: absolute;
          top: -10px;
          left: 25px;
          background-color: #fce5e7; /* 半透明背景防止遮挡文字 */
          color: #e60017;
          font-size: 10px;
          width: 30px;
          height: 16px;
          z-index: 2;
          white-space: nowrap;
          border-radius: 8px;
          font-family: 'PingFang SC';
          text-align: center;
        }
        .select {
          .is-selected(7px, 0, 0, 10px);
          padding: 10px;
        }
      }
    }
  }

  .bodyRightdraggable {
    display: grid;
    grid-template-columns: repeat(var(--maxColumnsLength), 144px);
    grid-auto-flow: column;
    grid-gap: 5px;
    grid-column-gap: 20px;
    grid-template-rows: repeat(var(--maxRowLength), 1fr);
    // grid-template-rows:  40px 40px 40px 40px 40px 40px 40px;
    overflow-x: auto;
    // .tabs {
    //   display: flex;
    //   height: 35px;
    //   font-size: 16px;
    //   align-items: center;
    //   display: flex;
    //   cursor: pointer;
    //   .shapeOutline {
    //     height: 100%;
    //     display: flex;
    //     align-items: center;
    //     border: 1px dashed #01408e;
    //   }
    //   .sortNum {
    //     min-width: 30px;
    //     height: 100%;
    //     font-size: 12px;
    //     border-right: 1px dashed #01408e;
    //     text-align: center;
    //     line-height: 35px;
    //   }
    //   .sortNumSelect {
    //     border-right: 1px solid #01408e;
    //   }
    //   .rule-attribute {
    //     width: 88px;
    //     padding: 5px 0;
    //     font-size: 12px;
    //     height: 35px;
    //     line-height: 35px;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     // border: 1px solid #d9d9d9;
    //     user-select: none; /* 标准语法 */
    //     -webkit-user-select: none; /* Safari */
    //     -moz-user-select: none; /* Firefox */
    //     -ms-user-select: none; /* IE/Edge */
    //   }

    //   .select {
    //     position: relative;
    //     text-align: left;
    //     color: #01408e;
    //     background-color: #fff;
    //     border-radius: 2px;
    //     border: 1px solid #01408e;
    //   }

    //   .select:before {
    //     content: '';
    //     position: absolute;
    //     right: 0;
    //     bottom: 0;
    //     border: 5px solid #01408e;
    //     border-top-color: transparent;
    //     border-left-color: transparent;
    //   }

    //   .select:after {
    //     content: '';
    //     width: 3px;
    //     height: 6px;
    //     position: absolute;
    //     right: 1px;
    //     bottom: 1px;
    //     border: 1px solid #fff;
    //     border-top-color: transparent;
    //     border-left-color: transparent;
    //     transform: rotate(35deg);
    //   }
    // }
    .chosen {
      .shapeOutline {
        border: 1px solid #01408e;
        background-color: #fff !important;
        color: #01408e;
        cursor: move;
      }
    }
  }
  .bidTag {
    position: absolute;
    top: -10px;
    left: -10px;
    background-color: #fce5e7; /* 半透明背景防止遮挡文字 */
    color: #e60017;
    font-size: 10px;
    width: 30px;
    height: 16px;
    z-index: 2;
    white-space: nowrap;
    border-radius: 8px;
    font-family: 'PingFang SC';
    text-align: center;
  }
  .bidTag1 {
    position: absolute;
    top: -10px;
    left: 25px;
    background-color: #fce5e7; /* 半透明背景防止遮挡文字 */
    color: #e60017;
    font-size: 10px;
    width: 30px;
    height: 16px;
    z-index: 2;
    white-space: nowrap;
    border-radius: 8px;
    font-family: 'PingFang SC';
    text-align: center;
  }
  .isWorkNumber {
  }
  .tabs {
    margin-top: 10px;
    display: flex;
    height: 35px;
    font-size: 16px;
    align-items: center;
    display: flex;
    cursor: pointer;
    .shapeOutline {
      margin-left: 10px;
      position: relative;
      height: 100%;
      display: flex;
      align-items: center;
      border: 1px dashed #01408e;
    }
    .shapeOutlineByWorkNumber {
      border: 1px dashed #fef6f7;
    }
    .sortNum {
      min-width: 30px;
      height: 100%;
      font-size: 12px;
      border-right: 1px dashed #01408e;
      text-align: center;
      line-height: 35px;
    }
    .sortNumSelect {
      border-right: 1px solid #01408e;
    }
    .sortNumSelectByWorkNumber {
      border-right: 1px dashed #e71025;
      background-color: #fef6f7;
    }
    .rule-attribute {
      width: 88px;
      padding: 5px 0;
      font-size: 12px;
      height: 35px;
      line-height: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      // border: 1px solid #d9d9d9;
      user-select: none; /* 标准语法 */
      -webkit-user-select: none; /* Safari */
      -moz-user-select: none; /* Firefox */
      -ms-user-select: none; /* IE/Edge */
    }

    .select {
      position: relative;
      text-align: left;
      color: #01408e;
      background-color: #fff;
      border-radius: 2px;
      border: 1px solid #01408e;
    }

    .not-rightSelect {
      position: relative;
      text-align: left;
      color: #01408e;
      background-color: #fff;
      border-radius: 2px;
      border: 1px solid #01408e;
    }

    .select:after {
      content: '';
      width: 3px;
      height: 6px;
      position: absolute;
      right: 1px;
      bottom: 1px;
      border: 1px solid #fff;
      border-top-color: transparent;
      border-left-color: transparent;
      transform: rotate(35deg);
    }
    .selectByWorkNumber {
      position: relative;
      text-align: left;
      background-color: #fef6f7; /* 半透明背景防止遮挡文字 */
      color: #e71025;
      border-radius: 2px;
      border: 1px solid #fef6f7;
    }

    .selectByWorkNumber:before {
      content: '';
      position: absolute;
      right: 0;
      bottom: 0;
      border: 5px solid #e71025;
      border-top-color: transparent;
      border-left-color: transparent;
    }
    .selectByWorkNumber:after {
      content: '';
      width: 3px;
      height: 6px;
      position: absolute;
      right: 1px;
      bottom: 1px;
      border: 1px solid #fff;
      border-top-color: transparent;
      border-left-color: transparent;
      transform: rotate(35deg);
    }

    .isHunting {
      position: relative;
      .rule-attribute {
        position: relative;
        z-index: 0;
      }
      &::after {
        content: 'H';
        position: absolute;
        top: -1px;
        right: 3px;
        color: red;
        font-size: 12px;
        z-index: 1;
        pointer-events: none; // 防止点击干扰
      }
    }
  }
  .edit-modal {
    :deep(.ant-modal-footer) {
      border-top: 0;
      padding: 11px 20px;
    }
    .button {
      height: 32px;
      padding: 0;
    }
    :deep(input::-webkit-inner-spin-button) {
      -webkit-appearance: none;
      margin: 0;
    }

    :deep(input[type='number']) {
      -moz-appearance: textfield;
    }
  }
</style>
