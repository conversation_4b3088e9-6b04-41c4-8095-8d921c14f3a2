<template>
  <div class="messageModal">
    <a-modal
      :visible="visible"
      :zIndex="1001"
      centered
      :closable="closable"
      :footer="null"
      :width="450"
      :height="330"
      :maskClosable="false"
      @cancel="cancel"
    >
      <!-- <template #title>
        <div class="header-title">
          <i class="iconfont icon-tishi" />
          <div class="headermessage">{{ $t('common.prompt') }}</div>
        </div>
      </template> -->
      <div class="content">
        <i class="iconfont icon-tishi" />
        <div class="message">{{ message }}</div>
        <div class="btnRow">
          <a-button v-show="displayCancelBtn" ghost type="primary" @click="cancel">{{
            $t('common.buttonCancel')
          }}</a-button>
          <a-button :loading="loading" type="primary" @click="confirm">{{
            $t('common.buttonConfirm')
          }}</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
  export default {
    name: 'MessageModal',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      message: {
        type: String,
        default() {
          return this.$t('common.enterOneSearchTip');
        },
      },
      loading: {
        type: Boolean,
        default: false,
      },
      displayCancelBtn: {
        type: Boolean,
        default: false,
      },
      closable: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      cancel() {
        this.$emit('cancel');
      },
      confirm() {
        this.$emit('confirm');
      },
    },
  };
</script>

<style scoped lang="less">
  .header-title {
    display: flex;
    align-items: center;
    gap: 5px;
    .iconfont {
      font-size: 20px;
    }
    .headermessage {
      font-size: 18px;
      font-weight: 400;
    }
  }

  .content {
    width: 100%;
    height: 100%;
    text-align: center;
    padding: 20px 0 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    .iconfont {
      font-size: 60px;
    }
    .message {
      margin: 0px 0 30px;
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #333333;
      white-space: pre-line; /* 添加这行支持message处理换行标签 */
    }
    .btnRow {
      display: flex;
      justify-content: center;
    }
    .btnRow button:first-child {
      margin-right: 20px !important;
    }
  }
  :deep(.ant-modal-body) {
    padding: 0 10px !important;
    width: 450px;
    height: 330px;
  }
  :deep(.ant-modal-header) {
    padding: 0 10px !important;
    border-bottom: 0px;
  }
  :deep(.ant-modal-content) {
    border-radius: 8px !important;
  }
</style>
