# PopWindow 组件注解

## 组件概述
`PopWindow` 是一个基于 `ant-design-vue` 的模态框（Modal）组件，用于在页面中弹出一个包含自定义内容的窗口。该组件支持自定义标题、确认加载状态、主体样式等。

## 属性（Props）

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `visible` | `Boolean` | `false` | 控制模态框的显示与隐藏。 |
| `title` | `String` | `""` | 模态框的标题。 |
| `confirmLoading` | `Boolean` | `false` | 控制确认按钮的加载状态。 |
| `bodyStyle` | `Object` | `{ height: "400px", overflow: "auto" }` | 模态框主体的样式。 |
| `modalWidth` | `String` | `"500px"` | 模态框的宽度。 |
| `footer` | `Boolean` | `false` | 控制是否显示默认的底部按钮栏，若为 `true` 则使用具名插槽 `footer` 自定义底部内容。 |

## 事件（Events）

| 事件名 | 参数 | 描述 |
| --- | --- | --- |
| `cancel` | `false` | 点击取消按钮或模态框关闭时触发，传递参数 `false`。 |
| `Ok` | - | 点击确认按钮时触发。 |

## 插槽（Slots）

| 插槽名 | 描述 |
| --- | --- |
| `Content` | 用于插入自定义的主体内容。 |
| `footer` | 当 `footer` 属性为 `true` 时，用于自定义模态框的底部内容。 |

## 样式（Styles）
组件内部使用了 `/deep/` 选择器来修改 `ant-design-vue` 的默认样式，具体修改了模态框的宽度、主体和底部的样式。