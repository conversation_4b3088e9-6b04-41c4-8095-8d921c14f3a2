<template>
  <a-modal
    :visible="visible"
    :title="title"
    :confirm-loading="confirmLoading"
    @cancel="() => $emit('cancel', false)"
    :bodyStyle="bodyStyle"
  >
    <slot name="Content"></slot>
    <!-- 默认插槽，用于插入自定义内容 -->
    <template #footer>
      <div v-if="!footer">
        <a-button
          class="modal-button-cancel"
          @click="() => $emit('cancel', false)"
          :loading="confirmLoading"
          >{{ $t('common.buttonCancel') }}</a-button
        >
        <a-button
          type="primary"
          @click="() => $emit('Ok')"
          class="moadl-button-Ok"
          :loading="confirmLoading"
          >{{ $t('common.buttonOk') }}</a-button
        >
      </div>
      <div v-else>
        <slot name="footer"></slot>
      </div>
    </template>
  </a-modal>
</template>

<script>
  import { Modal, Button } from 'ant-design-vue';
  export default {
    name: 'PopWindow',
    components: { AModal: Modal, AButton: Button },

    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      confirmLoading: {
        type: Boolean,
        default: false,
      },
      bodyStyle: {
        type: Object,
        default: () => ({
          height: '400px',
          overflow: 'auto',
        }),
      },
      modalWidth: {
        type: String,
        default: '500px',
      },
      footer: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {};
    },

    model: {
      prop: 'visible', //绑定的值
      event: 'cancel', //触发的事件
    },

    mounted() {
      document.documentElement.style.setProperty('--modalWidth', this.modalWidth);
    },

    methods: {},
  };
</script>

<style scoped lang="less">
  /deep/ .ant-modal {
    width: 880px !important;
  }

  /deep/ .ant-modal-body {
    padding: 10px 20px 10px 20px !important;
  }

  /deep/ .ant-modal-footer {
    border-top: none !important;
  }

  /deep/ .ant-modal-footer {
    padding: 10px 20px 20px 20px;
  }
</style>
