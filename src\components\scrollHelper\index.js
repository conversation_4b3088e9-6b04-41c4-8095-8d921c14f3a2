// scrollHelper.js
export default {
  install(Vue) {
    // 添加一个全局方法，可以在任意组件实例中访问
    Vue.prototype.$scrollToElement = function (elementId, smooth = false, alignToTop = false) {
      this.$nextTick(() => {
        const element = document.getElementById(elementId);
        if (element) {
          if (alignToTop) {
            element.scrollTo({
              top: Number(alignToTop),
              behavior: smooth ? smooth : 'smooth',
            });
          } else {
            element.scrollIntoView(false);
          }
        }
      });
    };
  },
};
