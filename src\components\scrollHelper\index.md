# scrollHelper.js 注释文档

## 描述

scrollHelper 是一个 Vue 插件，它向 Vue 的原型上添加了一个全局方法 $scrollToElement。这个方法可以在任意 Vue 组件实例中被调用，用于实现页面滚动到指定元素的功能。

## 安装

在Vue项目中使用这个插件，需要在主文件中引入并调用`install`方法：

```javascript
import Vue from 'vue';
import scrollHelper from './scrollHelper';

Vue.use(scrollHelper);
```

## 属性 (Props)：

调用方法
安装插件后，你可以在任意 Vue 组件中使用 $scrollToElement 方法。该方法接受三个参数：

- `elementId`:（必需）：要滚动到的目标元素的 ID。

- `smooth（可选）`：表示滚动行为是否平滑。默认为 false，表示平滑滚动。如果设置为 true，则滚动行为将根据传参自定义。

- `alignToTop（可选）`：表示是否将元素滚动到其顶部位置。默认为 false，表示滚动到顶部，而不是滚动到元素所在的位置。如果传参有值，则表示滚动的具体距离，元素将滚动到元素所在的位置。调用示例：

## 示例用法
```javascript
this.$scrollToElement('my-element', 'auto', 0);
```



## 注意事项

- 这个插件只在 Vue 组件实例中有效，在其他地方调用 $scrollToElement 方法可能会导致错误。
- 这个插件使用了 Vue.prototype 来添加全局方法