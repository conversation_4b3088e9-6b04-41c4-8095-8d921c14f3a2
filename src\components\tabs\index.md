# Tabs 组件注解

## 组件概述
`Tabs` 组件是一个可切换的选项卡组件，通过点击选项卡头部可以动态切换内容区域。它接收`tabData`（选项卡数据）和`contentComponents`（内容组件）作为属性，并根据用户的选择显示对应的内容组件。

## 属性（Props）


- `tabData`: 类型为数组，包含选项卡数据，是必需的。

- `contentComponents`: 类型为对象，包含与选项卡名称对应的内容组件，是必需的。

- `cachedComponents`: 类型为字符串，表示需要缓存的组件名称，默认为空字符串。

- `excludedComponents`: 类型为数组，表示需要排除的组件名称，默认为空数组。

- `LoadAll`: 类型为布尔值，表示是否一次性加载所有内容组件，默认为`false`。

## 数据（Data）


- `selectedTab`: 表示当前选中的选项卡索引，初始值为0。

- `tabs`: 表示选项卡数组，初始为空数组。

- `isadd`: 表示动画的方向，初始为`true`。

- `tabRefs`: 表示每个选项卡对应的组件引用，初始为空对象。

## 方法（Methods）


- `selectTab(index)`: 根据传入的索引选择对应的选项卡，并更新`selectedTab`和`isadd`的值。

- `clearCache()`: 清除缓存，并触发`clear-cache`事件。

## 样式（Styles）


- `.tab-bar`: 选项卡头部样式。

- `.tab-bar li`: 选项卡项样式。

- `.tab-content`: 选项卡内容容器样式。

- `.tab-text`: 选项卡文本样式。

- `.underline`: 选项卡下划线样式。

- `.triangle`: 选项卡三角形指示器样式。

- `.tab-info`: 选项卡内容区域样式。

- `.animate-slide-right` 和 `.animate-slide-left`: 动画效果样式。

## 使用方法

在父组件中使用`Tabs`组件时，需要传入`tabData`和`contentComponents`属性，并监听`clear-cache`事件以清除缓存。例如：

```vue
<tabs :tabData="tabsData" :contentComponents="components" @clear-cache="clearCache"></tabs>