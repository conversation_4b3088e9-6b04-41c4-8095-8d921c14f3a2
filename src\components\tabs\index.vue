<template>
  <div>
    <div class="secondLevel-header-title">{{ title }}</div>
    <ul class="tab-bar">
      <li v-for="(tab, index) in tabs" :key="index" @click="selectTab(index)">
        <div class="tab-content">
          <span class="tab-text">{{ tab.name }}</span>
          <div class="underline" v-if="tab.isActive"></div>
          <div class="triangle" v-if="tab.isActive"></div>
        </div>
      </li>
    </ul>
    <div v-if="selectedTab !== null" class="tab-info">
      <!-- 使用 <component> 标签动态切换组件 -->
      <div v-if="!LoadAll">
        <div :class="[animationEffect && (isadd ? 'animate-slide-right' : 'animate-slide-left')]">
          <keep-alive :include="cachedComponents" :exclude="excludedComponents">
            <component
              :is="contentComponents[tabs[selectedTab].name]"
              :ref="`tabRef${selectedTab}`"
            ></component>
          </keep-alive>
        </div>
      </div>
      <div v-else>
        <div :class="[animationEffect && (isadd ? 'animate-slide-right' : 'animate-slide-left')]">
          <component
            v-for="(tab, index) in tabs"
            :key="`${index}-tab`"
            :is="contentComponents[tab.name]"
            :ref="`tabRef${index}`"
            v-show="selectedTab === index"
          ></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Tabs',
    props: {
      name: {
        type: String,
        default: '',
      },
      title: {
        type: String,
        default: '',
      },
      tabData: {
        type: Array,
        required: true,
      },
      contentComponents: {
        type: Object,
        required: true,
      },
      cachedComponents: {
        type: String,
        default() {
          return '';
        },
      },
      excludedComponents: {
        type: Array,
        default() {
          return [];
        },
      },
      LoadAll: {
        type: Boolean,
        default() {
          return false;
        },
      },
      animationEffect: {
        type: Boolean,
        default() {
          return false;
        },
      },
    },
    data() {
      return {
        selectedTab: 0,
        tabs: [],
        isadd: true,
        tabRefs: {},
      };
    },
    created() {
      this.tabs = this.tabData;
    },
    beforeDestroy() {
      // 在组件销毁前清除缓存
      this.clearCache();
    },
    methods: {
      selectTab(index) {
        this.tabs.forEach(tab => {
          tab.isActive = false;
        });
        this.tabs[index].isActive = true;
        if (this.selectedTab < index) {
          this.isadd = true;
        } else {
          this.isadd = false;
        }
        this.selectedTab = index;
        this.$nextTick(() => {
          if (!this.$refs[`tabRef${index}`]) return;
          this.tabRefs[`tabRef${index}`] = this.$refs[`tabRef${index}`];
        });
      },
      clearCache() {
        this.$emit('clear-cache');
      },
    },
  };
</script>

<style scoped lang="less">
  .tab-bar {
    list-style-type: none;
    padding: 0 0 16px 0;
    margin: 0;
    display: flex;
    font-size: 14px;
  }

  .tab-bar li {
    padding: 10px 20px 0 22px;
    cursor: pointer;
  }

  .tab-content {
    position: relative;
  }

  .tab-bar li:hover {
    color: #01408e;
  }

  .tab-text {
    display: block;
    width: auto;
    min-height: 24px;
  }

  .underline {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #01408e;
  }

  .triangle {
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #01408e;
  }

  .tab-info {
    padding: 0 20px 0 22px;
  }

  .animate-slide-right {
    > div {
      animation: slide-out 0.5s;
    }
  }

  .animate-slide-left {
    > div {
      animation: slide-in 0.5s;
    }
  }

  @keyframes slide-out {
    0% {
      opacity: 0;
      transform: translateX(50%);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in {
    0% {
      opacity: 0;
      transform: translateX(-50%);
    }

    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
