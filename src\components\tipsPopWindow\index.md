# tipsPopWindow 组件注解

## 组件概述
`tipsPopWindow` 组件是一个提示弹窗组件，用于显示一些提示信息。它包含标题、内容、图标以及确认和取消按钮。

## 属性（Props）


- `visible`: 布尔值，控制弹窗的显示与隐藏，默认为`false`，是必需的。

- `loading`: 布尔值，表示是否正在加载，默认为`false`。

- `text`: 字符串，表示弹窗中显示的内容，默认为`"Please enter at least one condition !"`。

- `title`: 字符串，表示弹窗的标题，默认为`"Notice Information"`。

## 方法（Methods）


- `handleCancel()`: 点击取消按钮时触发，如果`loading`为`true`则不执行任何操作，否则触发`cancel`事件。

- `handleOk(event)`: 点击确认按钮时触发，阻止事件冒泡，并触发`Ok`事件。

## 样式（Styles）


- `.tips-modal`: 弹窗的容器样式。

- `.modal-container`: 弹窗的主要容器样式。

- `.modal-container1`: 弹窗内部的容器样式，包含标题、内容和图标。

- `.icon-container`: 图标的容器样式。

- `.title`: 标题的样式。

- `.content`: 内容的样式。

- `.button`: 按钮的容器样式。

- `.reset-button`: 取消按钮的样式。

- `.search-button`: 确认按钮的样式。

- `.modal-triangle`: 三角形的样式，用于遮挡弹窗。

## 使用方法

在父组件中使用`tipsPopWindow`组件时，需要传入相应的属性，并监听`cancel`和`Ok`事件。例如：

```vue
<tipsPopWindow :visible="showTips" :loading="loading" :text="tipText" :title="tipTitle" @cancel="handleCancel" @Ok="handleOk"></tipsPopWindow>