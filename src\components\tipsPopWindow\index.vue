<template>
  <div v-show="visible" class="tips-modal" @click="handleCancel">
    <div class="modal-container" @click.stop>
      <div class="modal-container1">
        <!-- <div class="header-title">
        <i class="iconfont icon-tishi" />
        <div class="headermessage">{{ $t('common.prompt') }}</div>
      </div> -->
        <div class="icon-container">
          <i class="iconfont icon-tishi" />
        </div>
        <div class="title">{{ title }}</div>
        <div class="content">{{ text }}</div>
      </div>
      <div class="button">
        <a-button
          v-if="cancelBtn"
          ghost
          type="primary"
          class="reset-button"
          @click="handleCancel"
          :loading="loading"
        >
          {{ $t('common.buttonCancel') }}
        </a-button>
        <a-button type="primary" class="search-button" @click="handleOk" :loading="loading">
          {{ $t('common.buttonConfirm') }}
        </a-button>
      </div>
      <div class="modal-triangle"></div>
    </div>
  </div>
</template>

<script>
  import { Button } from 'ant-design-vue';
  export default {
    name: 'TipsPopWindow',
    components: { AButton: Button },

    props: {
      visible: {
        type: Boolean,
        default: false,
        required: true,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      text: {
        type: String,
        default() {
          return this.$t('customerVerify.atLeastOneCondition');
        },
      },
      title: {
        type: String,
        default: '',
      },
      cancelBtn: {
        // 是否需要取消按钮
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        confirmLoading: false,
        imageSrc: '',
      };
    },

    created() {},

    methods: {
      handleCancel() {
        if (this.loading) return;
        this.$emit('cancel');
      },
      handleOk(event) {
        event.stopPropagation();
        this.$emit('Ok');
      },
    },
  };
</script>

<style scoped lang="less">
  .tips-modal {
    position: fixed;
    z-index: 1001;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;

    .tips-modal-button-cancel {
      /deep/ .anticon-loading {
        margin-left: -5px !important;
      }
    }

    .tips-moadl-button-Ok {
      /deep/ .anticon-loading {
        margin-left: -26px !important;
      }
    }

    .modal-container {
      z-index: 1002;
      display: flex;
      flex-direction: column;
      position: relative;
      background: #ffffff;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.14);
      width: 450px;
      height: 330px;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      .header-title {
        display: flex;
        align-items: center;
        gap: 5px;
        width: 100%;
        padding: 10px;
        line-height: 1;
        .iconfont {
          font-size: 20px;
          line-height: 1;
        }
        .headermessage {
          font-size: 18px;
          font-weight: 400;
        }
      }
      .modal-container1 {
        display: flex;
        font-size: 60px;
        flex-direction: column;
        align-items: center;

        .icon-container {
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          .iconfont {
            font-size: 60px;
          }
        }

        .title {
          font-family: PingFang-SC-Heavy;
          font-size: 24px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 500;
          padding: 10px 0;
        }

        .content {
          font-family: PingFangSC-Regular;
          font-size: 14px;
          color: #333333;
          letter-spacing: 0;
          font-weight: 540;
          margin-bottom: 30px;
          padding: 0 20px;
          text-align: center;
        }
      }

      .button {
        margin-bottom: 30px;
        button + button {
          margin-left: 10px;
        }
      }
    }
  }
</style>
