// 账户设置文件

const accountSetting = {
  // 账户设置主页
  accountSetting: 'Account Setting',
  ExistBillingAccount: 'Existing Billing Account',
  useExistBillingAccount: 'Use Exist Billing Account',
  newBillingAccount: 'New Billing Account',
  queryAccount: 'Query Account',

  // 账户设置 --- 选择已有帐户弹窗
  billingAccountNo: 'Billing Account No.',
  accountName: 'Account Name',
  AGN: 'AGN',
  billEmail: 'Bill Email',
  prodFamily: 'Prod Family',
  billDay: 'Bill Day',
  billMedia: 'Bill Media',
  paymentMethod: 'Payment Method',

  // 查询表格
  numberType: 'Number Type',
  chargeCategory: 'Charge Category',

  // 主页号码列表
  serviceNumList: 'Service No. List',
  serviceNumType: 'Service No. Type',
  criteria: 'Criteria',
  assignedDeptBill: 'Assigned Dept Bill',
  serviceNum: 'Service No',
  serviceNo: 'Service No.',
  accountNo: 'Account No',
  billingAccountNo_R: 'Billing Account No.(R)',
  existingBillingAccountNo_R: 'existing Billing Account No(R)',
  newBillingAccountNo_R: 'new Billing Account No(R)',
  billingAccountNo_I: 'Billing Account No.(I)',
  existingBillingAccountNo_I: 'existing Billing Account No(I)',
  newBillingccountNo_I: 'new Billing Account No.(I)',
  departmentalBill: 'Departmental Bill',
  existingDepartmentalBill: 'existing Departmental Bill',
  newDepartmentalBill: 'new Departmental Bill',
  // departmentalName: 'Departmental Name',
  assignAccount: 'Assign Account',
  assignDepartmentalBill: 'Assign Departmental Bill',
  removeAccount: 'Remove Account',
  removeDepartmentalBill: 'Remove Departmental Bill',
  selectOrUnselectAll: 'Select/Unselect All Numbers',
  useNew_DN: 'Use New DN',
  use_PIPB_DN: 'Use PIPB DN',
  noCriteria: 'No Criteria',
  preferredFirst: 'Preferred First 1-5 Digits',
  preferredLast: 'Preferred Last 6-8 Digits',
  assigned: 'Assigned',
  unAssigned: 'UnAssigned',

  // 查询部门账单弹窗
  queryDepartmentBill: 'Query Department Bill',
  brNo: 'BR No',
  customerName: 'Customer Name',
  newAccount: 'New',
  departmentalName: 'Departmental Bill Name',
  serviceType: 'Service Type',

  // 新建部门账单弹窗
  departmentNum: 'Dept No',
  departmentName: 'Dept Name',
  parentDepartment: 'Parent Dept',
  brNum: 'BR No',
  accountNum: 'Account No',
  remark: 'Remark',
  newAddress: 'New Address',

  // 新增部门账单
  addBillDepartment: 'Add Departmental Bill',

  // 新建账户

  // customer
  customerInformation: 'Customer Information',
  customerId: 'Customer ID',
  dragonCustomerNo: 'HKT Customer No.',
  documentType: 'Document Type',
  documentNo: 'Document No.',
  highLevelCustomer: 'High Level Customer',
  AGNName: 'AGN Name',
  marketSegment: 'Market Segment',
  marketSubSegment: 'Market Sub Segment',
  correspondenceAddress: 'Correspondence Address',
  lob: 'LOB',
  flag: 'Flag',
  windingUpBankruptcy: 'Winding-up/Bankruptcy',
  risky: 'Risky',
  contact: 'Contact',
  sales: 'Sales',
  Optout: 'Opt-out',
  document: 'Document',
  industrialType: 'Industrial Type',
  industrialSubType: 'Industrial Sub Type',
  badPayment: 'Bad Payment',
  premierType: 'Premier Type',
  customerVerified: 'Customer Verified',
  writtenApprovalRequired: 'Written Approval Required',
  specialHandling: 'Special Handling',
  speakingPreference: 'Speaking Preference',
  accountInfomation: 'Account Infomation',
  ebill: 'Ebill',
  EN: 'EN',
  ZH: 'ZH',
  POBox: 'PO Box',
  overseaAddress: 'Oversea Address',

  createNewAccount: 'Create New Account',
  productFamily: 'Product Family',
  brand: 'Brand',
  billContact: 'Bill Contact',
  billRecipient: 'Bill Recipient',
  billMail: 'Bill Mail',
  billLanguage: 'Bill Language',
  itemDisplay: '$0 Item Display',
  billAddressType: 'Bill Address Type',
  genAccountNo: 'Gen Account No.',
  billAddress: 'Bill Address',
  billSendAddress: 'Bill Send Address',
  queryAddress: 'Query Address',
  addressEn: '(EN)',
  addressZH: '(ZH)',
  splitAddress: 'Split Address',
  token: 'Token',
  add: 'Add',
  HKTBillContract: 'HKT Bill Contract',
  yes: 'YES',
  no: 'NO',
  creditCardToken: 'Credit Card Token',
  cardCountry: 'Card Country',
  cardName: 'Card Name',
  cantonese: 'Cantonese',
  mandarin: 'Mandarin',

  // editAccouontInfo
  paper: 'Paper',
  english: 'English',
  traditionalChinese: 'Traditional Chinese',
  addressMapping: 'Address Mapping',

  // paymentMethod:'Payment Method',
  PCardArea: 'PCard Area',
  cardType: 'Card Type',
  expirationDate: 'Expiration Date',
  cardNO: 'Card No.(Token#)',
  cardHolderName: 'Card Holder Name',
  bankNo: 'Bank No.',
  ccc: 'CCC',
  cash: 'Cash',
  creditCardAutopay: 'Credit Card Autopay',
  directDebit: 'Direct Debit',
  payment: 'Payment',
  HKTBillContact: 'HKT Bill Contact', // XXX 这个字段简体和繁体需要修改
  // HKTBillContact
  type: 'Type',
  name: 'Name',
  contactPhone: 'Contact Phone',
  email: 'Email',
  action: 'Action',

  // selectAddress
  SBNo: 'SB No.',
  address: 'Address',
  resAvali: 'Res Avali',
  spareAvali: 'Spare Avali',
  twoN: '2N',
  DP: 'DP',
  floor: 'Floor',
  flat: 'Flat',
  SEL: 'SEL',
  modifyCustomer: 'Modify Customer',
  customer360: 'Customer 360',
  accountManage: 'Account Manage',
  envelopeDisplay: 'Envelope Display',
  envelopeTip: 'The envelope display field must be filled in with one',
  displaybill: 'Display $0 Bill items',
  LOB: 'LOB',
  billFrequence: 'Bill Frequence',
  waivedPaperBillFee: 'Waived Paper Bill Fee',
  includeOddCentsInBill: 'Include Odd Cents In Bill',
  // 提示信息
  needChooseDepBillTips: 'Please select the data that needs to add department bills!',
  accountListTips:
    'The account list can only contain a maximum of two account records, with one being R and the other being I!',
  pleaseChooseTips: 'Please select the data that needs to ',
  newAddTips: 'add ',
  depBillDataTips: 'department bills',
  removeTips: 'remove ',
  successTips: 'successfully',
  successSubmitTips: 'successfully Submitted !',
  notRepeatTips: 'Not repeat ',
  displayBillTip1:
    'This switch is for disable $0 items in Bill presentation requested by Customer.',
  displayBillTip2: 'Yes(Default) - Display the $0 definded in product setup.',
  displayBillTip3: 'No - Skip the $0 items in Bill Presentation.(Requested by customer).',
  emailIsRequired: 'The Bill Contact Email number format is incorrect !',
  billTypeRequired: 'Type is required !',
  standard: 'Standard',
  nonStandard: 'Non-standard',
  month: 'Month',
  quarter: 'Quarter',
  year: 'Year',
  emailCannotBeEmpty: 'Email cannot be empty',
  numberOfEmail: 'The number of email addresses cannot exceed three',
  emailSymbol: 'Each email address must contain the @ symbol',
  emailFormat:
    'Email format error, multiple email addresses are separated by commas in English, with a maximum of three email addresses set, eg: <EMAIL>,<EMAIL>,<EMAIL>',
  numberAddAccountInfo: 'Please add at least one R account information to the number!',
  numberAddAccountInfoWithI: 'Please add an I account information for all numbers!',
  atLeastExistsRAccount: 'There should be at least one account record with R in the account list!',
  maxTwoAccountsError: 'Each account number can only add a maximum of two records',
  emailFormatError: 'The Email format is incorrect!',
  creditAdmin: 'Credit Admin',
  pleaseChooseOneOfDatas: 'Please select one of the data!',
  nameRequired: 'Name is required!',
  AddressEnMsg: 'Address_EN is required',
  requiredFields: 'Required fields cannot be empty or incorrectly formatted!',
  newAccountNo: 'Billing Account No.:',
  assignBillingAccount: 'Assign Billing Account',
  removeBillingAccount: 'Remove Billing Account',
};
export default accountSetting;
