// 公共翻译文件
export default {
  // 头部标题
  queryTitle: 'Search',
  // 按钮类型
  buttonBack: 'Back',
  buttonImport: 'Import',
  buttonExport: 'Export',
  buttonQuery: 'Search',
  buttonOk: 'OK',
  buttonCancel: 'Cancel',
  buttonAdd: 'Add',
  buttonReset: 'Reset',
  buttonDelete: 'Delete',
  buttonStatus1: 'Effective',
  buttonStatus2: 'Expired',
  buttonUpload: 'Go to Download',
  download: 'Download',
  send: 'Send',
  buttonClose: 'Close',
  buttonInquiry: 'Inquiry',
  buttonFilter: 'Filter',
  buttonConfirm: 'Confirm',
  // 通用描述语
  type: 'Type',
  upload: 'Upload',
  seq: 'Seq',
  name: 'Name',
  // 输入提示语
  inputPlaceholder: 'Please Enter',
  selectPlaceholder: 'Please Select',
  // 页码
  paginationTotal: 'Total',
  paginationitems: 'items',
  // 弹窗名字
  modaltitleAdd: 'Add New',
  modaltitleUpdate: 'Update',
  modaltitleEdit: 'Edit',
  // 全局提示message
  successMessage: 'Operation Successful',
  errorMessage: 'Operation Failed',
  // 表格
  action: 'Action',
  deleteDraft: 'Delete Draft',
  buttonAmend: 'Amend',
  buttonView: 'View',
  // 二次确认提示框
  prompt: 'Prompt',
  confirmSubmission: 'Confirm Submission',
  yes: 'Yes',
  no: 'No',
  tableTotalCount: 'Total {total} Items',
  previous: 'Previous',
  next: 'Next',
  submit: 'Submit',
  submitOrder: 'Submit Order',
  return: 'Return',
  reject: 'Reject',
  enterOneSearchTip: 'Please enter at least one search criteria.',
  edit: 'Edit',
  confirm: 'Confirm',
  newAddress: 'New Address',
  deleteConfirm: 'Confirm Deletion?',
  removeConfirm: 'Confirm Remove?',
  cancelConfirm: 'Confirm Cancel?',
  change: 'Change',
  changeList: 'Change List',
  noNull: 'Cannot Be Empty',
  selectOne: 'Please select at least one number',
  productName: 'Product Name',
  save: 'Save',
  successTip: 'Execution successful!',
  complete: 'complete',
  enterNumberTip: 'Please enter a non repeating number that belongs to the number range',
  // 操作提示语
  addAccountSuccess: 'Account added successfully!',
  removeAccountSuccess: 'Account removed successfully!',
  cannotBeEmpty: 'Cannot be empty',
  inputContainsIllegalCharactersError: 'Input contains illegal characters, please re-enter!',
  emailErrorTip: 'Please enter the correct email address!',
  phoneErrorTip: 'Please enter the correct number!',
  add: 'Add',
  waiveAmountError: 'The waived amount per line cannot exceed {maxCharge}!',
  totalChargeAmountError: 'The total charge amount cannot exceed {maxCharge}!',
  amountLessError: 'The amount cannot be less than 0 for verification!',
  otcAmountError: 'OTC charge cannot be empty!',
  submitOrderSuccess: 'Order Successfully Submitted !',
  cancelOrderSuccess: 'Order Successfully Cancelled !',
  amendOrderSuccess: 'Order { orderNo } Successfully Amended!',
  yourOrderNumber: 'Your Order Number: ',
  sameAddressError: 'Cannot select same address, please re-select! ',
  inputLetterNumber: 'Please type the letters and numbers',
  limitLength: 'Please enter a length of {length} characters',
  rangeLength: 'Please enter the {rangeLength} character length within the range',
  rangeNumber: 'Please enter a number within the range of {rangeNumber}',
  selectedNumberRestrict: 'Only {numberLength} number(s) can be selected! ',
  pureNumbers: 'Please enter pure numbers correctly',
  mutexTips: '{name} is mutually exclusive with the current check',
  mutexNextStepTips: '{name} and {target} are mutually exclusive',
  duplicateCheckedTips: 'The {name} element cannot be selected repeatedly',
  copyToAllOtherNumbers: 'Copy Product Configuration To All Other Numbers !',
  months: 'Months',
  days: 'Days',
  hours: 'Hours',
  minutes: 'Minutes',
  seconds: 'Seconds',
  needSaveBtn: 'Please click the save button first',
  partialDependenceTip:
    'Some dependencies exist between the current check box and {name}. If you click OK, the dependency will be automatically selected. Otherwise, the current check box will be canceled',
  partialDependenceTips:
    '{name} Some dependencies exist with the current check box. Please select at least one',
  completeDependenceCheckTip:
    'The currently selected part is fully dependent on {name}. If you click OK, the full dependency will be automatically selected. Otherwise, the currently selected part will be unselected',
  completeDependenceCancelTip:
    'The currently selected part has a complete dependency with {name}. Clicking OK will automatically cancel the complete dependency. Otherwise, the current check will remain',
  validateDependenceTip: '{currentName} and {name} have some dependencies, please manually select',
  validateDependenceAllTip: '{currentName} and {name} are completely dependent. Select manually',
  DocumentNoError:
    'The format of the Document number is incorrect. Please use XXXXXXXX-XXX or XXXXXXXXXXX format. X represents only numbers allowed',
  DocumentNoPassportError:
    'The format of the ID number is incorrect. Please enter content with fewer than 40 characters',
  noChangeData: 'The data has not changed and cannot be submitted',
  isRequired: 'is required !',
  needAddBtn: 'Please click the Add button first',
  serviceNoPlaceholder: 'Enter up to 20 numbers, separated by commas',
  cannotSelect: 'The currently checked part can only be ordered by the main number',
  cannotSelectNext: 'The {name} element can only be ordered by the main number',
  welcomeLetter: 'Welcome Letter',
  searchNotNull: 'The search conditions cannot be empty',
  noMatchFound: 'No matching data found !',
  repeatOrder:
    'The number {number} has already subscribed to {name}, and no need to subscribe again.',
  netWorkError: 'Network error, please try again later',
  serverError: 'Server error, please try again later',
  requestTimeout: 'Request timed out, please try again later',
  requestFailed: 'The requested resource does not exist',
  FileFormatVerification: 'Invalid file format, please upload {fileType} file',
  FileNumberVerification: 'The number of file cannot exceed {number}',
};
