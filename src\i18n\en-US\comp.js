const comp = {
  // 客户选择
  CustomerList: 'Customer List',
  CustomerName: 'Customer Name',
  CustomerNo: 'Customer No.',
  DocumentType: 'Document Type',
  DocumentNo: 'Document No.',
  LOB: 'LOB',
  Criteria: 'Criteria',
  Product: 'Product',
  CustomerOrderedList: 'Customer Ordered List',
  selection: 'Selection',
  Sel: 'Sel',
  ProductFamily: 'Product Family',
  ProductType: 'Product Type',
  ProductName: 'Product Name',
  InstallationAddress: 'Installation Address',
  ExistPending: 'Exist Pending',
  MACDCart: 'MACD Cart',
  MACDAction: 'MACD Action',
  MarketSegment: 'Market Segment',
  HKTCustomerNo: 'HKT Customer No.',
  AM: 'AM',
  ASM: 'ASM',
  oneCustomerError: 'Please select a customer!',
  // 客户选择 - 表单查询弹窗
  CustomerSelection: 'Customer Selection',
  DragonCustomerNo: 'HKT Customer No.',
  SalesTeam: 'Sales Team',
  SalesSubTeam: 'Sales Sub Team',
  //  产品树
  SelectProduct: 'Select Product',
  CustomerLob: 'Customer LOB',
  CustomerFamily: 'Customer Family',
  AddProductInstall: 'Add Product (New Install)',
  oscaNewProductTitle: 'Product Selection',
  oscaProductAddressTitle: 'Address Mapping & Select Product',
  Equipment: 'Equipment',
  Premium: 'Premium',
  Confirm: 'Confirm',
  Description: 'Description',
  serviceNo: 'Service No.',
  ProductSelection: 'Product Selection',
  AdditionalProduct: 'Additional Product',
  MainProduct: 'Main Product',
  standardMRC: 'Standard monthly rent',
  type: 'Type',
  Charge: 'Charge',
  Period: 'Period',
  RedeemedPoints: 'Redeemed Points',
  MonthlyRentalCharge: 'Monthly Rental Charge',
  selectMainProd: 'Please select the main product!',
  attributeEdit: 'Attribute Edit',
  selectMainProdOnlyOne: 'Only one main product can be selected',
  cannotMoreThan:
    'The data under the {name} can be selected at most {num} items and Currently {selectedCount} item has been selected',
  cannotLessThan:
    'At least {num} items need to be selected for data under the {name} and currently {selectedCount} item has been selected',
  selectMainProdMustHasPackageElement: 'Main product must have at least one package and element',
  attributeDetail: 'Attribute Detail',
  attributeMustEditConfim:
    'The properties of the {elementName} under the {packageName} under the {productName} need to be edited to confirm !',
  attributeEditCorrectAmount: 'Plesse enter an appropriate amount.',
  // 产品树 - 税率改变 - taxRateExchange
  taxRateExChangeTitle: 'Gold Rate Call Plan (Destination/Rate)',
  Country: 'Country',
  taxRateExChangePrompt: 'Please enter a valid positive or decimal number',
  selectedList: 'Selected List',
  RatePirceIsNotNull: '{RateName} price cannot be empty!',
  // 地址选择 - Address Mapping
  Address: 'Address',
  SBNo: 'SB No.',
  ServiceNo: 'ServiceNo',
  Floor: 'Floor',
  Flat: 'Flat',
  Resource: 'Resource',
  Spare: 'Spare',
  '2N': '2N',
  DP: 'DP',
  region: 'Region',
  street: 'Street',
  district: 'District',
  estate: 'Estate',
  building: 'Building',
  Flat_Unit_Room: 'Flat/Unit/Room',
  LotNumber: 'Lot Number',
  atLeastOneCondition: 'Please enter at least one condition',
  tipsText1: 'Please enter at least one search criteria.',
  tipsText2: 'Please select at least one address',
  tipsText3: 'Confirm order submission',
  tipsText4: 'Please add Service Number first!',
  tipsText5: 'Are you sure you want to cancel the current main product?',
  tipsText6: 'Please select at least one order',
  newAddressMappingTitle: 'New Address Mapping',
  RegioRuleMessage: 'Please select the Regio',
  DistrictRuleMessage: 'Please select the District',
  StreetRuleMessage: 'Please enter the Street',
  EstateRuleMessage: 'Please enter the Estate',
  BuildingRuleMessage: 'Please enter the Building',
  FloorRuleMessage: 'Please enter the Floor',
  LotNumberRuleMessage: 'Please enter the Lot Number',
  selectInquiry: 'Please select after Inquiry',
  showAllSBNOCheckBox: 'Show All SB No. associated with the address',
  checkShowAllSBNOCheckBox: 'Please check "Show All SB No. associated with the address"',
  SBNOListHasNoData: 'SBNO list has no data',
  noExitCurrentServiceNO: 'NO SUCH WORKING Service NO.',
  // 号码选择 - numberSelection
  tamplateTwoHeadTitle: 'Single Product Order - Number Selection',
  numberSelection: 'Number Selection',
  AddServiceNo: 'Add Service No.',
  addTitle: 'Service No. Search',
  SelectedServiceNoQty: 'Selected Service No. Qty',
  Allchecked: 'Select/Unselect all Service No. in current page.',
  RemoveSelected: 'Remove Selected',
  NoData: 'No Data',
  copyToAll: 'Copy Product Configuration To All Selected Numbers!',
  numberOfSelect: 'Number of Select',
  numberLimitQuantityPrompt: 'Up to 20 numbers can be entered, separated by commas',
  enterTheCustomerName: 'Please enter the customer name',
  completeNumber: 'Please enter a known 8-digit number',
  // 号码选择 - numberSelection - addPopWindow
  ServiceNoType: 'Service No. Type',
  ServiceNoQty: 'Service No. Qty',
  ServiceGroup: 'Service Group',
  selectType: 'Select Type',
  serviceGroup_Service: 'Service Group / Service',
  Service: 'Service',
  ReserveDN: 'Reserve DN',
  ReservationCode: 'Reservation Code',
  BOC: 'BOC',
  Project: 'Project',
  NewInstallation: 'New Installation',
  PIPBNumber: 'PIPB Number',
  NoCriteria: 'No Criteria',
  PreferredFirst: 'Preferred First 1-5 Digits',
  PreferredLast: 'Preferred Last 6-8 Digits',
  // 号码选择 - numberSelection - editProduct
  editProductTitle: 'Product/Package/Vas/Pricing Attributor Edit',
  addressMapping: 'Address Mapping',
  numberAttributor: 'Number Attributor',
  callForwardingNo: 'Call Forwarding No.',
  // 附件 - Attachment
  Attachment: 'Attachment',
  AttachmentName: 'Name',
  AttachmentRemark: 'Remark',
  UploadTime: 'Upload Time',
  UploadUser: 'Upload User',
  AttachmentType: 'Attachment Type',
  AttachmentUploading: 'Attachment Uploading',
  Upload: 'Upload',
  FileSizeLimit: 'The file size should not exceed 10MB',
  FileTypeLimit: 'Please upload the correct file type',
  UploadAttchmentTips: 'Please Upload Attachment',
};
export default comp;
