const customerVerify = {
  CustomerList: 'Customer List',
  // 列表字段
  CustomerName: 'Customer Name',
  CustomerNo: 'Customer No.',
  DocumentType: 'Document Type',
  DocumentNo: 'Document No.',
  LOB: 'LOB',
  Criteria: 'Criteria',
  Product: 'Product',
  CustomerOrderedList: 'Customer Ordered List',
  selection: 'Selection',
  Sel: 'Sel',
  ProductFamily: 'Product Family',
  ProductType: 'Product Type',
  ProductName: 'Product Name',
  InstallationAddress: 'Installation Address',
  ExistPending: 'Exist Pending',
  MACDCart: 'MACD Cart',
  MACDAction: 'MACD Action',
  MarketSegment: 'Market Segment',
  HKTCustomerNo: 'HKT Customer No.',
  AM: 'AM',
  ASM: 'ASM',
  // osca 产品树
  SelectProduct: 'Select Product',
  CustomerLob: 'Customer LOB',
  CustomerFamily: 'Customer Family',
  AddProductInstall: 'Add Product (New Install)',
  oscaNewProductTitle: 'Product Selection',
  oscaProductAddressTitle: 'Address Mapping & Select Product',
  Equipment: 'Equipment',
  Premium: 'Premium',
  Confirm: 'Confirm',
  Description: 'Description',
  // 表单查询弹窗
  CustomerSelection: 'Customer Selection',
  DragonCustomerNo: 'HKT Customer No.',
  SalesTeam: 'Sales Team',
  SalesSubTeam: 'Sales Sub Team',
  // 新增页面-底部工具栏
  Next: 'Next',
  Previous: 'Previous',
  Save: 'Save',
  Submit: 'Submit',
  Cancel: 'Cancel',
  printOrder: 'Print Order',
  addHunting: 'Add Hunting',
  // 新增页面-Address Mapping
  OK: 'OK',
  // 新增页面-tamplateOne
  pageTitle: 'Single Product Order - Address Mapping & Product Selection',
  headTitle: 'Address Mapping',
  SBNo: 'SB No.',
  serviceNo: 'Service No.',
  ProductSelection: 'Product Selection',
  PrimaryProduct: 'Primary Product',
  AdditionalProduct: 'Additional Product',
  MainProduct: 'Main Product',
  // 新增页面-Address Mapping
  Address: 'Address',
  'SB No': 'SB No.',
  ServiceNo: 'ServiceNo',
  Floor: 'Floor',
  Flat: 'Flat',
  Resource: 'Resource',
  Spare: 'Spare',
  '2N': '2N',
  DP: 'DP',
  region: 'Region',
  street: 'Street',
  district: 'District',
  estate: 'Estate',
  building: 'Building',
  Flat_Unit_Room: 'Flat/Unit/Room',
  LotNumber: 'Lot Number',
  atLeastOneCondition: 'Please enter at least one condition',
  tipsTitle1: 'Notice Information',
  tipsText1: 'Please enter at least one search criteria.',
  tipsText2: 'Please select at least one address',
  tipsText3: 'Confirm order submission',
  tipsText4: 'Please add Service Number before proceeding to next step!',
  tipsText5: 'Are you sure you want to cancel the current main product?',
  tipsText6: 'Please select at least one order',
  tipsText7: 'Please enter at least one lines',
  // 新增页面-Address Mapping-addAddress
  newAddressMappingTitle: 'New Address Mapping',
  RegioRuleMessage: 'Please select the Regio',
  DistrictRuleMessage: 'Please select the District',
  StreetRuleMessage: 'Please enter the Street',
  EstateRuleMessage: 'Please enter the Estate',
  BuildingRuleMessage: 'Please enter the Building',
  FloorRuleMessage: 'Please enter the Floor',
  LotNumberRuleMessage: 'Please enter the Lot Number',
  // 新增页面-Address Mapping-component1
  standardMRC: 'Standard monthly rent',
  type: 'Type',
  Charge: 'Charge',
  Period: 'Period',
  Action: 'Action',
  MonthlyRentalCharge: 'Monthly Rental Charge',
  // 新增页面-numberSelection
  tamplateTwoHeadTitle: 'Single Product Order - Number Selection',
  numberSelection: 'Number Selection',
  AddServiceNo: 'Add Service No.',
  addTitle: 'Service No. Search',
  SelectedServiceNoQty: 'Selected Service No. Qty',
  Allchecked: 'Select/Unselect all Service No. in current page.',
  RemoveSelected: 'Remove Selected',
  NoData: 'No Data',
  copyToAll: 'Copy Product Configuration To All Selected Numbers!',
  // 新增页面-numberSelection-addPopWindow
  ServiceNoType: 'Service No. Type',
  ServiceNoQty: 'Service No. Qty',
  ServiceGroup: 'Service Group',
  selectType: 'Select Type',
  serviceGroup_Service: 'Service Group / Service',
  Service: 'Service',
  ReserveDN: 'Reserve DN',
  ReservationCode: 'Reservation Code',
  BOC: 'BOC',
  Project: 'Project',
  NewInstallation: 'New Installation',
  PIPBNumber: 'PIPB Number',
  NoCriteria: 'No Criteria',
  PreferredFirst: 'Preferred First 1-5 Digits',
  PreferredLast: 'Preferred Last 6-8 Digits',
  // 新增页面-numberSelection-editProduct
  editProductTitle: 'Product/Package/Vas/Pricing Attributor Edit',
  addressMapping: 'Address Mapping',
  installationAddress: 'Installation Address',
  SB_No: 'SB No',
  numberAttributor: 'Number Attributor',
  callForwardingNo: 'Call Forwarding No.',
  // 新增页面-MainProduct&PremiumProduct-num&score
  ProductQuantity: 'Product Quantity',
  RedeemablePoints: 'Redeemable Points',
  NoOfLinesInstalled: 'No. Of Lines Installed',
  NoOfPremiumPoints: 'No. Of Premium Points',

  // 提示信息字段
  selectMainProd: 'Please select the main product!',
  accountListError: 'Please include at least one account list with chargeCategory I!',
  iddAccountListError: 'Please include at least one account list with chargeCategory I!',
  customerError: 'Please select customer(s)!',
  numberOfSelect: 'Number of Select',
  citinetPageTitle: 'Citinet Group Product Selection',
  huntingProductSelectPageTitle: 'Hunting Group Product Selection',
  idapProductPageTitle: 'IDAP(Site Level) Group Product Selection',
  attributeEdit: 'Attribute Edit',
  // selectType: 'Please select a type!',

  selectServiceNo: 'Please select at least one number',
  cancelIndividualVas: 'Please confirm if you want to cancel Individual VAS.',
  selectMainProdOnlyOne: 'Only one main product can be selected',
  numberNeedToSelectSubProduct: 'Number {number} need to select a sub product',
  numberSequenceUniqueness: 'The sequence {number} is repeated, please edit the order again',
  premiumProduct: 'PREMIUM PRODUCT',
  equipmentProduct: 'Equipment Product',
  cannotMoreThan:
    'The data under the {name} can be selected at most {num} items and Currently {selectedCount} item has been selected',
  cannotLessThan:
    'At least {num} items need to be selected for data under the {name} and currently {selectedCount} item has been selected',
  selectMainProdMustHasPackageElement: 'Main product must have at least one package and element',
  attributeDetail: 'Attribute Detail',
  selectInquiry: 'Please select after Inquiry',
  attributeMustEditConfim:
    'The properties of the {elementName} under the {packageName} under the {productName} need to be edited to confirm !',
  numberLimitQuantityPrompt: 'Up to 20 numbers can be entered, separated by commas',
  enterTheCustomerName: 'Please enter the customer name',
  jumpHuntingForCitinetTips: 'Jumping to huntingForCitinet business...',
  huntingForCitinetGroupProductSelection: 'Hunting For Citinet Group Product Selection',
  NoChange: 'No Change',
  showAllSBNOCheckBox: 'Show All SB No. associated with the address',
  Other: 'Other',
  ChangeDetail: 'Change Detail',
  citinetGroupProduct: 'Citinet Group Product',
  exDirectory: 'EX-Directory',
  directoryName: 'Directory Name',
  limitBusinessNumbers:
    'The current number of newly installed business numbers must not exceed {Number}',
  omitUnnecessaryNumbers:
    'The current number has reached {Number}, and any remaining numbers that cannot be added will be automatically omitted',
  oneCustomerError: 'Please select a customer!',
  badCustomerError:
    'This operation is not allowed because the current customer has a credit problem!',
  fixedLineOK: 'FixedLine:OK',
  FixedLineFalse: 'FixedLine:Customer payment overdue,as follow:',
  totalOverdueAmount: 'Total Overdue Amount:$',
  listofAccountNumber: 'List of Account Number(s)',
  pleaseSelectOneOfTheData: 'Please select one of the data',
  completeNumber: 'Please enter a known 8-digit number',
  pageTitleOfCommit: 'Order Result',
  ccpTaxRate:
    'The property of {elementName} under {productName} and {packageName} cannot be empty!',
  pendingOrderNotDoMACDTips:
    'DN ({SERVICE_NO_List}) is in transit and unable to perform MACD services',
  editDeviceAttributes: 'The properties of under the {productName} need to be edited to confirm !',
  accountInfoOfNewInstall: 'Please include at least one account list with chargeCategory R!',
  checkShowAllSBNOCheckBox: "Please check 'Show All SB No. associated with the address'",
  SBNOListHasNoData: 'SBNO list has no data',
  noExitCurrentServiceNO: 'NO SUCH WORKING Service NO.',
};

export default customerVerify;
