// 履约信息

const fulfillmentInfo = {
  fulfillmentInfo: 'Fulfillment Info',

  // HKT Contact List 表格字段
  HKTContactList: 'HKT Contact List',
  seq: 'Seq',
  type: 'Type',
  name: 'Name',
  salesCode: 'Sales Code',
  contactPhone: 'Contact Phone',
  mobile: 'Mobile',
  email: 'Email',
  dummyCode: 'Dummy Code',
  orderSalesCode: 'Order Sales Code',

  //Customer Contact List表格字段
  customerContactList: 'Customer Contact List',

  //SRD 字段
  srd: 'SRD',
  serviceNo: 'Service No.',
  appointmentDate: 'Appointment Date',
  preWiringDate: 'Pre-Wiring Date',
  srdNoNull: 'SRD time cannot be empty!',
  constructionReservationTips: 'Please make a construction appointment!',

  // otherInformation 字段
  otherInformation: 'Others',
  OASISSerialNo: 'OASIS Serial No',
  notEnteredOASISSerialNo: 'Please input the OASIS Serial No.',
  OASISSerialNoError:
    'The format of OASIS Serial No is incorrect. Please use XXYY-YYYYYYYY format.X represents allowing letters+numbers, Y represents allowing only numbers.',
  billingCustomerReference: 'Billing Customer Reference (To be displayed on bill)',
  project: 'Project Code',
  OPPID: 'OPPID',
  terminationReason: 'Termination Reason',
  terminationRemark: 'Termination Remark',
  welcomeLetterCustomerHotlineNumber: 'Welcome Letter Customer Hotline Number',
  welcomeLetter: 'Welcome Letter',
  orderRemark: 'Order Remark',
  newHuntingForCitinetGroup:
    'Is it possible to open a new Hunting for Citinet Group at the same time',
  // HKT Contact List 新增弹窗字段
  HKTContact: 'HKT Contact',
  participantsType: 'Partcipant Type',
  staffId: 'Staff Id',
  orderSalesType: 'Order Sales Type',
  agentCode: 'Agent Code',
  agentName: 'Agent Name',
  participantsTypeWarning: 'The format of the Participants Type field is incorrect!',
  contactPhoneWarning: 'The format of the Contact Phone field is incorrect!',
  mobileWarning: 'The format of the Mobile field is incorrect!',
  emailWarning: 'The format of the Email field is incorrect!',
  // Customer Contact List 新增弹窗字段
  title: 'Title',

  // SRD 弹窗
  SB_No: 'SB No.',
  address: 'Address',
  repeatChoose: "Can't repeat the selection!",
  customerContactTypeRequired: 'Customer Contact Type is required !',
  customerContactNameRequired: 'Customer Contact Name is required !',
  customerContactPhoneRequired: 'Customer Contact Contact Phone is required !',
  customerContactPhoneIncorrectFormat:
    'Customer Contact Contact Phone , Please enter eight digits starting with 5, 6, 8, or 9',
  customerContactMobileIncorrectFormat:
    'Customer Contact Mobile , Please enter eight digits starting with 5, 6, 8, or 9',
  emailIncorrectFormat: 'Please enter the correct email format',
  HKTTypeRequired: 'HKT Contact Participants Type is required !',
  HKTOrderSaleTypeRequired: 'HKT Contact Order Sale Type is required !',
  HKTEmailRequired: 'HKT Contact Email is required !',
  HKTMobileIncorrectFormat:
    'HKT Contact Mobile , Please enter eight digits starting with 5, 6, 8, or 9',
  HKTPhoneIncorrectFormat:
    'HKT Contact Contact Phone , Please enter eight digits starting with 5, 6, 8, or 9',
  HKTContactPhoneRequired: 'HKT Contact Contact Phone is required !',
  HKTStaffIdRequired: 'HKT Contact Staff ID is required !',
  HKTNameRequired: 'HKT Contact Name is required !',
  HKTAgentNameRequired: 'HKT Contact Agent Name is required !',
  HKTAgentCodeRequired: 'HKT Contact Agent Code is required !',
  dateComparisonVerification:
    'The SRD date should not be earlier than the latest of the appointment date and pre wiring date!',
  errorEmailTips: 'Incorrect email address, please re-enter',
  errorPhoneTips: 'Incorrect phone number format, please re-enter',
  HKTContactValidTips: 'HKT contacts must contain at least one piece of data!',
  customerContactValidTips:
    'The customer contact person must contain at least one piece of data with participant type Admin!',
  notNeedAppointmentTips: 'New address does not require construction appointment',
  gettingAppointmentTime: 'Getting reservation information ({second} seconds)',
  cancelReservation: 'Cancel Reservation',
  notificationLetter: 'Notification Letter',
  nonAppointmentItem: 'No field service appointments booked. Need to create one?',
};
export default fulfillmentInfo;
