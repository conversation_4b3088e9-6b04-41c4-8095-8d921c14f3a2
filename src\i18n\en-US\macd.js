export default {
  removeDepartmentalBill: 'Remove Departmental Bill',
  assignDepartmentalBill: 'Assign Departmental Bill',
  searchingCriteria: 'Searching Criteria',
  billingAccountExisting: 'Billing Account(Existing)',
  chargeCategoryExisting: 'Charge Category(Existing)',
  chargeCategoryNew: 'Charge Category(New)',
  billingAccountNew: 'Billing Account(New)',
  newDepartmentalBill: 'Departmental Bill(New)',
  oldDepartmentalBill: 'Departmental Bill(Existing)',
  originProductName: 'Origin Product Name',
  newProductName: 'New Product Name',
  change: 'Change',
  keyWord: 'Key Word',
  copyNumber: 'Copy Product Configuration To All Other Numbers !',
  noRepeatNumber: 'The number has already been added, please choose another number!',
  productVASPRICE: 'Product/VAS/PRICE',
  installationAddress: 'Installation Address',
  changeBillAddress: 'Change Bill Address',
  en: 'EN',
  cn: 'CN',
  moreChangeAction: 'More Change Action',
  oneCallPcf: 'ONE CALL PCF',
  pcf: 'PCF',
  disconnectReasonRequired: 'Disconnect Reason Required',
  applicationDateRequired: 'Application Date Required',
  srdRequired: 'SRD Required',
  appointmentDateRequired: 'Appointment Date Required',
  chargePerDn: 'Charge per DN',
  quantity: 'Quantity',
  standardCharge: 'Standard charge',
  waivedAmountPerLine: 'Waived amount per line',
  totalCharge: 'Total Charge',
  selectOne: 'You can only select one data item!',
  terminationExclusive: 'The termination service is exclusive to other services.', // 拆机业务和其他业务互斥
  noRepeatService: 'No repeat service provided!', //请勿重复选择做相同业务
  transferOwnership: 'Transfer Ownership',
  termination: 'Termination',
  bundleType: 'Bundle Type',
  groupNumber: 'Group Number',
  group: 'Group',
  onlyOneGroup: 'Only one group product can be selected!',
  selectProduct: 'Select Product',
  waivePenalty: 'Waive Penalty',
  salesRenewal: 'Sales Renewal',
  marketingRenewal: 'Marketing Renewal',
  changeType: 'Change Type',
  before: 'Before',
  after: 'After',
  rebateFeeMoreThanMrcFee: '{rebateElement} Rebate fee cannot be greater than {mrcElement} MRC fee',
  rebatePeriodEqualMrcPeriod:
    'The {rebateElement} Rebate contract period is equal to the {mrcElement} MRC contract period',
  onlyIDD: 'Only one IDD data can be selected!',
  productChangesExclusive: 'The second level business of product change is mutually exclusive',
  relationshipChangeExclusive:
    'Mutual exclusion of secondary business related to relationship changes',
  addressIsNull: 'Installation address is empty',
  selectAddress: 'Please select an address',
  selectServiceNo: 'Please check the number',
  sameProductExclusive: 'All numbers must be the same product',
  sameAddressExclusive: 'All numbers must be the same address',
  mrcValueUpdateNotChooseRebate: '{name} MRC Value has been modified, cannot select Rebate',
  needToChooseMrc: 'Need to select MRC {name} first',
  detail: 'Detail',
  onlyDDI: 'Only one DDI data can be selected!',
  mrcValueUpdateNotChooseRebateNext:
    '{mrcName} MRC Value has been modified, cannot select {rebateName}',
  changeGroupTips: 'Current group has not been changed, unable to perform this operation.',
  TOAST_MESSAGES: {
    TERMINATION_EXISTS:
      'This operation has already been performed on the current order and cannot be processed again!',
    TERMINATION_WARNING:
      'Processing this operation will withdraw all other operations of the current order!',
    INVALID_ACTION:
      'The current order has already been terminated, and this operation cannot be processed again!',
    SAMETYPE_ACTION:
      'The current order has already performed a similar operation, and this operation cannot be processed again!',
    NEWLIMIT_ACTION: 'This number is newly added and is not allowed to perform this operation!',
    TYPELIMIT_ACTION: 'This number type is not allowed to perform this operation!',
    TERMINATION_NEW: 'New member added, unable to perform this operation, please select again',
  },
  memberNumber: 'Member Number',
  noServiceChange: 'No change in the number.',
  changeGroupWarning: 'Please select the group main number to perform the Change Group operation',
  changeGroupError: 'The current operation cannot be continued. Please try again later!',
  relocationType: 'Relocation Type',
  BID: 'BID',
  view: 'View',
  errOrderTipText: 'The current order does not exist. Please try again!',
  appendContractTips:
    'The start time of the new contract will append the end time of the old contract.',
  macdActionIsNull: 'The data in the MACD cart is empty!',
  selectAccountRepeatError:
    'The selected {chargeCategory} account is duplicated, please select again',
  jumpageAddressMessage:
    'Different installation addresses do not permit relocation in a single action.',
  memberProductWarning:
    ' At present, only one member product is configured, and this business cannot be done temporarily',
  renewalWarning: 'Renewal backdate cannot perform other MACD actions',
  renewalError: 'Renewal cannot be processed if backdated more than 120 days',
  pcfProduct: 'Please select PCF product!',
  targetNumber: 'Please enter the target number!',
  IDDTypeMutualExclusionOtherType:
    'IDD type orders and other types of orders cannot perform MACD business simultaneously!',
  rAccountNotNull: 'The number {number} must contain at least one R-type account',
  pleaseSelectProduct: 'Please select product!',
  noDataChange: 'No data changes have occurred!',
};
