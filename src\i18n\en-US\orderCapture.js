export default {
  // 列表 start
  pleaseEnterCustomerName: 'Please enter customer name',
  serviceNo: 'Service No',
  billingAccountNo: 'Billing Account No',
  orderCaptureListFormTitleList: 'Offer Pre-Approval Form List',
  customAFList: 'Customer AF List',
  afInfo: 'AF Info',
  salesAndAsm: 'Sales&ASM',
  afNo: 'AF No.',
  afStatus: 'AF Status',
  afCreateDate: 'Create Date',
  afPendingApproval: 'AF Pending Approval',
  afApproved: 'AF Approved',
  afRejected: 'AF Rejected',
  afCompleted: 'AF Completed',
  afCancelled: 'Cancelled',
  salesSegment: 'Sales Segment',
  salesName: 'Sales Name',
  salesManCode: 'SalesMan Code',
  asmName: 'ASM Name',
  staffNo: 'Staff No.',
  nextApprove: 'Next Approve',
  status: 'Status',
  dragonCustNo: 'HKT Customer No.',
  pleaseEnterCustomerInfo: 'Please enter customer info',
  documentType: 'Document Type',
  documentNo: 'Document No.',
  lob: 'LOB',
  pleaseSelectProductName: 'Please select product name',
  // end
  // 详情 & 编辑 - information
  AfOperationTitle: 'Application Form',
  AfInformation: 'AF Information',
  EditAFInfo: 'Edit AF Info',
  SBNo: 'SB No',
  AfInfoNoDataTip: 'Click Edit "Edit AF Info" to add product details.',
  AddressProduct: 'Address&Product',
  DNSelection: 'DN Selection',
  Account: 'Account',
  Fulfillment: 'Fulfillment',
  // 详情 & 编辑 - endorsement
  AfEndorsement: 'AF Endorsement',
  TotalContractRevenue: 'Total Contract Revenue',
  AFEndorsementApprover: 'AF Endorsement Approver',
  AFGeneration: 'AF Generation',
  AFGenerationTip1: 'Click the button to preview and download the Application Form',
  AFGenerationTip2: 'Select download the Supplementarty Form',
  SupplementartyFormCitinet: 'Supplementarty Form-Citinet',
  SupplementartyFormIDAP: 'Supplementarty Form-IDAP',
  LastExportedRecord: 'Last exported record',
  UploadSignedAF: 'Upload Signed AF',
  LastestSignedAF: 'Lastest Signed AF',
  SignedFileName: 'Name',
  SalesNote: 'Sales Note',
  ASMSalesSupportNote: 'ASM/Sales Support Note',
  SupportedExtension: 'Supported extension',
  OrderRelatedIssue: 'Order related issue',
  // 详情 & 编辑 - order information
  AfOrderInformation: 'Order Information',
  OrderNo: 'Order No.',
  SubmitOrderUser: 'Submit Order User',
  SubmitOrderTime: 'Submit Order Time',
  OrderStatus: 'Order Status',
  Receiver: 'Receiver',
  ReceiveTime: 'Receive Time',
};
