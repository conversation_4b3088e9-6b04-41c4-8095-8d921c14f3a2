// 汇总界面

const orderSummary = {
  // Address Mapping
  orderSummary: 'Order Summary',
  addressMapping: 'Address Mapping',
  installationAddress: 'Installation Address',
  sbNo: 'SB No.',

  // Product Selected
  productSelected: 'Product Selected',
  primaryProduct: 'Primary Product',
  additionalProduct: 'Additional Product',
  type: 'Type',
  charge: 'Charge',
  period: 'Period',
  action: 'Action',

  // Account Info
  accountInfo: 'Account Info',
  accountName: 'Account Name',
  billingAccountNo: 'Billing Account No.',
  billDay: 'Bill Day',
  billMedia: 'Bill Media',
  paymentMethod: 'Payment Method',
  prodFamily: 'Prod Family',
  chargeCategory: 'Charge Category',

  // HKT Contact List
  HKT_ContactList: 'HKT Contact List',
  seq: 'Seq',
  name: 'Name',
  salesCode: 'Sales Code',
  phone: 'Phone',
  mobile: 'Mobile',
  email: 'Email',

  // Customer Contact List
  customerContactList: 'Customer Contact List',
  contactPhone: 'Contact Phone',

  // Fulfillment Info
  fulfillmentInfo: 'Fulfillment Info',
  srd: 'SRD',
  appointmentDate: 'Appointment Date',
  preWiringDate: 'Pre-Wiring Date',
  welcomeLetter: 'Welcome Letter',
  OASIS_SerialNo: 'OASIS Serial No.',
  billingCustomerReference: 'Billing Customer Reference',
  project: 'Project',
  orderRemark: 'Order Remark',
  welcomeLetterCustomerHotlineNumber: 'Welcome Letter Customer Hotline Number',
  OPPID: 'OPPID',
  // Charge List
  chargeList: 'Charge List',
  preview: 'Preview',
  sendEmail: 'Send Email',
  sentSuccessfully: 'Sent, please enter your email to check',
  sentFailure: 'Send failure',
  orderResult: 'Order Result',
  orderSuccessfullySubmitted: 'Order Successfully Submitted',
  thisIsNewOrderNum: 'This is new order num',
};

export default orderSummary;
