import Vue from 'vue';
import VueI18n from 'vue-i18n';
import tool from '@/utils/tool';

// 辅助函数，处理语言模块
const processLanguageContext = context => {
  const modules = {};
  context.keys().forEach(key => {
    const moduleName = key.replace(/^\.\/(.*)\.\w+$/, '$1');
    const module = context(key).default || context(key);
    modules[moduleName] = module;
  });
  return modules;
};

// 动态引入语言文件
const loadLanguageFiles = () => {
  try {
    // 为每种语言单独创建require.context调用，使用字面量路径
    const zhCNContext = require.context('./zh-CN', false, /\.js$/);
    const enUSContext = require.context('./en-US', false, /\.js$/);
    const zhHKContext = require.context('./zh-HK', false, /\.js$/);

    // 处理各语言
    const zhCNModules = processLanguageContext(zhCNContext);
    const enUSModules = processLanguageContext(enUSContext);
    const zhHKModules = processLanguageContext(zhHKContext);

    // 合并所有语言模块
    return {
      'zh-CN': zhCNModules,
      'en-US': enUSModules,
      'zh-HK': zhHKModules,
    };
  } catch (e) {
    console.error(`加载语言目录失败:`, e);
  }
};

Vue.use(VueI18n);

// 创建vue-i18n实例
const i18n = new VueI18n({
  // 设置默认语言
  locale: tool.local.get('accept-language') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  // 动态加载语言文件
  messages: loadLanguageFiles(),
});

// 暴露i18n
export default i18n;
