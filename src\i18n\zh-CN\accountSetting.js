// 账户设置文件

const accountSetting = {
  // 账户设置主页
  accountSetting: '账户设置',
  ExistBillingAccount: '现有账单账户',
  newBillingAccount: '新账单账户',
  queryAccount: '查询账户',

  // 账户设置 --- 选择已有账户弹窗
  billingAccountNo: '账单账户编号',
  accountName: '账户名称',
  AGN: 'AGN',
  billEmail: '账单邮箱',
  prodFamily: '产品系列',
  billDay: '账单日',
  billMedia: '账单媒介',
  paymentMethod: '付款方式',

  // 查询表格
  numberType: '号码类型',
  chargeCategory: '费用类别',

  // 主页号码列表
  serviceNumList: '服务编号列表',
  serviceNumType: '服务号码类型',
  criteria: '筛选条件',
  assignedDeptBill: '指派部门账单',
  serviceNum: '服务号码',
  serviceNo: '服务号码',
  accountNo: '账户编号',
  billingAccountNo_R: '账单账户编号（R）',
  billingAccountNo_I: '账单账户编号（I）',
  departmentalBill: '部门账单',
  // departmentalName: '部门名称',
  assignAccount: '分配账户',
  assignDepartmentalBill: '指派部门账单',
  removeAccount: '移除账户',
  removeDepartmentalBill: '移除部门账单',
  selectOrUnselectAll: '选择/取消选择所有号码',
  useNew_DN: '使用新DN',
  use_PIPB_DN: '使用PIPB DN',
  noCriteria: '无筛选条件',
  preferredFirst: '优先选择前1-5位数字',
  preferredLast: '优先选择后6-8位数字',
  assigned: '已分配',
  unAssigned: '未分配',

  // 查询部门账单弹窗
  queryDepartmentBill: '查询部门账单',
  brNo: 'BR编号',
  customerName: '客户名称',
  newAccount: '新建',
  departmentalName: '部门账单名称',
  serviceType: '服务类型',

  // 新建部门账单弹窗
  departmentNum: '部门编号',
  departmentName: '部门名称',
  parentDepartment: '上级部门',
  brNum: 'BR编号',
  accountNum: '账户编号',
  remark: '备注',
  newAddress: '新地址',

  // 新增部门账单
  addBillDepartment: '添加部门账单',

  // 新建账户

  // 客户信息
  customerInformation: '客户信息',
  customerId: '客户编号',
  dragonCustomerNo: 'HKT客户编号',
  documentType: '证件类型',
  documentNo: '证件号码',
  highLevelCustomer: '高级客户',
  AGNName: 'AGN名称',
  marketSegment: '市场细分',
  marketSubSegment: '市场子细分',
  correspondenceAddress: '通信地址',
  lob: '业务线',
  flag: '标记',
  windingUpBankruptcy: '清算/破产',
  risky: '高风险',
  contact: '联系人',
  sales: '销售',
  Optout: '退订',
  document: '文件',
  industrialType: '行业类型',
  industrialSubType: '行业子类型',
  badPayment: '不良支付',
  premierType: '高级类型',
  customerVerified: '客户验证',
  writtenApprovalRequired: '需要书面批准',
  specialHandling: '特殊处理',
  speakingPreference: '语言偏好',
  accountInfomation: '账户信息',
  ebill: '电子账单',
  EN: '英文',
  ZH: '中文',
  POBox: '邮政信箱',
  overseaAddress: '海外地址',

  createNewAccount: '创建新账户',
  productFamily: '产品系列',
  brand: '品牌',
  billContact: '账单联络人',
  billRecipient: '账单收件人',
  billMail: '账单邮件',
  billLanguage: '账单语言',
  itemDisplay: '$0项目显示',
  billAddressType: '账单地址类型',
  genAccountNo: '一般账户编号',
  billAddress: '账单地址',
  billSendAddress: '账单寄送地址',
  queryAddress: '查询地址',
  addressEn: '(英文)',
  addressZH: '(中文)',
  splitAddress: '拆分地址',
  token: '令牌',
  add: '添加',
  HKTBillContract: 'HKT账单合同',
  yes: '是',
  no: '否',
  creditCardToken: '信用卡令牌',
  cardCountry: '卡片国家',
  cardName: '卡片名称',
  cantonese: '粤语',
  mandarin: '普通话',

  // editAccouontInfo
  paper: '纸质',
  english: '英文',
  traditionalChinese: '繁体中文',
  addressMapping: '地址映射',

  // paymentMethod:'Payment Method',
  PCardArea: '信用卡区域',
  cardType: '卡片类型',
  expirationDate: '到期日期',
  cardNO: '卡号（令牌#）',
  cardHolderName: '持卡人姓名',
  bankNo: '银行编号',
  ccc: 'CCC',
  cash: '现金',
  creditCardAutopay: '信用卡自动支付',
  directDebit: '直接扣款',
  payment: '支付',
  HKTBillContact: 'HKT账单联系人', // XXX 这个字段简体和繁体需要修改
  // HKTBillContact
  type: '类型',
  name: '姓名',
  contactPhone: '联系电话',
  email: '邮箱',
  action: '操作',

  // selectAddress
  SBNo: 'SB编号',
  address: '地址',
  resAvali: '可用资源',
  spareAvali: '备用可用性',
  twoN: '2N',
  DP: 'DP',
  floor: '楼层',
  flat: '单元',
  SEL: 'SEL',
  modifyCustomer: '修改客户',
  customer360: '客户360',
  accountManage: '账户管理',
  envelopeDisplay: '信封显示',
  envelopeTip: '信封显示字段必须填写一个',
  displaybill: '在账单中显示0元',
  LOB: '业务线',
  billFrequence: '账单频率',
  waivedPaperBillFee: '免纸质帐单费用',
  includeOddCentsInBill: '在账单中包含零头',
  // 提示信息
  needChooseDepBillTips: '请选择需要添加部门账单的数据！',
  accountListTips: '账户列表最多只能包含两条记录，其中一条为R，另一条为I！',
  pleaseChooseTips: '请选择需要',
  newAddTips: '添加',
  depBillDataTips: '部门账单',
  removeTips: '移除',
  successTips: '成功',
  successSubmitTips: '提交成功!',
  notRepeatTips: '不重复',
  displayBillTip1: '此开关用于禁用客户请求的账单中$0项目的显示。',
  displayBillTip2: '是（默认）- 显示产品设置中定义的$0项目。',
  displayBillTip3: '否- 在账单显示中跳过$0项目。（客户请求）。',
  emailIsRequired: '账单联系人邮箱格式不正确！',
  billTypeRequired: '类型为必填项！',
  standard: '标准',
  nonStandard: '非标准',
  month: '月',
  quarter: '季度',
  year: '年',
  emailCannotBeEmpty: '邮箱不能为空',
  numberOfEmail: '邮箱数量不能大于三个',
  emailSymbol: '每个邮箱地址必须包含@符号',
  emailFormat:
    '邮箱格式错误，多个邮箱用英文逗号隔开，最多设置三个邮箱，eg: <EMAIL>,<EMAIL>,<EMAIL>',
  numberAddAccountInfo: '请给号码至少添加一条R账户信息！',
  numberAddAccountInfoWithI: '请给所有号码添加一条I账户信息！',
  atLeastExistsRAccount: '账户列表至少应存在一条为 R 的账户记录!',
  maxTwoAccountsError: '每个账户号码最多只能添加两条记录',
  maxOneAccountsError: '每个账户号码最多只能添加一条记录',
  emailFormatError: '电子邮件格式不正确！',
  creditAdmin: '信用管理',
  pleaseChooseOneOfDatas: '请选择其中一条数据！',
  nameRequired: '姓名是必填项！',
  AddressEnMsg: '地址(英文)是必填项',
  requiredFields: '必填字段不能為空或格式錯誤！',
  newAccountNo: '账户号码：',
  assignBillingAccount: '分配账单账户',
  removeBillingAccount: '移除账单账户',
};
export default accountSetting;
