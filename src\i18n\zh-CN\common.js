// 公共翻译文件
export default {
  // 头部标题
  queryTitle: '查询',
  // 按钮类型
  buttonBack: '返回',
  buttonImport: '导入',
  buttonExport: '导出',
  buttonQuery: '查询',
  buttonOk: '确定',
  buttonCancel: '取消',
  buttonAdd: '新建',
  buttonReset: '重置',
  buttonDelete: '删除',
  buttonStatus1: '生效',
  buttonStatus2: '失效',
  buttonUpload: '去下载',
  download: '下载',
  send: '发送',
  buttonClose: '关闭',
  buttonInquiry: '查询',
  buttonFilter: '过滤',
  buttonConfirm: '确认',
  // 通用描述语
  type: '类型',
  upload: '上传',
  seq: '序号',
  name: '姓名',
  // 输入提示语
  inputPlaceholder: '请输入',
  selectPlaceholder: '请选择',
  // 页码
  paginationTotal: '共',
  paginationitems: '条',
  // 弹窗名字
  modaltitleAdd: '新增',
  modaltitleUpdate: '修改',
  modaltitleEdit: '编辑',
  // 全局提示message
  successMessage: '操作成功',
  errorMessage: '操作失败',
  // 表格
  action: '操作',
  deleteDraft: '删除草稿',
  buttonAmend: '修改',
  buttonView: '详情',
  // 二次确认提示框
  prompt: '提示',
  confirmSubmission: '确认提交',
  yes: '是',
  no: '否',
  tableTotalCount: '共 {total} 条记录',
  previous: '上一页',
  next: '下一页',
  submit: '提交',
  submitOrder: '提交订单',
  return: '返回',
  reject: '拒绝',
  enterOneSearchTip: '请输入至少一个搜索条件。',
  edit: '编辑',
  confirm: '确认',
  newAddress: '新地址',
  deleteConfirm: '确认删除？',
  removeConfirm: '确认移除？',
  cancelConfirm: '确认取消？',
  change: '更改',
  changeList: '更改列表',
  noNull: '不能为空',
  selectOne: '至少选择一个号码！',
  productName: '产品名称',
  save: '保存',
  successTip: '执行成功！',
  complete: '完成',
  enterNumberTip: '请输入属于号段且不重复的号码',
  // 操作提示语
  addAccountSuccess: '账户添加成功！',
  removeAccountSuccess: '账户移除成功！',
  cannotBeEmpty: '不能为空',
  inputContainsIllegalCharactersError: '输入包含非法字符，请重新输入！',
  emailErrorTip: '请输入正确的邮箱!',
  phoneErrorTip: '请输入正确的号码!',
  add: '添加',
  waiveAmountError: '每线豁免金额不能大于 {maxCharge}!',
  totalChargeAmountError: '总收费金额不能超过 {maxCharge}!',
  amountLessError: '金额不能小于0校验!',
  otcAmountError: '一次性付费费用不能为空!',
  submitOrderSuccess: '订单提交成功！',
  cancelOrderSuccess: '订单撤单成功！',
  amendOrderSuccess: '订单 { orderNo } 修改成功！',
  yourOrderNumber: '您的订单号为：',
  sameAddressError: '不能选择相同的地址，请重新选择！',
  inputLetterNumber: '请输入数字或字母',
  limitLength: '请输入 {length} 位字符长度',
  rangeLength: '请输入范围内 {rangeLength} 位字符长度',
  rangeNumber: '请输入范围内 {rangeNumber} 的数字',
  selectedNumberRestrict: '只能选择 {numberLength} 个号码！',
  pureNumbers: '请正确输入纯数字',
  mutexTips: '{name} 与当前勾选互斥',
  mutexNextStepTips: '{name} 与 {target}存在互斥关系',
  duplicateCheckedTips: '{name} 元素不能重复选择',
  copyToAllOtherNumbers: '将产品配置复制到所有其他号码！',
  months: '月',
  days: '天',
  hours: '小时',
  minutes: '分钟',
  seconds: '秒',
  needSaveBtn: '请先点击保存按钮',
  partialDependenceTip:
    '当前勾选与{name} 存在部分依赖关系，点击确定会自动选中依赖项，否则会取消当前勾选',
  partialDependenceTips: '{name} 与当前勾选存在部分依赖关系，请至少选择一个',
  completeDependenceCheckTip:
    '当前勾选部分与 {name} 存在完全依赖关系，点击确定会自动选中完全依赖项，否则会取消当前勾选',
  completeDependenceCancelTip:
    '当前勾选部分与 {name} 存在完全依赖关系，点击确定会自动取消完全依赖项，否则会保持当前勾选',
  validateDependenceTip: '{currentName}与{name}存在部分依赖关系，请手动选择',
  validateDependenceAllTip: '{currentName}与{name}存在完全依赖关系，请手动选择',
  DocumentNoError: '证件号码格式不正确。请使用XXXXXXXX-XXX格式。X代表仅允许数字。',
  DocumentNoPassportError: '证件号码格式不正确。请输入小于40位字符的内容',
  noChangeData: '数据未发生变化，无法提交',
  isRequired: '是必填项！',
  needAddBtn: '请先点击添加按钮',
  serviceNoPlaceholder: '可输入最多20个号码，以逗号分隔',
  cannotSelect: '未找到匹配数据!',
  cannotSelectNext: '号码{number}已经订购过{name}，不需要再次订购。',
  welcomeLetter: '当前勾选部分只能被主号号码订购',
  searchNotNull: '{name} 元素只能被主号号码订购',
  noMatchFound: '欢迎信',
  repeatOrder: '搜索条件不能为空',
  netWorkError: '网络错误，请稍后再试',
  serverError: '服务器错误，请稍后再试',
  requestTimeout: '请求超时，请稍后再试',
  requestFailed: '请求的资源不存在',
  FileFormatVerification: '文件格式错误，请上传{fileType}文件',
  FileNumberVerification: '文件数量不能超于{number}个',
};
