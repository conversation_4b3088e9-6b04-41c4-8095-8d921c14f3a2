const comp = {
  // 客户选择
  CustomerList: '客户列表',
  CustomerName: '客户姓名',
  CustomerNo: '客户编号',
  DocumentType: '文件类型',
  DocumentNo: '文件编号',
  LOB: '业务线',
  Criteria: '范围',
  Product: '产品',
  CustomerOrderedList: '客户订单列表',
  selection: '选择',
  Sel: '选项',
  ProductFamily: '产品系列',
  ProductType: '产品类型',
  ProductName: '产品名称',
  InstallationAddress: '安装地址',
  ExistPending: '待处理',
  MACDCart: 'MACD购物车',
  MACDAction: 'MACD操作',
  MarketSegment: '市场部门',
  HKTCustomerNo: '香港电讯客户编号',
  AM: '客户经理',
  ASM: '客户服务审核人',
  oneCustomerError: '请选择一位客户！',
  // 客户选择 - 表单查询弹窗
  CustomerSelection: '客户选择',
  DragonCustomerNo: 'HKT客户编号',
  SalesTeam: '销售团队',
  SalesSubTeam: '销售子团队',
  //  产品树
  SelectProduct: '选择产品',
  CustomerLob: '客户LOB',
  CustomerFamily: '客户系列',
  AddProductInstall: '添加产品安装',
  oscaNewProductTitle: '产品选择',
  oscaProductAddressTitle: '地址映射和选择产品',
  Equipment: '设备',
  Premium: '礼品',
  Confirm: '确认',
  Description: '描述',
  serviceNo: '服务号码',
  ProductSelection: '产品选择',
  AdditionalProduct: '附加产品',
  MainProduct: '主要产品',
  standardMRC: '标准月租',
  type: '类型',
  Charge: '收费',
  Period: '期间',
  RedeemedPoints: '礼品积分',
  MonthlyRentalCharge: '月租费',
  selectMainProd: '请选择主产品！',
  attributeEdit: '属性编辑',
  selectMainProdOnlyOne: '只能选择一个主产品',
  cannotMoreThan: '{name}下最多可以选择{num}项，目前已选择{selectedCount}项',
  cannotLessThan: '{name}下至少需要选择{num}项，目前已选择{selectedCount}项',
  selectMainProdMustHasPackageElement: '主产品必须至少包含一个套餐和元素',
  attributeDetail: '属性详情',
  attributeMustEditConfim:
    '需要编辑{productName}下{packageName}下的{elementName}的属性以进行确认！',
  attributeEditCorrectAmount: '请输入合适的金额。',
  // 产品树 - 税率改变 - taxRateExchange
  taxRateExChangeTitle: '黄金费率通话计划（目的地/费率）',
  Country: '国家',
  taxRateExChangePrompt: '请输入有效的正数或小数',
  selectedList: '选定清单',
  RatePirceIsNotNull: '{RateName}价格不能为空！',
  // 地址选择 - Address Mapping
  Address: '地址',
  SBNo: 'SB编号',
  ServiceNo: '服务编号',
  Floor: '楼层',
  Flat: '单位',
  Resource: '资源',
  Spare: '备用',
  '2N': '2N',
  DP: 'DP',
  region: '区域',
  street: '街道',
  district: '区',
  estate: '住宅',
  building: '大厦',
  Flat_Unit_Room: '单位/房间',
  LotNumber: '地段编号',
  atLeastOneCondition: '请至少输入一个条件',
  tipsText1: '请输入至少一个搜索条件。',
  tipsText2: '请选择至少一个地址。',
  tipsText3: '确认订单提交',
  tipsText4: '请先添加服务号码！',
  tipsText5: '你确定要取消当前主产品吗？',
  tipsText6: '请选择至少一条订单。',
  newAddressMappingTitle: '新增地址映射',
  RegioRuleMessage: '请选择地区',
  DistrictRuleMessage: '请选择区域',
  StreetRuleMessage: '请输入街道',
  EstateRuleMessage: '请输入小区',
  BuildingRuleMessage: '请输入建筑',
  FloorRuleMessage: '请输入楼层',
  LotNumberRuleMessage: '请输入地段编号',
  selectInquiry: '请在查询后选择',
  showAllSBNOCheckBox: '显示与地址关联的所有SB编号',
  checkShowAllSBNOCheckBox: '请勾选“显示与地址关联的所有SB编号”',
  SBNOListHasNoData: 'SBNO列表没有数据',
  noExitCurrentServiceNO: '没有这样的工作服务号',
  // 号码选择 - numberSelection
  tamplateTwoHeadTitle: '单产品订单 - 号码选择',
  numberSelection: '号码选择',
  AddServiceNo: '添加服务号码',
  addTitle: '服务号码搜索',
  SelectedServiceNoQty: '已选择服务号码数量',
  Allchecked: '选择/取消选择当前页面所有服务号码',
  RemoveSelected: '移除所选',
  NoData: '无数据',
  copyToAll: '将产品配置复制到所有选中的号码！',
  numberOfSelect: '选择数量',
  numberLimitQuantityPrompt: '可输入最多20个号码，以逗号分隔',
  enterTheCustomerName: '请输入客户名称',
  completeNumber: '请输入一个已知的8位数号码',
  // 号码选择 - numberSelection - addPopWindow
  ServiceNoType: '服务号码类型',
  ServiceNoQty: '服务号码数量',
  ServiceGroup: '服务组',
  selectType: '选择类型',
  serviceGroup_Service: '服务组/服务',
  Service: '服务',
  ReserveDN: '预留号码',
  ReservationCode: '预约码',
  BOC: 'BOC',
  Project: '项目',
  NewInstallation: '新安装',
  PIPBNumber: '携入携出号码',
  NoCriteria: '无筛选条件',
  PreferredFirst: '优先选择前1-5位数字',
  PreferredLast: '优先选择后6-8位数字',
  // 号码选择 - numberSelection - editProduct
  editProductTitle: '产品/套餐/增值服务/定价属性编辑',
  addressMapping: '地址映射',
  numberAttributor: '号码属性',
  callForwardingNo: '呼叫转移号码',
  // 附件 - Attachment
  Attachment: '附件',
  AttachmentName: '附件名称',
  AttachmentRemark: '备注',
  UploadTime: '上传时间',
  UploadUser: '上传者',
  AttachmentType: '附件类型',
  AttachmentUploading: '附件上传中',
  Upload: '上传',
  FileSizeLimit: '文件大小不超过10MB',
  FileTypeLimit: '请上传正确的文件类型',
  UploadAttchmentTips: '请上传附件',
};
export default comp;
