const customerVerify = {
  CustomerList: 'Customer List',
  // 列表字段
  CustomerName: '客户姓名',
  CustomerNo: '客户编号',
  DocumentType: '文件类型',
  DocumentNo: '文件编号',
  LOB: '业务线',
  Criteria: '范围',
  Product: '产品',
  CustomerOrderedList: '客户订单列表',
  selection: '选择',
  Sel: 'Sel',
  ProductFamily: '产品系列',
  ProductType: '产品类型',
  ProductName: '产品名称',
  InstallationAddress: '安装地址',
  ExistPending: '待处理',
  MACDCart: 'MACD购物车',
  MACDAction: 'MACD操作',
  MarketSegment: 'Market Segment',
  HKTCustomerNo: 'HKT Customer No.',
  AM: 'AM',
  ASM: 'ASM',
  // osca 产品树
  SelectProduct: '选择产品',
  CustomerLob: '客户LOB',
  CustomerFamily: '客户系列',
  AddProductInstall: '添加产品安装',
  oscaNewProductTitle: '产品选择',
  oscaProductAddressTitle: '地址映射和选择产品',
  Equipment: '设备',
  Premium: '礼品',
  Confirm: '确认',
  Description: '描述',
  // 表单查询弹窗
  CustomerSelection: '客户选择',
  DragonCustomerNo: 'HKT客户编号',
  SalesTeam: '销售团队',
  SalesSubTeam: '销售子团队',
  // 新增页面-底部工具栏
  Next: '下一步',
  Previous: '上一步',
  Save: '保存',
  Submit: '提交',
  Cancel: '取消',
  printOrder: '打印订单',
  addHunting: '添加猎群',
  // 新增页面-Address Mapping
  OK: '确定',
  // 新增页面-tamplateOne
  pageTitle: '单一产品订单 - 地址映射与产品选择',
  headTitle: '地址映射',
  SBNo: 'SB编号',
  serviceNo: '服务号码',
  ProductSelection: '产品选择',
  PrimaryProduct: '主要产品',
  AdditionalProduct: '附加产品',
  MainProduct: '主要产品',
  // 新增页面-Address Mapping
  Address: '地址',
  'SB No': 'SB编号',
  ServiceNo: '服务编号',
  Floor: '楼层',
  Flat: '单位',
  Resource: '资源',
  Spare: '备用',
  '2N': '2N',
  DP: 'DP',
  region: '区域',
  street: '街道',
  district: '区',
  estate: '住宅',
  building: '大厦',
  Flat_Unit_Room: '单位/房间',
  LotNumber: '地段编号',
  atLeastOneCondition: '请至少输入一个条件',
  tipsTitle1: '通知信息',
  tipsText1: '请输入至少一个搜索条件。',
  tipsText2: '请选择至少一个地址。',
  tipsText3: '确认订单提交',
  tipsText4: '请在继续下一步之前添加服务号码！',
  tipsText5: '你确定要取消当前主产品吗？',
  tipsText6: '请选择至少一条订单。',
  // 新增页面-Address Mapping-addAddress
  newAddressMappingTitle: '新增地址映射',
  RegioRuleMessage: '请选择地区',
  DistrictRuleMessage: '请选择区域',
  StreetRuleMessage: '请输入街道',
  EstateRuleMessage: '请输入小区',
  BuildingRuleMessage: '请输入建筑',
  FloorRuleMessage: '请输入楼层',
  LotNumberRuleMessage: '请输入地段编号',
  // 新增页面-Address Mapping-component1
  standardMRC: '标准月租',
  type: '类型',
  Charge: '收费',
  Period: '期间',
  Action: '操作',
  MonthlyRentalCharge: '月租费',
  // 新增页面-numberSelection
  tamplateTwoHeadTitle: '单产品订单 - 号码选择',
  numberSelection: '号码选择',
  AddServiceNo: '添加服务号码',
  addTitle: '服务号码搜索',
  SelectedServiceNoQty: '已选择服务号码数量',
  Allchecked: '选择/取消选择当前页面所有服务号码',
  RemoveSelected: '移除所选',
  NoData: '无数据',
  copyToAll: '将产品配置复制到所有选中的号码！',
  // 新增页面-numberSelection-addPopWindow
  ServiceNoType: '服务号码类型',
  ServiceNoQty: '服务号码数量',
  ServiceGroup: '服务组',
  selectType: '选择类型',
  serviceGroup_Service: '服务组/服务',
  Service: '服务',
  ReserveDN: '预留DN',
  ReservationCode: '预约码',
  BOC: 'BOC',
  Project: '项目',
  NewInstallation: '新安装',
  PIPBNumber: 'PIPB号码',
  NoCriteria: '无筛选条件',
  PreferredFirst: '优先选择前1-5位数字',
  PreferredLast: '优先选择后6-8位数字',
  // 新增页面-numberSelection-editProduct
  editProductTitle: '产品/套餐/增值服务/定价属性编辑',
  addressMapping: '地址映射',
  installationAddress: '安装地址',
  SB_No: 'SB编号',
  numberAttributor: '号码属性',
  callForwardingNo: '呼叫转移号码',
  // 新增页面-MainProduct&PremiumProduct-num&score
  ProductQuantity: '产品数量',
  RedeemablePoints: '可兑换积分',
  NoOfLinesInstalled: '已安装的线路数量',
  NoOfPremiumPoints: '已选择的产品积分',

  // 提示信息字段
  selectMainProd: '请选择主产品！',
  accountListError: '请至少包含一个收费类别为I的账户列表！',
  iddAccountListError: '请至少包含一个收费类别为I的账户列表！',
  customerError: '请选择客户！',
  numberOfSelect: '选择数量',
  citinetPageTitle: 'Citinet群组产品选择',
  huntingProductSelectPageTitle: '猎号群组产品选择',
  idapProductPageTitle: 'IDAP（站点级别）组产品选择',
  attributeEdit: '属性编辑',
  // selectType: '请选择一个类型！',

  selectServiceNo: '请选择至少一个号码',
  cancelIndividualVas: '请确认是否取消个人增值服务。',
  selectMainProdOnlyOne: '只能选择一个主产品',
  numberNeedToSelectSubProduct: '号码{number}需要选择一个子产品',
  numberSequenceUniqueness: '顺序{number}重复，请重新编辑顺序',
  premiumProduct: '高端产品',
  equipmentProduct: '设备产品',
  cannotMoreThan: '{name}下最多可以选择{num}项，目前已选择{selectedCount}项',
  cannotLessThan: '{name}下至少需要选择{num}项，目前已选择{selectedCount}项',
  selectMainProdMustHasPackageElement: '主产品必须至少包含一个套餐和元素',
  attributeDetail: '属性详情',
  selectInquiry: '请在查询后选择',
  attributeMustEditConfim:
    '需要编辑{productName}下{packageName}下的{elementName}的属性以进行确认！',
  numberLimitQuantityPrompt: '可输入最多20个号码，以逗号分隔',
  enterTheCustomerName: '请输入客户名称',
  jumpHuntingForCitinetTips: '正在跳转huntingForCitinet业务...',
  huntingForCitinetGroupProductSelection: 'Citinet群组产品选择猎号',
  NoChange: '无变更内容',
  showAllSBNOCheckBox: '显示与地址关联的所有SB编号',
  Other: '其他',
  ChangeDetail: '变更详情',
  citinetGroupProduct: 'Citinet集团产品',
  exDirectory: '未列入电话号码簿的',
  directoryName: '目录名',
  limitBusinessNumbers: '当前新装业务的号码数量不得超过{Number}',
  omitUnnecessaryNumbers: '当前号码数量已达{Number}，剩余未能添加的号码将会自动省略',
  oneCustomerError: '请选择一位客户！',
  badCustomerError: '不允许此操作，因为当前客户有信用问题!',
  fixedLineOK: '固定线路：正常',
  FixedLineFalse: '固定线路：客户付款逾期，如下：',
  totalOverdueAmount: '逾期总金额：$',
  listofAccountNumber: '账号列表',
  pleaseSelectOneOfTheData: '请选择其中一条数据',
  completeNumber: '请输入一个已知的8位数号码',
  pageTitleOfCommit: '订单结果',
  ccpTaxRate: '{productName}下{packageName}下的{elementName}的属性不能为空！',
  pendingOrderNotDoMACDTips: 'DN（{SERVICE_NO_List}）在途中，无法做MACD业务',
  editDeviceAttributes: '需要编辑｛productName｝下的属性以确认！',
  accountInfoOfNewInstall: '请至少包含一个收费类别为R的账户列表！',
  checkShowAllSBNOCheckBox: '请勾选“显示与地址关联的所有SB编号”',
  SBNOListHasNoData: 'SBNO列表没有数据',
  noExitCurrentServiceNO: '没有这样的工作服务号',
};

export default customerVerify;
