// 履约信息

const fulfillmentInfo = {
  fulfillmentInfo: '履行信息',

  // HKT Contact List 表格字段
  HKTContactList: 'HKT联络人列表',
  seq: '序号',
  type: '类型',
  name: '名称',
  salesCode: '销售代码',
  contactPhone: '联系电话号码',
  mobile: '手提号码',
  email: '电子邮件',

  //Customer Contact List表格字段
  customerContactList: '客户联络人列表',

  //SRD 字段
  srd: 'SRD',
  serviceNo: '服务号码',
  appointmentDate: '约定日期',
  preWiringDate: '预布线日期',
  srdNoNull: 'SRD时间不可为空！',
  constructionReservationTips: '请进行施工预约！',

  // otherInformation 字段
  otherInformation: '其他',
  OASISSerialNo: 'OASIS序列号',
  notEnteredOASISSerialNo: '请输入OASIS序列号。',
  OASISSerialNoError:
    'OASIS序列号格式不正确。请使用XXYY-YYYYYYYY格式。X代表允许字母+数字，Y代表仅允许数字。',
  billingCustomerReference: '账单客户参考（显示在账单上）',
  project: '项目代码',
  OPPID: 'OPPID',
  terminationReason: '拆机原因',
  terminationRemark: '拆机备注',
  welcomeLetterCustomerHotlineNumber: '欢迎信顾客服务热线',
  welcomeLetter: '欢迎信',
  orderRemark: '订单备注',
  newHuntingForCitinetGroup: '是否同时新开Hunting for Citinet Group',
  // HKT Contact List 新增弹窗字段
  HKTContact: 'HKT联系人',
  participantsType: '参与者类型',
  staffId: '员工编号',
  orderSalesType: '订单销售类型',
  agentCode: '代理代码',
  agentName: '代理姓名',
  participantsTypeWarning: '参与者类型字段格式不正确！',
  contactPhoneWarning: '联系电话字段格式不正确！',
  mobileWarning: '移动电话字段格式不正确！',
  emailWarning: '邮箱字段格式不正确！',
  // Customer Contact List 新增弹窗字段
  title: '标题',

  // SRD 弹窗
  SB_No: 'SB编号',
  address: '地址',
  repeatChoose: '不能重复选择！',
  customerContactTypeRequired: '客户联系人类型是必填项！',
  customerContactNameRequired: '客户联系人姓名是必填项！',
  customerContactPhoneRequired: '客户联系人联系电话是必填项！',
  customerContactPhoneIncorrectFormat: '客户联系人联系电话，请输入以5、6、8或9开头的八位数字',
  customerContactMobileIncorrectFormat: '客户联系人移动电话，请输入以5、6、8或9开头的八位数字',
  emailIncorrectFormat: '请输入正确的邮箱格式',
  HKTTypeRequired: 'HKT联系人参与者类型是必填项！',
  HKTOrderSaleTypeRequired: 'HKT联系人订单销售类型是必填项！',
  HKTEmailRequired: 'HKT联系人邮箱是必填项！',
  HKTMobileIncorrectFormat: 'HKT联系人移动电话，请输入以5、6、8或9开头的八位数字',
  HKTPhoneIncorrectFormat: 'HKT联系人联系电话，请输入以5、6、8或9开头的八位数字',
  HKTContactPhoneRequired: 'HKT联系人联系电话是必填项！',
  HKTStaffIdRequired: 'HKT联系人员工编号是必填项！',
  HKTNameRequired: 'HKT联系人姓名是必填项！',
  HKTAgentNameRequired: 'HKT联系人代理商名称是必填项！',
  HKTAgentCodeRequired: 'HKT联系人代理代码是必填项！',
  dateComparisonVerification: 'SRD日期不应早于预约日期和预布线日期中最晚的一个!',
  errorEmailTips: '错误的邮箱地址，请重新输入',
  errorPhoneTips: '错误的手机号码格式，请重新输入',
  HKTContactValidTips: 'HKT联系人至少包含一条数据！',
  customerContactValidTips: '客户联系人至少包含一条参与者类型为Admin的数据！',
  notNeedAppointmentTips: '新建地址不需要施工预约',
  gettingAppointmentTime: '正在获取预约信息（{second}秒）',
  cancelReservation: '取消预约',
  notificationLetter: '通知信',
  nonAppointmentItem: '目前没有预约的现场服务安排，需要创建一个吗？',
};
export default fulfillmentInfo;
