export default {
  removeDepartmentalBill: '移除部门账单',
  assignDepartmentalBill: '分配部门账单',
  searchingCriteria: '搜索条件',
  billingAccountExisting: '账单账户（现有）',
  chargeCategoryExisting: '费用类别（现有）',
  billingAccountNew: '账单账户（新建）',
  originProductName: '原产品名称',
  newProductName: '新产品名称',
  change: '更改',
  keyWord: '关键词',
  copyNumber: '将产品配置复制到所有其他号码！',
  noRepeatNumber: '号码已经添加过了，请选择其它号码!',
  productVASPRICE: '产品/增值服务/价格',
  installationAddress: '安装地址',
  changeBillAddress: '更改账单地址',
  en: '英文',
  cn: '中文',
  moreChangeAction: '更多更改操作',
  oneCallPcf: '一键PCF',
  pcf: 'PCF',
  disconnectReasonRequired: '必须填写拆机原因',
  applicationDateRequired: '必须填写申请日期',
  srdRequired: '必须填写SRD',
  appointmentDateRequired: '必须填写预约日期',
  chargePerDn: '每个DN的费用',
  quantity: '数量',
  standardCharge: '标准费用',
  waivedAmountPerLine: '每线豁免金额',
  totalCharge: '总费用',
  selectOne: '您只能选择一条数据！',
  terminationExclusive: '拆机业务与其他业务互斥。',
  noRepeatService: '请勿重复选择相同业务。',
  transferOwnership: '过户',
  termination: '拆机',
  bundleType: '套餐类型',
  groupNumber: '组号码',
  group: '组',
  onlyOneGroup: '只能选择一个组产品！',
  selectProduct: '选择产品',
  waivePenalty: '豁免罚金',
  salesRenewal: '销售续费',
  marketingRenewal: '市场续费',
  changeType: '修改类型',
  before: '修改前',
  after: '修改后',
  rebateFeeMoreThanMrcFee: '{rebateElement} Rebate费用不能大于{mrcElement} MRC费用',
  rebatePeriodEqualMrcPeriod: '{rebateElement} Rebate合约期要与{mrcElement} MRC合约期相等',
  onlyIDD: '只能选择一条IDD数据',
  productChangesExclusive: '产品变更类的二级业务互斥',
  relationshipChangeExclusive: '关系变更类的二级业务互斥',
  addressIsNull: '安装地址为空',
  selectAddress: '请选择地址',
  selectServiceNo: '请勾选号码',
  sameProductExclusive: '所有号码必须是相同的产品',
  sameAddressExclusive: '所有号码必须是相同的地址',
  mrcValueUpdateNotChooseRebate: '{name} MRC值发生修改，不能选择Rebate',
  needToChooseMrc: '需要先选择MRC {name}',
  detail: '细节',
  onlyDDI: '只能选择一条DDI数据',
  mrcValueUpdateNotChooseRebateNext: '{mrcName} MRC值发生修改，不能选择 {rebateName}',
  changeGroupTips: '当前群组未变更，无法执行此操作',
  TOAST_MESSAGES: {
    TERMINATION_EXISTS: '当前订单已办理此操作，不允许再次办理此项业务！',
    TERMINATION_WARNING: '办理当前业务时，会撤回当前订单其余所有业务！',
    INVALID_ACTION: '当前订单已办理拆机操作，不允许再次办理此项业务！',
    SAMETYPE_ACTION: '当前订单已办理同类型操作，不允许再次办理此项业务！',
    NEWLIMIT_ACTION: '该号码为新增，不允许办理此项业务！',
    TYPELIMIT_ACTION: '该号码类型，不允许办理此项业务！',
    TERMINATION_NEW: '新增成员，无法进行该操作，请重新选择',
  },
  memberNumber: '成员号码',
  noServiceChange: '未有号码变更',
  changeGroupWarning: '请选择群组主号码操作 Change Group',
  changeGroupError: '当前操作无法继续执行，请稍后再试！',
  relocationType: '重定位类型',
  BID: 'BID',
  view: '查看',
  errOrderTipText: '当前订单不存在，请重新操作！',
  appendContractTips: '新合同的开始时间将附加旧合同的结束时间。',
  macdActionIsNull: 'MACD购物车中的数据为空！',
  selectAccountRepeatError: '当前选择的{chargeCategory}账户重复，请重新选择',
  jumpageAddressMessage: '不同的安装地址不允许在一次操作中进行迁移。',
  memberProductWarning: '成员产品目前只配置一个，暂不能做这个业务',
  renewalWarning: '续约回溯不能执行其他 MACD 操作',
  renewalError: '不允许超过120天办理续约业务',
  pcfProduct: '请选择pcf产品!',
  targetNumber: '请输入目标号码!',
  IDDTypeMutualExclusionOtherType: 'IDD类型的单和其他类型的单不能同时做macd业务!',
  rAccountNotNull: '号码{number}至少要包含一个R类型账号',
  pleaseSelectProduct: '请选择产品!',
  noDataChange: '没有发生数据变更！',
};
