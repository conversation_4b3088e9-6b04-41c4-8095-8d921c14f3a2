// 汇总界面

const orderSummary = {
  // Address Mapping
  orderSummary: '订单摘要',
  addressMapping: '地址映射',
  installationAddress: '安装地址',
  sbNo: 'SB编号',

  // Product Selected
  productSelected: '已选产品',
  primaryProduct: '主要产品',
  additionalProduct: '附加产品',
  type: '类型',
  charge: '费用',
  period: '周期',
  action: '操作',

  // Account Info
  accountInfo: '账户信息',
  accountName: '账户名称',
  billingAccountNo: '账单账户编号',
  billDay: '账单日',
  billMedia: '账单媒介',
  paymentMethod: '付款方式',
  prodFamily: '产品系列',
  chargeCategory: '收费类别',

  // HKT Contact List
  HKT_ContactList: 'HKT联系人列表',
  seq: '序号',
  name: '姓名',
  salesCode: '销售代码',
  phone: '电话',
  mobile: '移动电话',
  email: '邮箱',

  // Customer Contact List
  customerContactList: '客户联系人列表',
  contactPhone: '联系电话',

  // Fulfillment Info
  fulfillmentInfo: '履约信息',
  srd: 'SRD',
  appointmentDate: '预约日期',
  preWiringDate: '预布线日期',
  welcomeLetter: '欢迎信',
  OASIS_SerialNo: 'OASIS序列号',
  billingCustomerReference: '账单客户参考',
  project: '项目',
  orderRemark: '订单备注',
  welcomeLetterCustomerHotlineNumber: '欢迎信客户热线号码',
  OPPID: 'OPPID',
  // Charge List
  chargeList: '费用列表',
  preview: '预览',
  sendEmail: '发送邮箱',
  sentSuccessfully: '已发送，请进入您的邮箱查收',
  sentFailure: '发送失败',
  orderResult: '订单结果',
  orderSuccessfullySubmitted: '订单成功提交',
  thisIsNewOrderNum: '此为新订单编号',
};

export default orderSummary;
