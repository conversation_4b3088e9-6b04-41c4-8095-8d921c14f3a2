export default {
  // 其他
  AtLeastOneCondition: '请至少输入一个条件',
  CustomerSelection: '客户选择',
  PreAFList: '报价预审批表列表',
  ApprovalReason: '审批原因',
  NewPreAF: '新建预审单',
  SaveAsDraft: '保存为草稿',
  OfferPending: '报价待定',
  OfferApproved: '报价批准',
  OfferRejected: '报价拒绝',
  returnHeaderView: '详情',
  // 其他 - 按钮
  CancelPreAF: '取消预审单',
  CopyQuotation: '复制报价单',
  // 查询条件
  CustomerName: '客户名称',
  PreAFNo: '预审单编号',
  SalesmanCode: '销售员代码',
  SalesmanName: '销售员姓名',
  SalesSegment: '销售部门',
  NextApprover: '下一个审批人',
  FormStatus: '表单状态',
  CreateDate: '创建日期',
  CreateDateTimeLimit: '查询最长时间范围为：3个月',
  // 表格表头
  MarketSegment: '市场部门',
  SalesMan: '销售员代码',
  ASM: '客户服务审核人',
  // 新增/编辑
  CustomerInformation: '客户信息',
  LOB: '业务线',
  SalesASMInformation: '销售和客户服务团队信息',
  SalesName: '销售姓名',
  SalesTeam: '销售团队',
  SalesContactNo: '销售联系号码',
  SalesEmail: '销售邮箱',
  SalesSM: '销售SM',
  ASMStaffNo: 'ASM员工编号',
  ASMName: 'ASM名称',
  ASMContactNo: 'ASM联系电话',
  ASMEmail: 'ASM电子邮件',
  // 新增/编辑 - 地址区域 - Address Zone
  AddressZone: '地址区域',
  // Attachment
  // 新增/编辑 - 预批准 - Offer Pre-Approval Remark
  OfferPreApprovalRemark: '报价预批准备注',
  Competitors: '竞争对手',
  CompetitorsOffers: '竞争对手的报价',
  ImpactIfLoseTheDeal: '失去交易的影响（理由）',
  UpcomingBusinessOpportunities: '即将到来的商业机会',
  // 新增/编辑 - 批准提示信息 - Approve Tips Message
  ConfirmProceed: '确认要继续吗',
  ApproveInfo: '以下产品触发批准',
  Approver: '批准人',
  // 新增/编辑 - 主产品和积分合计 - MainProduct&PremiumProduct-num&score
  ProductQuantity: '产品数量',
  RedeemablePoints: '可兑换积分',
  NoOfLinesInstalled: '已安装的线路数量',
  NoOfPremiumPoints: '已选择的产品积分',
  TotalMRCForAllLine: '所有线路月租费合计',
  TotalOTCForAllLine: '所有线路一次性费用合计',
  TotalMRCForAllLinePeriod: '所有线路合约期月租费总和',
  // 新增/编辑 - 时间线
  Timeline: '时间表',
  RejectReason: '驳回原因',
  NextOperator: '下一位操作人',
  // 新增/编辑 - 进度条
  Quotation: '报价',
  // 详情
  ApprovalReference: '批准参考',
  ViewNewForm: '查看新报价单',
  NewPreApprovalFormCreate: '一个新的报价单已经生成！',
  FormNo: '报价单编码',
  Status: '状态',
  CancelPreFormInfo: '确认是否取消报价单？',
  // osca 步骤信息
  PreAFCreation: 'Pre-AF单创建',
  PreAFApproval: 'Pre-AF单审批',
  AFEndorsment: 'AF单签署',
  AFVerification: 'AF单验证',
  AFCompletion: 'AF单完成',
};
