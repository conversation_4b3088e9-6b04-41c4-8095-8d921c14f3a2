// 公共翻译文件
export default {
  // 头部标题
  queryTitle: '查詢',
  // 按钮类型
  buttonBack: '返回',
  buttonImport: '導入',
  buttonExport: '導出',
  buttonQuery: '查詢',
  buttonOk: '確定',
  buttonCancel: '取消',
  buttonAdd: '新建',
  buttonReset: '重置',
  buttonDelete: '刪除',
  buttonStatus1: '生效',
  buttonStatus2: '失效',
  buttonUpload: '去下載',
  download: '下載',
  send: '發送',
  buttonClose: '關閉',
  buttonInquiry: '查詢',
  buttonFilter: '過濾',
  buttonConfirm: '確認',
  // 通用描述语
  type: '類型',
  upload: '上傳',
  seq: '序號',
  name: '姓名',
  // 输入提示语
  inputPlaceholder: '請輸入',
  selectPlaceholder: '請選擇',
  // 页码
  paginationTotal: '共',
  paginationitems: '條',
  // 弹窗名字
  modaltitleAdd: '新增',
  modaltitleUpdate: '修改',
  modaltitleEdit: '編輯',
  // 全局提示message
  successMessage: '操作成功',
  errorMessage: '操作失敗',
  // 表格
  action: '操作',
  deleteDraft: '刪除草稿',
  buttonAmend: '修改',
  buttonView: '詳情',
  // 二次确认提示框
  prompt: '提示',
  confirmSubmission: '確認提交',
  yes: '是',
  no: '否',
  tableTotalCount: '共 {total} 條記錄',
  previous: '上一頁',
  next: '下一頁',
  submit: '提交',
  submitOrder: '提交訂單',
  return: '返回',
  reject: '拒絕',
  enterOneSearchTip: '請輸入至少一個搜索條件。',
  edit: '編輯',
  confirm: '確認',
  newAddress: '新地址',
  deleteConfirm: '確認刪除？',
  removeConfirm: '確認移除？',
  cancelConfirm: '確認取消？',
  change: '更改',
  changeList: '更改列表',
  noNull: '不能為空',
  selectOne: '至少選擇一個號碼！',
  productName: '產品名稱',
  save: '保存',
  successTip: '執行成功！',
  complete: '完成',
  enterNumberTip: '請輸入屬於號段且不重複的號碼',
  // 操作提示语
  addAccountSuccess: '賬戶添加成功！',
  removeAccountSuccess: '賬戶移除成功！',
  cannotBeEmpty: '不能為空',
  inputContainsIllegalCharactersError: '輸入包含非法字符，請重新輸入！',
  emailErrorTip: '請輸入正確的郵箱!',
  phoneErrorTip: '請輸入正確的號碼!',
  add: '添加',
  waiveAmountError: '每線豁免金額不能大於 {maxCharge}!',
  totalChargeAmountError: '總收費金額不能超過 {maxCharge}!',
  amountLessError: '金額不能小於0校驗!',
  otcAmountError: '一次性付費費用不能為空!',
  submitOrderSuccess: '訂單提交成功！',
  cancelOrderSuccess: '訂單撤單成功！',
  amendOrderSuccess: '訂單 { orderNo } 修改成功！',
  yourOrderNumber: '您的訂單號為：',
  sameAddressError: '不能選擇相同的地址，請重新選擇！',
  inputLetterNumber: '請輸入數字或字母',
  limitLength: '請輸入 {length} 位字符長度',
  rangeLength: '請輸入範圍內 {rangeLength} 位字符長度',
  rangeNumber: '請輸入範圍內 {rangeNumber} 的數字',
  selectedNumberRestrict: '只能選擇 {numberLength} 個號碼！',
  pureNumbers: '請正確輸入純數字',
  mutexTips: '{name} 與當前勾選互斥',
  mutexNextStepTips: '{name} 與 {target}存在互斥關係',
  duplicateCheckedTips: '{name} 元素不能重複選擇',
  copyToAllOtherNumbers: '將產品配置複製到所有其他號碼！',
  months: '月',
  days: '天',
  hours: '小時',
  minutes: '分鐘',
  seconds: '秒',
  needSaveBtn: '請先點擊保存按鈕',
  partialDependenceTip:
    '當前勾選與{name} 存在部分依賴關係，點擊確定會自動選中依賴項，否則會取消當前勾選',
  partialDependenceTips: '{name} 與當前勾選存在部分依賴關係，請至少選擇一個',
  completeDependenceCheckTip:
    '當前勾選部分與 {name} 存在完全依賴關係，點擊確定會自動選中完全依賴項，否則會取消當前勾選',
  completeDependenceCancelTip:
    '當前勾選部分與 {name} 存在完全依賴關係，點擊確定會自動取消完全依賴項，否則會保持當前勾選',
  validateDependenceTip: '{currentName}與{name}存在部分依賴關係，請手動選擇',
  validateDependenceAllTip: '{currentName}與{name}存在完全依賴關係，請手動選擇',
  DocumentNoError: '證件號碼格式不正確。請使用XXXXXXXX-XXX格式。X代表僅允許數字。',
  DocumentNoPassportError: '證件號碼格式不正確。請輸入小於40位字符的內容',
  noChangeData: '數據未發生變化，無法提交',
  isRequired: '是必填項！',
  needAddBtn: '請先點擊添加按鈕',
  serviceNoPlaceholder: '可輸入最多20個號碼，以逗號分隔',
  cannotSelect: '未找到匹配數據!',
  cannotSelectNext: '號碼{number}已經訂購過{name}，不需要再次訂購。',
  welcomeLetter: '當前勾選部分只能被主號號碼訂購',
  searchNotNull: '{name} 元素只能被主號號碼訂購',
  noMatchFound: '歡迎信',
  repeatOrder: '搜索條件不能為空',
  netWorkError: '網絡錯誤，請稍後再試',
  serverError: '服務器錯誤，請稍後再試',
  requestTimeout: '請求超時，請稍後再試',
  requestFailed: '請求的資源不存在',
  FileFormatVerification: '文件格式錯誤，請上傳{fileType}文件',
  FileNumberVerification: '文件數量不能超於{number}個',
};
