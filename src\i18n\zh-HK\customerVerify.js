const customerVerify = {
  CustomerList: 'Customer List',
  // 列表字段
  CustomerName: '客戶姓名',
  CustomerNo: '客戶編號',
  DocumentType: '文件類型',
  DocumentNo: '文件編號',
  LOB: '業務線',
  Criteria: '範圍',
  Product: '產品',
  CustomerOrderedList: '客戶訂單列表',
  selection: '選擇',
  Sel: 'Sel',
  ProductFamily: '產品系列',
  ProductType: '產品類型',
  ProductName: '產品名稱',
  InstallationAddress: '安裝地址',
  ExistPending: '待處理',
  MACDCart: 'MACD購物車',
  MACDAction: 'MACD操作',
  MarketSegment: 'Market Segment',
  HKTCustomerNo: 'HKT Customer No.',
  AM: 'AM',
  ASM: 'ASM',
  // osca 产品树
  SelectProduct: '選擇產品',
  CustomerLob: '客戶LOB',
  CustomerFamily: '客戶系列',
  AddProductInstall: '添加產品安裝',
  oscaNewProductTitle: '產品選擇',
  oscaProductAddressTitle: '地址影射和選擇產品',
  Equipment: '設備',
  Premium: '禮品',
  Confirm: '確認',
  Description: '描述',
  // 表单查询弹窗
  CustomerSelection: '客戶選擇',
  DragonCustomerNo: 'HKT客戶編號',
  SalesTeam: '銷售團隊',
  SalesSubTeam: '銷售子團隊',
  // 新增页面-底部工具栏
  Next: '下一步',
  Previous: '上一步',
  Save: 'Save', // TOOD
  Submit: '提交',
  Cancel: '取消',
  printOrder: '打印訂單',
  addHunting: '添加獵群',
  // 新增页面-Address Mapping
  OK: '確定',
  // 新增页面-tamplateOne
  pageTitle: '單一產品訂單 - 地址映射與產品選擇',
  headTitle: '地址映射',
  SBNo: 'SB編號',
  serviceNo: '服務編號',
  ProductSelection: '產品選擇',
  PrimaryProduct: '主要產品',
  AdditionalProduct: '附加產品',
  MainProduct: '主要產品',
  // 新增页面-Address Mapping
  Address: '地址',
  'SB No': 'SB編號',
  ServiceNo: '服務編號',
  Floor: '樓層',
  Flat: '單位',
  Resource: '資源',
  Spare: '備用',
  '2N': '2N',
  DP: 'DP',
  region: '區域',
  street: '街道',
  district: '區',
  estate: '住宅',
  building: '大廈',
  Flat_Unit_Room: '單位/房間',
  LotNumber: '地段編號',
  atLeastOneCondition: '請至少輸入一個條件',
  tipsTitle1: '通知信息',
  tipsText1: '請輸入至少一個搜索條件。',
  tipsText2: '請選擇至少一個地址。',
  tipsText3: '確認訂單提交',
  tipsText4: '請在繼續下一步之前添加服務號碼！',
  tipsText5: '你確定要取消當前主產品嗎？',
  tipsText6: '請選擇至少一條訂單。',
  // 新增页面-Address Mapping-addAddress
  newAddressMappingTitle: '新增地址映射',
  RegioRuleMessage: '請選擇地區',
  DistrictRuleMessage: '請選擇區域',
  StreetRuleMessage: '請輸入街道',
  EstateRuleMessage: '請輸入小區',
  BuildingRuleMessage: '請輸入建築',
  FloorRuleMessage: '請輸入樓層',
  LotNumberRuleMessage: '請輸入地段編號',
  // 新增页面-Address Mapping-component1
  standardMRC: '標准月租',
  type: '類型',
  Charge: '收費',
  Period: '期間',
  Action: '操作',
  MonthlyRentalCharge: '月租費',
  // 新增页面-numberSelection
  tamplateTwoHeadTitle: '單產品訂單 - 號碼選擇',
  numberSelection: '號碼選擇',
  AddServiceNo: '添加服務號碼',
  addTitle: '服務號碼搜索',
  SelectedServiceNoQty: '已選擇服務號碼數量',
  Allchecked: '選擇/取消選擇當前頁面所有服務號碼',
  RemoveSelected: '移除所選',
  NoData: '無數據',
  copyToAll: '將產品配置複製到所有選中的號碼！',
  // 新增页面-numberSelection-addPopWindow
  ServiceNoType: '服務號碼類型',
  ServiceNoQty: '服務號碼數量',
  ServiceGroup: '服務組',
  selectType: '選擇類型',
  serviceGroup_Service: '服務組/服務',
  Service: '服務',
  ReserveDN: '預留DN',
  ReservationCode: '預約碼',
  BOC: 'BOC',
  Project: '項目',
  NewInstallation: '新安裝',
  PIPBNumber: 'PIPB號碼',
  NoCriteria: '無篩選條件',
  PreferredFirst: '優先選擇前1-5位數字',
  PreferredLast: '優先選擇後6-8位數字',
  // 新增页面-numberSelection-editProduct
  editProductTitle: '產品/套餐/增值服務/定價屬性編輯',
  addressMapping: '地址映射',
  installationAddress: '安裝地址',
  SB_No: 'SB編號',
  numberAttributor: '號碼屬性',
  callForwardingNo: '呼叫轉移號碼',
  // 新增页面-MainProduct&PremiumProduct-num&score
  ProductQuantity: '產品數量',
  RedeemablePoints: '可兌換積分',
  NoOfLinesInstalled: '已安裝的線路數量',
  NoOfPremiumPoints: '已选择的產品積分',

  // 提示信息字段
  selectMainProd: '請選擇主產品！',
  accountListError: '請至少包含一個收費類別為I的帳戶列表！',
  iddAccountListError: '請至少包含一個收費類別為I的帳戶列表！',
  customerError: '請選擇客戶！',
  numberOfSelect: '選擇數量',
  citinetPageTitle: 'Citinet群組產品選擇',
  huntingProductSelectPageTitle: '獵號群組產品選擇',
  idapProductPageTitle: 'IDAP（站點級別）組產品選擇',
  attributeEdit: '屬性編輯',
  // selectType: '請選擇一個類型！',

  selectServiceNo: '請選擇至少一個號碼',
  cancelIndividualVas: '請確認是否取消個人增值服務。',
  selectMainProdOnlyOne: '只能選擇一個主產品',
  numberNeedToSelectSubProduct: '號碼{number}需要選擇一個子產品',
  numberSequenceUniqueness: '順序{number}重複，請重新編輯順序',
  premiumProduct: '高端產品',
  equipmentProduct: '設備產品',
  cannotMoreThan: '{name}下最多可以選擇{num}項，目前已選擇{selectedCount}項',
  cannotLessThan: '{name}下至少需要選擇{num}項，目前已選擇{selectedCount}項',
  selectMainProdMustHasPackageElement: '主產品必須至少包含一個套餐和元素',
  attributeDetail: '屬性詳情',
  selectInquiry: '請在查詢後選擇',
  attributeMustEditConfim:
    '需要編輯{productName}下{packageName}下的{elementName}的内容以進行確認！',
  numberLimitQuantityPrompt: '可輸入最多20個號碼，以逗號分隔',
  enterTheCustomerName: '請輸入客戶名稱',
  jumpHuntingForCitinetTips: '正在跳轉huntingForCitinet業務…',
  huntingForCitinetGroupProductSelection: 'Citinet群組產品選擇獵號',
  NoChange: '無變更內容',
  showAllSBNOCheckBox: '顯示與地址關聯的所有SB編號',
  Other: '其他',
  ChangeDetail: '變更詳情',
  citinetGroupProduct: 'Citinet集團產品',
  exDirectory: '未列入電話號碼簿的',
  directoryName: '目錄名',
  limitBusinessNumbers: '當前新裝業務的號碼數量不得超過{Number}',
  omitUnnecessaryNumbers: '当前号码数量已达{Number}，剩余未能添加的号码将会自动省略',
  oneCustomerError: '請選擇一位客戶！',
  badCustomerError: '唔允許此操作，因為當前客戶有信用問題!',
  fixedLineOK: '固定線路：正常',
  FixedLineFalse: '固定线路：客戶付款逾期，如下：',
  totalOverdueAmount: '逾期總金額：$',
  listofAccountNumber: '帳號清單',
  pleaseSelectOneOfTheData: '請選擇其中一條數據',
  completeNumber: '請輸入一個已知的8位數號碼',
  pageTitleOfCommit: '訂單結果',
  ccpTaxRate: '{productName}下{packageName}下的{elementName}的内容不能為空！',
  pendingOrderNotDoMACDTips: 'DN（{SERVICE_NO_List}）在途中，無法做MACD業務',
  editDeviceAttributes: '需要編輯｛productName｝下的内容以確認！',
  accountInfoOfNewInstall: '請至少包含一個收費類別為R的帳戶列表！',
  checkShowAllSBNOCheckBox: '請勾選“顯示與地址關聯的所有SB編號”',
  SBNOListHasNoData: 'SBNO清單沒有數據',
  noExitCurrentServiceNO: '沒有這樣的工作服務號',
};

export default customerVerify;
