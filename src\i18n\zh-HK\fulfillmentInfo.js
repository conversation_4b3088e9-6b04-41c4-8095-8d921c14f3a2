// 履约信息

const fulfillmentInfo = {
  fulfillmentInfo: '履行信息',

  // HKT Contact List 表格字段
  HKTContactList: 'HKT聯絡人列表',
  seq: '序號',
  type: '類型',
  name: '名稱',
  salesCode: '銷售代碼',
  contactPhone: '聯繫電話號碼',
  mobile: '手提號碼',
  email: '電子郵件',

  //Customer Contact List表格字段
  customerContactList: '客戶聯絡人列表',

  //SRD 字段
  srd: 'SRD',
  serviceNo: '服務號碼',
  appointmentDate: '約定日期',
  preWiringDate: '預布線日期',
  srdNoNull: 'SRD時間不可為空！',
  constructionReservationTips: '請進行施工預約！',

  // otherInformation 字段
  otherInformation: '其他',
  OASISSerialNo: 'OASIS序列號',
  notEnteredOASISSerialNo: '請輸入OASIS序列號。',
  OASISSerialNoError:
    'OASIS序列號格式不正確。請使用XXYY-YYYYYYYY格式。X代表允許字母+數字，Y代表僅允許數字。',
  billingCustomerReference: '帳單客戶參攷（顯示在帳單上）',
  project: '項目程式碼',
  OPPID: 'OPPID',
  terminationReason: '拆機原因',
  terminationRemark: '拆機備註',
  welcomeLetterCustomerHotlineNumber: '歡迎信顧客服務熱綫',
  welcomeLetter: '歡迎信',
  orderRemark: '訂單備註',
  newHuntingForCitinetGroup: '是否同時新開Hunting for Citinet Group',
  // HKT Contact List 新增弹窗字段
  HKTContact: 'HKT聯繫人',
  participantsType: '參與者類型',
  staffId: '員工編號',
  orderSalesType: '訂單銷售類型',
  agentCode: '代理代碼',
  agentName: '代理姓名',
  participantsTypeWarning: '參與者類型字段格式不正確！',
  contactPhoneWarning: '聯繫電話字段格式不正確！',
  mobileWarning: '行動電話字段格式不正確！',
  emailWarning: '電子郵件字段格式不正確！',
  // Customer Contact List 新增弹窗字段
  title: '標題',

  // SRD 弹窗
  SB_No: 'SB編號',
  address: '地址',
  repeatChoose: '不能重複選擇！',
  customerContactTypeRequired: '客戶聯繫人類型是必填項！',
  customerContactNameRequired: '客戶聯繫人姓名是必填項！',
  customerContactPhoneRequired: '客戶聯繫人聯繫電話是必填項！',
  customerContactPhoneIncorrectFormat: '客戶聯繫人聯繫電話，請輸入以5、6、8或9開頭的八位數字',
  customerContactMobileIncorrectFormat: '客戶聯繫人行動電話，請輸入以5、6、8或9開頭的八位數字',
  emailIncorrectFormat: '請輸入正確的電子郵件格式',
  HKTTypeRequired: 'HKT聯繫人參與者類型是必填項！',
  HKTOrderSaleTypeRequired: 'HKT聯繫人訂單銷售類型是必填項！',
  HKTEmailRequired: 'HKT聯繫人電子郵件是必填項！',
  HKTMobileIncorrectFormat: 'HKT聯繫人行動電話，請輸入以5、6、8或9開頭的八位數字',
  HKTPhoneIncorrectFormat: 'HKT聯繫人聯繫電話，請輸入以5、6、8或9開頭的八位數字',
  HKTContactPhoneRequired: 'HKT聯繫人聯繫電話是必填項！',
  HKTStaffIdRequired: 'HKT聯繫人員工編號是必填項！',
  HKTNameRequired: 'HKT聯繫人姓名是必填項！',
  HKTAgentNameRequired: 'HKT聯繫人代理商名稱是必填項！',
  HKTAgentCodeRequired: 'HKT聯繫人代理代碼是必填項！',
  dateComparisonVerification: 'SRD日期不應早於預約日期和預佈線日期中最晚的一個!',
  errorEmailTips: '錯誤的郵箱地址，請重新輸入',
  errorPhoneTips: '錯誤的手機號碼格式，請重新輸入',
  HKTContactValidTips: 'HKT連絡人至少包含一條數據！',
  customerContactValidTips: '客戶連絡人至少包含一條參與者類型為Admin的數據！',
  notNeedAppointmentTips: '新建地址不需要施工預約',
  gettingAppointmentTime: '正在獲取預約資訊（{second}秒）',
  cancelReservation: '取消預約',
  notificationLetter: '通知信',
  nonAppointmentItem: '目前沒有預約的現場服務安排，需要創建一個嗎？',
};
export default fulfillmentInfo;
