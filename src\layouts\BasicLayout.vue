<template>
  <a-layout id="components-layout-demo-custom-trigger">
    <a-layout-sider v-model="collapsed" :trigger="null" collapsible>
      <div class="logo" />
      <a-button type="primary" style="margin-bottom: 16px" @click="toggleCollapsed">
        <a-icon :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
      </a-button>
      <a-menu theme="dark" mode="inline" @click="handleMenuClick" :selectedKeys="selectedKeys">
        <a-menu-item v-for="item in menuItems" :key="item.key">
          <a-icon :type="item.icon" />
          <span>{{ item.text }}</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-content
        :style="{
          background: '#fff',
          minHeight: '280px',
          position: 'relative',
        }"
      >
        <b-router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script>
  import bRouterView from '@/layouts/bRouterView.vue';
  export default {
    name: 'BasicLayout',
    components: { bRouterView },
    data() {
      return {
        collapsed: false,
        menuItems: [],
      };
    },
    computed: {
      selectedKeys() {
        return [`${this.$route.name}`];
      },
    },
    mounted() {
      this.generateMenuItems();
    },
    methods: {
      generateMenuItems() {
        this.menuItems = this.$router.options.routes
          .filter(route => route.meta && route.meta.menu)
          .map(route => ({
            key: route.name,
            icon: route.meta.menu.icon,
            text: route.meta.title,
          }));
      },
      handleMenuClick({ key }) {
        if (key == this.selectedKeys[0]) {
          return;
        }
        this.$router.push({ name: String(key) });
      },
      toggleCollapsed() {
        this.collapsed = !this.collapsed;
      },
    },
  };
</script>

<style lang="less" scoped>
  /* ...样式代码 */
  /deep/ .ant-layout-sider {
    z-index: 10;
  }
</style>
