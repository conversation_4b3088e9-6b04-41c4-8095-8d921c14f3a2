<template>
  <div class="switchContainer">
    <span
      class="base-option"
      :style="languageStyle(item.acceptlanguage)"
      v-for="item in languageList"
      :key="item.acceptlanguage"
      :class="{ active: item.acceptlanguage === language }"
      @click="changeLanguage(item.acceptlanguage, item.acceptlanguage)"
    >
      {{ item.label }}
    </span>
  </div>
</template>

<script>
  import tool from '@/utils/tool';
  export default {
    name: 'i18nSelector',
    data() {
      return {
        languageList: [
          {
            acceptlanguage: 'zh-CN',
            label: '简中',
          },
          {
            acceptlanguage: 'en-US',
            label: 'En',
          },
          {
            acceptlanguage: 'zh-HK',
            label: '繁中',
          },
        ],
        language: tool.local.get('accept-language') || 'zh-CN',
      };
    },
    methods: {
      languageStyle(language) {
        return {
          color: language === this.language ? '#fff' : 'rgb(36, 108, 243)',
          background: language === this.language ? 'rgb(36, 108, 243)' : 'inherit',
        };
      },
      changeLanguage(language, acceptlanguage) {
        this.language = language;
        tool.local.set('accept-language', acceptlanguage);
        window.location.reload(true);
      },
    },
  };
</script>

<style lang="less" scoped>
  .switchContainer {
    overflow: hidden;
    width: 99px;
    height: 22px;
    background: rgba(188, 188, 188, 0.23);
    border-radius: 16px;
    font-size: 12px;
    display: flex;
    .base-option {
      width: 30px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      cursor: pointer;
    }
    .active {
      width: 36px;
      height: 22px;
      line-height: 22px;
      box-shadow: 0px 0px 5px 0px rgba(0, 79, 113, 0.45);
      border-radius: 16px;
      color: #ffffff;
      text-align: center;
    }
  }
</style>
