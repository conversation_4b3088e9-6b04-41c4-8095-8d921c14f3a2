<template>
  <div>
    <div class="outer-header">
      <i18nSelector />
    </div>
    <b-router-view />
  </div>
</template>
<script>
  import ssoLogin from './sso';
  import i18nSelector from './i18n.vue';
  import bRouterView from '@/layouts/bRouterView.vue';
  export default {
    name: 'OuterHeader',
    components: {
      i18nSelector,
      bRouterView,
    },
    mounted() {
      // ssoLogin();
    },
  };
</script>

<style lang="less" scoped>
  .outer-header {
    width: 100%;
    display: flex;
    height: 40px;
    background-color: white;
    align-items: center;
    justify-content: flex-end;
    padding: 0 12px;
    border-bottom: 1px solid #f0f0f0;
  }
</style>
