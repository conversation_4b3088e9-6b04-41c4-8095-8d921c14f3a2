import tool from '@/utils/tool';
import { getMenu, getStaffFromId, getStaffId, toSSO } from '@/api/sso';
import router from '@/router';

export default async () => {
  // 开发环境直接return
  if (process.env.NODE_ENV === 'development') return;
  const isLogin = await checkLogin();
  if (!isLogin) {
    toSSO();
  }
};

export const checkLogin = async () => {
  try {
    // 1. 检查本地存储中是否有用户信息
    const staff = await getLocalStaff();
    if (staff?.STAFF_ID && (await hasPermission(staff))) {
      return true;
    }

    // 2. 如果没有用户信息，尝试通过nonce获取
    const { nonce } = getUrlParams();
    if (!staff?.STAFF_ID && nonce) {
      const staffInfo = await getStaffInfoByNonce(nonce);
      if (staffInfo && (await hasPermission(staffInfo))) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('SSO权限验证失败:', error);
    return false;
  }
};

/**
 * 从本地存储获取用户信息
 * @returns {Object|null} 用户信息
 */
const getLocalStaff = () => {
  try {
    return JSON.parse(tool.local.get('staff'));
  } catch (error) {
    console.error('获取本地用户信息失败:', error);
    return null;
  }
};

/**
 * 通过nonce获取用户信息
 * @param {string} nonce - 临时令牌
 * @returns {Object|null} 用户信息
 */
const getStaffInfoByNonce = async nonce => {
  try {
    const res = await getStaffId(nonce);
    if (!res.data) return null;

    const staffRes = await getStaffFromId(res.data);
    if (!staffRes) return null;

    const staffInfo = staffRes?.RSP?.DATA[0]?.STAFF_INFO;
    if (staffInfo) {
      tool.local.set('staff', JSON.stringify(staffInfo));
    }
    return staffInfo;
  } catch (error) {
    console.error('通过nonce获取用户信息失败:', error);
    return null;
  }
};

export const getUrlParams = () => {
  const hashIndex = window.location.search.indexOf('?');
  const queryString = hashIndex !== -1 ? window.location.search.slice(hashIndex + 1) : '';
  const urlParams = new URLSearchParams(queryString);
  const paramsObj = {};

  for (const [key, value] of urlParams.entries()) {
    paramsObj[key] = value;
  }
  return paramsObj;
};

export const hasPermission = async staff => {
  const PERMISSION_MENU_ID = router.currentRoute?.meta?.authMenuId;
  const menu = await getMenu(
    staff.STAFF_ID,
    staff.PROVINCE_CODE,
    staff.EPARCHY_CODE,
    staff.DEPART_ID,
  );
  if (menu.find(item => item.MENU_ID === PERMISSION_MENU_ID)) return true;
  return false;
};
