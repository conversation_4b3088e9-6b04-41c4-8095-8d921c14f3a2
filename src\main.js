/* eslint-disable vue/require-name-property */
import ScrollHelper from '@/components/scrollHelper';
import router from '@/router';
import Antd, { message } from 'ant-design-vue';
import Vue from 'vue';
import App from './App.vue';
import store from './store';
import {
  containsSqlInjection,
  containsSqlInjectionSquarebrackets,
  validateNumber,
  validateNumberPoint,
  validateHKTPhone,
  validateEmail,
  validateEmailMore,
  validateNumberAndPoint,
  validateAddress,
  buttonPermission,
  debounceClick,
} from './utils/directives';
import i18n from '@/i18n/index';
import tool from './utils/tool';

import 'ant-design-vue/dist/antd.less';
import 'core-js/stable';
import 'regenerator-runtime/runtime';
import './assets/iconfont/iconfont.css';
import './styles/flex-common.less';
import './styles/global.less';
import './styles/normalize.css';
import '@/utils/navc';

Vue.prototype.$message = message;
Vue.config.productionTip = false;
Vue.directive('containsSqlInjection', containsSqlInjection);
Vue.directive('containsSqlInjectionSquarebrackets', containsSqlInjectionSquarebrackets);
Vue.directive('validateHKTPhone', validateHKTPhone);
Vue.directive('validateNumber', validateNumber);
Vue.directive('validateNumberPoint', validateNumberPoint);
Vue.directive('validateEmail', validateEmail);
Vue.directive('validateEmailMore', validateEmailMore);
Vue.directive('validateNumberAndPoint', validateNumberAndPoint);
Vue.directive('validateAddress', validateAddress);
Vue.directive('buttonPermission', buttonPermission);
Vue.directive('debounceClick', debounceClick);

// 全局挂载
Vue.prototype.$tool = tool;

Vue.use(Antd);
Vue.use(ScrollHelper);

export default new Vue({
  router,
  store,
  i18n,
  render: h => h(App),
}).$mount('#app');
