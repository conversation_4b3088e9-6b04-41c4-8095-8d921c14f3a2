import Vue from 'vue';
import VueRouter from 'vue-router';
import createStack from '@/utils/routerStack';

Vue.use(VueRouter);

/**
 * 内网路由配置
 * @param {String} path 路由路径
 * @param {String} name 路由名称
 * @param {Object} meta 路由元信息  1.不写meta便隐藏该目录，可以根据业务进行跳转  2.目前开发阶段先放出来写页面
 */
const innerRoutes = [
  {
    path: '/',
    redirect: '/quotation/quotationManagement',
  },
  {
    // 内网报价单审批界面
    path: 'intra/preApproval',
    name: 'preApproval',
    meta: {
      title: '内网报价单审批',
    },
    component: () => import('@/views/intra/preApproval/index.vue'),
  },
  {
    // 内网AF单审批界面
    path: 'intra/afApproval',
    name: 'afApproval',
    meta: {
      title: '内网AF单审批',
    },
    component: () => import('@/views/intra/afApproval/index.vue'),
  },
  {
    // 报价单管理列表
    path: '/quotation/quotationManagement',
    name: 'quotationManagement',
    meta: {
      title: '报价单管理',
      keepAlive: true,
      menu: { icon: 'unordered-list' },
    },
    component: () => import('@/views/intra/quotation/quotationManagement/index.vue'),
  },
  {
    // 创建报价单
    path: '/quotation/addQuotation',
    name: 'addQuotation',
    meta: {
      keepAlive: true,
    },
    component: () => import('@/views/intra/quotation/addEditQuotation'),
  },
  {
    // 报价单详情
    path: '/quotation/quotationDetail',
    name: 'quotationDetail',
    component: () => import('@/views/intra/quotation/quotationDetail/index.vue'),
  },
  {
    // 产品树
    path: '/del/oscaAddProductTree',
    name: 'oscaAddDelProductTree',
    component: () =>
      import('@/views/intra/quotation/addEditQuotation/addEditProductInfo/del/index.vue'),
  },
  {
    path: '/idd/oscaAddProductTree',
    name: 'oscaAddIddProductTree',
    component: () =>
      import('@/views/intra/quotation/addEditQuotation/addEditProductInfo/idd/index.vue'),
  },
  {
    // 订单捕获管理列表
    path: '/orderCapture/orderCapture/aFManagement',
    name: 'afManagement',
    meta: {
      title: '订单捕获管理',
      menu: { icon: 'account-book' },
      keepAlive: true,
    },
    component: () => import('@/views/intra/orderCapture/afOrderManagement/index.vue'),
  },
  {
    // 订单捕获 - 编辑&详情
    path: '/orderCapture/orderCapture/afOrderEditDetail',
    name: 'afOrderEditDetail',
    meta: {
      title: '订单捕获编辑详情',
      menu: { icon: 'account-book' },
    },
    component: () => import('@/views/intra/orderCapture/afOrderEditDetail/index.vue'),
  },
  {
    // 订单捕获 - 编辑Af信息
    path: '/orderCapture/orderCapture/afEditAfInfo',
    name: 'afEditAfInfo',
    meta: {
      title: '订单捕获编辑Af信息',
      menu: { icon: 'account-book' },
    },
    component: () => import('@/views/intra/orderCapture/afEditAfInfo/index.vue'),
  },
  // ... 其他内网路由
];

// 外网路由配置
const outerRoutes = [
  {
    path: '/outer/preApproval',
    name: 'opre-approval',
    meta: {
      title: '外网报价单审批',
      authMenuId: 'xxxxxxxxx1', // TODO: 询问服务端，对应的MENU_ID，oss会用
    },
    component: () => import('@/views/outer/preApproval/index.vue'),
  },
  {
    path: '/outer/afApproval',
    name: 'oaf-approval',
    meta: {
      title: '外网AF单审批',
      authMenuId: 'xxxxxxxxx2', // TODO: 询问服务端，对应的MENU_ID，oss会用
    },
    component: () => import('@/views/outer/afApproval/index.vue'),
  },
];

// 开发环境下合并所有路由，生产环境下根据环境变量选择路由
const routes =
  process.env.NODE_ENV === 'development'
    ? [...innerRoutes, ...outerRoutes]
    : process.env.VUE_APP_MODE === 'outer'
    ? outerRoutes
    : innerRoutes;

const router = new VueRouter({
  mode: 'hash',
  base: process.env.VUE_APP_BASE_URL,
  routes,
});

createStack(router);
export default router;
