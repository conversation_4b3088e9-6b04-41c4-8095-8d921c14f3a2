import Vuex from 'vuex';
import Vue from 'vue';
import createPersistedState from 'vuex-persistedstate';
import quotation from './modules/quotation';
import orderCapture from './modules/orderCapture';
import app from './modules/app';
Vue.use(Vuex);

// // ���ؿ���Ĭ�Ͽ����־û��洢 �������
const isPersistenceEnabled = process.env.VUE_APP_ENABLE_VUEX_PERSISTENCE === 'true';

const state = {};
const actions = {};
const getters = {};
const mutations = {};
const modules = {
  quotation,
  orderCapture,
  app,
};

// TODO: 本地持久化部分store，是为了研发阶段方便，业务设计时候考虑下
const plugins = isPersistenceEnabled
  ? [
      createPersistedState({
        storage: window.localStorage,
        paths: ['quotation'],
      }),
    ]
  : [];

export default new Vuex.Store({
  state,
  actions,
  getters,
  mutations,
  modules,
  plugins,
});
