const orderCapture = {
  namespaced: true,
  state: {
    numberSelectionPage: {}, // 选号页面
    isEbillMedia: false, // 账户media类型
    accountSettingPage: {}, // 账户设置：包含 选择账户信息PAYRELATION_INFO,numbersWithAccountInfo
  },
  mutations: {
    // 选号页面
    Set_NumberSelectionPage(state, payload) {
      state.numberSelectionPage = payload;
    },

    // 账户media类型
    Set_isEbillMedia(state, payload) {
      state.isEbillMedia = payload;
    },

    // 账户设置
    Set_AccountSettingPage(state, payload) {
      state.accountSettingPage = payload;
    },

    Set_cleanState(state) {
      state.numberSelectionPage = {};
      state.isEbillMedia = false;
      state.accountSettingPage = {};
    },
  },
  actions: {
    // 选号页面
    setNumberSelection({ commit }, payload) {
      commit('Set_NumberSelectionPage', payload);
    },
    // 账户media类型
    setIsEbillMedia({ commit }, payload) {
      commit('Set_isEbillMedia', payload);
    },
    // 账户设置
    setAccountSettingPage({ commit }, payload) {
      commit('Set_AccountSettingPage', payload);
    },
    setCleanState({ commit }) {
      commit('Set_cleanState');
    },
  },
};
export default orderCapture;
