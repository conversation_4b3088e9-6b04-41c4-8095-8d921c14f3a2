const quotation = {
  namespaced: true,
  state: {
    // new
    custId: '', //  客户ID
    productSelection: {}, // 选产品
    productType: {}, // Product Type 选中的数据
    productStatus: 'add', //当前产品是新增还是编辑
    isCORP: false, //是否大客户
  },
  mutations: {
    Set_CustId(state, payload) {
      state.custId = payload;
    },
    // 选产品
    Set_ProductSelection(state, payload) {
      state.productSelection = payload;
    },
    Set_ProductTypeData(state, payload) {
      state.productType = payload;
    },
    Set_ProductStatus(state, payload) {
      state.productStatus = payload;
    },
    Set_IsCORP(state, payload) {
      state.isCORP = payload;
    },
    Set_cleanState(state) {
      state.productSelection = {};
      state.productType = {};
      state.productStatus = 'add';
      state.custId = '';
    },
  },
  actions: {
    setCustId({ commit }, payload) {
      commit('Set_CustId', payload);
    },
    setProductSelection({ commit }, payload) {
      commit('Set_ProductSelection', payload);
    },
    setProductTypeData({ commit }, payload) {
      commit('Set_ProductTypeData', payload);
    },
    setProductStatus({ commit }, payload) {
      commit('Set_ProductStatus', payload);
    },
    setIsCORP({ commit }, payload) {
      commit('Set_IsCORP', payload);
    },
    setCleanState({ commit }) {
      commit('Set_cleanState');
    },
  },
};
export default quotation;
