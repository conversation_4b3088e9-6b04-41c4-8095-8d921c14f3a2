@active-primary-bg-color: rgb(255, 233, 233);

/* HKT下规范 */
@color-gray: #ededed; // 辅助灰色
/* prettier-ignore */
@cuecolor-gray: #DCDCDC; // 提示灰色
@primary-color: #01408e; // 全局主色
@text-color: #333333; // 全局字体颜色
@font-family: PingFangSC; // 全局通用字体
@font-size: 14px; // 全局通用字体大小
@font-weight: 500; // 全局通用字体宽度

/* 标题样式 */
@firstLevelHeading-fontSize: 18px; // 全局一级标题字体大小
@secondLevelHeading-fontSize: 16px; // 全局二级标题字体大小
@heading-font-weight: 600; // 标题字体宽度
@heading-margin-bottom: 10px; // 标题底部间距

/* 按钮样式 */
@button-background: @primary-color; // 按钮背景色
@button-color: #ffffff; // 按钮字体色
@button-width: 90px; // 按钮宽度
@button-height: 32px; // 按钮高度
@moadl-button-width: 60px; // 弹框按钮
@moadl-button-height: 22px; // 弹框按钮
@fixed-bottom-button-width: 120px; // 底部栏按钮
@fixed-bottom-button-height: 40px; // 底部栏按钮

/* 表单下所有样式 */
@input-border: 1px solid @color-gray;
@input-border-radius: 2px;
@input-height: 32px;
@input-line-height: 32px;
@padding-bottom: 10px;

/* tabel表格样式 */
@height: 40px; // 高度
@line-height: 1.429; // 行高
@th-padding: 10px 15px; // 表格头内边距
@td-padding: 0 15px; // 表格行内边距
