 // 自定义 disabled 状态下 ant-checkbox-input 的边框颜色和背景颜色
.ant-checkbox-input[disabled] + .ant-checkbox-inner {
    color: #9e9e9e !important; // 修改为你想要的颜色
    background-color: #e9e9e9 !important;
    border-color: #9e9e9e  !important;
  }

  // 自定义 disabled 状态下 ant-checkbox-input 的边框颜色和背景颜色（选中状态）
.ant-checkbox-input[disabled]:checked + .ant-checkbox-inner {
    color: #9e9e9e !important; // 修改为你想要的颜色
    background-color: #e9e9e9 !important;
    border-color: #9e9e9e  !important;
  }

  // 自定义 disabled 状态下 ant-checkbox-input 的边框颜色和背景颜色（选中状态下的伪元素）
.ant-checkbox-input[disabled]:checked + .ant-checkbox-inner::after {
    color: #9e9e9e !important; // 修改为你想要的颜色
    background-color: #e9e9e9 !important;
    border-color: #9e9e9e  !important;
  }