// a-form-model 组件
.commonFormModel {
  // 上下分表单间距
  .ant-form-item-label {
    margin-bottom: -10px !important;
  }
  // 表单标签和控件上下布局，避免间距过大，margin-bottom重置为0
  // .ant-form-item {
  //   margin-bottom: 0;
  // }
  // 在<col>里面的按钮 置右边
  .textAlignRight {
    text-align: right;
  }
  // 单独一行的按钮 置右边
  .textAlignRightMargin {
    text-align: right;
    margin: 10px 0;
  }
}

// 在<col>里面的按钮 置右边
.textAlignRight {
  text-align: right;
}

// 单独一行的按钮 置右边
.textAlignRightMargin {
  text-align: right;
  margin: 10px 0;
}

.ant-form-item-control {
  line-height: @input-height !important;
}

.ant-form-item-label > label {
  color: @text-color;
}

.ant-form-item-label {
  height: @input-height !important;
  line-height: @input-height !important;
  padding-bottom: @padding-bottom !important;
  margin-bottom: -5px;
}
.ant-form-item{
  margin-bottom: 10px !important;
  padding-bottom: 0px !important;
}
.ant-form-explain{
  position: absolute;
}

.form-item-mar-btm-20 {
  .ant-form-item {
    margin-bottom: 20px !important;
  }
}

.form-item-btn {
  .ant-form-item {
    margin: 10px 0 0 !important;
  }
}