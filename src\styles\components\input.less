.ant-input {
  // 输入框
  // height: @input-height;
  // line-height: @input-line-height !important;
  border-radius: 0px;
  color: #333;
  margin-right: 30px;
}
.ant-input[disabled]{
  color: #9e9e9e !important; // 修改为你想要的颜色
  background-color: #e9e9e9 !important;
  border-color: #9e9e9e  !important;
}
.ant-input::placeholder {
  color: #999 !important; // 修改为你想要的颜色
}
// 自定义 disabled 状态下输入框 placeholder 的字体颜色
.ant-input[disabled]::placeholder {
  color: #9e9e9e !important; // 修改为你想要的颜色
  background-color: #e9e9e9 !important;
  border-color: #9e9e9e  !important;
}
.ant-input::placeholder {
  color: #999 !important; // 修改为你想要的颜色
}
// 自定义 a-range-picker 在禁用状态下的 placeholder 颜色
.ant-picker-input[disabled] > input::placeholder {
  color: #FF0000 !important; // 修改为你想要的颜色
}