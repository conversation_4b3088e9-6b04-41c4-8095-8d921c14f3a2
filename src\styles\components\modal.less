.ant-modal div[aria-hidden='true'] {
  // 解决Modal组件渲染后控制台报错“aria-hidden“
  display: none !important;
}

// 弹窗名字
.ant-modal-title {
  font-size: 16px !important;
  color: #333333 !important;
  letter-spacing: 0 !important;
  text-align: left !important;
  font-weight: 600 !important;
}

.info-header-title {
  // 弹窗标题
  font-size: 12px;
  letter-spacing: 0;
  text-align: left;
  font-weight: 600;
  color: @text-color !important;
}

.ant-modal-header {
  padding: 0 20px;
  line-height: 40px;
  height: 40px;
  display: flex;
  align-items: center;
}

.ant-modal-close-x {
  line-height: 40px;
}

.ant-modal-body {
  padding: 10px 20px 0 20px;
}

.ant-modal-footer {
  padding: 20px 20px;
}
.ant-input {
  border-radius: 0px !important;
  color: #333;
  margin-right: 0px !important;
}
