.ant-table-thead > tr > th {
  color: @text-color;
  background: @color-gray;
  // line-height: @line-height;
  padding: @th-padding;
  border: none;
}

.ant-table-tbody > tr > td {
  color: @text-color;
  padding: @td-padding;
  // line-height: @height;
  height: @height;
}

.ant-table-fixed {
  min-width: 100%;
}

.active-button {
  overflow: hidden;
  display: block;
  height: @height;
}

.ant-table-fixed-columns-in-body {
  span {
    display: flex;
    overflow: hidden;
  }
}

.ant-table-footer {
  background: #ffffff !important;
  padding: 16px 0 16px 16px;
}

.ant-pagination-item-active {
  border: none !important;
  background: #00408d;
  border-radius: 2px;

  a {
    color: #ffffff !important;
  }
}

.ant-pagination-item a {
  color: #73777a;
}

.ant-pagination-options {
  color: #73777a;

  .ant-select-selection {
    list-style: none;
    font-variant: tabular-nums;
    font-feature-settings: 'tnum';
    color: #73777a;
  }

  .ant-select-selection-selected-value {
    color: #73777a;
  }
}

.ant-pagination-total-text {
  color: #73777a;
  float: right;
  margin-left: 10px;
  line-height: 32px;
}
.ant-table-thead > tr:first-child > th:last-child.action-column {
  text-align: center;
  justify-content: center;
  display: flex;
}
.ant-table-tbody > tr > td:last-child.action-column {
  text-align: center;
}
.ant-table-tbody > tr > td input ,
.ant-table-tbody > tr > td .ant-select-selection {
  width: 120px !important;
  // height: 22px;
}
// 表格行hover样式
.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
  background: #EFF6FF !important; // 你可以将 #EFF6FF 修改为你想要的颜色
}
.ant-collapse-header{
  height: 40px !important;
}
.ant-table-pagination.ant-pagination {
  margin: 40px 0 !important;
}