@import './_variable.less';
@import './components/formModel.less';
@import './components/pagination.less';
@import './components/datePicker.less';
@import './components/divider.less';
@import './components/input.less';
@import './components/modal.less';
@import './components/select.less';
@import './components/table.less';
@import './components/tree.less';
@import './components/upload.less';
@import './components/btn.less';
@import './components/checkbox.less';
@import './components/descriptions.less';

/* 页面标题区域 ====== start */
.firstLevel-header-title {
  font-family: @font-family;
  font-size: @firstLevelHeading-fontSize;
  color: @text-color;
  letter-spacing: 0;
  text-align: left;
  font-weight: @heading-font-weight;
  margin-bottom: @heading-margin-bottom;
}

.secondLevel-header-title {
  // 二级标题
  font-family: @font-family;
  font-size: @secondLevelHeading-fontSize;
  color: @text-color;
  letter-spacing: 0;
  text-align: left;
  font-weight: @heading-font-weight;
  margin-bottom: @heading-margin-bottom;
  position: relative; // 确保伪元素相对于 .com-title 定位
  padding: 0 10px;
  &::before {
    content: '';
    position: absolute; // 使用绝对定位
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 4px;
    height: 15px;
    background-color: @primary-color;
    left: 0; // 确保伪元素在文本前面
  }
}
.mr10 {
  // margin-right: 10px;
}
/* 页面标题区域 ====== end */

/* 页面内边距 ====== start */
.routerView {
  min-height: 100vh;
  // padding: 10px 15px 15px 15px;
  padding: 20px;
  font-size: 14px;
  color: #333;
}
/* 页面内边距 ====== end */

/* 按钮区域 ====== start */
.line-button {
  // 检索按钮的行距
  margin: 10px 0 0 0;
}

.main-button {
  //主要按钮
  height: 30px !important;
  min-width: 80px;
  line-height: 30px;
  padding: 0px 10px !important;
  font-size: 12px !important;
  border-radius: 2px !important;
  background: rgba(254, 50, 56, 0.11) !important;
  border: 1px solid rgba(254, 50, 56, 1) !important;
  color: #fe3238 !important;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.gray-button {
  //灰色按钮
  height: 30px !important;
  min-width: 80px;
  line-height: 30px;
  padding: 0px 10px !important;
  font-size: 12px !important;
  border-radius: 2px !important;
  border: 1px solid rgba(229, 229, 229, 1) !important;
  color: #333333 !important;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.text-button {
  //文字按钮-表格操作
  padding: 0 !important;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #3377ff;
  letter-spacing: 0;
  text-align: left;
  line-height: 40px;
  font-weight: 400;
  height: 40px;
}

.search-button {
  // 头部检索按钮
  height: @button-height;
  width: @button-width;
  letter-spacing: 0;
  padding: 0 !important;
  color: @button-color;
  background: @button-background;
  border-radius: 2px !important;
  border: none !important;
}

.reset-button {
  // 导入按钮
  height: @button-height;
  width: @button-width;
  padding: 0 !important;
  background: #ffffff;
  border: 1px solid rgba(1, 64, 142, 1);
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 14px;
  color: #01408e;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.import-button {
  // 导入按钮
  width: @moadl-button-width;
  height: @moadl-button-height;
  padding: 0 !important;
  background: #ffffff;
  border: 1px solid rgba(1, 64, 142, 1);
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #01408e;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.table-button {
  // 列表功能按钮
  width: 60px;
  height: 22px;
  padding: 0 !important;
  background: #ffffff;
  border: 1px solid rgba(1, 64, 142, 1);
  border-radius: 2px;
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: #01408e;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.table-add-button {
  // 列表新增按钮
  background: @button-background;
  border-radius: 2px;
  color: #ffffff;
  width: 60px;
  height: 22px;
  border: 1px solid rgba(1, 64, 142, 1);
  font-size: 12px;
  letter-spacing: 0;
  padding: 0 !important;
}

.moadl-button-Ok {
  // 弹窗确定按钮
  width: @moadl-button-width;
  background: @button-background;
  border-radius: 2px !important;
  font-size: 12px !important;
  color: #ffffff !important;
  letter-spacing: 0 !important;
  text-align: center !important;
  font-weight: 400 !important;
}

.modal-button-ghost {
  // 弹窗幽灵按钮
  width: @moadl-button-width;
  height: @moadl-button-height;
  border-radius: 2px;
  border-color: @button-background;
  color: @button-background;
  font-size: 12px;
  letter-spacing: 0;
  padding: 0 !important;
}

.modal-button-cancel {
  // 弹窗取消按钮
  width: @moadl-button-width;
  // height: @moadl-button-height;
  border-radius: 2px !important;
  font-size: 12px !important;
  color: #333333 !important;
  letter-spacing: 0 !important;
  text-align: center !important;
  font-weight: 400 !important;
  padding: 0 !important;
}

.tips-moadl-button-Ok {
  // 提示框确定按钮
  width: @moadl-button-width;
  height: @moadl-button-height;
  background: @button-background;
  border-radius: 2px !important;
  font-size: 12px !important;
  color: #ffffff !important;
  letter-spacing: 0 !important;
  text-align: center !important;
  font-weight: 400 !important;
}

.tips-modal-button-cancel {
  // 提示框取消按钮
  width: @moadl-button-width;
  height: @moadl-button-height;
  font-size: 12px !important;
  color: #333333 !important;
  letter-spacing: 0 !important;
  text-align: center !important;
  font-weight: 400 !important;
  margin-right: 10px;
  padding: 0 !important;
}

.import-Tips-button-Ok {
  // 导入提示框确定按钮
  width: 90px !important;
  height: 30px !important;
  border-radius: 2px !important;
  background: #01408e !important;
  color: #ffffff !important;
  border-color: #01408e !important;
  font-size: 14px !important;
  letter-spacing: 0 !important;
}

.import-Tips-button-cancel {
  // 导入提示框取消按钮
  width: 90px !important;
  height: 30px !important;
  border-radius: 2px !important;
  color: #4371ab !important;
  border-color: #4371ab !important;
  margin-right: 10px !important;
  letter-spacing: 0 !important;
  font-size: 14px !important;
}

.primary-button {
  // 无长度限制primary按钮
  border-radius: 2px;
  border: 1px solid #01408e;
}

.defulat-botton {
  color: #01408e;
  border: 1px solid #01408e;
}

.common-button {
  // 无长度限制普通按钮
  // padding: 0 22px;
  font-family: PingFangSC-Regular;
  border-radius: 2px;
  border: 1px solid #01408e;
  color: #01408e !important;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.main-btn {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 2px;

  &:hover {
    opacity: 0.8;
    color: #ffffff;
    background: #0051b6;
  }

  &.active,
  &:focus {
    background: @primary-color;
    -webkit-box-shadow: none;
    box-shadow: none;

    &::before {
      content: '';
      /* 必须要有内容才能显示伪元素 */
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
      /* 黑色透明度背景 */
    }
  }

  // 禁选
  &[disabled],
  &[disabled] :hover,
  &[disabled] :focus,
  &[disabled] :active {
    color: #c0bfbf !important;
    background: #ededed !important;
    border-color: rgba(199, 199, 199, 1) !important;
  }

  &.ant-btn-loading {
    background: #01408e !important;
    color: #ffffff !important;
  }

  &[large] {
    width: 120px;
    height: 40px;
  }

  &[mid] {
    width: 90px;
    height: 32px;
  }

  &[small] {
    width: 60px;
    height: 22px;
    line-height: 0;
    padding: 0;
  }
}

// 次要按钮
.secondary-btn {
  color: @primary-color;
  border: 1px solid @primary-color;
  border-radius: 2px;
  -webkit-box-shadow: none;
  box-shadow: none;
  position: relative;

  &:hover {
    background: #d2e6ff;
    opacity: 0.8;
  }

  &.active,
  &:focus {
    color: #ffffff;
    background: #b1d4ff;
    // &::before {
    //   content: "";
    //   /* 必须要有内容才能显示伪元素 */
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 100%;
    //   background-color: rgba(0, 0, 0, 0.2);
    //   /* 黑色透明度背景 */
    // }
  }

  // 禁选
  &[disabled],
  &[disabled] :hover,
  &[disabled] :focus,
  &[disabled] :active {
    color: #c0bfbf;
    background: #ededed;
    border-color: rgba(199, 199, 199, 1);
  }

  // &.is-loading {
  //   background: $btn-color-secondary !important;
  //   color: var(--primary-color) !important;
  // }

  &[large] {
    width: 120px;
    height: 40px;
  }

  &[mid] {
    width: 90px;
    height: 32px;
  }

  &[small] {
    width: 60px;
    height: 22px;
    line-height: 0;
    padding: 0;
  }
}

/* 按钮区域 ====== end */

/* 滚动条样式 ====== start */
// ::-webkit-scrollbar {
//   width: 6px;
//   height: 6px;
// }

// ::-webkit-scrollbar-track {
//   box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
//   border-radius: 6px;
//   background-color: #f5f5f5;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 6px;
//   box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
//   background-color: #e8e8e8;
// }

// ::-webkit-scrollbar-thumb:window-inactive {
//   background-color: #555;
// }

// * {
//   //Firefox浏览器滚动条样式
//   scrollbar-color: #e5e5e5 #f7f7f9; //滚动条轨道颜色、滚动条滑块的颜色
//   scrollbar-width: thin; //thin模式下滚动条两端的三角按钮会消失
// }

// @-moz-document url-prefix() {
//   * {
//     //Firefox浏览器滚动条样式
//     scrollbar-color: #e5e5e5 #f7f7f9; //滚动条轨道颜色、滚动条滑块的颜色
//     scrollbar-width: thin; //thin模式下滚动条两端的三角按钮会消失
//   }
// }

body {
  //IE浏览器滚动条样式
  scrollbar-shadow-color: #e5e5e5;
  scrollbar-face-color: #e5e5e5;
  scrollbar-base-color: #ffffff;
  scrollbar-arrow-color: #444040;
}

/* 滚动条样式 ====== end */

/* ant-table 表格样式 ====== start */

.table-pagination {
  // 自定义总页数
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  margin: 40px 0 !important;
  .table-pagination-total {
    margin-left: 10px;
    color: #73777a;
  }
}
/* ant-table 表格样式 ====== end */

.common-custom-title {
  color: #333333;
  font-weight: 600;
  font-size: 15px;
  margin: 10px 0;
  padding: 0px 10px;
  align-items: center;
  position: relative; // 确保伪元素相对于 .com-title 定位
  &::before {
    content: '';
    position: absolute; // 使用绝对定位
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 4px;
    height: 15px;
    background-color: @primary-color;
    left: 0; // 确保伪元素在文本前面
  }
}

// 上层返回按钮
.topReturnBtn {
  color: #0076ff;
  cursor: pointer;
  font-weight: 600;
  height: 36px;
  .icon {
    margin-right: 5px;
  }
}

// 右边悬浮定位按钮
.fixedRightBtn {
  position: fixed;
  bottom: 40%;
  right: 20px;
  z-index: 1000;
}

// 底部栏按钮
.fixed-bottom-btn {
  width: @fixed-bottom-button-width;
  height: @fixed-bottom-button-height;
  padding: 0 15px;
  background: #ffffff;
  border: 1px solid @primary-color;
  border-radius: 2px;
  font-family: @font-family;
  font-size: 18px;
  color: @primary-color;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  &.w-auto {
    width: auto;
  }
  &.bg-blue {
    background: @primary-color;
    color: #fff;
  }
  &.danger-type {
    border: 1px solid #e60017;
    color: #e60017;
  }
}

// 页面底部固定操作栏
.fixedBottomPartPlaceholder {
  height: 120px;
  .fixedBottomPart {
    width: 100%;
    height: 80px;
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 999;
    box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #fff;
    .btn {
      color: @primary-color;
      border-color: @primary-color;
    }
    .submitBtn {
      color: #fff;
    }
    .tipDescription {
      margin: 10px 0;
    }
  }
}

.ant-table-fixed-right td {
  // box-sizing: content-box;
  // > span {
  //   display: flex;
  //   align-items: center; /* 垂直居中对齐 */
  //   justify-content: center; /* 水平居中对齐 */
  // }
}

.ant-table-fixed-header .ant-table-body-inner {
  // overflow: auto !important;
}
.ant-divider-horizontal {
  margin: 20px 0 !important;
  border-color: #d9d9d9;
  border-top: 1.5px solid #d9d9d9;
}
.ant-divider-dashed {
  border-color: #d9d9d9 !important;
  border-top: 1.5px dashed #d9d9d9;
  margin: 10px 0 !important;
}
.dashedLine {
  margin: 10px 0 !important;
  border-color: #d9d9d9 !important;
  border-top: 1.5px dashed #000;
}
.left {
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    position: absolute; // 使用绝对定位
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 4px;
    height: 15px;
    background-color: @primary-color;
    left: 0; // 确保伪元素在文本前面
  }
}
.Info-title {
  position: relative;
  padding-left: 10px;
  &::before {
    content: '';
    position: absolute; // 使用绝对定位
    top: 50%;
    transform: translateY(-50%);
    display: inline-block;
    width: 4px;
    height: 15px;
    background-color: @primary-color;
    left: 0; // 确保伪元素在文本前面
  }
}
.label {
  color: #333 !important;
  font-weight: bolder;
  white-space: nowrap;
}
.input-search {
  width: 120px !important;
}

// 表格无数据 - 公用
.custom-empty {
  margin: 21px 0 10px;
  & > img {
    width: 90px;
    height: 80.4px;
  }
  & > p {
    margin: 7.5px 0 0;
  }
}
