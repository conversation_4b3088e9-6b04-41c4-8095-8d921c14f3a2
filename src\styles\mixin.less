@import './_variable.less';

// 选中左下角蓝三角 + 白钩(字符版)
.is-selected(@borderSize, @tickRight: 0, @tickBottom: 0, @tickSize: 12px) {
  position: relative;
  background-color: #fff;
  border-radius: 2px;
  border-color: @primary-color;
  border-style: solid;
  color: @primary-color;
  &::before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    border: @borderSize solid @primary-color;
    border-top-color: transparent;
    border-left-color: transparent;
  }
  &::after {
    position: absolute;
    right: @tickRight;
    bottom: @tickBottom;
    content: '\2713';
    font-size: @tickSize;
    line-height: @tickSize;
    color: #fff;
    transform: scale(0.8);
    z-index: 9;
  }
}

// 选中右下角蓝三角 + 白钩(样式实现版)
.is-selected__tick(@borderBottomWidth: 19px, @borderLeftWidth: 21px,  @tickRight: 1px, @tickBottom: 4px, @tickWidth: 12px, @tickHeight: 6px) {
  position: relative;
  background-color: #fff;
  border-radius: 2px;
  border-color: @primary-color;
  border-style: solid;
  color: @primary-color;
  &::before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 @borderBottomWidth @borderLeftWidth;
    border-color: transparent transparent @primary-color transparent;
  }
  &::after {
    content: '';
    position: absolute;
    right: @tickRight;
    bottom: @tickBottom;
    width: @tickWidth;
    height: @tickHeight;
    border: solid white;
    border-width: 0 0 1px 1px;
    transform: rotate(-45deg);
  }
}
