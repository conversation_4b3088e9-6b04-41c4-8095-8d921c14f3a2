// 新装业务页面
.newinstall-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 100vh;

  &.is-submitted {
    height: 100vh;
    overflow: hidden;

    // 解决打印状态下tab下标异常
    /deep/.tabList .active {
      &::before {
        left: 10% !important;
      }
      &::after {
        left: 40% !important;
      }
    }
  }

  .page {
    padding: 10px 15px 15px 15px;
    flex: 1;
    .currentpage {
      height: 100%;
    }
  }
  .footer-bar {
    button + button {
      margin-left: 10px;
    }
  }
}