import i18n from '@/i18n/index';
import tool from '@/utils/tool';
import { message } from 'ant-design-vue';
import { debounce } from './utils';

/**
 * 绑定 input 事件监听器，用于过滤输入中的敏感关键字和符号。
 * 用于防止 SQL 注入攻击。
 * 使用方法：v-containsSqlInjection
 * @param {HTMLElement} el - 需要绑定事件监听器的 DOM 元素。
 */
export const containsSqlInjection = {
  bind(el, binding, vnode) {
    // 校验非法字符的正则表达式
    // sql 注入原版
    // const regex =
    //   /(\b(truncate|alter|grant|select|update|and|or|delete|insert|trancate|where|char|chr|into|exists|union|all|substr|ascii|declare|exec|count|sum|master|drop|execute|waitfor)\b|(\*|;|@|\uFF01|\uFF1F|!|[?]|'|#|%|<|>|\\|\||\^|`|"|\{|\}|\[|\]|\(|\)|\$|-|&|=|\+))/gi;
    const regex =
      /(\b(truncate|alter|grant|select|update|delete|insert|trancate|where|char|chr|into|exists|union|all|substr|ascii|declare|exec|count|sum|master|drop|execute|waitfor)\b|(\*|;|@|\uFF01|\uFF1F|!|[?]|'|#|%|<|>|\\|\||\^|`|"|\{|\}|\[|\]|\(|\)|\$|&|=|\+))/gi;

    // 校验函数
    const validateInput = value => {
      if (regex.test(value)) {
        message.error(i18n.t('common.inputContainsIllegalCharactersError'));
        return false; // 校验失败
      }
      return true; // 校验通过
    };

    // 更新Vue组件数据的函数
    const updateVueData = newValue => {
      if (vnode.componentInstance) {
        // 如果是组件实例，直接更新
        const model = vnode.data.model;
        if (model && model.callback) {
          model.callback(newValue);
        }
      } else {
        // 如果是普通元素，通过context更新
        const context = vnode.context;
        const expression = vnode.data.model && vnode.data.model.expression;
        if (context && expression) {
          // 解析表达式路径（如 form.CUSTOMER_NAME）
          const keys = expression.split('.');
          let obj = context;
          for (let i = 0; i < keys.length - 1; i++) {
            obj = obj[keys[i]];
          }
          if (obj) {
            obj[keys[keys.length - 1]] = newValue;
          }
        }
      }
    };

    // 监听输入事件
    el.addEventListener('input', function (event) {
      const value = event.target.value;
      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 更新Vue组件数据
        updateVueData('');

        // 手动触发 input 事件以确保其他事件监听器也能接收到
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);
      }
    });

    // 监听粘贴事件
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框

        // 更新Vue组件数据
        updateVueData('');

        // 手动触发 input 事件以确保其他事件监听器也能接收到
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);

        // 避免重复提示
        return;
      }

      // 如果粘贴内容合法，延迟触发 input 事件以确保粘贴内容已经填入
      setTimeout(() => {
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);
      }, 0);
    });
  },
};

/**
 * 绑定 input 事件监听器，用于过滤输入中的敏感关键字和符号。 去除中括号
 * 用于防止 SQL 注入攻击。
 * 使用方法：v-containsSqlInjectionSquarebrackets
 * @param {HTMLElement} el - 需要绑定事件监听器的 DOM 元素。
 */
export const containsSqlInjectionSquarebrackets = {
  bind(el, binding, vnode) {
    // 校验非法字符的正则表达式
    const regex =
      /(\b(truncate|alter|grant|select|update|and|or|delete|insert|trancate|where|char|chr|into|exists|union|all|substr|ascii|declare|exec|count|sum|master|drop|execute|waitfor)\b|(\*|;|@|\uFF01|\uFF1F|!|[?]|'|#|%|<|>|\/|\\|\||\^|`|"|\{|\}|\(|\)|\$|-|&|=|\+))/gi;

    // 校验函数
    const validateInput = value => {
      if (regex.test(value)) {
        message.error(i18n.t('common.inputContainsIllegalCharactersError'));
        return false; // 校验失败
      }
      return true; // 校验通过
    };

    // 更新Vue组件数据的函数
    const updateVueData = newValue => {
      if (vnode.componentInstance) {
        // 如果是组件实例，直接更新
        const model = vnode.data.model;
        if (model && model.callback) {
          model.callback(newValue);
        }
      } else {
        // 如果是普通元素，通过context更新
        const context = vnode.context;
        const expression = vnode.data.model && vnode.data.model.expression;
        if (context && expression) {
          // 解析表达式路径（如 form.CUSTOMER_NAME）
          const keys = expression.split('.');
          let obj = context;
          for (let i = 0; i < keys.length - 1; i++) {
            obj = obj[keys[i]];
          }
          if (obj) {
            obj[keys[keys.length - 1]] = newValue;
          }
        }
      }
    };

    // 监听输入事件
    el.addEventListener('input', function (event) {
      const value = event.target.value;
      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 更新Vue组件数据
        updateVueData('');

        // 手动触发 input 事件以确保其他事件监听器也能接收到
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);
      }
    });

    // 监听粘贴事件
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框

        // 更新Vue组件数据
        updateVueData('');

        // 手动触发 input 事件以确保其他事件监听器也能接收到
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);

        // 避免重复提示
        return;
      }

      // 如果粘贴内容合法，延迟触发 input 事件以确保粘贴内容已经填入
      setTimeout(() => {
        const evt = new Event('input', { bubbles: true });
        event.target.dispatchEvent(evt);
      }, 0);
    });
  },
};
/**
 * 用于限制输入框只能输入数字,同时增加自定义位数限制。
 * 当用户输入时，会自动过滤掉所有非数字字符，仅保留纯数字输入。
 *
 * 使用方法：v-validate-number
 * 在Vue组件的模板中，将此指令添加到需要限制数字输入的<input>元素上。
 * 示例：<input v-model="form.fieldName" v-validate-number="length" type="text" placeholder="请输入数字" />
 *
 */
export const validateNumber = {
  bind(el, binding) {
    // 从绑定值中获取最大长度，默认为8
    let maxLength = binding.value || 8;
    // 监听输入事件
    el.addEventListener('input', function (event) {
      // 获取当前输入值
      let value = event.target.value;
      // 使用正则表达式只保留数字字符
      const numericValue = value.replace(/[^\d]/g, '');
      // 如果输入值被更改（即存在非法字符），或者长度超过了指定的最大长度
      if (value !== numericValue || numericValue.length > maxLength) {
        // 截取到最大长度
        event.target.value = numericValue.slice(0, maxLength);
        // 手动触发 input 事件以更新 v-model（Vue通常会自动处理，但这里为了确保）
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });

    // 监听粘贴事件，确保粘贴的内容也是数字且不超过最大长度
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text/plain'); // 注意这里使用 'text/plain' 以确保兼容性
      // 只保留数字字符并截取到最大长度
      const numericPaste = pastedText.replace(/[^\d]/g, '').slice(0, maxLength);

      // 如果粘贴的内容与原始内容不同（包含非法字符或超过长度），则阻止粘贴并更新输入框值
      if (pastedText !== numericPaste) {
        event.preventDefault();
        event.target.value = numericPaste;
        // 手动触发 input 事件以更新 v-model
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });
  },
};

/**
 * 用于限制输入框只能输入数字跟小数点,同时增加自定义位数限制。
 * 当用户输入时，会自动过滤掉所有非数字字符，仅保留纯数字跟小数点输入。
 *
 * 使用方法：v-validate-number-point
 * 在Vue组件的模板中，将此指令添加到需要限制数字输入的<input>元素上。
 * 示例：<input v-model="form.fieldName" v-validate-number-point="length" type="text" placeholder="请输入数字" />
 *
 */
export const validateNumberPoint = {
  bind(el, binding) {
    // 从绑定值中获取最大长度，默认为8
    let maxLength = binding.value || 8;
    // 监听输入事件
    el.addEventListener('input', function (event) {
      // 获取当前输入值
      let value = event.target.value;
      // 1. 过滤非数字和小数点
      let numericValue = value.replace(/[^\d.]/g, '');
      // 2. 处理小数点逻辑
      const parts = numericValue.split('.');
      if (parts.length > 2) {
        numericValue = parts[0] + '.' + parts.slice(1).join('');
      }
      // 3. 自动补全前导0（可选）
      if (numericValue.startsWith('.')) {
        numericValue = '0';
      }
      // 4. 禁止以小数点结尾（可选）
      // if (numericValue.endsWith('.')) {
      //   numericValue = numericValue.slice(0, -1);
      // }
      // 5. 以0开头的
      if (/^0/.test(numericValue) && numericValue !== '0') {
        numericValue = numericValue.slice(1);
      }
      // 如果输入值被更改（即存在非法字符），或者长度超过了指定的最大长度
      if (value !== numericValue) {
        event.target.value = numericValue;
        // 手动触发 input 事件以更新 v-model（Vue通常会自动处理，但这里为了确保）
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
      if (numericValue.length > maxLength) {
        // 截取到最大长度
        event.target.value = numericValue.slice(0, maxLength);
        // 手动触发 input 事件以更新 v-model（Vue通常会自动处理，但这里为了确保）
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });

    // 监听粘贴事件，确保粘贴的内容也是数字且不超过最大长度
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text/plain'); // 注意这里使用 'text/plain' 以确保兼容性
      // 只保留数字字符并截取到最大长度
      const numericPaste = pastedText.replace(/[^\d.]/g, '').slice(0, maxLength);

      // 如果粘贴的内容与原始内容不同（包含非法字符或超过长度），则阻止粘贴并更新输入框值
      if (pastedText !== numericPaste) {
        event.preventDefault();
        event.target.value = numericPaste;
        // 手动触发 input 事件以更新 v-model
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });
  },
};

/**
 * 绑定 input 事件监听器，用于校验输入框的手机号码格式。
 * 使用方法：v-validateHKTPhone
 * @param {HTMLElement} el - 需要绑定事件监听器的 DOM 元素。
 */
// 正常长度8位，IDAP那些有加前缀1946 +8位，都为数字
export const validateHKTPhone = {
  bind(el) {
    // 号码校验正则表达式
    const regex = /^\d{1,12}$/;

    // 校验函数
    const validateInput = value => {
      if (!regex.test(value)) {
        message.error(i18n.t('common.phoneErrorTip'));
        return false; // 校验失败
      }
      return true; // 校验通过
    };

    // 输入框失焦后校验输入的联系号码内容
    el.addEventListener('blur', function (event) {
      const value = event.target.value;
      if (!value) return;
      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      }
    });

    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框
        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
        // 避免重复提示
        return;
      }

      // 如果粘贴内容合法，手动触发 input 事件以更新 v-model
      const evt = new Event('input', { bubbles: true });
      el.dispatchEvent(evt);
    });
  },
};
/**
 * 绑定 input 事件监听器，用于校验输入框的邮箱格式。
 * 使用方法：v-validateEmail
 * @param {HTMLElement} el - 需要绑定事件监听器的 DOM 元素。
 */
export const validateEmail = {
  bind(el) {
    // 邮箱校验正则表达式
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/;

    // 校验函数
    const validateInput = value => {
      // 去除前后及中间的所有空格
      const trimmedValue = value.replace(/\s+/g, '');

      // 校验是否有且仅有一个 '@' 符号
      const atIndex = trimmedValue.indexOf('@');
      if (atIndex === -1 || trimmedValue.indexOf('@', atIndex + 1) !== -1) {
        message.error(i18n.t('common.emailErrorTip'));
        return false; // 没有 '@' 或者有多个 '@'
      }
      // 校验总长度是否超过50个字符
      if (trimmedValue.length > 50) {
        message.error(i18n.t('common.inputTooLongError')); // 自定义错误消息
        return false;
      }

      if (!regex.test(value)) {
        message.error(i18n.t('common.emailErrorTip'));
        return false; // 校验失败
      }
      return true; // 校验通过
    };

    // 输入框失焦后校验输入的邮箱内容
    el.addEventListener('blur', function (event) {
      const value = event.target.value;
      if (!value) return;
      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      }
    });

    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框
        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
        // 避免重复提示
        return;
      }

      // 如果粘贴内容合法，手动触发 input 事件以更新 v-model
      const evt = new Event('input', { bubbles: true });
      el.dispatchEvent(evt);
    });
  },
};

export const validateEmailMore = {
  bind(el) {
    // 邮箱校验正则表达式
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*\.[a-zA-Z]{2,}$/;

    // 校验函数：支持多个邮箱地址
    const validateInput = value => {
      // 去除前后及中间的所有空格
      const trimmedValue = value.replace(/\s+/g, '');

      // 如果没有输入内容，则直接返回 true
      if (!trimmedValue) return true;

      // 将输入的字符串按逗号分割成多个邮箱地址
      const emailList = trimmedValue.split(',');

      // 校验每个邮箱地址
      for (const email of emailList) {
        if (!email) continue; // 跳过空值

        // 校验是否有且仅有一个 '@' 符号
        const atIndex = email.indexOf('@');
        if (atIndex === -1 || email.indexOf('@', atIndex + 1) !== -1) {
          message.error(i18n.t('common.emailErrorTip'));
          return false; // 没有 '@' 或者有多个 '@'
        }

        // 校验单个邮箱长度是否超过50个字符
        if (email.length > 50) {
          message.error(i18n.t('common.inputTooLongError')); // 自定义错误消息
          return false;
        }

        // 使用正则表达式校验邮箱格式
        if (!regex.test(email)) {
          message.error(i18n.t('common.emailErrorTip'));
          return false; // 校验失败
        }
      }

      return true; // 所有邮箱都校验通过
    };

    // 输入框失焦后校验输入的邮箱内容
    el.addEventListener('blur', function (event) {
      const value = event.target.value;
      if (!value) return;

      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      }
    });

    // 粘贴时校验内容
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      } else {
        // 如果粘贴内容合法，手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      }
    });
  },
};

/**
 * 绑定 input 事件监听器，用于校验选址格式。
 * 除空格，逗号，单引号，数字，中英文，减号外其他不可输入
 * 使用方法：v-validateAddress
 */
// 地址通常包含数字（门牌号、邮编）
// 英文字母（道路名称、单位名称）
// 空格（分隔各部分）
// 特殊符号（如"-"、"/"、"#"等

export const validateAddress = {
  bind(el) {
    // 校验非法字符的正则表达式
    const regex =
      /(\b(truncate|alter|grant|select|update|and|or|delete|insert|trancate|where|char|chr|into|exists|union|all|substr|ascii|declare|exec|count|sum|master|drop|execute|waitfor)\b|(\*|;|@|\uFF01|\uFF1F|!|[?]|%|<|>|\\|\||\^|`|"|\{|\}|\[|\]|\(|\)|\$|&|=|\+))/gi;
    // 校验函数
    const validateInput = value => {
      if (regex.test(value)) {
        message.error(i18n.t('common.inputContainsIllegalCharactersError'));
        return false; // 校验失败
      }
      return true; // 校验通过
    };

    // 监听输入事件
    el.addEventListener('input', function (event) {
      const value = event.target.value;
      if (!validateInput(value)) {
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);
      }
    });

    // 监听粘贴事件
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text');

      if (!validateInput(pastedText)) {
        event.preventDefault(); // 阻止粘贴
        event.target.value = ''; // 清空输入框

        // 手动触发 input 事件以更新 v-model
        const evt = new Event('input', { bubbles: true });
        el.dispatchEvent(evt);

        // 避免重复提示
        return;
      }

      // 如果粘贴内容合法，手动触发 input 事件以更新 v-model
      const evt = new Event('input', { bubbles: true });
      el.dispatchEvent(evt);
    });
  },
};

// 使用方法：v-validateNumberAndPoint
// 校验只填小数点和数字
export const validateNumberAndPoint = {
  bind(el, binding) {
    // 从绑定值中获取最大长度，默认为8
    let maxLength = binding.value || 8;

    // 监听输入事件
    el.addEventListener('input', function (event) {
      // 获取当前输入值
      let value = event.target.value;

      // 使用正则表达式只保留数字字符和小数点
      let numericValue = value.replace(/[^\d.]/g, '');

      // 检查是否有多余的小数点
      const dotCount = (numericValue.match(/\./g) || []).length;
      if (dotCount > 1) {
        // 如果有多余的小数点，移除多余的
        numericValue = numericValue.replace(/\.(?=.*\.)/g, '');
      }

      // 如果小数点之前没有数字，自动补零
      if (numericValue.startsWith('.')) {
        numericValue = '0' + numericValue;
      }

      // 如果第一个字符是0，且第二个字符也是0，则清空输入框
      if (numericValue.startsWith('00')) {
        numericValue = '';
      } else if (
        numericValue.startsWith('0') &&
        numericValue.length > 1 &&
        !numericValue.startsWith('0.')
      ) {
        // 如果第一个字符是0，且后面有其他数字，则去除前导零，但保留0.
        numericValue = numericValue.replace(/^0+/, '');
      }

      // 限制小数部分最多两位
      if (numericValue.includes('.')) {
        const [integerPart, decimalPart] = numericValue.split('.');
        if (decimalPart && decimalPart.length > 2) {
          numericValue = `${integerPart}.${decimalPart.slice(0, 2)}`;
        }
      }

      // 如果输入值被更改（即存在非法字符），或者长度超过了指定的最大长度
      if (value !== numericValue || numericValue.length > maxLength) {
        // 截取到最大长度
        event.target.value = numericValue.slice(0, maxLength);
        // 手动触发 input 事件以更新 v-model
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });

    // 监听粘贴事件，确保粘贴的内容也是数字且不超过最大长度
    el.addEventListener('paste', function (event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedText = clipboardData.getData('text/plain'); // 注意这里使用 'text/plain' 以确保兼容性

      // 只保留数字字符和小数点并截取到最大长度
      let numericPaste = pastedText.replace(/[^\d.]/g, '');
      let dotCount = (numericPaste.match(/\./g) || []).length;
      if (dotCount > 1) {
        // 如果有多余的小数点，移除多余的
        numericPaste = numericPaste.replace(/\.(?=.*\.)/g, '');
      }

      // 如果小数点之前没有数字，自动补零
      if (numericPaste.startsWith('.')) {
        numericPaste = '0' + numericPaste;
      }

      // 如果第一个字符是0，且第二个字符也是0，则清空输入框
      if (numericPaste.startsWith('00')) {
        numericPaste = '';
      } else if (
        numericPaste.startsWith('0') &&
        numericPaste.length > 1 &&
        !numericPaste.startsWith('0.')
      ) {
        // 如果第一个字符是0，且后面有其他数字，则去除前导零，但保留0.
        numericPaste = numericPaste.replace(/^0+/, '');
      }

      // 限制小数部分最多两位
      if (numericPaste.includes('.')) {
        const [integerPart, decimalPart] = numericPaste.split('.');
        if (decimalPart && decimalPart.length > 2) {
          numericPaste = `${integerPart}.${decimalPart.slice(0, 2)}`;
        }
      }

      // 如果粘贴的内容与原始内容不同（包含非法字符或超过长度），则阻止粘贴并更新输入框值
      if (pastedText !== numericPaste) {
        event.preventDefault();
        event.target.value = numericPaste.slice(0, maxLength);
        // 手动触发 input 事件以更新 v-model
        const inputEvent = new Event('input', { bubbles: true });
        el.dispatchEvent(inputEvent);
      }
    });
  },
};

// 使用方法：v-buttonPermission=""
export const buttonPermission = {
  bind(el, binding) {
    const btnPermission = binding.value;
    // 获取缓存中的userInfoAll，通过 FUNC_RIGHT 看是否展示按钮
    const userInfoAll = JSON.parse(tool.local.get('userInfoAll') || '{}');
    const { FUNC_RIGHT } = userInfoAll;
    if (!FUNC_RIGHT) {
      el.style.display = 'none';
      return;
    }

    const hasPermission = Object.keys(FUNC_RIGHT).includes(btnPermission);

    if (!hasPermission) {
      el.style.display = 'none';
    }
    if (typeof btnPermission !== 'string') {
      throw new Error('value must be a string!');
    }
  },
};

// 防抖(点击事件) -  自定义命令
// 使用方法：v-debounce:【time时间(300) or none】，例：v-debounce:500
// 实现原因：解决场景 - debounce调用时需要入参但无法传入方法里导致undefined的情况
// 传入参数： fn: 想执行的参数, args: [想传入的值]
export const debounceClick = {
  inserted(el, binding) {
    const delay = binding.arg ? parseInt(binding.arg) : 300;
    const { fn, args = [] } = binding.value;
    el.__debounceHandler__ = debounce(() => {
      fn && fn(...args);
    }, delay);
    el.addEventListener('click', el.__debounceHandler__);
  },
  unbind(el) {
    el.removeEventListener('click', el.__debounceHandler__);
    delete el.__debounceHandler__;
  },
};
