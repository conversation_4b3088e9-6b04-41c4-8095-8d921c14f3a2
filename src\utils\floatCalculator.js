/**
 * 高精度计算工具库 - 解决JavaScript浮点数精度问题
 */
export const FloatCalculator = {
  /**
   * 获取数字的精度（小数点后的位数）
   */
  getPrecision(num) {
    const str = num.toString();
    if (str.includes('e-')) {
      return parseInt(str.split('e-')[1], 10);
    }
    return str.includes('.') ? str.split('.')[1].length : 0;
  },

  /**
   * 获取两个数中最大精度
   */
  getMaxPrecision(a, b) {
    return Math.max(this.getPrecision(a), this.getPrecision(b));
  },

  /**
   * 加法运算
   */
  add(a, b) {
    const p = this.getMaxPrecision(a, b);
    const factor = Math.pow(10, p);
    return (a * factor + b * factor) / factor;
  },

  /**
   * 减法运算
   */
  subtract(a, b) {
    return this.add(a, -b);
  },

  /**
   * 乘法运算
   */
  multiply(a, b) {
    const p1 = this.getPrecision(a);
    const p2 = this.getPrecision(b);
    const factor = Math.pow(10, p1 + p2);
    return (a * Math.pow(10, p1) * b * Math.pow(10, p2)) / factor;
  },

  /**
   * 除法运算
   */
  divide(a, b) {
    if (b === 0) throw new Error('除数不能为零');
    const p1 = this.getPrecision(a);
    const p2 = this.getPrecision(b);
    const factor = Math.pow(10, Math.max(p1, p2));
    return (a * factor) / (b * factor);
  },

  /**
   * 四舍五入
   */
  round(num, decimalPlaces) {
    const factor = Math.pow(10, decimalPlaces);
    return this.divide(Math.round(this.multiply(num, factor)), factor);
  },

  /**
   * 比较两个浮点数是否相等（考虑精度误差）
   */
  isEqual(a, b, epsilon = Number.EPSILON) {
    return Math.abs(a - b) < epsilon;
  },
};

// 使用示例
// console.log(FloatCalculator.add(0.1, 0.2)); // 0.3
// console.log(FloatCalculator.subtract(0.3, 0.1)); // 0.2
// console.log(FloatCalculator.multiply(0.2, 0.3)); // 0.06
// console.log(FloatCalculator.divide(0.3, 0.1)); // 3
// console.log(FloatCalculator.round(0.1234, 2)); // 0.12
// console.log(FloatCalculator.isEqual(0.1 + 0.2, 0.3)); // true
