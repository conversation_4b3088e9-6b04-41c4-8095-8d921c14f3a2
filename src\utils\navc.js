window.inFrame = (function inFrame() {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
})();

window.nav = function (path, query) {
  window.parent.postMessage(
    {
      type: 'nav',
      path,
      query,
    },
    `${window.location.origin}/`,
  );
};

window.back = function () {
  window.parent.postMessage(
    {
      type: 'customBack',
    },
    `${window.location.origin}/`,
  );
};
