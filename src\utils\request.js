import axios from 'axios';
import { message } from 'ant-design-vue';
import tool from '@/utils/tool';
import _this from '@/main';
import i18n from '@/i18n/index';

const baseURL = {
  account: process.env.VUE_APP_ACCOUNT_BASE_API + 'cim',
  customer: process.env.VUE_APP_BASE_API + 'cim',
  user: process.env.VUE_APP_USER_BASE_API + 'cim',
  order: process.env.VUE_APP_ORDER_BASE_API + 'order',
  product: process.env.VUE_APP_PRODUCT_BASE_API + 'prods',
  trades: process.env.VUE_APP_TRADES_BASE_API + 'trades',
  auths: process.env.VUE_APP_AUTHS_BASE_API + 'auths',
  osca: process.env.VUE_APP_BASE_API + 'osca',
};
// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 30000,
  headers: {
    'Accept-Language': tool.local.get('accept-language'),
    'Staff-Id': tool.local.get('userInfo') && JSON.parse(tool.local.get('userInfo')).STAFF_ID,
    'Random-Num': tool.local.get('randomnum') && JSON.parse(tool.local.get('randomnum')),
    'loginStaffInfo':
      '%7B%22domainUserId%22%3A%20%22xasi0098%22%2C%22username%22%3A%20%22%E7%AB%A5%E8%91%A3%22%2C%22departId%22%3A%20%221%22%7D',
  },
});
// request拦截器
service.interceptors.request.use(
  config => {
    config.headers = {
      ...config.headers,
    };

    // needREQ字段判断有些接口不需要包裹一层REQ对象
    if (config.needREQ && config.method === 'post') {
      config.data = {
        REQ: config.data,
        reqHead: JSON.parse(tool.local.get('userInfo') || '{}'),
      };
    }
    return config;
  },
  error => {
    console.log(error);
  },
);

// 响应拦截器
service.interceptors.response.use(
  res => {
    if (res.status == '200' || res.status == '201' || res.status == '204') {
      if (res.data.code && res.data.code == '401') {
        window.parent.postMessage('loginExpired', `${window.location.origin}/`);
        return Promise.reject(res.data);
      }
      if (res.config.url === '/order/receive/orderExport/exportOrderData') {
        return res;
      }
      if (res.config.url.indexOf('download') > -1 || res.config.url.indexOf('import') > -1) {
        return res.data;
      }
      if (res.data.STATUS?.startsWith('9')) {
        message.error(res.data.MSG);
        return Promise.reject(res.data);
      }
      if (res.data.STATUS?.startsWith('0')) {
        return res.data.RSP;
      }
    } else {
      message.error(i18n.t('common.netWorkError'));
      return res.data;
    }
  },
  error => {
    // return Promise.reject(error)
    if (error.message.includes('500')) {
      message.error(i18n.t('common.serverError'));
      return Promise.reject(error);
    }
    if (error.message.includes('timeout')) {
      let res = {
        code: 'timeout',
        msg: i18n.t('common.requestTimeout'),
      };
      return res;
    } else if (error.message.includes('403')) {
      // message.error('当前页面没有操作权限');
      return error.response.data;
      // return Promise.reject(error);
    } else if (error.message.includes('404')) {
      message.error(i18n.t('common.requestFailed'));
      return Promise.reject(error);
    }
    //400、500状态码不进行拦截，由调用方法决定处理结果
    return error?.response?.data;
  },
);

// 专门用于下载流文件的函数
export function downloadFile(module = 'customer', url, data = {}, filename = 'file') {
  return new Promise((resolve, reject) => {
    service({
      method: 'post',
      url,
      data,
      module,
      responseType: 'blob', // 关键：指定响应类型为 blob
    })
      .then(response => {
        // 创建 Blob 对象
        console.log(response);
        //如果返回是application/json类型则是接口报错 返回提示语 终止下载
        if (response.type == 'application/json')
          return _this.$message.error(_this.$t('accountManager.downloadingFailed'));
        const blob = new Blob([response], { type: response.type });

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.setAttribute('download', filename); // 设置下载文件名
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        resolve(response);
      })
      .catch(error => {
        reject(error);
      });
  });
}
/**
 * 获取对应模块的完整接口地址
 * @param {module} module 对应的模块
 * @param {url} url 后缀地址
 */
export function getApiPrefix(module = 'customer', url) {
  return `${baseURL[module]}${url}`;
}
export default service;
