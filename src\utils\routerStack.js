export default function (router) {
  let historyStack = []; // 用于保存历史路径

  // 清理历史栈中的重复路径
  function cleanHistoryStack(stack, currentPath) {
    const reversedStack = [...stack].reverse();
    // 通过path属性去重，保留最后一个对象
    const uniquePathMap = new Map();
    reversedStack.forEach(item => {
      if (item.path !== currentPath) {
        uniquePathMap.set(item.path, item);
      }
    });
    const cleanedStack = Array.from(uniquePathMap.values()).reverse();
    return cleanedStack;
  }

  // 全局前置守卫：在每次路由跳转之前保存当前路径
  router.beforeEach((to, from, next) => {
    if (from.path && to.path !== from.path) {
      historyStack.push(from);
      historyStack = cleanHistoryStack(historyStack, to.path); // 清理重复路径
    }
    // console.log('historyStack', historyStack);
    next();
  });

  // 自定义 back 方法
  router.customBack = function () {
    if (historyStack.length > 1) {
      console.log('内部push代替back', historyStack);
      const _route = historyStack.pop(); // 获取并移除最后一个保存的路径
      router.push({
        path: _route.path,
        query: _route.query,
        params: _route.params,
      }); // 使用原始的 push 方法进行跳转
      historyStack.pop();
    } else {
      console.log('外部back', router.currentRoute.fullPath);
      // window.back();
      window.history.back();
    }
  };
}
