const tool = {
  pageSize: 20,
  /** localStorage */
  local: {
    set(key, data) {
      return localStorage.setItem(key, data);
    },
    get(key) {
      const data = localStorage.getItem(key) || '';
      return data;
    },
    remove(key) {
      return localStorage.removeItem(key);
    },
    clear() {
      return localStorage.clear();
    },
  },

  /** 数组对象去重 */
  concatArray: function (array, flag, _flag) {
    return array.reduce((arrList, current) => {
      const fountIndex = arrList.findIndex(
        item => item[flag] === current[flag] && item[_flag] === current[_flag],
      );
      if (fountIndex === -1) {
        arrList.push(current);
      }
      return arrList;
    }, []);
  },

  /** 获取标准年月日 */
  getLastDate: function (time) {
    const date = new Date(time);
    const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    return date.getFullYear() + '-' + month + '-' + day;
  },

  /**
   * @des: 判断对象是否有值
   * @param {*} obj 传入对象
   * @returns 返回布尔值
   */
  hasAnyValue: function (obj) {
    return Object.values(obj).some(value => {
      if (Array.isArray(value)) {
        return value.length != 0;
      }
      return value !== null && value !== undefined && value !== '';
    });
  },

  /**
   * 去除对象属性值为空或undefined的属性
   */
  removeEmptyProperties: function (obj) {
    Object.keys(obj).forEach(item => {
      // 字段是数组
      if (Array.isArray(obj[item])) {
        if (obj[item].length === 0) {
          delete obj[item];
        }
      } else {
        // 字段是字符串或数字
        if (obj[item] === '' || obj[item] === undefined) {
          delete obj[item];
        }
      }
    });
    return obj;
  },

  /**
   * @des: 比较两个对象的差异
   * @param {Object} oldObj 旧对象
   * @param {Object} newObj 新对象
   * @returns {Object} 返回属性值有变化的对象
   */
  getChangedProperties: function (oldObj, newObj) {
    const changed = {};
    Object.keys(newObj).forEach(key => {
      if (newObj[key] !== oldObj[key]) {
        changed[key] = newObj[key];
      }
    });
    return changed;
  },
};

export default tool;
