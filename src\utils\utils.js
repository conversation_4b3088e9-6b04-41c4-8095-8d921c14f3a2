import { lobQuery, productFamilyQuery, qryCurrentTime } from '@/api/common';
import that from '@/main.js';
import html2canvas from 'html2canvas';
import moment from 'moment';
import printJS from 'print-js';
import { v4 as uuidv4 } from 'uuid';
/**
 * 生成列表的函数
 * @param {Array} data - 数据数组
 * @param {string} childrenName - 子节点名称
 */
let dataList = [];
function generateListfn(data, childrenName) {
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    dataList.push(node);
    if (node[childrenName]) {
      generateListfn(node[childrenName], childrenName);
    }
  }
}
export function generateList(data, childrenName = 'children') {
  dataList = [];
  generateListfn(data, childrenName);
  return dataList;
}

/**
 * @des: 根据list生成tree
 * @param {*} data  原始数据
 * @param {*} key 树的key对应的id
 * @param {*} parentKey 树的key对应的父id
 * @param {*} title 树的title
 * @return {List}   树形结构
 */
export function generateTree(
  data,
  // key = 'CATL_ID',
  parentKey = 'SUPR_CATL_ID',
  // title = 'CATL_NM',
  // checked = 'IS_EXISTS',
) {
  const tree = [];
  const map = {};
  if (!hasAnyValue(data)) return [];
  data.forEach(item => {
    delete item.children;
    map[item.key] = item;
  });
  data.forEach(item => {
    const parent = map[item[parentKey]];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      tree.push(item);
    }
  });
  return tree;
}

// 防抖函数
export function debounce(fn, delay) {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fn && fn();
    }, delay);
  };
}
/**
 * @des: 判断对象是否有值
 * @param {*} obj 传入对象
 * @returns 返回布尔值
 */
export function hasAnyValue(obj) {
  return Object.values(obj).some(value => value !== null && value !== undefined && value !== '');
}

/*
    表格单个多选
    selectedRows 选中的列表
    key 唯一标识字段
    record 当前项
    selected 选中状态true false
 */
export const tableOnRowSelect = function (selectedRows, key, record, selected) {
  let list = [...selectedRows];
  if (!selected) {
    let index = list.findIndex(i => i[key] === record[key]);
    if (index !== -1) list.splice(index, 1);
  } else {
    list.push(record);
  }
  return list;
};

/*
    表格全选
    selectedRows 选中的列表
    key 唯一标识字段
    record 当前项
    selected 选中状态true false
 */
export const tableOnRowSelectAll = function (selectedRows, key, selected, changeRows) {
  let list = [...selectedRows];
  if (!selected) {
    changeRows.forEach(item => {
      let index = list.findIndex(i => i[key] === item[key]);
      if (index !== -1) list.splice(index, 1);
    });
  } else {
    list = list.concat(changeRows);
  }
  return list;
};

/**
 * 该方法挂载在全局
 * 重置表单布局，避免切换国际化标签显示出现问题，标签长度对齐(统一为最长标签的长度)
 */
export const resetFormLayout = function (idName) {
  if (!idName) return;
  const labels = document.querySelectorAll(`#${idName} .ant-form-item label`);
  const maxWidth = Math.max(...Array.from(labels).map(l => l.offsetWidth));
  labels.forEach(label => (label.style.width = `${maxWidth}px`));
};

/** 数组对象去重 */
export const concatArray = function (array, flag) {
  return array.reduce((arrList, current) => {
    const fountIndex = arrList.findIndex(item => item[flag] === current[flag]);
    if (fountIndex === -1) {
      arrList.push(current);
    }
    return arrList;
  }, []);
};

// 香港手机号格式校验
// ^表示开头
// （5|6|8|9）表示首位数字必须是 5、6、8或9 中的一位
// \d{7}表示后面跟着7个数字
// $表示结尾
// 因此，符合条件的香港手机号码必须是首位为5、6、8 或9，后面跟着7个数字，共计8位数字。
export const validateHKTPhone = function (email) {
  var regex = /^(5|6|8|9)\d{7}$/;
  return regex.test(email);
};

// 邮箱格式校验
export const validateEmail = function (email) {
  var regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
  return regex.test(email);
};

/*
	files 文件夹路径，引入文件夹下的全部vue组件
	isBig 设置组件名称是否首字母大写
*/
export const getFolderComponents = function (files, isBig) {
  const path = require('path');
  const modules = {};
  files.keys().forEach(key => {
    let name = path.basename(key, '.vue');
    if (isBig) {
      name = name.charAt(0).toUpperCase() + name.slice(1);
    }
    modules[name] = files(key).default || files(key);
  });
  return modules;
};

// LOB 枚举查询
export const getLobList = function () {
  const params = {};
  return lobQuery(params)
    .then(res => {
      return res.DATA || [];
    })
    .catch(() => {});
};

// Product Family枚举
export const getProductFamilyList = function (obj) {
  const params = {
    LOB: obj.LOB,
  };
  return productFamilyQuery(params)
    .then(res => {
      return res.DATA || [];
    })
    .catch(() => {});
};

// 校验是否为n位数的数字
export const validateLengthNumber = function (length, value) {
  // 生成正则表达式，校验是否为 n 位数的数字
  const regex = new RegExp(`^\\d{${length}}$`);
  return regex.test(value);
};

// 判断树结构下某项的子数据是否符合特定条件
export const hasTreeChildCondition = function (node, childrenKey, conditionFn) {
  if (node[childrenKey] && node[childrenKey].length > 0) {
    for (const child of node[childrenKey]) {
      if (conditionFn(child)) {
        return true;
      }
      if (hasTreeChildCondition(child, conditionFn)) {
        return true;
      }
    }
  }
  return false;
};

/**
 * 校验当前层级及其子层级的勾选数量是否符合 MIN_NUMBER 和 MAX_NUMBER 的限制
 * @param {Object} node 当前节点
 * @returns {boolean} 是否校验通过
 */
function validateSelection(node) {
  // 如果当前节点没有 children，直接返回 true
  // if (!node.children || node.children.length === 0) {
  //   return true;
  // }
  if (!node.children) {
    node.children = [];
  }

  // 获取 MIN_NUMBER 和 MAX_NUMBER
  const min = parseInt(node.MIN_NUMBER, 10);
  const max = parseInt(node.MAX_NUMBER, 10);

  // 如果 MIN_NUMBER 或 MAX_NUMBER 为 -1，表示没有限制
  const hasLimit = min !== -1 || max !== -1;

  if (hasLimit) {
    // 计算当前层级已勾选的项数
    const selectedCount = node.children.filter(child => child.checked).length;

    // 校验选择的项数是否符合 MIN_NUMBER 和 MAX_NUMBER 的限制
    if (min !== -1 && selectedCount < min) {
      throw that.$t('customerVerify.cannotLessThan', {
        name: node.NAME,
        num: min,
        selectedCount: selectedCount,
      });
      // `${node.NAME} 至少需要选择 ${min} 项，当前选择了 ${selectedCount} 项。`;
    }
    if (max !== -1 && selectedCount > max) {
      throw that.$t('customerVerify.cannotMoreThan', {
        name: node.NAME,
        num: max,
        selectedCount: selectedCount,
      });
      // `${node.NAME} 最多只能选择 ${max} 项，当前选择了 ${selectedCount} 项。`;
    }
  }

  // 递归校验每个子节点
  for (const child of node.children) {
    validateSelection(child);
  }
}

/**
 * 校验整个 productData 数组
 * @param {Array} productData 产品数据数组
 * @returns {boolean} 是否所有数据都校验通过
 */
export const validateProductData = function (productData) {
  try {
    // 遍历每一条数据
    for (const product of productData) {
      validateSelection(product);
    }
  } catch (err) {
    return err;
  }
};

/**
 * 判断不同包下是否选中同一元素
 * @param {Array} selectedList 原始数据
 * @returns NULL | String 返回错误元素名称或者NULL
 */
export const checkDuplicateOrder = selectedList => {
  const elementMap = new Map();
  // 递归检查所有子项
  const checkChildren = items => {
    for (const item of items) {
      if (item.checked) {
        // 检查元素级别
        if (['Vas', 'Pricing'].includes(item.type)) {
          const elementKey = item.DISCNT_CODE || item.SERVICE_ID;
          if (elementKey) {
            if (elementMap.has(elementKey)) {
              // 找到重复项
              return item.NAME;
            }
            elementMap.set(elementKey, item);
          }
        }

        // 递归检查子项
        if (item.children && item.children.length > 0) {
          const result = checkChildren(item.children);
          if (result) return result;
        }
      }
    }
    return null;
  };

  return checkChildren(selectedList);
};

/**
 * 生成一个下单EXT_ORDER_ID(16位，纯数字，随机数3+时间戳13)
 * @returns {Number} 返回一个随机
 */
// 生成一个下单EXT_ORDER_ID(16位，纯数字，随机数3+时间戳13)
export const getUniqueOrderID = function () {
  // const currentTimestamp = String(Date.now());
  // const randomNum = String(Math.floor(Math.random() * 1000));
  // const EXT_ORDER_ID = Number(currentTimestamp + randomNum);
  // return EXT_ORDER_ID;
  const digits = '0123456789';
  let uuid = '';
  for (let i = 0; i < 16; i++) {
    uuid += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  return uuid;
};

// 格式化函数
export const getFormatTime = date => {
  const year = date.getUTCFullYear(); // 获取年份
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
  const day = String(date.getUTCDate()).padStart(2, '0'); // 获取日期
  const hours = String(date.getUTCHours()).padStart(2, '0'); // 获取小时
  const minutes = String(date.getUTCMinutes()).padStart(2, '0'); // 获取分钟
  const seconds = String(date.getUTCSeconds()).padStart(2, '0'); // 获取秒

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 截止时间
 * @returns {String} 返回一个规定格式的截止时间
 */
export const getDeadlineTime = function () {
  return '2099-12-31 23:59:59';
};

// 通过传入的页面id生成页面快照并唤起打印预览
export const handlePrint = function (id, callback) {
  // 获取要转换成 canvas 的DOM元素
  const element = document.getElementById(id);
  // 先通过html2canvas 转换，然后通过 printJS 打印预览
  setTimeout(() => {
    // 调整截图清晰度
    const ratio = window.devicePixelRatio || 1;
    html2canvas(element, {
      scale: ratio,
      onclone: cb => {
        // 显示指定元素
        cb.getElementById(id).style.display = 'block';
      },
    })
      .then(canvas => {
        const url = canvas.toDataURL('image/jpeg', 1.0);
        // 唤起打印预览
        printJS({
          printable: url,
          type: 'image',
          style: `@media print{ @page { size: A4; margin: 0; mso-header: none; mso-footer: none; } body{margin:0px  } img {display: block; margin: 0px;}}`, // 解决多页打印时第一页空白问题
        });
      })
      .finally(() => {
        callback && callback();
      });
  }, 200);
};

// 对比两个数组，并且返回新增和删除的数据
export const findDifferences = function (oldTree, newTree) {
  function compareNodes(oldNode, newNode) {
    let added = null;
    let removed = null;

    // 如果新旧节点都有子节点，则进一步比较
    if (oldNode.children && newNode.children) {
      const oldChildrenMap = new Map(oldNode.children.map(child => [child.id, child]));
      const newChildrenMap = new Map(newNode.children.map(child => [child.id, child]));

      const allIds = new Set([...oldChildrenMap.keys(), ...newChildrenMap.keys()]);
      const addedChildren = [];
      const removedChildren = [];

      for (let id of allIds) {
        const oldChild = oldChildrenMap.get(id);
        const newChild = newChildrenMap.get(id);

        if (!oldChild && newChild) {
          // 新增节点
          addedChildren.push({ ...newChild });
        } else if (oldChild && !newChild) {
          // 移除节点
          removedChildren.push({ ...oldChild });
        } else if (oldChild && newChild) {
          // 存在对应节点，递归检查子节点
          const { added: childAdded, removed: childRemoved } = compareNodes(oldChild, newChild);
          if (childAdded || childRemoved) {
            if (childAdded) {
              addedChildren.push(childAdded);
            }
            if (childRemoved) {
              removedChildren.push(childRemoved);
            }
          }
        }
      }

      if (addedChildren.length > 0) {
        added = { ...newNode, children: addedChildren };
      }
      if (removedChildren.length > 0) {
        removed = { ...oldNode, children: removedChildren };
      }
    } else if (!isEqual(oldNode, newNode)) {
      // 如果没有子节点且节点不同，则直接返回新节点或旧节点（视情况而定）
      added = { ...newNode };
      removed = { ...oldNode };
    }

    return { added, removed };
  }

  function isEqual(node1, node2) {
    return JSON.stringify(node1) === JSON.stringify(node2);
  }

  const result = compareNodes(oldTree[0], newTree[0]);
  return {
    added: result.added ? [result.added] : [],
    removed: result.removed ? [result.removed] : [],
  };
};

// 产品树 根据元素名称查询过滤出数据
export const productTreeDatafilterName = function (data, targetName) {
  let list = data.filter(item => item.NAME.includes(targetName));
  if (list.length) {
    return list;
  }
  const result = []; // 用于存储所有匹配的结果
  const visited = new Set(); // 用于记录已经处理过的节点

  // 递归函数，用于遍历层级结构
  function traverse(node, path) {
    // 如果当前节点已经被处理过，直接返回
    if (visited.has(node)) {
      return;
    }

    // 标记当前节点为已处理
    visited.add(node);

    // 如果当前节点的 NAME 包含目标字符串（模糊匹配），将当前路径添加到结果中
    if (node.NAME.includes(targetName)) {
      result.push([...path, node]); // 将完整路径添加到结果中
    }

    // 如果有子节点，继续递归遍历
    if (node.children) {
      for (let child of node.children) {
        traverse(child, [...path, node]); // 将当前节点添加到路径中
      }
    }
  }

  // 遍历整个数据结构
  for (let item of data) {
    traverse(item, []); // 从根节点开始遍历
  }

  // 优化输出，只保留从根节点到目标节点的路径
  return result.map(path => {
    let current = { ...path[0] }; // 复制根节点
    let parent = current;

    for (let i = 1; i < path.length; i++) {
      parent.children = [{ ...path[i] }]; // 只保留匹配的子节点
      parent = parent.children[0];
    }

    return current;
  });
};

// 递归函数：将当前订购数据中新增的部分完整插入到已订购数据的对应层级中
export const mergeNewData = function (orderedData, currentData, serviceNo, instance) {
  // 遍历当前订购数据
  currentData.forEach(currentItem => {
    // 查找已订购数据中是否存在相同的 PRODUCT_ID
    const orderedProduct = orderedData.find(
      item => item.PRODUCT_ID == (currentItem.PRODUCT_ID || currentItem.ID),
    );

    if (orderedProduct) {
      // 创建一个数组存储所有重复的项用作提示
      const duplicateItems = [];
      // 如果存在相同的 PRODUCT_ID，继续对比 PACKAGE_ID
      currentItem.children.forEach(currentPackage => {
        const orderedPackage = orderedProduct.children.find(
          pkg => pkg.PACKAGE_ID === currentPackage.PACKAGE_ID,
        );

        if (orderedPackage) {
          // 如果存在相同的 PACKAGE_ID，继续对比 DISCNT_CODE 或 SERVICE_ID
          currentPackage.children.forEach(currentChild => {
            // 判断是否为新增数据
            if (currentChild.checked && !currentChild.initStatusBool) {
              // 判断当前项是 DISCNT_CODE 还是 SERVICE_ID
              if (currentChild.DISCNT_CODE) {
                // 查找是否已存在相同的 DISCNT_CODE
                const exists = orderedPackage.children.some(
                  child => child.DISCNT_CODE == currentChild.DISCNT_CODE,
                );

                if (!exists) {
                  // 如果不存在，将新增的 DISCNT_CODE 完整数据插入到已订购数据的对应层级中
                  orderedPackage.children.push({ ...currentChild });
                } else {
                  // 将重复项添加到数组中
                  duplicateItems.push({
                    number: serviceNo,
                    name: currentChild.DISCNT_NAME,
                  });
                }
              } else if (currentChild.SERVICE_ID) {
                // 查找是否已存在相同的 SERVICE_ID
                const exists = orderedPackage.children.some(
                  child => child.SERVICE_ID == currentChild.SERVICE_ID,
                );

                if (!exists) {
                  // 如果不存在，将新增的 SERVICE_ID 完整数据插入到已订购数据的对应层级中
                  orderedPackage.children.push({ ...currentChild });
                } else {
                  // 将重复项添加到数组中
                  duplicateItems.push({
                    number: serviceNo,
                    name: currentChild.SERVICE_NAME,
                  });
                }
              }
            }
          });
        } else {
          // 如果不存在相同的 PACKAGE_ID，将新增的 PACKAGE 完整数据插入到已订购数据的对应层级中
          const newPackage = {
            ...currentPackage,
            children: currentPackage.children.filter(
              child => child.checked && !child.initStatusBool,
            ),
          };

          if (newPackage.children.length > 0) {
            orderedProduct.children.push(newPackage);
          }
        }
      });
      // 在方法结束前检查是否有重复项，如果有则统一提示
      if (duplicateItems.length > 0) {
        const message = duplicateItems
          .map(item =>
            that.$t('common.repeatOrder', {
              number: item.number,
              name: item.name,
            }),
          )
          .join('\n');

        instance.showTipModal(message);
      }
    } else {
      // 如果不存在相同的 PRODUCT_ID，将新增的 PRODUCT 完整数据插入到已订购数据中
      const newProduct = {
        ...currentItem,
        isAdd: true,
        children: currentItem.children
          .map(pkg => ({
            ...pkg,
            children: pkg.children.filter(child => child.checked && !child.initStatusBool),
          }))
          .filter(pkg => pkg.children.length > 0), // 过滤掉没有新增 DISCNT_CODE 或 SERVICE_ID 的 PACKAGE
      };

      if (newProduct.children.length > 0) {
        orderedData.push(newProduct);
      }
    }
  });

  return orderedData;
};

/**
 * 把登录信息存入HKT联系人字段里面
 * @returns {Object} 返回一个HKT联系人对象
 */
export const getLoginInfoHKTContact = function (userInfo) {
  let loginInfoHKTContact = {
    PARTICIPANT_TYPE: '09',
    PARTICIPANT_ID: userInfo.STAFF_ID,
    SALES_CODE: userInfo.SALES_CODE,
    AGENT_CODE: '',
    ORDER_SALE_TYPE: '',
    PARTICIPANT_NAME: userInfo.STAFF_NAME,
    PARTICIPANT_LANDLINE_PHONE: userInfo.CONTACT_PHONE,
    PARTICIPANT_MOBILE: '',
    PARTICIPANT_EMAIL: userInfo.EMAIL,
    CONTACT_TYPE: '1', // 0：Customer   1:HKT    2:myHKT
  };
  return loginInfoHKTContact;
};

// 只能输入数字！0可以输入
// ，00,000，000001，这些无效
// 10.00 10.01 10.10 有效
// 最多只能小数点后两位
export const validateIsNumber = function (value) {
  var regex = /^(0|[1-9]\d*(\.\d{1,2})?)$/;
  return regex.test(value);
};

// 只能输入数字或字母
// 例如： 111、aaa、111aaa、aaa111 11.22（有小数点的数字）
export const validateAlphanumeric = function (value) {
  var regex =
    /^(?:[A-Za-z]+|\d+(?:\.\d+)?|[A-Za-z]+\d+|\d+[A-Za-z]+|[A-Za-z]+\d+[A-Za-z]+|\d+[A-Za-z]+\d+)$/;
  return regex.test(value);
};

/**
 * 深拷贝对象
 * @returns {Object} 返回一个新的对象
 */
export const deepClone = function (obj) {
  // 如果是 null 或非对象类型，直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item));
  }

  // 处理 Date 对象
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  // 处理 Map 对象
  if (obj instanceof Map) {
    const map = new Map();
    obj.forEach((value, key) => {
      map.set(key, deepClone(value));
    });
    return map;
  }

  // 处理 Set 对象
  if (obj instanceof Set) {
    const set = new Set();
    obj.forEach(value => {
      set.add(deepClone(value));
    });
    return set;
  }

  // 处理普通对象
  const clone = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key]);
    }
  }
  // const keys = Reflect.ownKeys(obj); // 包含 Symbol 属性和不可枚举属性
  // for (const key of keys) {
  //   clone[key] = deepClone(obj[key]);
  // }
  return clone;
};

// 将已订购的数据结构转换成含children字段的层级结构
export const transferOrderedChildrenList = function (list, packageName, discntName, serviceName) {
  return list.map(item => {
    return {
      ...item,
      children: item[packageName].map(iitem => {
        return {
          ...iitem,
          children: [...iitem[discntName], ...iitem[serviceName]].map(iiitem => {
            return {
              ...iiitem,
            };
          }),
        };
      }),
    };
  });
};

// 将合并的数组进行去重
export const mergeAndDeduplicate = function (combinedArray) {
  const result = [];

  combinedArray.forEach(product => {
    // 查找已存在的 PRODUCT_ID
    let existingProduct = result.find(p => p.PRODUCT_ID === product.PRODUCT_ID);

    if (!existingProduct) {
      // 如果不存在，直接添加
      result.push({ ...product });
      existingProduct = result[result.length - 1];
    }

    // 遍历 product 的 children（PACKAGE_ID 层）
    product.children.forEach(packageItem => {
      let existingPackage = existingProduct.children.find(
        p => p.PACKAGE_ID === packageItem.PACKAGE_ID,
      );

      if (!existingPackage) {
        // 如果不存在，直接添加到现有 product 的 children 中
        existingProduct.children.push({ ...packageItem });
        existingPackage = existingProduct.children[existingProduct.children.length - 1];
      }

      // 遍历 packageItem 的 children（DISCNT_CODE 或 SERVICE_ID 层）
      packageItem.children.forEach(child => {
        const key = child.DISCNT_CODE || child.SERVICE_ID;

        // 检查是否已存在相同的 DISCNT_CODE 或 SERVICE_ID
        const existingChild = existingPackage.children.find(
          c => (c.DISCNT_CODE || c.SERVICE_ID) === key,
        );

        if (!existingChild) {
          // 如果不存在，直接添加到现有 package 的 children 中
          existingPackage.children.push({ ...child });
        }
      });
    });
  });

  return result;
};

// 递归遍历并赋值
export const mergeProducts = function (ordered, selected) {
  ordered.forEach(orderedProduct => {
    // 找到对应的 selectedProduct
    const selectedProduct = selected.find(item => item.PRODUCT_ID == orderedProduct.PRODUCT_ID);

    if (selectedProduct) {
      // 遍历 children (PACKAGE)
      orderedProduct.children.forEach(orderedPackage => {
        const selectedPackage = selectedProduct.children.find(
          item => item.PACKAGE_ID == orderedPackage.PACKAGE_ID,
        );

        if (selectedPackage) {
          // 遍历 children (DISCNT_CODE)
          orderedPackage.children.forEach(orderedElement => {
            // 资费
            const selectedElement_fee = selectedPackage.children.find(
              item => item.DISCNT_CODE && item.DISCNT_CODE == orderedElement.DISCNT_CODE,
            );
            if (selectedElement_fee) {
              // 对比 attrList 和 interfaceElementList
              if (orderedElement.attrList && selectedElement_fee.interfaceElementList) {
                orderedElement.attrList.forEach(attrItem => {
                  // 找到 interfaceElementList 中 elementCode 和 CRM_ATTR_CODE 匹配的项
                  const matchedInterfaceItem = selectedElement_fee.interfaceElementList.find(
                    interfaceItem => interfaceItem.elementCode === attrItem.CRM_ATTR_CODE,
                  );

                  // 如果匹配成功，则赋值
                  if (matchedInterfaceItem) {
                    attrItem[attrItem.CRM_ATTR_CODE] =
                      matchedInterfaceItem[matchedInterfaceItem.elementCode];
                  }
                });
              }
            }

            // 服务
            const selectedElement_service = selectedPackage.children.find(
              item => item.SERVICE_ID && item.SERVICE_ID == orderedElement.SERVICE_ID,
            );
            if (selectedElement_service) {
              // 对比 attrList 和 interfaceElementList
              if (orderedElement.attrList && selectedElement_service.interfaceElementList) {
                orderedElement.attrList.forEach(attrItem => {
                  // 找到 interfaceElementList 中 elementCode 和 CRM_ATTR_CODE 匹配的项
                  const matchedInterfaceItem = selectedElement_service.interfaceElementList.find(
                    interfaceItem => interfaceItem.elementCode === attrItem.CRM_ATTR_CODE,
                  );

                  // 如果匹配成功，则赋值
                  if (matchedInterfaceItem) {
                    attrItem[attrItem.CRM_ATTR_CODE] =
                      matchedInterfaceItem[matchedInterfaceItem.elementCode];
                  }
                });
              }
            }
          });
        }
      });
    }
  });
  return ordered;
};

// 生成UUID
export const generateUUID = function () {
  return uuidv4();
};

// 深度对比两个对象或两个数组
export const deepEqual = (obj1, obj2) => {
  // 如果两个值严格相等，直接返回 true
  if (obj1 === obj2) return true;

  // 如果一个是数组，另一个不是，直接返回 false
  if (Array.isArray(obj1) !== Array.isArray(obj2)) return false;

  // 如果是对象或数组，递归比较
  if (obj1 && obj2 && typeof obj1 === 'object' && typeof obj2 === 'object') {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // 如果键的数量不一致，直接返回 false
    if (keys1.length !== keys2.length) return false;

    // 递归比较每个键值
    for (const key of keys1) {
      if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
        return false;
      }
    }

    return true;
  }

  // 如果以上都不满足，说明两个值不相等
  return false;
};

/**
 * 返回当前服务端时间
 * @returns {String} 返回一个格式后的时间
 */
export const getCurrentTime = async function () {
  try {
    const res = await qryCurrentTime();
    return res && res.DATA[0];
  } catch (error) {
    // 打印错误信息
    console.error('获取服务端时间时出错:', error);
    // 发生异常时返回 null
    return null;
  }
};
/**
 * @des: 通过ids找出所有依赖项
 * @param data  产品数据
 * @param ids 依赖项数组
 * @param type  类型，区分包还是元素
 * @return Array
 */
export const findItemsByIds = function (data, ids, type) {
  if (!Array.isArray(ids) || ids.length === 0 || type === undefined) return [];
  // 递归搜索函数
  function search(items, result) {
    for (let item of items) {
      // 检查 Package 类型
      if (item.type === 'Package' && ids.includes(item.PACKAGE_ID)) {
        result.push(item);
      }
      // 检查 element 类型
      if (type === 'element') {
        if (
          (item.type === 'Vas' && ids.includes(item.SERVICE_ID)) ||
          (item.type === 'Pricing' && ids.includes(item.DISCNT_CODE))
        ) {
          result.push(item);
        }
      }
      // 递归查找 children
      if (item.children && item.children.length > 0) {
        search(item.children, result);
      }
    }
  }

  let result = [];
  search(data, result);
  return result;
};

/**
 * @des: 更新产品树中的选中状态
 * @param productTreeList  产品数据
 * @param currentProductId 当前产品id
 * @param targetObj  修改的目标对象
 * @param checked 修改选中的状态值
 * @return Array
 */
export const updateProductTreeCheckedStatus = function (
  productTreeList,
  currentProductId,
  targetObj,
  checked,
) {
  return JSON.parse(JSON.stringify(productTreeList)).map(item => {
    if (item.ID === currentProductId) {
      item.children = item.children.map(packageItem => {
        let hasCheckedElement = false;
        packageItem.children = (packageItem.children || []).map(element => {
          if (element.key === targetObj.key) {
            element.checked = checked;
          }
          // 记录是否有选中的元素
          if (element.checked) {
            hasCheckedElement = true;
          } else if (checked && hasCheckedElement) {
            // 如果是选中操作且有子元素被选中，则将包也设置为选中
            packageItem.checked = true;
          }
          return element;
        });
        if (packageItem.PACKAGE_ID === targetObj.PACKAGE_ID) {
          packageItem.checked = checked;
        }
        return packageItem;
      });
      // 检查是否有选中的包
      const hasCheckedPackage = item.children.some(pkg => pkg.checked);
      // 如果有选中的包，则将产品也设置为选中
      if (hasCheckedPackage) {
        item.checked = true;
      }
    }
    return item;
  });
};

// 节流函数
export const throttle = (func, wait) => {
  // 初始化事件开始的时间为0
  let preTime = 0;
  return function () {
    // 下面两行不懂的可以看看防抖实现的那篇文章
    let context = this;
    let args = arguments;
    // 获取当前的时间，使用+来转化为数字类型，方便后面做减法
    let now = +new Date();
    // 当前时间减去之前的时间，结果大于设定的间隔时间才会执行函数
    if (now - preTime > wait) {
      func.apply(context, args);
      preTime = now;
    }
  };
};

export const qryBadPaymentIndicatorInfo = function (customerInfo) {
  return new Promise(resolve => {
    const { BAD_PAYMENT, TERMINATION, SUSPENDED } = customerInfo;
    let permission = (!TERMINATION && !SUSPENDED && BAD_PAYMENT) || (TERMINATION && SUSPENDED);
    console.log(BAD_PAYMENT, TERMINATION, SUSPENDED);
    console.log(permission);

    resolve(permission);
  });
};

/**
 * @des: 封装一个除法函数，传入总数和数量，返回商，保留两位小数
 * @param total 总数
 * @param count 数量
 * @returns 商
 */
export const divide = (total, count) => {
  return (total / count).toFixed(2);
};

// 当前时间（2025-03-30 00:00:00）减去一秒
export const dateSubtractOneSecond = function (time) {
  // 创建 Moment 对象表示 2025-03-30 00:00:00
  let date = moment(time);
  // 减去一秒
  date.subtract(1, 'second');

  // 输出结果
  console.log(date.format('YYYY-MM-DD HH:mm:ss'));

  return date.format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 获取URL参数
 *
 * @returns 返回一个包含URL参数的对象
 */
export const getUrlParams = () => {
  const hashIndex = window.location.hash.indexOf('?');
  const queryString = hashIndex !== -1 ? window.location.hash.slice(hashIndex + 1) : '';
  const urlParams = new URLSearchParams(queryString);
  const paramsObj = {};

  for (const [key, value] of urlParams.entries()) {
    paramsObj[key] = value;
  }
  return paramsObj;
};

/**
 * 对比两个对象数组任意属性是否发生改变
 * @param {Array} arr1 - 第一个对象数组
 * @param {Array} arr2 - 第二个对象数组
 * @returns {boolean} - 如果有属性发生改变返回 true，否则返回 false
 */
export const compareObjectArrays = (arr1, arr2) => {
  if (arr1.length !== arr2.length) {
    return false;
  }
  return arr2.every(obj1 =>
    arr1.find(obj2 => Object.keys(obj1).every(key => obj1[key] === obj2[key])),
  );
};

/**
 * 对比两个对象数组是否相等
 * @param {Array} arr1 - 第一个对象数组
 * @param {Array} arr2 - 第二个对象数组
 * @returns {boolean} - 如果有属性发生改变返回 true，否则返回 false
 */
export const isArrayEqual = (arr1, arr2) => {
  return JSON.stringify(arr1) === JSON.stringify(arr2);
};

/**
 * 等待 获取数据 所需 key 都有数据再继续
 * @param {Function} getDataFn - 获取数据的方法，返回对象
 * @param {Function} completeConditionCallback - 判断完成结束 - 回调
 * @param {Number} maxTry - 最大尝试次数
 * @param {Number} interval - 轮询间隔(ms)
 * @returns {Promise<Object>}
 */
export const waitForDataFnComplete = async (
  getDataFn,
  completeConditionCallback,
  maxTry = 10,
  interval = 200,
) => {
  let tryCount = 0;
  console.log('waitForAllData', tryCount);
  while (tryCount < maxTry) {
    const data = await getDataFn();
    const isComplete = completeConditionCallback(data);
    if (isComplete) return data;
    await new Promise(resolve => setTimeout(resolve, interval));
    tryCount++;
  }
  throw new Error('数据未能在规定时间内加载完成');
};
