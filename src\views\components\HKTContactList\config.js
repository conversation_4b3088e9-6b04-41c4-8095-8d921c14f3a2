/*
 * @Author: sly <EMAIL>
 * @Date: 2025-05-27 10:08:14
 * @LastEditors: sly <EMAIL>
 * @LastEditTime: 2025-05-29 15:28:53
 * @FilePath: \crm-front-handling\salesorder_front\src\views\components\HKTContactList\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import that from '@/main.js';
export default {
  columns: [
    {
      title: that.$t('orderSummary.seq'),
      dataIndex: 'id',
      key: 'id',
      showFn: isDetail => {
        return !isDetail;
      },
      width: 120,
    },
    {
      title: that.$t('orderSummary.type'),
      scopedSlots: { customRender: 'PARTICIPANTS_TYPE' },
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('type') : true;
      },
      width: 120,
    },
    {
      title: that.$t('fulfillmentInfo.staffId'),
      dataIndex: 'STAFF_ID',
      key: 'STAFF_ID',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('staffId') : true;
      },
      width: 120,
    },
    {
      title: that.$t('fulfillmentInfo.name'),
      dataIndex: 'NAME',
      key: 'NAME',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('name') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.salesCode'),
      dataIndex: 'SALES_CODE',
      key: 'SALES_CODE',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('salesCode') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.contactPhone'),
      dataIndex: 'CONTACT_PHONE',
      key: 'CONTACT_PHONE',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('contactPhone') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.mobile'),
      dataIndex: 'MOBILE',
      key: 'MOBILE',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('mobile') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.email'),
      dataIndex: 'EMAIL',
      key: 'EMAIL',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('email') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.orderSalesType'),
      scopedSlots: { customRender: 'ORDER_SALES_TYPE' },
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('orderSalesType') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.agentCode'),
      dataIndex: 'AGENT_CODE',
      key: 'AGENT_CODE',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('agentCode') : true;
      },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.agentName'),
      dataIndex: 'AGENT_NAME',
      key: 'AGENT_NAME',
      showFn: (isDetail, excludeElementsList) => {
        return excludeElementsList ? !excludeElementsList.includes('agentName') : true;
      },
      width: 150,
      fixed: 'right',
    },
  ],
};
