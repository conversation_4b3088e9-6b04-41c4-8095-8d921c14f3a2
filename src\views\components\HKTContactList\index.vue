<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('orderSummary.HKT_ContactList') }}</div>
    <a-table :columns="tableColumns" :data-source="datas" :pagination="false" :scroll="{ x: 1000 }">
      <span slot="PARTICIPANTS_TYPE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="STAFF_ID" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="NAME" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="SALES_CODE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="CONTACT_PHONE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="MOBILE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="EMAIL" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="ORDER_SALES_TYPE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="AGENT_CODE" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="AGENT_NAME" slot-scope="text, record, index">
        <EllipsisPopover :text="text || ''" />
      </span>
      <span slot="PARTICIPANTS_TYPE" slot-scope="record, index">
        {{ changeLabel(record, 'PARTICIPANTS_TYPE') }}
      </span>
      <span slot="ORDER_SALES_TYPE" slot-scope="record, index">
        {{ changeLabel(record, 'ORDER_SALES_TYPE') }}
      </span>
    </a-table>
    <a-divider />
  </div>
</template>

<script>
  import config from './config';
  import { queryCreateCustomerEnum } from '@/api/fulfillmentInfo';
  import EllipsisPopover from '@/views/components/ellipsisPopover';
  export default {
    components: {
      EllipsisPopover,
    },
    props: {
      datas: {
        type: Array,
        default: () => [],
      },
      isDetail: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        columns: config.columns,
        participantsType: [],
        orderSalesTypeList: [],
      };
    },
    computed: {
      tableColumns() {
        const { isDetail } = this;
        return config.columns.filter(item => item.showFn(isDetail, ['staffId', 'agentName']));
      },
    },
    mounted() {
      this.getTypeList('PARTICIPANTS_TYPE_HKT', 'participantsType');
      this.getTypeList('ORDER_SALES_TYPE', 'orderSalesTypeList');
    },
    methods: {
      // 查枚举数据
      async getTypeList(codeType, targetKey) {
        const params = {
          CODE_TYPE: codeType,
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this[targetKey] = res.DATA || [];
        } catch (error) {
          console.log(error);
        }
      },
      // 动态获取标签
      changeLabel(record, type) {
        const enumList =
          type === 'PARTICIPANTS_TYPE' ? this.participantsType : this.orderSalesTypeList;
        const key = type === 'PARTICIPANTS_TYPE' ? 'PARTICIPANTS_TYPE' : 'ORDER_SALES_TYPE';
        return enumList.find(item => item.CODE_VALUE === record[key])?.CODE_NAME || null;
      },
    },
  };
</script>
