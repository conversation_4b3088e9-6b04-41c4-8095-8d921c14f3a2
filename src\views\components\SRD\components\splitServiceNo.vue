<template>
  <div class="page">
    <a-form-model :model="form" :colon="false" layout="vertical" ref="searchFormRules">
      <a-row :gutter="24">
        <a-col :span="24">
          <a-form-model-item :label="$t('customerVerify.Address')">
            <a-input v-containsSqlInjection v-model.trim="form.Address" :disabled="true" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-model-item :label="$t('customerVerify.SB_NO')">
            <a-input v-containsSqlInjection v-model.trim="form.SB_NO" :disabled="true" />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('customerVerify.ServiceNo')">
            <a-input v-containsSqlInjection v-model.trim="form.ServiceNo" />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="24" class="b-btns">
          <div class="btns-container">
            <a-button type="primary" class="search-button" @click="filterServiceNo">
              {{ $t('common.buttonInquiry') }}
            </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form-model>
    <div class="checkbox-grid">
      <a-row v-for="(row, rowIndex) in rows" :key="rowIndex" class="row">
        <a-col v-for="(item, index) in row" :key="index" :span="4" class="cell">
          <a-checkbox :disabled="item.disabled" v-model="item.checked" @change="onChange(item)">{{
            item.value
          }}</a-checkbox>
        </a-col>
      </a-row>
    </div>
    <!-- 底部按钮 -->
    <div class="footer">
      <a-button class="cancel-botton" @click="handleCancel">{{
        $t('common.buttonCancel')
      }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.buttonConfirm') }}</a-button>
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import config from '../config';
  export default {
    name: 'SelectAddress',
    props: {
      splitServiceNoObj: {
        type: Object,
        default: () => {},
      },
      splitServiceNoList: {
        type: Array,
        default: () => [],
      },
    },
    components: {},
    data() {
      return {
        form: {
          Address: this.splitServiceNoObj.Address,
          SB_NO: this.splitServiceNoObj.SB_NO,
          ServiceNo: '',
        },
        checkBoxGroup: [],
        numberList: [],
        originalNumberList: [], // 用于存储原始数据
        selectedNumbers: [],
      };
    },
    computed: {
      ...mapState('customerVerify', {
        SERVICE_NO_List: state => state.numberSelectionPage.SERVICE_NO_List,
      }),
      rows() {
        const rows = [];
        for (let i = 0; i < this.numberList?.length; i += 5) {
          rows.push(this.numberList.slice(i, i + 5));
        }
        return rows;
      },
    },
    watch: {},
    methods: {
      // 验证是否已被选择
      // handleNumberChoosed() {
      //   let numbers = [];
      //   let SERVICE_NO_LIST = [
      //     '11111111',
      //     '11111112',
      //     '11111113',
      //     '11111114',
      //     '11111115',
      //     '11111116',
      //     '11111117',
      //   ];
      //   SERVICE_NO_LIST.forEach(item => {
      //     if (this.splitServiceNoList.includes(item)) {
      //       numbers.push({
      //         disabled: false,
      //         checked: false,
      //         value: item,
      //       });
      //     } else {
      //       numbers.push({
      //         disabled: true,
      //         checked: true,
      //         value: item,
      //       });
      //     }
      //   });
      //   this.numberList = numbers;
      //   this.originalNumberList = [...numbers]; // 保存原始数据
      // },

      handleNumberChoosed() {
        let numbers = [];
        this.SERVICE_NO_List.forEach(item => {
          if (this.splitServiceNoList.includes(item)) {
            numbers.push({
              disabled: false,
              checked: false,
              value: item,
            });
          } else {
            numbers.push({
              disabled: true,
              checked: true,
              value: item,
            });
          }
        });
        this.numberList = numbers;
        this.originalNumberList = [...numbers]; // 保存原始数据
      },
      onChange(e) {},
      // 过滤号码
      filterServiceNo() {
        const { ServiceNo } = this.form;
        if (!ServiceNo) {
          this.numberList = this.originalNumberList;
        } else {
          this.numberList = this.originalNumberList.filter(item => item.value.includes(ServiceNo));
        }
      },
      handleOk() {
        // 过滤出已选择但没被禁用的号码
        const filterValidNumber = this.numberList
          .filter(item => item.checked && !item.disabled)
          .map(item => item.value);
        this.$emit('confirm', filterValidNumber);
      },
      handleCancel() {
        this.$emit('cancel');
      },
    },
  };
</script>

<style lang="less" scoped>
  .page {
    padding-bottom: 15px;
    .footer {
      margin-top: 15px;
      display: flex;
      justify-content: end;
      align-items: center;
    }
    .cancel-botton {
      color: #01408e;
      border: 1px solid #01408e;
      margin-right: 10px;
    }
    .b-btns {
      .btns-container {
        height: 74px;
        padding-bottom: 12px;
        display: flex;
        align-items: end;
        justify-content: end;
      }
    }

    .checkbox-grid {
      max-height: 250px; /* 设置最大高度，允许滚动 */
      overflow-y: auto; /* 允许垂直滚动 */
      width: 100%;
    }

    .row {
      // display: flex;
      // flex-wrap: wrap;
      // justify-content: space-between;
    }

    .cell {
      width: 18%; /* 五列，每列占 20% 的宽度，留出一些空间用于间隔 */
      margin: 1%;
      text-align: center;
    }
  }
</style>
