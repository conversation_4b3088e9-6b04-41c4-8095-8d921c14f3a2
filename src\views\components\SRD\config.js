import i18n from '@/i18n/index';

export default {
  SRDTableColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
    },
    {
      title: i18n.t('fulfillmentInfo.SB_No'),
      dataIndex: 'SB_NO',
      key: 'SB_NO',
    },
    {
      title: i18n.t('fulfillmentInfo.address'),
      dataIndex: 'masterAddress',
      key: 'masterAddress',
    },
    {
      // title: i18n.t('fulfillmentInfo.srd'),
      scopedSlots: { title: 'srdTitle', customRender: 'srd' },
    },
    {
      title: i18n.t('fulfillmentInfo.appointmentDate'),
      scopedSlots: { customRender: 'appointmentDate' },
    },
    {
      title: i18n.t('fulfillmentInfo.preWiringDate'),
      scopedSlots: { customRender: 'preWiringDate' },
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      className: 'action-column',
    },
  ],
  formRules: {
    SRD: [{ required: true, message: i18n.t('common.noNull'), trigger: 'change' }],
  },
  // 展示serviceNo的srd表格列
  hasServiceNoSRDTableColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
    },
    {
      title: i18n.t('fulfillmentInfo.serviceNo'),
      dataIndex: 'SERVICE_NO',
      key: 'SERVICE_NO',
    },
    {
      title: i18n.t('fulfillmentInfo.SB_No'),
      dataIndex: 'SB_NO',
      key: 'SB_NO',
    },
    {
      title: i18n.t('fulfillmentInfo.address'),
      dataIndex: 'masterAddress',
      key: 'masterAddress',
    },
    {
      // title: i18n.t('fulfillmentInfo.srd'),
      scopedSlots: { title: 'srdTitle', customRender: 'srd' },
    },
    {
      title: i18n.t('fulfillmentInfo.appointmentDate'),
      scopedSlots: { customRender: 'appointmentDate' },
    },
    {
      title: i18n.t('fulfillmentInfo.preWiringDate'),
      scopedSlots: { customRender: 'preWiringDate' },
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      className: 'action-column',
    },
  ],
  srdTableOfBBIColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
      width: 70,
    },
    {
      title: i18n.t('fulfillmentInfo.SB_No'),
      dataIndex: 'SB_NO',
      key: 'SB_NO',
    },
    {
      title: i18n.t('fulfillmentInfo.address'),
      dataIndex: 'masterAddress',
      key: 'masterAddress',
      ellipsis: true,
    },
    {
      title: i18n.t('fulfillmentInfo.serviceNo'),
      // dataIndex: 'SERVICE_NO',
      // key: 'SERVICE_NO',
      scopedSlots: { customRender: 'serviceNo' },
      // width: 250,
      ellipsis: true,
    },
    {
      scopedSlots: { title: 'srdTitle', customRender: 'srd' },
    },
    {
      title: i18n.t('fulfillmentInfo.appointmentDate'),
      scopedSlots: { customRender: 'appointmentDate' },
    },
    {
      title: i18n.t('fulfillmentInfo.preWiringDate'),
      scopedSlots: { customRender: 'preWiringDate' },
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      className: 'action-column',
      width: 120,
    },
  ],
};
