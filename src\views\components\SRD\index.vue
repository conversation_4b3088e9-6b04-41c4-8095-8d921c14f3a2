<!-- SRD两种形式组件：From 和 Table -->
<template>
  <div>
    <!-- 优先判读是否是需要BBI组件 -->
    <!-- 通过changeForm判断是否将默认的form表格转变成table形式 -->
    <SRDTableOfBBBasic
      ref="SRDTableOfBBBasicRef"
      v-if="BBBasicType"
      :SRDTableDataOfBBI="SRDTableDataOfBBI"
    />
    <SRDTable
      v-else-if="changeTable"
      ref="SRDTableRef"
      :showServiceNo="showServiceNo"
      :SRDTableData="SRDTableData"
    />
    <SRDForm ref="SRDFormRef" :showSRD="showSRD" v-else />
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import SRDForm from './srdForm.vue';
  import SRDTable from './srdTable.vue';
  import SRDTableOfBBBasic from './srdTableOfBBBasic.vue';

  export default {
    name: 'SRD',
    components: {
      SRDForm,
      SRDTable,
      SRDTableOfBBBasic,
    },
    props: {
      changeTable: {
        type: Boolean,
        default: false,
      },
      SRDTableData: {
        type: Array,
        default: () => [],
      },
      showSRD: {
        type: Boolean,
        default: true,
      },
      showServiceNo: {
        type: Boolean,
        default: false,
      },
      BBBasicType: {
        type: Boolean,
        default: false,
      },
      SRDTableDataOfBBI: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {};
    },
    computed: {
      ...mapState('quotation', {
        productType: state => state.productType,
      }),
    },
    methods: {
      async validate() {
        if (this.BBBasicType) {
          await this.$refs.SRDTableOfBBBasicRef.validate();
          console.log('校验BBI表格是否填入srd!');
        } else if (this.changeTable) {
          await this.$refs.SRDTableRef.validate();
          console.log('校验表格是否填入srd!');
        } else {
          console.log('校验表单是否填入srd!');
          await this.$refs.SRDFormRef.validate();
        }
      },
    },
  };
</script>
<style lang="less" scoped></style>
