<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('fulfillmentInfo.srd') }}</div>
    <a-form-model ref="formRules" :model="form" :colon="false" layout="vertical" :rules="formRules">
      <a-row :gutter="24" justify="start" type="flex">
        <!-- SRD -->
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('fulfillmentInfo.srd')" labelAlign="left" prop="SRD">
            <a-date-picker
              style="width: 100%"
              v-model="form.SRD"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD 00:00:00"
              :placeholder="$t('common.inputPlaceholder')"
              @change="onDateChange"
            >
              <i slot="suffixIcon" class="dateIcon iconfont icon-rili" style="color: #d1d1d1"></i>
            </a-date-picker>
          </a-form-model-item>
        </a-col>
        <!-- appointmentDate -->
        <a-col :span="6" flex="flex-start" v-if="showSRD">
          <a-form-model-item :label="$t('fulfillmentInfo.appointmentDate')" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.appointmentDateWithTime"
              :disabled="true"
            />
          </a-form-model-item>
        </a-col>
        <!-- preWiringDate -->
        <a-col :span="6" flex="flex-start" v-if="showSRD">
          <a-form-model-item :label="$t('fulfillmentInfo.preWiringDate')" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.preWiringDateWithTime"
              :disabled="true"
            />
          </a-form-model-item>
        </a-col>
        <!-- 施工预约按钮 -->
        <a-col :span="6" flex="flex-start" class="b-btns" v-if="showSRD">
          <div class="btns-container">
            <a-button type="primary" class="search-button" @click="constructionReservation">
              <i class="iconfont icon-rili" style="color: #ffffff"></i>
            </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="messageModalConfirm"
    />
  </div>
</template>
<script>
  import { makeAppointment, queryAppointmentResult } from '@/api/fulfillmentInfo';
  import MessageModal from '@/components/messageModal';
  import { mapState } from 'vuex';
  import config from './config';

  export default {
    name: 'srdForm',
    components: { MessageModal },
    props: {
      showSRD: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        form: {
          SRD: '',
          appointmentDate: '',
          preWiringDate: '',
          appointmentDateWithTime: '',
          preWiringDateWithTime: '',
        },
        appointmentResults: {},
        formRules: config.formRules,
        tipsVisible: false,
        tipsMessage: '',
        checkResultInterval: '',
        countdownInterval: '',
        SRDIsGreaterThanBusinessTime: true, // srd时间是否大于施工预约时间
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
        productType: state => state.productType,
        productSelection: state => state.productSelection,
      }),
      ...mapState('customerVerify', {
        productFamily: state => state.productFamily,
      }),
      ...mapState('orderCapture', {
        numberSelectionPage: state => state.numberSelectionPage,
      }),
    },
    methods: {
      // 施工预约的逻辑：
      // 1、首先先调用这个施工预约接口，打开一个新的标签页，然后在新标签页上输入信息，
      // 2、调用完这个施工预约的接口后，紧接着采用轮询的方式调用预约结果查询接口，将获取的预约信息回填到输入框内并vuex保存
      // 注意：只需要调用两个接口，（一个是施工预约，一个是预约查询结果，取消接口与前端无关，是后端调用）

      // 施工预约功能
      async constructionReservation() {
        console.log('this.productSelection', this.productSelection);
        if (!this.productSelection?.addressInfo?.SB_NO) {
          this.$message.warning(this.$t('fulfillmentInfo.notNeedAppointmentTips'));
          return;
        }
        clearInterval(this.checkResultInterval);

        // todo -------tanwl3------PRODUCT_TYPE不是productFamily，传啥还不清楚-------
        let parmas = {
          BLOCK_WIRING2N_INDICATOR: '', // 暂时不填
          ORDER_ID: '', // 暂时不填
          SB: this.productSelection?.addressInfo?.SB_NO ?? '', // 标准地址ID --- 要是新建地址就没有SBNO
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID || 'xasi0137', // 操作员标识ID
          PRODUCT_TYPE: 'V' || this.productFamily.value, // Product Family
          ORDER_NUMBER: this.numberSelectionPage.serviceNoList[0]?.value, // 用户号码，一批只传第一个。
          LINE_NUMBER: '1', // No. of Service Line。1, 10, 20 (暂时写1)
          TO_VISIT_IND: this.productSelection.siteWorkAppointment ? 'Y' : 'N', // 填入任意一个服务的siteWorkAppoint的属性值
          FROM_VISIT_IND: '',
          APPOINTMENT_ID: this.appointmentResults.APPOINTMENT_ID ?? '',
          params_transfer_arr: ['IN'],
        };

        try {
          const res = await makeAppointment(parmas);
          const APPOINTMENT_URL = res.DATA[0].APPOINTMENT_URL;
          if (APPOINTMENT_URL) {
            window.open(APPOINTMENT_URL);

            // 启动轮询
            this.checkResultInterval = setInterval(() => {
              this.getAppointmentResult().then(() => {
                // 如果 getAppointmentResult 接口返回数据，则停止轮询
                if (this.form.appointmentDate && Object.keys(this.appointmentResults).length > 0) {
                  this.messageModalConfirm();
                }
              });
            }, 5000); // 每5秒轮询一次，可以根据实际情况调整时间间隔

            // 启动倒计时（30秒）
            this.startCountdown(300);
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 施工预约预约结果查询
      async getAppointmentResult() {
        let parmas = {
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID || 'xasi0137', // 操作员标识ID
          ORDER_NUMBER: this.numberSelectionPage.serviceNoList[0].value ?? '', // 用户号码
        };
        try {
          const res = await queryAppointmentResult(parmas);
          const {
            SERVICE_REQUEST_DATE,
            APPOINTMENT_DATE,
            PREWIRING_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          } = res.DATA[0];
          // 回填数据到输入框内
          this.form.SRD = SERVICE_REQUEST_DATE;
          this.form.appointmentDate = APPOINTMENT_DATE;
          this.form.preWiringDate = PREWIRING_DATE;
          this.form.appointmentDateWithTime = this.formattedDate(
            APPOINTMENT_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
          );
          this.form.preWiringDateWithTime = this.formattedDate(
            PREWIRING_DATE,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          );
          this.appointmentResults = res.DATA[0]; // 存储施工预约结果
          this.checkDates();
          this.$refs.formRules.validateField('SRD');
        } catch (error) {
          console.log(error);
        }
      },
      // 倒计时方法
      startCountdown(duration) {
        // this.tipsVisible = true;
        let countdown = duration; // 倒计时时间（秒）
        this.countdownInterval = setInterval(() => {
          // this.tipsMessage = this.$t('fulfillmentInfo.gettingAppointmentTime', { second: countdown });
          countdown--;

          // 如果倒计时结束
          if (countdown < 0) {
            this.messageModalConfirm();
            this.tipsMessage = '';
          }
        }, 1000); // 每秒更新一次
      },
      // 检查SRD日期是否早于Appointment Date和Pre-Wiring Date
      checkDates() {
        const srdDate = this.form.SRD;
        const appointmentDate = this.form.appointmentDate;
        const preWiringDate = this.form.preWiringDate;

        // 如果 SRD 日期为空，直接返回
        if (!srdDate) {
          console.log('SRD 日期为空，跳过验证');
          return;
        }

        // 如果 appointmentDate 和 preWiringDate 都为空，直接返回
        if (!appointmentDate && !preWiringDate) {
          console.log('预约日期和预布线日期都为空，跳过验证');
          return;
        }

        // 将 appointmentDate 和 preWiringDate 转换为时间戳（只保留日期部分）
        const appointmentDateTimestamp = appointmentDate
          ? new Date(appointmentDate).setHours(0, 0, 0, 0)
          : 0;
        const preWiringDateTimestamp = preWiringDate
          ? new Date(preWiringDate).setHours(0, 0, 0, 0)
          : 0;

        // 找出两个日期中最晚的一个
        const latestDate = Math.max(appointmentDateTimestamp, preWiringDateTimestamp);

        // 将 srdDate 转换为时间戳（只保留日期部分）
        const srdDateObj = new Date(srdDate);
        srdDateObj.setHours(0, 0, 0, 0); // 设置时分秒为 0
        const srdDateTimestamp = srdDateObj.getTime();
        if (srdDateTimestamp >= latestDate) {
          // SRD 日期晚于最晚日期，业务正常
          console.log('SRD 日期晚于最晚日期，业务正常');
          this.SRDIsGreaterThanBusinessTime = true;
        } else {
          // SRD 日期早于最晚日期，提示错误
          this.SRDIsGreaterThanBusinessTime = false;
          this.$message.error(this.$t('fulfillmentInfo.dateComparisonVerification'));
        }
      },
      // 改变日期函数
      onDateChange(date) {
        // 如果返回的日期存在，然后再次检查是否满足条件
        if (this.form.appointmentDate || this.form.preWiringDate) {
          this.checkDates();
        }
      },
      messageModalConfirm() {
        if (this.checkResultInterval || this.checkResultInterval) {
          clearInterval(this.countdownInterval);
          clearInterval(this.checkResultInterval);
        }
        this.tipsVisible = false;
      },
      // 格式化日期方法
      formattedDate(Date, fromTime, toTime) {
        return Date ? Date + ' ' + fromTime + '-' + toTime : '';
      },
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.formRules.validate(valid => {
            if (!valid) {
              // 验证失败，直接返回
              reject(new Error('表单验证失败'));
              return;
            }

            // 判断是否IDD
            if (!this.showSRD) {
              resolve();
              return;
            }

            // 判断是否新建地址
            if (!this.productSelection?.addressInfo?.SB_NO) {
              resolve();
              return;
            }

            // 检查预约日期或预布线日期是否已填写
            if (!this.form.appointmentDate && !this.form.preWiringDate) {
              this.tipsMessage = this.$t('fulfillmentInfo.constructionReservationTips');
              this.tipsVisible = true;
              return;
            }

            // 检查srd时间是否大于施工预约时间
            if (!this.SRDIsGreaterThanBusinessTime) {
              this.tipsMessage = this.$t('fulfillmentInfo.dateComparisonVerification');
              this.tipsVisible = true;
              return;
            }

            // 所有检查通过，调用 resolve
            resolve();
          });
        });
      },
    },
    beforeDestroy() {
      // 在组件销毁前清除定时器
      if (this.checkResultInterval) {
        clearInterval(this.checkResultInterval);
      }
    },
  };
</script>
<style lang="less" scoped>
  .b-btns {
    padding-right: 0 !important;
    .btns-container {
      height: 100%;
      display: flex;
      margin-top: 32px;
    }
  }
</style>
