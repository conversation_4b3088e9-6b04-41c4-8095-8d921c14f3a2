<template>
  <div class="customerContact">
    <a-table
      :columns="srdTableColumns"
      :data-source="localSRDTableData"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
    >
      <!-- srd -->
      <template slot="srdTitle">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.srd') }}
      </template>
      <span slot="seq" slot-scope="record, text, index">
        {{ index + 1 }}
      </span>
      <span slot="address" slot-scope="record, index">
        <!-- <a-tooltip placement="topLeft">
          <template slot="title">
            <span>        {{ formatAddress(record) }}</span>
          </template> -->
        {{ formatAddress(record) }}
        <!-- </a-tooltip> -->
      </span>
      <span slot="srd" slot-scope="record, index">
        <a-date-picker
          style="width: 100%"
          v-model="record.SRD"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD 00:00:00"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onDateChange(record)"
        >
          <i slot="suffixIcon" class="dateIcon iconfont icon-rili" style="color: #d1d1d1"></i>
        </a-date-picker>
      </span>
      <span slot="appointmentDate" slot-scope="record, index">
        <a-tooltip>
          <template slot="title">
            {{ record.appointmentDateWithTime }}
          </template>
          <div class="content">
            <a-input
              v-containsSqlInjection
              v-model.trim="record.appointmentDateWithTime"
              :disabled="true"
            />
          </div>
        </a-tooltip>
      </span>
      <span slot="preWiringDate" slot-scope="record, index">
        <a-tooltip>
          <template slot="title">
            {{ record.preWiringDateWithTime }}
          </template>
          <div class="content">
            <a-input
              v-containsSqlInjection
              v-model.trim="record.preWiringDateWithTime"
              :disabled="true"
            />
          </div>
        </a-tooltip>
      </span>
      <template slot="action" slot-scope="record, text, index">
        <div class="actionColumns">
          <i
            class="iconfont icon-rili"
            style="color: #3377ff; cursor: pointer"
            @click="constructionReservation(record, index)"
          ></i>
        </div>
      </template>
    </a-table>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="messageModalConfirm"
    />
  </div>
</template>

<script>
  import { makeAppointment, queryAppointmentResult } from '@/api/fulfillmentInfo';
  import MessageModal from '@/components/messageModal';
  import eventBus from '@/mixin/eventBus';
  import { mapState } from 'vuex';
  import config from './config';

  export default {
    name: 'SRDTable',
    components: { MessageModal },
    props: {
      SRDTableData: {
        type: Array,
        default: () => [],
      },
      showServiceNo: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        tipsVisible: false,
        tipsMessage: '',
        checkResultInterval: '',
        countdownInterval: '',
        localSRDTableData: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
        productSelection: state => state.productSelection,
      }),
      ...mapState('orderCapture', {
        accountSettingPage: state => state.accountSettingPage,
      }),
      ...mapState('orderQuery', {
        productSelectionFromOrderQuery: state => state.productSelection,
        accountSettingPageFromOrderQuery: state => state.accountSettingPage,
        macdAmendOrderPage: state => state.macdAmendOrderPage,
      }),
      srdTableColumns() {
        return this.showServiceNo ? config.hasServiceNoSRDTableColumns : config.SRDTableColumns;
      },
    },
    watch: {
      SRDTableData: {
        handler(newVal, oldVal) {
          this.localSRDTableData = JSON.parse(JSON.stringify(newVal)); // 深拷贝
        },
        deep: true,
        immediate: true,
      },
      localSRDTableData: {
        handler(newVal, oldVal) {
          this.$store.dispatch('customerVerify/setFulfillmentInfoTableData', newVal);
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      // 施工预约的逻辑：
      // 1、首先先调用这个施工预约接口，打开一个新的标签页，然后在新标签页上输入信息，
      // 2、调用完这个施工预约的接口后，紧接着采用轮询的方式调用预约结果查询接口，将获取的预约信息回填到输入框内并vuex保存
      // 注意：只需要调用两个接口，（一个是施工预约，一个是预约查询结果，取消接口与前端无关，是后端调用）

      // 施工预约功能
      async constructionReservation(record, index) {
        console.log('施工预约功能', record);
        if (!record.SB_NO) {
          this.$message.warning(this.$t('fulfillmentInfo.notNeedAppointmentTips'));
          return;
        }
        clearInterval(this.checkResultInterval);
        const orderNumberData = this.accountSettingPageFromOrderQuery?.numbersWithAccountInfo;
        console.log('this.accountSettingPageFromOrderQuery', this.accountSettingPageFromOrderQuery);
        console.log('orderNumberData', orderNumberData);

        let parmas = {
          BLOCK_WIRING2N_INDICATOR: '', // 暂时不填
          ORDER_ID: record.ORDER_ID, // 暂时不填
          SB: record.SB_NO || this.productSelectionFromOrderQuery?.addressInfo?.SB_NO, // 标准地址ID
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID, // 操作员标识ID
          PRODUCT_TYPE: 'V' || this.productFamily.value, // Product Type：Broadband, PND, Voice。
          ORDER_NUMBER: record.SERIAL_NUMBER || (orderNumberData && orderNumberData[0].value), // 用户号码，一批只传第一个。
          LINE_NUMBER: '1', // No. of Service Line。1, 10, 20
          // TO_VISIT_IND: this.productSelection.siteWorkAppointment ?? '', // 填入任意一个服务的siteWorkAppoint的属性值
          TO_VISIT_IND: '', // 填入任意一个服务的siteWorkAppoint的属性值
          FROM_VISIT_IND: '',
          APPOINTMENT_ID: record.appointmentResults?.APPOINTMENT_ID ?? '',
        };

        let targetArr = ['34100'];
        let arr = this.macdAmendOrderPage?.macdRawData?.filter(item => {
          return (
            (record.SB_NO === item.NEW_INSTALLATION_ADDRESS_ID ||
              record.SB_NO === item.OLD_INSTALLATION_ADDRESS_ID) &&
            (record.ADDRESS === item.NEW_INSTALLATION_ADDRESS ||
              record.ADDRESS === item.OLD_INSTALLATION_ADDRESS)
          );
        });

        if (arr?.length) {
          let obj = arr.find(e => targetArr.includes(e.SCENE_TYPE));
          console.log('是不是外移数据', obj);
          if (obj) {
            parmas.params_transfer_arr = [obj.SCENE_TYPE];
          } else {
            parmas.params_transfer_arr = arr && arr.map(it => it?.SCENE_TYPE);
          }
        }

        // 如果record.params_transfer_arr存在，则添加到parmas中
        if (parmas.params_transfer_arr?.length) {
          let obj = this.executeOperation(parmas.params_transfer_arr);
          console.log('obj = ', obj);
          parmas = {
            ...parmas,
            ...obj,
          };
        }

        try {
          const res = await makeAppointment(parmas);
          const APPOINTMENT_URL = res.DATA[0].APPOINTMENT_URL;
          if (APPOINTMENT_URL) {
            window.open(APPOINTMENT_URL);

            // 启动轮询
            this.checkResultInterval = setInterval(() => {
              this.getAppointmentResult(record, index).then(() => {
                // 如果getAppointmentResult接口返回数据，则停止轮询
                if (record.APPOINTMENTDATE && Object.keys(record.appointmentResults).length > 0) {
                  this.messageModalConfirm();
                }
              });
            }, 5000); // 每5秒轮询一次，可以根据实际情况调整时间间隔

            // 启动倒计时（30秒）
            this.startCountdown(300);
          }
        } catch (error) {
          console.log(error);
        }
      },
      /**
       * 根据数组内容执行不同操作
       * @param {string[]} arr - 输入数组
       * @returns {string} 执行的操作名称
       */
      executeOperation(arr) {
        if (!arr) return {};

        let params = {};
        // FROM_VISIT_IND 和 TO_VISIT_IND 都要 (外移)
        const condition1Items = ['34100'];
        // 只要 FROM_VISIT_IND  (拆机)
        const condition2Items = ['19200', '127000', '192001', '19201'];
        // 只要 TO_VISIT_IND  (新安装、改产品、内移)
        const condition3Items = [
          '10000',
          '12700',
          '11000',
          '34001',
          '34002',
          '12701',
          '12704',
          '12703',
          '34000',
          '34003',
          '34004',
          '34005',
          '34101',
        ];
        // 其余的类型都不需要 FROM_VISIT_IND、TO_VISIT_IND

        if (arr) {
          // 如果是数组
          if (Array.isArray(arr)) {
            // 检查数组中是否存在外移
            // 检查数组中是否存在特定值中的任意一个
            if (arr.some(item => condition1Items.includes(item))) {
              return {
                FROM_VISIT_IND: 'Y',
                TO_VISIT_IND: 'Y',
              };
            }

            // 检查数组中是否存在(拆机)
            if (arr.some(item => condition2Items.includes(item))) {
              params.FROM_VISIT_IND = 'Y';
            }

            // 检查数组中是否存在(新安装、改产品、内移)
            // 检查数组中是否存在特定值中的任意一个
            if (arr.some(item => condition3Items.includes(item))) {
              params.TO_VISIT_IND = 'Y';
            }

            return params;
          }

          // 不是数组
          let SCENE_TYPE = arr;
          if (condition1Items.includes(SCENE_TYPE)) {
            // 检查是否是外移
            return {
              FROM_VISIT_IND: 'Y',
              TO_VISIT_IND: 'Y',
            };
          } else if (condition2Items.includes(SCENE_TYPE)) {
            // 检查是否是（拆机）
            return {
              FROM_VISIT_IND: 'Y',
            };
          } else if (condition3Items.includes(SCENE_TYPE)) {
            // 检查是否是拆机 (新安装、改产品、内移)
            return {
              TO_VISIT_IND: 'Y',
            };
          }

          // 其余的类型都不需要
          return {};
        }
      },
      // 施工预约预约结果查询
      async getAppointmentResult(record, index) {
        const orderNumberData = this.accountSettingPageFromOrderQuery?.numbersWithAccountInfo;

        let parmas = {
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID, // 操作员标识ID
          ORDER_NUMBER: record.SERIAL_NUMBER || (orderNumberData && orderNumberData[0].value), // 用户号码
        };
        try {
          const res = await queryAppointmentResult(parmas);
          const {
            SERVICE_REQUEST_DATE,
            APPOINTMENT_DATE,
            PREWIRING_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          } = res.DATA[0];
          // 回填数据到输入框内
          eventBus.$emit('update-srd-row', { index, data: res.DATA[0] });

          record.SRD = SERVICE_REQUEST_DATE;
          record.APPOINTMENTDATE = APPOINTMENT_DATE;
          record.PREWIRINGDATE = PREWIRING_DATE;
          record.appointmentDateWithTime = this.formattedDate(
            APPOINTMENT_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
          );
          record.preWiringDateWithTime = this.formattedDate(
            PREWIRING_DATE,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          );
          record.appointmentResults = res.DATA[0] ?? {}; // 存储施工预约结果
          record.SRDIsGreaterThanBusinessTime = true;
          this.checkDates(record);
        } catch (error) {
          console.log(error);
        }
      },
      // 倒计时方法
      startCountdown(duration) {
        // this.tipsVisible = true;
        let countdown = duration; // 倒计时时间（秒）
        this.countdownInterval = setInterval(() => {
          // this.tipsMessage = this.$t('fulfillmentInfo.gettingAppointmentTime', { second: countdown });
          countdown--;

          // 如果倒计时结束
          if (countdown < 0) {
            this.tipsMessage = '';
            this.messageModalConfirm();
          }
        }, 1000); // 每秒更新一次
      },
      // 检查SRD日期是否早于Appointment Date和Pre-Wiring Date
      checkDates(record) {
        const srdDate = record.SRD;
        const APPOINTMENTDATE = record.APPOINTMENTDATE;
        const PREWIRINGDATE = record.PREWIRINGDATE;

        // 如果 SRD 日期为空，直接返回
        if (!srdDate) {
          // console.log('SRD 日期为空，跳过验证');
          return;
        }

        // 如果 appointmentDate 和 preWiringDate 都为空，直接返回
        if (!APPOINTMENTDATE && !PREWIRINGDATE) {
          // console.log('预约日期和预布线日期都为空，跳过验证');
          return;
        }

        // 将 appointmentDate 和 preWiringDate 转换为时间戳（只保留日期部分）
        const appointmentDateTimestamp = APPOINTMENTDATE
          ? new Date(APPOINTMENTDATE).setHours(0, 0, 0, 0)
          : 0;
        const preWiringDateTimestamp = PREWIRINGDATE
          ? new Date(PREWIRINGDATE).setHours(0, 0, 0, 0)
          : 0;

        // 找出两个日期中最晚的一个
        const latestDate = Math.max(appointmentDateTimestamp, preWiringDateTimestamp);

        // 将 srdDate 转换为时间戳（只保留日期部分）
        const srdDateObj = new Date(srdDate);
        srdDateObj.setHours(0, 0, 0, 0); // 设置时分秒为 0
        const srdDateTimestamp = srdDateObj.getTime();
        console.log('test-compare', srdDateTimestamp, latestDate);
        if (srdDateTimestamp >= latestDate) {
          // SRD 日期晚于最晚日期，业务正常
          // console.log('SRD 日期晚于最晚日期，业务正常');
          record.SRDIsGreaterThanBusinessTime = true;
        } else {
          // SRD 日期早于最晚日期，提示错误
          record.SRDIsGreaterThanBusinessTime = false;
          this.$message.error(this.$t('fulfillmentInfo.dateComparisonVerification'));
        }
      },
      // 格式化日期方法
      formattedDate(Date, fromTime, toTime) {
        return Date ? Date + ' ' + fromTime + '-' + toTime : '';
      },
      onDateChange(record) {
        eventBus.$emit('change-srd-row', record);
        // 如果返回的日期存在，然后再次检查是否
        if (record.APPOINTMENTDATE || record.PREWIRINGDATE) {
          this.checkDates(record);
        }
      },
      messageModalConfirm() {
        if (this.checkResultInterval || this.checkResultInterval) {
          clearInterval(this.countdownInterval);
          clearInterval(this.checkResultInterval);
        }
        this.tipsVisible = false;
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
        throw console.log('阻断继续往下执行');
      },
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          this.localSRDTableData.forEach(item => {
            if (!item.SRD) {
              this.showTipModal(this.$t('fulfillmentInfo.srdNoNull'));
            }
            // 若新增地址，不用通过预约装机
            if (item.SB_NO) {
              // 当不是编辑订单且预约日期为空时，显示提示弹窗
              if (!item.isEditOrderFlag && !item.APPOINTMENTDATE) {
                this.showTipModal(this.$t('fulfillmentInfo.constructionReservationTips'));
              }
              if (!item.SRDIsGreaterThanBusinessTime) {
                this.showTipModal(this.$t('fulfillmentInfo.dateComparisonVerification'));
              }
            }
          });
          console.log('111');
          resolve();
        });
      },
    },
    beforeDestroy() {
      // 在组件销毁前清除定时器
      if (this.checkResultInterval) {
        clearInterval(this.checkResultInterval);
      }
    },
  };
</script>

<style lang="less" scoped>
  .customerContact {
    margin-bottom: 20px;
    .required {
      color: #f5222d;
      font-size: 14px;
    }
    .dateIcon {
      width: 16px;
      height: 16px;
      margin-top: -8px;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
    .actionColumns {
      display: flex;
      justify-content: space-evenly;
    }
  }
</style>
