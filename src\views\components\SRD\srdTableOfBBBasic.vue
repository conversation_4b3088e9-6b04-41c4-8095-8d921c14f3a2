<template>
  <div class="customerContact">
    <a-table
      :columns="srdTableOfBBIColumns"
      :data-source="localSRDTableData"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
    >
      <!-- srd -->
      <template slot="srdTitle">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.srd') }}
      </template>
      <span slot="seq" slot-scope="record, text, index">
        {{ index + 1 }}
      </span>
      <span slot="address" slot-scope="record, index">
        {{ formatAddress(record) }}
      </span>
      <span slot="srd" slot-scope="record, index">
        <a-date-picker
          style="width: 100%"
          v-model="record.SRD"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD 00:00:00"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onDateChange(record)"
        >
          <i slot="suffixIcon" class="dateIcon iconfont icon-rili" style="color: #d1d1d1"></i>
        </a-date-picker>
      </span>
      <span slot="serviceNo" slot-scope="record, index">
        <a-tooltip>
          <template slot="title">
            {{ serviceNoString(record.SERVICE_NO) }}
          </template>
          <div class="ellipsis-text">
            <span>
              {{ serviceNoString(record.SERVICE_NO) }}
            </span>
          </div>
        </a-tooltip>
      </span>
      <span slot="appointmentDate" slot-scope="record, index">
        <a-tooltip>
          <template slot="title">
            {{ record.appointmentDateWithTime }}
          </template>
          <div class="content">
            <a-input
              v-containsSqlInjection
              v-model.trim="record.appointmentDateWithTime"
              :disabled="true"
            />
          </div>
        </a-tooltip>
      </span>
      <span slot="preWiringDate" slot-scope="record, index">
        <a-tooltip>
          <template slot="title">
            {{ record.preWiringDateWithTime }}
          </template>
          <div class="content">
            <a-input
              v-containsSqlInjection
              v-model.trim="record.preWiringDateWithTime"
              :disabled="true"
            />
          </div>
        </a-tooltip>
      </span>
      <template slot="action" slot-scope="record, text, index">
        <div class="actionColumns">
          <i
            class="iconfont icon-rili"
            style="color: #3377ff; cursor: pointer"
            @click="constructionReservation(record, index)"
          ></i>
          <i
            v-if="record.parent"
            class="iconfont icon-split"
            style="color: #3377ff; cursor: pointer"
            @click="handleSplit(record, index)"
          ></i>
          <i
            v-else
            class="iconfont icon-shanchu"
            style="color: #3377ff; cursor: pointer"
            @click="handleDelect(record, index)"
          ></i>
        </div>
      </template>
    </a-table>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="messageModalConfirm"
    />
    <a-modal
      title="Split Service No."
      :width="680"
      destroyOnClose
      :footer="null"
      :visible="splitServiceNoVisible"
      @cancel="splitServiceNoVisible = false"
    >
      <SplitServiceNo
        ref="SplitServiceNoRef"
        :splitServiceNoList="splitServiceNoList"
        :splitServiceNoObj="splitServiceNoObj"
        @confirm="handleNumberTimeSplit"
        @cancel="splitServiceNoVisible = false"
      />
    </a-modal>
  </div>
</template>

<script>
  import config from './config';
  import { mapState } from 'vuex';
  import eventBus from '@/mixin/eventBus';
  import MessageModal from '@/components/messageModal';
  import { makeAppointment, queryAppointmentResult } from '@/api/fulfillmentInfo';
  import SplitServiceNo from './components/splitServiceNo.vue';
  export default {
    name: 'srdTableOfBBBasic',
    components: { MessageModal, SplitServiceNo },
    props: {
      SRDTableDataOfBBI: {
        type: Array,
        default: () => [],
      },
      showServiceNo: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        tipsVisible: false,
        tipsMessage: '',
        checkResultInterval: '',
        countdownInterval: '',
        localSRDTableData: [],
        srdTableOfBBIColumns: config.srdTableOfBBIColumns,
        splitServiceNoVisible: false,
        splitServiceNoObj: {
          Address: '',
          SB_NO: '',
        },
        splitServiceNoList: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
        productSelection: state => state.productSelection,
        addressInfo: state => state.productSelection.addressInfo,
      }),
      ...mapState('orderQuery', {
        productSelectionFromOrderQuery: state => state.productSelection,
      }),
    },
    watch: {
      SRDTableDataOfBBI: {
        handler(newVal, oldVal) {
          console.log(newVal, 'newVal------------------------------------------SRDTableDataOfBBI');
          this.localSRDTableData = JSON.parse(JSON.stringify(newVal)); // 深拷贝
        },
        deep: true,
        immediate: true,
      },
      // SRDTableDataOfBBI: {
      //   handler(newVal, oldVal) {
      //     this.localSRDTableData = [
      //       {
      //         SB_NO: '111122',
      //         masterAddress: 'masterAddress111111111111111111111',
      //         SERVICE_NO: [
      //           '11111111',
      //           '11111112',
      //           '11111113',
      //           '11111114',
      //           '11111115',
      //           '11111116',
      //           '11111117',
      //         ],
      //         SRD: '',
      //         appointmentDateWithTime: '',
      //         preWiringDateWithTime: '',
      //         parent: true,
      //       },
      //     ];
      //     // this.localSRDTableData = JSON.parse(JSON.stringify(newVal)); // 深拷贝
      //   },
      //   deep: true,
      //   immediate: true,
      // },
      localSRDTableData: {
        handler(newVal, oldVal) {
          this.$store.dispatch('customerVerify/setFulfillmentInfoTableData', []);
          this.$store.dispatch('customerVerify/setFulfillmentInfoTableData', newVal);
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      serviceNoString(serviceArr) {
        return serviceArr.join('.');
      },
      // 施工预约的逻辑：
      // 1、首先先调用这个施工预约接口，打开一个新的标签页，然后在新标签页上输入信息，
      // 2、调用完这个施工预约的接口后，紧接着采用轮询的方式调用预约结果查询接口，将获取的预约信息回填到输入框内并vuex保存
      // 注意：只需要调用两个接口，（一个是施工预约，一个是预约查询结果，取消接口与前端无关，是后端调用）

      // 施工预约功能
      async constructionReservation(record, index) {
        if (!record.SB_NO) {
          this.$message.warning(this.$t('fulfillmentInfo.notNeedAppointmentTips'));
          return;
        }
        clearInterval(this.checkResultInterval);

        let parmas = {
          BLOCK_WIRING2N_INDICATOR: '', // 暂时不填
          ORDER_ID: '', // 暂时不填
          SB: record.SB_NO || this.productSelectionFromOrderQuery?.addressInfo?.SB_NO, // 标准地址ID
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID, // 操作员标识ID
          PRODUCT_TYPE: 'V' || this.productFamily.value, // Product Type：Broadband, PND, Voice。
          ORDER_NUMBER: record.SERVICE_NO[0], // 用户号码，一批只传第一个。
          LINE_NUMBER: '1', // No. of Service Line。1, 10, 20
          toVisitind: this.productSelection.siteWorkAppointment ?? '', // 填入任意一个服务的siteWorkAppoint的属性值
          fromVisitInd: '',
          APPOINTMENT_ID: record.appointmentResults?.APPOINTMENT_ID ?? '',
          params_transfer_str: 'IN',
        };

        try {
          const res = await makeAppointment(parmas);
          const APPOINTMENT_URL = res.DATA[0].APPOINTMENT_URL;
          if (APPOINTMENT_URL) {
            window.open(APPOINTMENT_URL);

            // 启动轮询
            this.checkResultInterval = setInterval(() => {
              this.getAppointmentResult(record, index).then(() => {
                // 如果getAppointmentResult接口返回数据，则停止轮询
                if (record.APPOINTMENTDATE && Object.keys(record.appointmentResults).length > 0) {
                  this.messageModalConfirm();
                }
              });
            }, 5000); // 每5秒轮询一次，可以根据实际情况调整时间间隔

            // 启动倒计时（30秒）
            this.startCountdown(300);
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 施工预约预约结果查询
      async getAppointmentResult(record, index) {
        let parmas = {
          STAFF_ID: this.$store.state.app.userInfo.STAFF_ID, // 操作员标识ID
          ORDER_NUMBER: record.SERVICE_NO[0], // 用户号码
        };
        try {
          const res = await queryAppointmentResult(parmas);
          const {
            SERVICE_REQUEST_DATE,
            APPOINTMENT_DATE,
            PREWIRING_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          } = res.DATA[0];
          // 回填数据到输入框内
          eventBus.$emit('update-srd-row', { index, data: res.DATA[0] });

          record.SRD = SERVICE_REQUEST_DATE;
          record.APPOINTMENTDATE = APPOINTMENT_DATE;
          record.PREWIRINGDATE = PREWIRING_DATE;
          record.appointmentDateWithTime = this.formattedDate(
            APPOINTMENT_DATE,
            APPOINTMENT_FROM_TIME,
            APPOINTMENT_TO_TIME,
          );
          record.preWiringDateWithTime = this.formattedDate(
            PREWIRING_DATE,
            PREWIRING_FROM_TIME,
            PREWIRING_TO_TIME,
          );
          record.appointmentResults = res.DATA[0] ?? {}; // 存储施工预约结果
          record.SRDIsGreaterThanBusinessTime = true;
          this.checkDates(record);
        } catch (error) {
          console.log(error);
        }
      },
      // 倒计时方法
      startCountdown(duration) {
        // this.tipsVisible = true;
        let countdown = duration; // 倒计时时间（秒）
        this.countdownInterval = setInterval(() => {
          // this.tipsMessage = this.$t('fulfillmentInfo.gettingAppointmentTime', { second: countdown });
          countdown--;

          // 如果倒计时结束
          if (countdown < 0) {
            this.tipsMessage = '';
            this.messageModalConfirm();
          }
        }, 1000); // 每秒更新一次
      },
      // 检查SRD日期是否早于Appointment Date和Pre-Wiring Date
      checkDates(record) {
        const srdDate = record.SRD;
        const APPOINTMENTDATE = record.APPOINTMENTDATE;
        const PREWIRINGDATE = record.PREWIRINGDATE;

        // 如果 SRD 日期为空，直接返回
        if (!srdDate) {
          // console.log('SRD 日期为空，跳过验证');
          return;
        }

        // 如果 appointmentDate 和 preWiringDate 都为空，直接返回
        if (!APPOINTMENTDATE && !PREWIRINGDATE) {
          // console.log('预约日期和预布线日期都为空，跳过验证');
          return;
        }

        // 将 appointmentDate 和 preWiringDate 转换为时间戳（只保留日期部分）
        const appointmentDateTimestamp = APPOINTMENTDATE
          ? new Date(APPOINTMENTDATE).setHours(0, 0, 0, 0)
          : 0;
        const preWiringDateTimestamp = PREWIRINGDATE
          ? new Date(PREWIRINGDATE).setHours(0, 0, 0, 0)
          : 0;

        // 找出两个日期中最晚的一个
        const latestDate = Math.max(appointmentDateTimestamp, preWiringDateTimestamp);

        // 将 srdDate 转换为时间戳（只保留日期部分）
        const srdDateObj = new Date(srdDate);
        srdDateObj.setHours(0, 0, 0, 0); // 设置时分秒为 0
        const srdDateTimestamp = srdDateObj.getTime();
        console.log('test-compare', srdDateTimestamp, latestDate);
        if (srdDateTimestamp >= latestDate) {
          // SRD 日期晚于最晚日期，业务正常
          // console.log('SRD 日期晚于最晚日期，业务正常');
          record.SRDIsGreaterThanBusinessTime = true;
        } else {
          // SRD 日期早于最晚日期，提示错误
          record.SRDIsGreaterThanBusinessTime = false;
          this.$message.error(this.$t('fulfillmentInfo.dateComparisonVerification'));
        }
      },
      // 格式化日期方法
      formattedDate(Date, fromTime, toTime) {
        return Date ? Date + ' ' + fromTime + '-' + toTime : '';
      },
      onDateChange(record) {
        eventBus.$emit('change-srd-row', record);
        // 如果返回的日期存在，然后再次检查是否
        if (record.APPOINTMENTDATE || record.PREWIRINGDATE) {
          this.checkDates(record);
        }
      },
      messageModalConfirm() {
        if (this.checkResultInterval || this.checkResultInterval) {
          clearInterval(this.countdownInterval);
          clearInterval(this.checkResultInterval);
        }
        this.tipsVisible = false;
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
        throw console.log('阻断继续往下执行');
      },
      // 删除当前行并且释放号码
      handleDelect(record, index) {
        console.log(record, 'record---');
        console.log(index, 'index---');
        this.localSRDTableData = this.localSRDTableData.filter((_, i) => i !== index);
        this.localSRDTableData[0].SERVICE_NO = [
          ...record.SERVICE_NO,
          ...this.localSRDTableData[0].SERVICE_NO,
        ].sort((a, b) => a - b);
      },
      handleSplit(record) {
        this.splitServiceNoObj = {
          Address: record.masterAddress,
          SB_NO: record.SB_NO,
        };
        this.splitServiceNoList = record.SERVICE_NO || [];
        this.splitServiceNoVisible = true;
        this.$nextTick(() => {
          this.$refs.SplitServiceNoRef.handleNumberChoosed();
        });
      },
      // 处理号码分隔
      handleNumberTimeSplit(filterValidNumber) {
        const poiltAddressServiceNO = this.localSRDTableData[0].SERVICE_NO;
        const uniqueItems = poiltAddressServiceNO.filter(item => !filterValidNumber.includes(item));

        // 新增表格栏并删除第一行已被选择的号码
        this.localSRDTableData.push({
          SB_NO: this.addressInfo?.SB_NO ?? '',
          masterAddress: this.addressInfo?.masterAddress ?? '',
          SERVICE_NO: filterValidNumber,
          SRD: '',
          appointmentDateWithTime: '',
          preWiringDateWithTime: '',
          addressInfo: this.addressInfo ?? '',
        });
        this.localSRDTableData[0].SERVICE_NO = uniqueItems;

        this.splitServiceNoVisible = false;
      },
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          this.localSRDTableData.forEach(item => {
            if (!item.SRD) {
              this.showTipModal(this.$t('fulfillmentInfo.srdNoNull'));
            }
            // 若新增地址，不用通过预约装机
            if (item.SB_NO) {
              // 当不是编辑订单且预约日期为空时，显示提示弹窗
              if (!item.isEditOrderFlag && !item.APPOINTMENTDATE) {
                this.showTipModal(this.$t('fulfillmentInfo.constructionReservationTips'));
              }
              if (!item.SRDIsGreaterThanBusinessTime) {
                this.showTipModal(this.$t('fulfillmentInfo.dateComparisonVerification'));
              }
            }
          });
          resolve();
        });
      },
    },
    beforeDestroy() {
      // 在组件销毁前清除定时器
      if (this.checkResultInterval) {
        clearInterval(this.checkResultInterval);
      }
    },
  };
</script>

<style lang="less" scoped>
  .customerContact {
    margin-bottom: 20px;
    .required {
      color: #f5222d;
      font-size: 14px;
    }
    .dateIcon {
      width: 16px;
      height: 16px;
      margin-top: -8px;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
    .actionColumns {
      display: flex;
      justify-content: space-evenly;
    }
    .ellipsis-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px; /* 设置最大宽度 */
    }
  }
</style>
