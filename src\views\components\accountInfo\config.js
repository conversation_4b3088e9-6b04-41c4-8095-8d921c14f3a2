import that from '@/main.js';
export default {
  columns: [
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_NO',
      key: 'ACCOUNT_NO',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA_NAME',
      key: 'BILL_MEDIA_NAME',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD_NAME',
      key: 'PAYMENT_METHOD_NAME',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCT_FAMILY_NAME',
      key: 'PRODUCT_FAMILY_NAME',
    },
    {
      title: that.$t('orderSummary.chargeCategory'),
      dataIndex: 'chargeCategory',
      key: 'chargeCategory',
    },
  ],
  accountColumns: [
    {
      title: that.$t('accountSetting.serviceNo'),
      dataIndex: 'SERIAL_NUMBER',
      key: 'SERIAL_NUMBER',
    },
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_ID',
      key: 'ACCOUNT_ID',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA',
      key: 'BILL_MEDIA',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD',
      key: 'PAYMENT_METHOD',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCE_FAMILY',
      key: 'PRODUCE_FAMILY',
    },
    {
      title: that.$t('orderSummary.chargeCategory'),
      dataIndex: 'CHARGE_CATEGORY',
      key: 'CHARGE_CATEGORY',
    },
  ],
};
