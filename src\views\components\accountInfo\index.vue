<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('orderSummary.accountInfo') }}</div>
    <a-table :columns="filteredColumns" :data-source="datas" :pagination="false" />
    <a-divider />
  </div>
</template>
<script>
  import config from './config';
  export default {
    props: {
      datas: {
        type: Array,
        default: () => [],
      },
      showServiceNo: {
        type: Boolean,
        default: false,
      },
      customColumns: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        columns: config.columns || [],
        accountColumns: config.accountColumns || [],
      };
    },
    computed: {
      filteredColumns() {
        // 若传入 customColumns 且长度不为 0，优先使用 customColumns
        if (this.customColumns && this.customColumns.length > 0) {
          return this.customColumns;
        }
        // 否则保持原逻辑
        return this.showServiceNo ? this.accountColumns : this.columns;
      },
    },
  };
</script>
