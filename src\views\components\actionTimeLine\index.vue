<template>
  <div class="action-timeline">
    <div class="action-divider" v-if="ifShowExpand">
      <div class="divider-line"></div>
      <div class="divider-bump" @click.stop="expandTimeLine">
        <em class="iconfont icon-xiala bump-icon" :class="!ifExpand ? 'expand-rotate' : ''"></em>
      </div>
    </div>

    <!-- 标题 -->
    <div class="scroll-height" :class="ifExpand ? 'expanded' : ''">
      <div class="secondLevel-header-title" v-if="ifShowTitle">
        {{ $t('quotation.Timeline') }}
      </div>

      <div class="timelineBox-wrap">
        <div class="timelineBox" id="box">
          <div class="timeline-up">
            <div class="timePoint" v-for="(point, index) in showData" :key="index">
              <ul class="list">
                <LiTemplate
                  v-for="(pointItem, pointIndex) in point.showCols"
                  :key="`${index}_${pointIndex}`"
                  :data="pointItem"
                >
                  {{ point[pointItem.key] }}
                </LiTemplate>
              </ul>
            </div>
          </div>
          <div class="timeline">
            <div class="timePoint" v-for="(point, index) in showData" :key="index">
              <div class="listItem">
                <div :class="['point', point.circleClass]">{{ index + 1 }}</div>
                <span class="line"></span>
              </div>
            </div>
          </div>
          <div class="timeline-down">
            <div class="timePoint" v-for="(point, index) in showData" :key="index">
              <ul class="list">
                <LiTemplate
                  v-for="(pointItem, pointIndex) in point.showCols"
                  :key="`${index}_${pointIndex}`"
                  :data="pointItem"
                >
                  {{ point[pointItem.key] }}
                </LiTemplate>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  import { orderTrackRecord } from '@/api/quotation';
  import moment from 'moment';

  export default {
    name: 'actionTimeLine',
    components: {
      LiTemplate: {
        functional: true, // 可选，提升性能
        render(h, ctx) {
          return (
            <li class="listItem">
              {ctx.props.data.showTitle ? (
                <span class="sub-title">{ctx.props.data.title}</span>
              ) : null}
              {ctx.props.data.type != 'text' ? (
                <span class={ctx.props.data.class}>{ctx.slots().default}</span>
              ) : (
                <span class={ctx.props.data.class}>{ctx.props.data.content}</span>
              )}
            </li>
          );
        },
      },
    },
    props: {
      // 是否显示标题
      ifShowTitle: {
        type: Boolean,
        default: true,
      },
      // 是否显示展开收缩按钮
      ifShowExpand: {
        type: Boolean,
        default: true,
      },
      orderId: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        ifExpand: true,
        showData: [],
      };
    },
    watch: {
      orderId(val) {
        this.init();
      },
    },
    mounted() {
      // let _this = this;
      // _this.setWidth();
      // window.addEventListener('resize', function () {
      //   _this.setWidth();
      // });
      this.init();
    },
    methods: {
      // setWidth() {
      //   this.mainWidth = document.getElementById('box').clientWidth - 100;
      //   let listWidth = 110;
      //   let len = this.showData.length;
      //   let n = Math.ceil(len / 2);
      //   // 减去一些边距
      //   listWidth = (this.mainWidth - 20) / n - 10;
      //   this.listWidth = listWidth;
      // },
      async init() {
        // 获取订单轨迹
        const res = await orderTrackRecord({ ORDER_ID: this.orderId });
        if (res.DATA && res.DATA.length) {
          this.showData = this.formatTimelineData(res.DATA);
          console.log(this.showData, res, 'showData');
        }
      },
      // 数据 - 包装
      // 类型 - 【time / staff / action / text】 - 目前按展示的字段区分，以防止后面改动
      // text - 纯文本形式
      formatTimelineData(arr) {
        const STATIC_LI = [
          { type: 'time', class: 'f-14', key: 'ACTION_TIME', showTitle: false },
          {
            type: 'staff',
            class: 'f-16',
            key: 'STAFF_NAME',
            showTitle: false,
            title: this.$t('common.name'),
          },
          {
            type: 'action',
            class: 'f-16',
            key: 'ACTION_TYPE',
            showTitle: true,
            title: this.$t('common.action'),
          },
        ];
        let result = [];

        for (let i = 0; i < arr.length; i++) {
          let result_li = [];
          const ele = arr[i];
          // 格式化 - 时间
          if (ele.ACTION_TIME && ele.ACTION_TIME != '') {
            ele.ACTION_TIME = moment(ele.ACTION_TIME).format('MM/DD/YYYY HH:mm:ss');
          }
          // 判断逻辑 - 显示哪些数据 || 下一个执行人显示
          // #TODO 缺少对应字段 - 判断是否有节点 - 【下个执行人】
          if (ele.ORDER_ID) {
            result_li = result_li.concat(STATIC_LI);
            // 判断是否为最新节点，依据 - 暂时以最后一个为最新节点
            if (i === arr.length - 1) {
              ele.circleClass = 'yellow';
              result_li.find(x => x.type === 'staff').showTitle = true;
              // #TODO - 缺ROLE,高保真上有角色显示
            }
            // 判断状态 - S3【报价单审批拒绝】、S6【AF单审批拒绝】
            // #TODO - 内容key值不一定是remark
            if (ele.CURRENT_STATUS === 'S3' || ele.CURRENT_STATUS === 'S6') {
              result_li.push({
                class: 'f-16',
                type: 'rejectReason',
                key: 'REMARK',
                showTitle: true,
                title: this.$t('quotation.RejectReason'),
              });
            }
          } else {
            ele.circleClass = 'blue';
            result_li.push(
              {
                type: 'text',
                content: this.$t('quotation.NextOperator'),
                class: 'f-14',
              },
              {
                type: 'staff',
                class: 'f-16 fw-500',
                key: 'STAFF_NAME',
                showTitle: false,
              },
            );
          }
          result.push({ ...ele, showCols: { ...result_li } });
        }
        console.log(result, 'timeline - result');
        return result;
      },
      updateInfo() {
        this.init();
      },
      expandTimeLine() {
        this.ifExpand = !this.ifExpand;
      },
    },
  };
</script>

<style lang="less" scoped>
  .action-timeline {
    margin: 30px 0 0;
  }
  .action-divider {
    position: relative;

    padding-bottom: 6.5px;
    .divider-bump {
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      border-left: 1px solid #d9d9d9;
      border-right: 1px solid #d9d9d9;
      border-top: 1px solid #d9d9d9;
      border-bottom: 0;
      background: #fff;
      width: 40px;
      // border-radius: 10px;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      height: 12px;
      // clip-path: inset(0); /* 裁剪出小方块区域 */
      overflow: hidden;
      border-radius: 10px 10px 0 0; /* 只保留下边圆角 */
      clip-path: polygon(100% 100%, 0% 100%, 5% -50%, 95% -50%);
    }
    .divider-line {
      padding-bottom: 6.5px;
      width: 100%;
      border-top: 1px solid #d9d9d9;
      position: absolute;
      top: 2px;
      left: 0;
      background: #fff;
    }
    .bump-icon {
      font-size: 12px;
      color: #333;
      margin-top: -2px;
      cursor: pointer;
      &.expand-rotate {
        transform: rotate(180deg);
      }
    }
  }
  .scroll-height {
    max-height: 0;
    transition: max-height 0.3s ease;
    overflow: hidden;
    &.expanded {
      max-height: 2000px; /* 设置一个足够大的值 */
    }
  }

  .timelineBox-wrap {
    margin: 0 auto;
    overflow: scroll;
    padding: 0 10px;
  }
  .timelineBox {
    .timeline {
      height: 1px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      z-index: 10;
    }
    .list {
      padding: 0;
      margin: 0;
      width: 160px;
      display: flex;
      flex-direction: column;
      .listItem {
        width: 100%;
        line-height: 1.5;
        border-radius: 4px;
        font-size: 13px;
        text-align: left;
        padding: 0 5px;
        color: #333333;
        letter-spacing: 0;
        font-weight: 400;
        .sub-title {
          font-weight: 600;
          font-size: 16px;
          &::after {
            content: ':';
            padding: 0 5px;
          }
        }
        .f-14 {
          font-size: 14px;
        }
        .f-16 {
          font-size: 16px;
        }
        .fw-500 {
          font-size: 500;
        }
      }
    }

    .timePoint {
      width: 160px;
      height: auto;
      .listItem {
        width: 160px;
        max-width: 200px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        position: relative;
      }

      .point {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #e9e9e9;

        font-size: 14px;
        color: #9e9e9e;
        letter-spacing: 0;
        text-align: left;
        font-weight: 400;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1;
        &.blue {
          background-color: #0072ff;
          color: #fff;
        }
        &.yellow {
          background-color: #ffb100;
          color: #fff;
        }
      }
      .line {
        background-color: #d9d9d9;
        height: 1px;
        width: 100%;

        position: absolute;
        z-index: 0;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .timeline-up,
    .timeline-down {
      display: flex;
      .timePoint {
        display: flex;
        justify-content: center;
        .listItem {
          width: 160px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }
      }
    }
    .timeline-up {
      padding-bottom: 22px;
      .list {
        // flex-direction: column-reverse;
        .listItem {
          margin-top: 8px;
        }
        &:before {
          bottom: -44px;
        }
      }
      .timePoint:nth-child(2n) {
        visibility: hidden;
      }
    }
    .timeline-down {
      padding-top: 22px;
      .list {
        .listItem {
          margin-bottom: 8px;
        }
        &:before {
          top: -44px;
          z-index: 0;
        }
      }
      .timePoint:nth-child(odd) {
        visibility: hidden;
      }
    }
  }
</style>
