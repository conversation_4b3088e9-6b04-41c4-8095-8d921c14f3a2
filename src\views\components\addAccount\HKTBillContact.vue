/** HKT Bill Contact */
<template>
  <div class="HKTContact">
    <div class="commonTitle">{{ $t('accountManager.HKTBillContact') }}</div>
    <a-table :columns="columns" :data-source="dataList" rowKey="index" :pagination="false">
      <span slot="type" slot-scope="record, index">
        <a-select
          v-model="record.PARTICIPANTS_TYPE"
          :placeholder="$t('common.selectPlaceholder')"
          allowClear
          style="minwidth: 150px; width: 100%"
        >
          <a-select-option v-for="item in typeList" :key="item.CODE_VALUE" :value="item.CODE_VALUE">
            {{ item.CODE_NAME }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="name" slot-scope="record, index">
        <a-input v-model="record.NAME" :placeholder="$t('common.pleaseEnter')" />
      </span>
      <span slot="contact" slot-scope="record, index">
        <a-input v-model="record.CONTACT_PHONE" :placeholder="$t('common.pleaseEnter')" />
      </span>
      <span slot="email" slot-scope="record, index">
        <a-input v-validateEmail v-model="record.EMAIL" :placeholder="$t('common.pleaseEnter')" />
      </span>
      <span slot="action">
        <R-popover @onConfirm="handleDeleteContact()" :content="$t('common.deleteConfirm')">
          <template slot="button">
            <a class="iconfont icon-shanchu"></a>
          </template>
        </R-popover>
      </span>
    </a-table>
    <div class="listAdd" @click="onAdd" v-show="!dataList.length">+ {{ $t('common.add') }}</div>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  import { HKTBillContactColumns } from './config';
  import { queryCreateCustomerEnum } from '@/request/customer';
  import { validateHKTPhone, validateEmail } from '@/utils/utils';
  import MessageModal from '@/components/messageModal';

  export default {
    components: {
      MessageModal,
    },
    data() {
      return {
        columns: HKTBillContactColumns,
        // TODO :工号信息后续替换
        dataList: [],
        tipsVisible: false,
        tipsMessage: '',
        typeList: [],
      };
    },
    mounted() {
      // 编辑，回填数据
      this.getTypeList();
      if (this.$route.query?.type === 'update' || this.$route.query?.type === 'copy') {
        const accountData = this.$store.state.account.accountData;
        let list = accountData.ACCOUNT_CONTACT_LIST?.[0];
        this.dataList = [
          {
            PARTICIPANTS_TYPE: list?.PARTICIPANTS_TYPE,
            NAME: list.NAME,
            CONTACT_PHONE: list.CONTACT_PHONE,
            EMAIL: list.EMAIL,
            MODIFY_TAG: '2',
            'UPDATE_STAFF_ID': '111111',
            'CREATE_STAFF_ID': '111111',
            'CREATE_STAFF_NAME': '小王',
            'UPDATE_STAFF_NAME': '小王',
          },
        ];
      }
    },
    methods: {
      // 查枚举数据
      async getTypeList() {
        const params = {
          'CODE_TYPE': 'PARTICIPANTS_TYPE',
          // "CODE_TYPE": "PARTICIPANTS_TYPE_BILL"
        };
        let res = await queryCreateCustomerEnum(params);
        if (res?.STATUS === '0000') {
          this.typeList = res.RSP?.DATA || [];
        }
      },

      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },

      // 返回校验过的contact list
      returnContactList() {
        // 校验type字段是否为空,邮箱\手机号格式
        // 联系人可为空，不校验，如果设置了联系人，则需要校验
        if (!this.dataList.length) {
          return [];
        }
        // TODO： 手机号码校验暂时去掉
        // if(!this.dataList[0].PARTICIPANTS_TYPE) {
        //   this.showTipModal(this.$t('accountManager.billTypeRequired'));
        //   return false;
        // } else if(this.dataList[0].CONTACT_PHONE && !validateHKTPhone(this.dataList[0].CONTACT_PHONE)) {
        //   this.showTipModal(this.$t('accountManager.phoneIsRequired'));
        //   return false;
        // } else if(this.dataList[0].EMAIL && !validateEmail(this.dataList[0].EMAIL)) {
        //   this.showTipModal(this.$t('accountSetting.emailIsRequired'));
        //   return false;
        // }
        if (!this.dataList?.[0]?.PARTICIPANTS_TYPE) {
          this.showTipModal(this.$t('accountSetting.billTypeRequired'));
          return false;
        } else if (this.dataList?.[0]?.EMAIL && !validateEmail(this.dataList?.[0]?.EMAIL)) {
          this.showTipModal(this.$t('accountSetting.emailIsRequired'));
          return false;
        }
        return this.dataList;
      },

      // 新装联系人
      onAdd() {
        const { type } = this.$route.query;
        this.dataList = [
          {
            PARTICIPANTS_TYPE: undefined,
            NAME: '',
            CONTACT_PHONE: '',
            EMAIL: '',
            MODIFY_TAG: type === 'update' ? '2' : '0',
            UPDATE_STAFF_ID: '111111',
            CREATE_STAFF_ID: '111111',
            CREATE_STAFF_NAME: '小王',
            UPDATE_STAFF_NAME: '小王',
          }, // MODIFY_TAG 0: 新增，2：更改（默认新增）
        ];
      },

      // 删除联系人
      handleDeleteContact() {
        this.dataList = [];
      },
    },
  };
</script>

<style lang="less" scoped>
  .HKTContact {
    margin-bottom: 20px;
    .commonTitle {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .listAdd {
      margin-top: 10px;
      width: 100%;
      height: 40px;
      background-color: #ffffff;
      border: 1px dashed #0076ff;
      color: #0076ff;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
  }
</style>
