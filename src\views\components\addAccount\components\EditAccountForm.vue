<template>
  <div>
    <a-form-model
      :model="form"
      ref="editAccountFormRef"
      :rules="isEbillMedia ? emailUpdateFormRules : updateRules"
    >
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.accountName')" prop="ACCOUNT_NAME">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.ACCOUNT_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.LOB')" prop="LOB">
            <a-select
              v-model="form.LOB"
              :placeholder="$t('common.selectPlaceholder')"
              @change="handleLOBChange"
              allowClear
            >
              <a-select-option v-for="item in lobList" :key="item.LOB_NAME" :value="item.LOB">
                {{ item.LOB_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.productFamily')" prop="PRODUCT_FAMILY">
            <a-select
              v-model="form.PRODUCT_FAMILY"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option
                v-for="item in productFamilyList"
                :key="item.PRODUCT_FAMILY"
                :value="item.PRODUCT_FAMILY"
              >
                {{ item.PRODUCT_FAMILY_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <!-- bill day字段新增由后端生成，修改的时候放开该字段给用户修改 -->
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billDay')" prop="BILL_DAY">
            <a-select
              v-model="form.BILL_DAY"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in billDayList" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <!-- Bill Account No字段修改的时候也放开展示，只读 -->
          <a-form-model-item :label="$t('accountSetting.billAccountNo')">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.ACCT_ID"
              :placeholder="$t('common.inputPlaceholder')"
              disabled
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billContact')">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.BILL_CONTACT"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billRecipient')">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.BILL_RECIPIENT"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billEmail')" prop="BILL_EMAIL">
            <a-input
              v-validateEmailMore
              v-model.trim="form.BILL_EMAIL"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import config from '../config';
  import { getProductFamilyList } from '@/utils';
  import { lobQuery } from '@/api/customerVerify';

  export default {
    name: 'EditAccountForm',
    data() {
      return {
        updateRules: config.updateRules,
        emailUpdateFormRules: config.emailUpdateFormRules,
        billDayList: config.billDayList,
        form: {
          ACCOUNT_NAME: '',
          LOB: '',
          PRODUCT_FAMILY: '',
          BILL_CONTACT: '',
          BILL_RECIPIENT: '',
          BILL_EMAIL: '',
          BILL_DAY: '',
          ACCT_ID: '',
        },
        productFamilyList: [],
        lobList: [],
      };
    },
    computed: {
      // media 选择了Ebill，则校验邮箱格式
      isEbillMedia() {
        return this.$store.state.orderCapture.isEbillMedia;
      },
    },
    mounted() {
      this.handleInitLOBData();
      this.handleLOBQuery();
    },
    methods: {
      // 获取lob，存入store
      async handleLOBQuery() {
        try {
          const res = await lobQuery();
          this.lobList = res.DATA;
        } catch (error) {
          console.log(error);
        }
      },
      // 按接口字段组装数据
      handleformatData() {
        let flag = true;
        let data = {};
        this.$refs.editAccountFormRef.validate(valid => {
          if (!valid) {
            flag = false;
          } else {
            data = this.form;
          }
        });
        return flag ? data : flag;
      },
      // LOB切换,PRODUCT_FAMILY默认会置空
      async handleLOBChange(value, type = 'default') {
        try {
          this.productFamilyList = await getProductFamilyList({ LOB: value });
        } catch (error) {
          console.log(error);
        }
        if (type === 'default') {
          this.form.PRODUCT_FAMILY = '';
        }
      },
    },
  };
</script>

<style scoped></style>
