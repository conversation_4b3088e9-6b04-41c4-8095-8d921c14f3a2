/** HKT Bill Contact */
<template>
  <div class="HKTContact">
    <div class="commonTitle">{{ $t('accountSetting.creditAdmin') }}</div>
    <a-table :columns="columns" :data-source="dataList" rowKey="index" :pagination="false">
      <template slot="creditType">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.type') }}
      </template>
      <template slot="creditName">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.name') }}
      </template>
      <span slot="type" slot-scope="record, index">
        <a-select
          v-model="record.PARTICIPANTS_TYPE"
          :placeholder="$t('common.selectPlaceholder')"
          allowClear
          style="minwidth: 150px; width: 100%"
        >
          <a-select-option v-for="item in typeList" :key="item.DATA_ID" :value="item.DATA_ID">
            {{ item.DATA_NAME }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="name" slot-scope="record, index">
        <a-input
          v-containsSqlInjection
          v-model.trim="record.NAME"
          :maxLength="10"
          :placeholder="$t('common.inputPlaceholder')"
          :disabled="record.NAME_DISABLED"
        />
      </span>
      <span slot="staffId" slot-scope="record, index">
        <div class="inputSearch">
          <a-input
            v-containsSqlInjection
            class="input"
            v-model.trim="record.STAFF_ID"
            :placeholder="$t('common.inputPlaceholder')"
          />
          <i class="searchIcon iconfont icon-sousuo" @click="clickChange(record, 'STAFF_ID')"></i>
        </div>
      </span>
      <span slot="contact" slot-scope="record, index">
        <a-input
          v-validate-number
          v-model="record.CONTACT_PHONE"
          :placeholder="$t('common.inputPlaceholder')"
          :disabled="record.CONTACT_PHONE_DISABLED"
        />
      </span>
      <span slot="email" slot-scope="record, index">
        <a-input
          v-validateEmail
          v-model.trim="record.EMAIL"
          :placeholder="$t('common.inputPlaceholder')"
          :disabled="record.EMAIL_DISABLED"
        />
      </span>
      <span slot="action">
        <Rpopover @onConfirm="handleDeleteContact()" :content="$t('common.deleteConfirm')">
          <template slot="button">
            <i class="iconfont icon-shanchu"></i>
          </template>
        </Rpopover>
      </span>
    </a-table>
    <div class="listAdd" @click="onAdd" v-show="!dataList.length">+ Add</div>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  import config from '../config';
  import { validateHKTPhone, validateEmail } from '@/utils/utils';
  import MessageModal from '@/components/messageModal';
  import Rpopover from '@/components/Rpopover';
  import { qryStaticList } from '@/api/accountSetting';
  import { staffQuery } from '@/api/common';

  export default {
    name: 'HKTBillContact',
    components: {
      MessageModal,
      Rpopover,
    },
    data() {
      return {
        columns: config.HKTBillContactColumns,
        // TODO :工号信息后续替换
        dataList: [],
        tipsVisible: false,
        tipsMessage: '',
        typeList: [],
      };
    },
    mounted() {
      // 编辑，回填数据
      this.getTypeList();
    },
    methods: {
      // 查枚举数据
      async getTypeList() {
        const params = {
          TYPE_ID: 'PARTICIPANTS_TYPE',
        };
        let res = await qryStaticList(params);
        this.typeList = res?.DATA || [];
      },

      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },

      // 返回校验过的contact list
      returnContactList() {
        // 校验type字段是否为空,邮箱\手机号格式
        // 联系人可为空，不校验，如果设置了联系人，则需要校验
        if (!this.dataList.length) {
          return [];
        }
        if (!this.dataList?.[0]?.PARTICIPANTS_TYPE) {
          this.showTipModal(this.$t('accountSetting.billTypeRequired'));
          return false;
        } else if (!this.dataList?.[0]?.NAME) {
          this.showTipModal(this.$t('accountSetting.nameRequired'));
          return false;
        } else if (this.dataList?.[0]?.EMAIL && !validateEmail(this.dataList?.[0]?.EMAIL)) {
          this.showTipModal(this.$t('accountSetting.emailIsRequired'));
          return false;
        }
        return this.dataList;
      },

      // 新装联系人
      onAdd() {
        this.dataList = [
          {
            PARTICIPANTS_TYPE: undefined,
            NAME: '',
            CONTACT_PHONE: '',
            EMAIL: '',
            MODIFY_TAG: '0',
            UPDATE_STAFF_ID: this.$store.state.app?.userInfo?.STAFF_ID || '',
            CREATE_STAFF_ID: this.$store.state.app?.userInfo?.STAFF_ID || '',
            CREATE_STAFF_NAME: this.$store.state.app?.userInfo?.STAFF_NAME || '',
            UPDATE_STAFF_NAME: this.$store.state.app?.userInfo?.STAFF_NAME || '',
          }, // MODIFY_TAG 0: 新增，2：更改（默认新增）
        ];
      },

      // 删除联系人
      handleDeleteContact() {
        this.dataList = [];
      },
      // 点击搜索
      async clickChange(record, key) {
        // 未输入内容 不可点击
        if (!record.STAFF_ID) {
          return;
        }

        const params = {
          STAFF_ID: record.STAFF_ID,
        };
        try {
          const res = await staffQuery(params);
          let obj = res.DATA[0]?.STAFF_INFO || {};
          // 搜索的结果不可修改
          this.updateRecord(record, {
            NAME: obj.STAFF_NAME,
            NAME_DISABLED: Boolean(obj.STAFF_NAME),

            CONTACT_PHONE: obj.CONTACT_PHONE,
            CONTACT_PHONE_DISABLED: Boolean(obj.CONTACT_PHONE),

            EMAIL: obj.EMAIL,
            EMAIL_DISABLED: Boolean(obj.EMAIL),
          });
        } catch (error) {
          console.log(error);
        }
      },
      // 更新record对象里面的数据
      updateRecord(record, updates) {
        Object.keys(updates).forEach(key => {
          this.$set(record, key, updates[key]);
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .HKTContact {
    margin-bottom: 20px;
    .commonTitle {
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .listAdd {
      margin-top: 10px;
      width: 100%;
      height: 40px;
      background-color: #ffffff;
      border: 1px dashed #0076ff;
      color: #0076ff;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
    .required {
      color: #f5222d;
      font-size: 14px;
    }
    .inputSearch {
      position: relative;
      .input {
        padding-right: 30px;
      }
      .searchIcon {
        position: absolute;
        left: 40%;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #666;
      }
    }
  }
</style>
