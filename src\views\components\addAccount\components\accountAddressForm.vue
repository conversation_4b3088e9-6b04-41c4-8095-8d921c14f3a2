<template>
  <div>
    <a-form-model
      :model="standardAddressForm"
      :rules="rules"
      v-show="isStandard"
      ref="accountAddressForm"
    >
      <a-row>
        <a-col :span="12">
          <a-form-model-item :colon="false" prop="ADDRESS_EN" class="billAddress">
            <template v-slot:label>
              <span style="margin-right: 10px">{{ $t('accountSetting.billAddress') }}</span>
              <a-button type="primary" size="small" @click="handleOpenSplitAddress">{{
                $t('accountSetting.splitAddress')
              }}</a-button>
            </template>
            <a-input
              :maxLength="160"
              v-containsSqlInjection
              v-model.trim="standardAddressForm.ADDRESS_EN"
              :placeholder="$t('accountSetting.EN')"
            />
          </a-form-model-item>
          <a-input
            style="margin-top: 10px"
            v-model="standardAddressForm.ADDRESS_ZH"
            :placeholder="$t('accountSetting.ZH')"
          />
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.envelopeDisplay')" required>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE1_EN"
                  :placeholder="$t('accountSetting.EN')"
                  :maxLength="40"
                  @blur="handleCheckEnvelope"
                />
              </a-col>
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE1_ZH"
                  :placeholder="$t('accountSetting.ZH')"
                  :maxLength="40"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE2_EN"
                  :placeholder="$t('accountSetting.EN')"
                  :maxLength="40"
                  @blur="handleCheckEnvelope"
                />
              </a-col>
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE2_ZH"
                  :placeholder="$t('accountSetting.ZH')"
                  :maxLength="40"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE3_EN"
                  :placeholder="$t('accountSetting.EN')"
                  :maxLength="40"
                  @blur="handleCheckEnvelope"
                />
              </a-col>
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE3_ZH"
                  :placeholder="$t('accountSetting.ZH')"
                  :maxLength="40"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE4_EN"
                  :placeholder="$t('accountSetting.EN')"
                  :maxLength="40"
                  @blur="handleCheckEnvelope"
                />
              </a-col>
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="standardAddressForm.ADDRESS_LINE4_ZH"
                  :placeholder="$t('accountSetting.ZH')"
                  :maxLength="40"
                />
              </a-col>
            </a-row>
            <div v-show="envelopeRequired" class="required-text">
              {{ $t('accountSetting.EN') }} {{ $t('accountSetting.envelopeDisplay') }}
              {{ $t('common.isRequired') }}
            </div>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <a-form-model :model="noStandardAddressForm" v-show="!isStandard">
      <a-row>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.POBox')" :colon="false" prop="PO_BOX">
            <a-input
              v-containsSqlInjection
              v-model.trim="noStandardAddressForm.PO_BOX"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.overseaAddress')" prop="Address">
            <a-row :gutter="16">
              <a-col :span="24">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="noStandardAddressForm.ADDRESS_LINE1_EN"
                  :placeholder="$t('accountSetting.EN')"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="noStandardAddressForm.ADDRESS_LINE2_EN"
                  :placeholder="$t('accountSetting.EN')"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="noStandardAddressForm.ADDRESS_LINE3_EN"
                  :placeholder="$t('accountSetting.EN')"
                />
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="noStandardAddressForm.ADDRESS_LINE4_EN"
                  :placeholder="$t('accountSetting.EN')"
                />
              </a-col>
            </a-row>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- Split Address 弹窗 -->
    <a-modal
      :title="$t('accountSetting.addressMapping')"
      width="80%"
      destroyOnClose
      :footer="null"
      :visible="selectAddressVisible"
      @cancel="selectAddressCancel"
    >
      <SelectAddress
        @cancel="selectAddressCancel"
        @ok="handleConfirmAddress"
        :showAddress="true"
        :needQuerySBList="false"
      />
    </a-modal>
  </div>
</template>

<script>
  import { splitAddress } from '@/api/common';
  import SelectAddress from '@/views/components/selectAddress';

  export default {
    name: 'accountAddressForm',
    components: {
      SelectAddress,
    },
    data() {
      return {
        isStandard: true,
        addAddressVisible: false,
        standardAddressForm: {
          ADDRESS_EN: '',
          ADDRESS_ZH: '',
          ADDRESS_LINE1_EN: '',
          ADDRESS_LINE1_ZH: '',
          ADDRESS_LINE2_EN: '',
          ADDRESS_LINE2_ZH: '',
          ADDRESS_LINE3_EN: '',
          ADDRESS_LINE3_ZH: '',
          ADDRESS_LINE4_EN: '',
          ADDRESS_LINE4_ZH: '',
        },
        noStandardAddressForm: {
          PO_BOX: '',
          ADDRESS_LINE1_EN: '',
          ADDRESS_LINE2_EN: '',
          ADDRESS_LINE3_EN: '',
          ADDRESS_LINE4_EN: '',
        },
        selectAddressVisible: false,
        rules: {
          ADDRESS_EN: [
            { required: true, message: this.$t('accountSetting.AddressEnMsg'), trigger: 'blur' },
          ],
        },
        envelopeRequired: false,
      };
    },
    methods: {
      toggleEnvelopeRequired(envelopeRequired) {
        this.envelopeRequired = envelopeRequired;
      },
      handleCheckEnvelope() {
        const { ADDRESS_LINE1_EN, ADDRESS_LINE2_EN, ADDRESS_LINE3_EN, ADDRESS_LINE4_EN } =
          this.standardAddressForm;
        // 四个英文值都为空的时候，进行提示
        if (!ADDRESS_LINE1_EN && !ADDRESS_LINE2_EN && !ADDRESS_LINE3_EN && !ADDRESS_LINE4_EN) {
          this.toggleEnvelopeRequired(true);
        } else {
          this.toggleEnvelopeRequired(false);
        }
      },
      // 选址 弹窗 关闭
      selectAddressCancel() {
        this.selectAddressVisible = false;
      },
      // 标准地址和非标准地址处理,按接口字段组装数据
      handleformatAddressData() {
        return this.isStandard ? this.standardAddressForm : this.noStandardAddressForm;
      },

      // 标准地址切割回填
      async handleConfirmAddress(row) {
        // BID存在则为在已有的地址选择,否则即为新建地址
        // 确保 row.masterAddress 是一个对象
        const masterAddress = row.masterAddress || {};
        console.log(masterAddress, 'masterAddress---');

        // 检查 masterAddress.BID 是否存在
        if (masterAddress.BID) {
          try {
            const parmas = {
              GEOSEQ: row.GEOSEQ,
              FLOOR: row.FLOOR,
              FLAT: row.FLAT,
            };
            const res = await splitAddress(parmas);
            const detailsEN = res.DATA?.[0]?.['MAIL_DETAILS_EN'];
            const detailsZH = res.DATA?.[0]?.['MAIL_DETAILS_ZH'];
            // const EN_ADDR1 = row?.EN_ADDR1 ? row?.EN_ADDR1 + ',' : '';
            // const EN_ADDR3 = row?.EN_ADDR3 ? row?.EN_ADDR3 + ',' : '';
            const data = {
              // EN_ADDR1: row?.EN_ADDR1 ?? '',
              // EN_ADDR3: row?.EN_ADDR3 ?? '', //单位
              ADDRESS_EN: res.DATA?.[0]?.['DETAILS_EN']?.['ADDRESS'],
              ADDRESS_ZH: res.DATA?.[0]?.['DETAILS_ZH']?.['ADDRESS'],
              ADDRESS_LINE1_EN: detailsEN?.[0],
              ADDRESS_LINE2_EN: detailsEN?.[1],
              ADDRESS_LINE3_EN: detailsEN?.[2],
              ADDRESS_LINE4_EN: detailsEN?.[3],
              ADDRESS_LINE1_ZH: detailsZH?.[0],
              ADDRESS_LINE2_ZH: detailsZH?.[1],
              ADDRESS_LINE3_ZH: detailsZH?.[2],
              ADDRESS_LINE4_ZH: detailsZH?.[3],
            };
            this.standardAddressForm = data;
            this.selectAddressVisible = false;
          } catch (error) {
            console.log(error);
          }
        } else {
          this.standardAddressForm = {
            // EN_ADDR1: row?.EN_ADDR1 ?? '',
            // EN_ADDR3: row?.EN_ADDR3 ?? '',
            ADDRESS_EN: row.SB_ADDR,
            ADDRESS_ZH: '',
            ADDRESS_LINE1_EN: '',
            ADDRESS_LINE1_ZH: '',
            ADDRESS_LINE2_EN: '',
            ADDRESS_LINE2_ZH: '',
            ADDRESS_LINE3_EN: '',
            ADDRESS_LINE3_ZH: '',
            ADDRESS_LINE4_EN: '',
            ADDRESS_LINE4_ZH: '',
          };
          this.selectAddressVisible = false;
        }
      },

      // 打开地址弹窗
      handleOpenSplitAddress() {
        this.selectAddressVisible = true;
      },

      validate() {
        let validateFlag = true;
        // 标准地址校验--Bill Address Type选择Standard
        if (this.isStandard) {
          this.rules = {
            ADDRESS_EN: [
              { required: true, message: this.$t('accountSetting.AddressEnMsg'), trigger: 'blur' },
            ],
          };
          this.handleCheckEnvelope();
        } else {
          this.rules = {};
        }
        this.$refs.accountAddressForm.validate(valid => {
          validateFlag = valid;
        });
        console.log(validateFlag, '---Bill Address Type---校验');
        return validateFlag;
      },
    },
  };
</script>

<style lang="less" scoped>
  .billAddress {
    /deep/.ant-form-item-label {
      margin-bottom: 0px;
      margin-top: 0px;
    }
  }
  .ant-input {
    margin-top: 10px;
  }
  // 定义动画，名为 fadeInDown
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .required-text {
    position: absolute;
    margin-top: -2px;
    font-size: 14px;
    line-height: 1.5;
    color: #f5222d;
    transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    animation: fadeInDown 0.3s forwards;
  }
</style>
