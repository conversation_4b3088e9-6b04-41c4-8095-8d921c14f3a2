<template>
  <div class="checkForm">
    <a-form-model :model="checkForm" layout="inline" :rules="checkFormRules" ref="checkFormRef">
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billMedia')" prop="BILL_MEDIA">
            <a-checkbox-group v-model="checkForm.BILL_MEDIA" @change="handleBillMediaChange">
              <a-checkbox
                v-for="item in billTypeList"
                :key="item.DATA_ID"
                :value="item.DATA_ID"
                name="type"
              >
                {{ item.DATA_NAME }}
              </a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billLanguage')" prop="BILL_LANGUAGE">
            <a-checkbox-group v-model="checkForm.BILL_LANGUAGE">
              <a-checkbox
                v-for="item in billLanguageTypeList"
                :key="item.DATA_ID"
                :value="item.DATA_ID"
                name="type"
              >
                {{ item.DATA_NAME }}
              </a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billFrequence')" prop="BILL_FREQUENCY">
            <a-radio-group
              :options="billFrequenceOption"
              v-model="checkForm.BILL_FREQUENCY"
            ></a-radio-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.waivedPaperBillFee')"
            prop="WAIVED_PAPER_BILL_FEE"
          >
            <a-switch
              v-model="checkForm.WAIVED_PAPER_BILL_FEE"
              :disabled="switchDisabled"
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            />
            <!-- <a-switch
              v-model="checkForm.WAIVED_PAPER_BILL_FEE"
              disabled
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            /> -->
            <!-- <span v-if="checkForm.WAIVED_PAPER_BILL_FEE " class="iconfont  icon-Yesbiaoqian"></span>
            <span v-else class="iconfont icon-Nobiaoqian"></span> -->
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.includeOddCentsInBill')"
            prop="INCLUDE_ODD_CENTS_IN_BILL"
          >
            <a-switch
              v-model="checkForm.INCLUDE_ODD_CENTS_IN_BILL"
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            />
            <!-- <a-switch
              disabled
              v-model="checkForm.INCLUDE_ODD_CENTS_IN_BILL"
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            /> -->
            <!-- <span v-if="checkForm.INCLUDE_ODD_CENTS_IN_BILL" class="iconfont  icon-Yesbiaoqian"></span>
            <span v-else class="iconfont icon-Nobiaoqian"></span> -->
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.displaybill')"
            prop="DISPLAY_ZERO_BILL_ITEMS"
          >
            <a-switch
              v-model="checkForm.DISPLAY_ZERO_BILL_ITEMS"
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            />
            <!-- <a-switch
              disabled
              v-model="checkForm.DISPLAY_ZERO_BILL_ITEMS"
              :checked-children="$t('common.yes')"
              :un-checked-children="$t('common.no')"
            /> -->
            <!-- <span v-if="checkForm.DISPLAY_ZERO_BILL_ITEMS" class="iconfont  icon-Yesbiaoqian"></span>
            <span v-else class="iconfont icon-Nobiaoqian"></span> -->
            <a-popover>
              <template slot="content">
                <ul style="color: #333333">
                  <li>{{ $t('accountSetting.displayBillTip1') }}</li>
                  <li>{{ $t('accountSetting.displayBillTip2') }}</li>
                  <li>{{ $t('accountSetting.displayBillTip3') }}</li>
                </ul>
              </template>
              <a-icon
                type="info-circle"
                style="color: #0072ff; font-size: 16px; margin-left: 6px"
              />
            </a-popover>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-divider dashed />
      <a-row>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.billAddressType')" prop="BILL_ADDRESS_TYPE">
            <a-radio-group
              :options="billAddressOption"
              v-model="checkForm.BILL_ADDRESS_TYPE"
              @change="handleChangeAddressType"
            ></a-radio-group>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 账户地址切割 -->
    <accountAddressForm ref="accountAddressFormRef" />
  </div>
</template>

<script>
  import config from '../config';
  import accountAddressForm from './accountAddressForm.vue';
  import { qryStaticList } from '@/api/accountSetting';

  export default {
    name: 'accountcheckForm',
    components: {
      accountAddressForm,
    },
    data() {
      return {
        checkForm: {
          BILL_MEDIA: [],
          BILL_LANGUAGE: [],
          BILL_FREQUENCY: '1',
          BILL_ADDRESS_TYPE: '1',
          // WAIVED_PAPER_BILL_FEE: true,
          // INCLUDE_ODD_CENTS_IN_BILL: false,
          // DISPLAY_ZERO_BILL_ITEMS: true,
          // R1上线前调整
          WAIVED_PAPER_BILL_FEE: true,
          INCLUDE_ODD_CENTS_IN_BILL: false,
          DISPLAY_ZERO_BILL_ITEMS: false,
        },
        switchDisabled: false,
        billTypeList: [],
        billLanguageTypeList: [],
        billFrequenceOption: [],
        billAddressOption: [],
        checkFormRules: config.checkFormRules,
      };
    },
    mounted() {
      this.handleInitStaticData();
    },
    methods: {
      // 获取接口枚举
      async handleInitStaticData() {
        this.billTypeList = await this.handleStaticList('BILL_MEDIA_TYPE');
        this.billLanguageTypeList = await this.handleStaticList('BILL_LANGUAGE_TYPE');
        const f = this.billLanguageTypeList.map(f => f.DATA_ID);
        this.checkForm.BILL_LANGUAGE = f;
        const billFrequenceList = await this.handleStaticList('BILL_FREQUENCY_TYPE');
        this.billFrequenceOption = billFrequenceList?.map(x => {
          return { value: x.DATA_ID, label: x.DATA_NAME };
        });
        const billAddressList = await this.handleStaticList('BILL_ADDRESS_TYPE');
        this.billAddressOption = billAddressList?.map(x => {
          return { value: x.DATA_ID, label: x.DATA_NAME };
        });
      },

      // 枚举请求方法统一封装
      async handleStaticList(TYPE_ID) {
        const res = await qryStaticList({ TYPE_ID });
        return res.DATA;
      },

      // 按接口字段组装数据
      handleformatData() {
        let flag = true;
        let data = {};
        const validateFlag = this.$refs.accountAddressFormRef.validate();
        const h = this.$refs.accountAddressFormRef.handleformatAddressData();
        // 当Bill Address Type选择Standard检验
        if (!validateFlag) return;
        if (this.checkForm.BILL_ADDRESS_TYPE == '1') {
          //必填校验
          if (
            h.ADDRESS_LINE1_EN?.length == 0 &&
            h.ADDRESS_LINE2_EN?.length == 0 &&
            h.ADDRESS_LINE3_EN?.length == 0 &&
            h.ADDRESS_LINE4_EN?.length == 0
          ) {
            this.$refs.accountAddressFormRef.toggleEnvelopeRequired(true);
            flag = false;
          }
          // //必填校验
          // if(h.ADDRESS_LINE1_ZH?.length==0 && h.ADDRESS_LINE2_ZH?.length==0 && h.ADDRESS_LINE3_ZH?.length==0 && h.ADDRESS_LINE4_ZH?.length==0){
          //   flag = false;
          // }
          //必填校验
          if (h.ADDRESS_EN?.length == 0) {
            flag = false;
          }
        }
        this.$refs.checkFormRef.validate(valid => {
          if (!valid) {
            flag = false;
          } else {
            // checkForm校验通过，整理地址数据
            data = {
              ...this.handleCheckFormField(),
              ...this.$refs.accountAddressFormRef.handleformatAddressData(),
            };
          }
        });
        console.log(flag, '---------BILL_MEDIA---校验');
        return flag ? data : flag;
      },
      // media切换，多选包含Paper，则Waived Paper Bill Fee开关设置为true，
      handleBillMediaChange(arr) {
        console.log(arr, 'arr===');

        let flag = arr.findIndex(x => x === '1');
        if (flag !== -1) {
          this.checkForm.WAIVED_PAPER_BILL_FEE = true;
        }

        // 如果包含Ebill，则校验表单emaill格式
        flag = arr.findIndex(x => x === '2');
        if (flag !== -1) {
          this.$store.dispatch('orderCapture/setIsEbillMedia', true);
        } else {
          this.$store.dispatch('orderCapture/setIsEbillMedia', false);
        }
        if (arr.length == 1 && arr[0] == '2') {
          this.switchDisabled = true;
          this.checkForm.WAIVED_PAPER_BILL_FEE = false;
        } else {
          this.switchDisabled = false;
          // this.checkForm.WAIVED_PAPER_BILL_FEE = true;
        }
      },
      // 标准和非标准地址切换
      handleChangeAddressType(item) {
        if (item.target.value === '1') {
          this.$refs.accountAddressFormRef.isStandard = true;
        } else {
          this.$refs.accountAddressFormRef.isStandard = false;
        }
      },

      // 处理checkForm数据，多选转为字符串，逗号隔开
      handleCheckFormField() {
        const formObj = this.checkForm;
        return {
          BILL_ADDRESS_TYPE: formObj.BILL_ADDRESS_TYPE,
          BILL_FREQUENCY: formObj.BILL_FREQUENCY,
          WAIVED_PAPER_BILL_FEE: formObj.WAIVED_PAPER_BILL_FEE ? '1' : '0',
          INCLUDE_ODD_CENTS_IN_BILL: formObj.INCLUDE_ODD_CENTS_IN_BILL ? '1' : '0',
          DISPLAY_ZERO_BILL_ITEMS: formObj.DISPLAY_ZERO_BILL_ITEMS ? '1' : '0',
          BILL_MEDIA: formObj['BILL_MEDIA'].join(),
          BILL_LANGUAGE: formObj['BILL_LANGUAGE'].join(),
        };
      },
    },
  };
</script>

<style scoped></style>
