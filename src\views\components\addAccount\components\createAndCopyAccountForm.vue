<template>
  <div>
    <a-form-model
      :model="form"
      ref="createAndCopyAccountFormRef"
      :rules="isEbillMedia ? emailFormRules : formRules"
    >
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.accountName')" prop="ACCOUNT_NAME">
            <a-input
              :maxLength="40"
              v-containsSqlInjection
              v-model.trim="form.ACCOUNT_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.LOB')" prop="LOB">
            <a-select
              v-model="form.LOB"
              :placeholder="$t('common.selectPlaceholder')"
              @change="handleLOBChange"
              allowClear
              disabled
            >
              <a-select-option v-for="item in lobList" :key="item.LOB_NAME" :value="item.LOB">
                {{ item.LOB_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.productFamily')" prop="PRODUCT_FAMILY">
            <a-select
              v-model="form.PRODUCT_FAMILY"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option
                v-for="item in productFamilyList"
                :key="item.PRODUCT_FAMILY"
                :value="item.PRODUCT_FAMILY"
              >
                {{ item.PRODUCT_FAMILY_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billContact')" prop="BILL_CONTACT">
            <a-input
              :maxLength="20"
              v-containsSqlInjection
              v-model.trim="form.BILL_CONTACT"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billRecipient')" prop="BILL_RECIPIENT">
            <a-input
              :maxLength="40"
              v-model.trim="form.BILL_RECIPIENT"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.billEmail')" prop="BILL_EMAIL">
            <a-input
              v-validateEmailMore
              v-model.trim="form.BILL_EMAIL"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import config from '../config';
  import { getLobList, getProductFamilyList } from '@/utils/utils';
  import { queryByCustId } from '@/api/accountSetting';
  import { mapState } from 'vuex';

  export default {
    name: 'createAndCopyAccountForm',
    data() {
      return {
        formRules: config.formRules,
        emailFormRules: config.emailFormRules,
        form: {
          ACCOUNT_NAME: '',
          LOB: '',
          PRODUCT_FAMILY: '',
          BILL_CONTACT: '',
          BILL_RECIPIENT: '',
          BILL_EMAIL: '',
        },
        lobList: [],
        productFamilyList: [],
      };
    },
    computed: {
      // media 选择了Ebill，则校验邮箱格式
      isEbillMedia() {
        return this.$store.state.orderCapture.isEbillMedia;
      },
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      // ...mapState('macd', {
      //   transferCustInfo: state => state.transferCustInfo,
      // }),
    },
    mounted() {
      this.handleInitLOBData();
      // this.form.ACCOUNT_NAME = this.$store.state.customerVerify.customerInfo?.CUSTOMER_NAME;
      // 查详情
      this.getDetailInfo();
    },
    methods: {
      // 按接口字段组装数据
      handleformatData() {
        let flag = true;
        let data = {};
        this.$refs.createAndCopyAccountFormRef.validate(valid => {
          if (!valid) {
            flag = false;
          } else {
            data = this.form;
          }
        });
        return flag ? data : flag;
      },

      // 获取lob枚举数据
      async handleInitLOBData() {
        this.lobList = await getLobList();
      },
      // LOB切换,PRODUCT_FAMILY默认会置空
      async handleLOBChange(value) {
        this.productFamilyList = await getProductFamilyList({ LOB: value });
        this.form.PRODUCT_FAMILY = '';
      },
      // 查详情
      async getDetailInfo() {
        const params = {
          'CUST_ID': (this.transferCustInfo && this.transferCustInfo.CUSTOMER_NO) || this.custId,
        };
        try {
          const res = await queryByCustId(params);
          this.$set(this.form, 'ACCOUNT_NAME', res.DATA[0].CUST_NAME);
          this.$set(this.form, 'LOB', res.DATA[0].LOB_TYPE[0]);
          this.handleLOBChange(this.form.LOB);
        } catch (error) {
          console.log(error, 'error');
        }
      },
    },
  };
</script>

<style scoped></style>
