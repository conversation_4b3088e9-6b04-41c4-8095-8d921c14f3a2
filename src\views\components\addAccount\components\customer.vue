<template>
  <div class="page">
    <!-- 客户信息 -->
    <CustomerInfomation ref="CustomerInfomation" :detail="detail" :isShowReturn="false" />

    <!-- 语言 多选 -->
    <SpeakingPreference :isDisabled="true" :selectedArray="speakingPreferenceSelectedList" />

    <!-- LOB 多选 -->
    <LobCheckBox :selectedArray="lobSelectedList" />

    <!-- 开关 -->
    <Switchs :isDisabled="true" :detail="detail" />
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import CustomerInfomation from './customerInformation.vue';
  import SpeakingPreference from './speakingPreference.vue';
  import LobCheckBox from './lobCheckBox.vue';
  import Switchs from './switchs.vue';
  import { queryByCustId } from '@/api/accountSetting';

  export default {
    name: 'customer',
    components: {
      CustomerInfomation,
      SpeakingPreference,
      LobCheckBox,
      Switchs,
    },
    data() {
      return {
        detail: {},
        speakingPreferenceSelectedList: [],
        lobSelectedList: [],
      };
    },
    created() {
      // 查详情
      this.getDetailInfo();
    },
    mounted() {},
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      // ...mapState('macd', {
      //   transferCustInfo: state => state.transferCustInfo,
      // }),
    },
    methods: {
      // 查详情
      async getDetailInfo() {
        const params = {
          'CUST_ID': (this.transferCustInfo && this.transferCustInfo.CUSTOMER_NO) || this.custId,
        };
        const res = await queryByCustId(params);
        this.detail = res?.DATA[0] || {};
        if (this.detail.SPEAKING_PREFERENCE) {
          this.speakingPreferenceSelectedList = this.detail.SPEAKING_PREFERENCE.split(',');
        }
        this.lobSelectedList = this.detail.LOBS || [];
      },
    },
  };
</script>

<style scoped lang="less"></style>
