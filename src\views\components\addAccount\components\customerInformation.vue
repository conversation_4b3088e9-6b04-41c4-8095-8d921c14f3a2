<template>
  <div class="customerInformation">
    <!-- 返回按钮 -->
    <div class="return" @click="goBack" v-if="isShowReturn">
      <a-icon type="left" class="icon" />
      <span>{{ $t('common.return') }}</span>
    </div>

    <!-- 标题 -->
    <div class="commonTitle" v-if="isShowTitle">{{ $t('accountSetting.customerInformation') }}</div>

    <!-- 客户信息 -->
    <a-row class="aRow">
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.customerName') }} :&nbsp;</div>
        <div class="value">{{ detail.CUST_NAME }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.customerId') }} :&nbsp;</div>
        <div class="value">{{ detail.CUST_ID }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.dragonCustomerNo') }} :&nbsp;</div>
        <div class="value">{{ detail.DRAGON_CUST_NO }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.documentType') }} :&nbsp;</div>
        <div class="value">{{ detail.DOCUMENT_TYPE_NAME }}</div>
      </a-col>
    </a-row>

    <a-row class="aRow">
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.documentNo') }} :&nbsp;</div>
        <div class="value">{{ detail.DOCUMENT_NO }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.highLevelCustomer') }} :&nbsp;</div>
        <div class="value">{{ detail.SUPER_GROUP_NAME }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.AGN') }} :&nbsp;</div>
        <div class="value">{{ detail.AGN }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.AGNName') }} :&nbsp;</div>
        <div class="value">{{ detail.AGN_NAME }}</div>
      </a-col>
    </a-row>

    <a-row class="aRow">
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.marketSegment') }} :&nbsp;</div>
        <div class="value">{{ detail.MARKET_SEGMENT }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.marketSubSegment') }} :&nbsp;</div>
        <div class="value">{{ detail.MARKET_SUB_SEGMENT }}</div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.industrialType') }} :&nbsp;</div>
        <a-tooltip>
          <template slot="title"> {{ detail.INDUSTRIAL_TYPE_NAME }}</template>
          <div class="value">{{ detail.INDUSTRIAL_TYPE_NAME }}</div>
        </a-tooltip>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.industrialSubType') }} :&nbsp;</div>
        <div class="value">{{ detail.INDUSTRIAL_SUB_TYPE_NAME }}</div>
      </a-col>
    </a-row>

    <!-- 查询客户不良状态 -->
    <a-row class="aRow">
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.badPayment') }} :&nbsp;</div>
        <div class="value">
          <BadPayment :CUST_ID="detail.CUST_ID" />
        </div>
      </a-col>
      <a-col :span="6">
        <div class="label">{{ $t('accountSetting.premierType') }} :&nbsp;</div>
        <div class="value">{{ detail.PREMIER_TYPE }}</div>
      </a-col>
    </a-row>

    <!-- 地址 -->
    <a-row class="aRow">
      <a-col :span="18">
        <div class="longLabel label">{{ $t('accountSetting.correspondenceAddress') }} :&nbsp;</div>
        <div class="value">{{ detail.CORRESPONDENCE_ADDRESS }}</div>
      </a-col>
      <a-col :span="6">
        <div class="longLabel label">{{ $t('accountSetting.SBNo') }} :&nbsp;</div>
        <div class="value">{{ detail.SB_NUMBER }}</div>
      </a-col>
    </a-row>

    <div class="dashedLine"></div>
  </div>
</template>

<script>
  import BadPayment from '@/components/badPayment';
  export default {
    name: 'customerInformation',
    components: {
      BadPayment,
    },
    props: {
      detail: {
        type: Object,
        default: () => {},
      },
      isShowReturn: {
        type: Boolean,
        default: true,
      },
      isShowTitle: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      // 返回
      goBack() {
        this.$router.customBack();
      },
    },
  };
</script>

<style lang="less" scoped>
  .customerInformation {
    .return {
      color: #0076ff;
      cursor: pointer;
      font-weight: 600;
      margin-bottom: 10px;
      .icon {
        margin-right: 5px;
      }
    }
    .commonTitle {
      font-weight: bold;
      font-size: 14px;
      position: relative; // 确保伪元素相对于 .com-title 定位
      margin: 10px 0;
      padding-left: 10px;
      &::before {
        content: '';
        position: absolute; // 使用绝对定位
        top: 50%;
        transform: translateY(-50%);
        display: inline-block;
        width: 4px;
        height: 15px;
        background-color: @primary-color;
        left: 0; // 确保伪元素在文本前面
      }
    }
    /deep/ .aRow {
      margin: 10px 0;

      .ant-col {
        display: flex;
      }
      .value {
        color: #333 !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .dashedLine {
      border: 1px dashed #e9e9e9;
      margin: 10px 0;
    }
  }
  /deep/ .ant-select-selection {
    width: 100px !important;
  }
</style>
