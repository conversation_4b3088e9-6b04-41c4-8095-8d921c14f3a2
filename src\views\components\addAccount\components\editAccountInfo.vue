<!-- 账户编辑内容 -->
<template>
  <div class="page">
    <div class="secondLevel-header-title">{{ $t('accountSetting.accountInfomation') }}</div>

    <!-- 新增和复制表单 -->
    <CreateAndCopyAccountFormVue ref="CreateAndCopyAccountFormVueRef" />

    <!-- 账户配置信息 -->
    <accountcheckFormVue ref="accountcheckFormVueRef" />

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  import { validateEmail } from '@/utils/utils';
  import MessageModal from '@/components/messageModal';
  import accountcheckFormVue from './accountcheckForm.vue';
  import CreateAndCopyAccountFormVue from './createAndCopyAccountForm.vue';
  export default {
    name: 'editAccountInfo',
    components: {
      MessageModal,
      accountcheckFormVue,
      CreateAndCopyAccountFormVue,
    },
    data() {
      return {
        tipsVisible: false,
        tipsMessage: '',
      };
    },
    methods: {
      // 整理表单数据，统一返回给父组件
      handleReturnFormData() {
        let flag = true;
        let formData = {};
        let checkData = {};
        let ACCOUNT_ITEM_LIST = [];

        // 校验账户表单组件
        if (this.$route.query?.type === 'update') {
          formData = this.$refs.EditAccountFormVueRef.handleformatData();
          if (!formData) {
            flag = false;
          }
        } else {
          formData = this.$refs.CreateAndCopyAccountFormVueRef.handleformatData();
          if (!formData) {
            flag = false;
          }
        }

        // 如果填写邮箱，则校验邮箱格式，最多三个邮箱，以英文逗号隔开
        if (formData.BILL_EMAIL) {
          const emailArr = formData.BILL_EMAIL.split(',');
          if (emailArr.length === 1) {
            flag = validateEmail(emailArr[0]);
          } else {
            emailArr.forEach(x => {
              !validateEmail(x) && (flag = false);
            });
          }

          // TODO:邮箱格式国际化
          if (!flag) {
            this.showTipModal(this.$t('accountSetting.emailFormat'));
            return false;
          }
        }

        // 校验checkForm组件
        checkData = this.$refs.accountcheckFormVueRef.handleformatData();
        if (!checkData) {
          flag = false;
        }

        // 校验不通过，返回提示
        if (!flag) {
          this.showTipModal(this.$t('accountSetting.requiredFields'));
          return false;
        }
        console.log('return false---------------');

        // 新增、复制BILL_FREQUENCY字段抽离出来，封装成数组入参,如果是修改，则bill day字段也抽离出来
        // TODO: 工号信息后续接口代替
        if (this.$route.query?.type === 'update') {
          ACCOUNT_ITEM_LIST = [
            {
              ACCT_ID: this.$route.query?.CUST_ID,
              BILL_DAY: formData['BILL_DAY'],
              UPDATE_DEPART_ID: '1',
              UPDATE_STAFF_ID: '111111',
              UPDATE_TIME: '',
            },
            {
              ACCT_ID: this.$route.query?.CUST_ID,
              BILL_FREQUENCY: checkData['BILL_FREQUENCY'],
              UPDATE_DEPART_ID: '1',
              UPDATE_STAFF_ID: '111111',
              UPDATE_TIME: '',
            },
          ];
        } else {
          ACCOUNT_ITEM_LIST = [
            {
              ACCT_ID: this.$route.query?.CUST_ID,
              BILL_FREQUENCY: checkData['BILL_FREQUENCY'],
              UPDATE_DEPART_ID: '1',
              UPDATE_STAFF_ID: '111111',
              UPDATE_TIME: '',
            },
          ];
        }
        delete checkData['BILL_FREQUENCY'];

        // TODO 工号
        const params = {
          ACCOUNT_NAME: formData['ACCOUNT_NAME'],
          LOB: formData['LOB'],
          PRODUCT_FAMILY: formData['PRODUCT_FAMILY'],
          ACCOUNT_BILL: {
            BILL_CONTACT: formData['BILL_CONTACT'],
            BILL_RECIPIENT: formData['BILL_RECIPIENT'],
            BILL_EMAIL: formData['BILL_EMAIL'],
            ...checkData,
            UPDATE_STAFF_ID: this.$store.state.app?.userInfo?.STAFF_ID || '',
            CREATE_STAFF_ID: this.$store.state.app?.userInfo?.STAFF_ID || '',
            CREATE_STAFF_NAME: this.$store.state.app?.userInfo?.STAFF_NAME || '',
            UPDATE_STAFF_NAME: this.$store.state.app?.userInfo?.STAFF_NAME || '',
          },
          ACCOUNT_ITEM_LIST,
        };

        return params;
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },
    },
  };
</script>

<style scoped lang="less"></style>
