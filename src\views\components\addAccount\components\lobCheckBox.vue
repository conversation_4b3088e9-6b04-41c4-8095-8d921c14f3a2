/** LOB */
<template>
  <div class="lobCheckBox">
    <!-- 多选 -->
    <div class="checkContent">
      <span class="checkOption label">{{ $t('accountSetting.lob') }} :&nbsp;</span>
      <a-checkbox-group :options="lobOptions" v-model="localSelectedArray" disabled />
    </div>
  </div>
</template>

<script>
  import { getLobList } from '@/utils/utils';
  export default {
    name: 'lobCheckBox',
    props: {
      isDisabled: {
        type: Boolean,
        default: false,
      },
      selectedArray: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      selectedArray: {
        handler(newValue) {
          // 当选中节点发生变化时，更新选中树
          if (newValue) {
            this.localSelectedArray = this.selectedArray.slice();
          }
        },
        deep: true,
      },
    },
    data() {
      return {
        localSelectedArray: [], // 创建副本
        lobOptions: [],
      };
    },
    mounted() {
      // 查LOB枚举数据
      getLobList().then(res => {
        this.lobOptions = res.map(item => {
          return { label: item.LOB_NAME, value: item.LOB };
        });
      });
    },
  };
</script>

<style lang="less" scoped>
  .lobCheckBox {
    .checkContent {
      margin-top: 20px;
      .checkOption {
      }
      /deep/ .ant-checkbox + span {
        color: #000;
      }
    }
  }
</style>
