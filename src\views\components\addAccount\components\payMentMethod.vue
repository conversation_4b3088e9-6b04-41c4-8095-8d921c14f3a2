<template>
  <div class="page">
    <div class="secondLevel-header-title">{{ $t('accountSetting.paymentMethod') }}</div>
    <a-form-model :model="formData" :rules="formRules" ref="formDataRef">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.payment')" prop="PAYMENT_METHOD">
            <a-select
              v-model="formData.PAYMENT_METHOD"
              :placeholder="$t('common.inputPlaceholder')"
              @change="handlePaymentChange"
            >
              <a-select-option v-for="item in payMentMethodTabList" :key="item.value">{{
                item.label
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD !== 'Cash'">
          <a-form-model-item :label="$t('accountSetting.cardCountry')" prop="CARD_COUNTRY">
            <a-select
              v-model="formData.CARD_COUNTRY"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in cardCountryList" :key="item.CODE_VALUE">{{
                item.CODE_NAME
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'CreditCard'">
          <a-form-model-item :label="$t('accountSetting.cardType')" prop="CARD_TYPE">
            <a-select
              v-model="formData.CARD_TYPE"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in cardTypeList" :key="item.CODE_VALUE">{{
                item.CODE_NAME
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'CreditCard'">
          <a-form-model-item :label="$t('accountSetting.expirationDate')" prop="EXPIRATION_DATE">
            <a-date-picker
              valueFormat="MMDD"
              style="width: 100%"
              v-model="formData.EXPIRATION_DATE"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'DirectDebit'">
          <a-form-model-item :label="$t('accountSetting.creditCardToken')" prop="CREDIT_CARD_TOKEN">
            <a-input-search
              v-containsSqlInjection
              v-model.trim="formData.CREDIT_CARD_TOKEN"
              :placeholder="$t('common.inputPlaceholder')"
              enter-button="Token"
              @search="handleTokenSearch"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'DirectDebit'">
          <a-form-model-item :label="$t('accountSetting.cardName')" prop="CARD_NAME">
            <a-input
              v-containsSqlInjection
              v-model.trim="formData.CARD_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16" v-if="formData.PAYMENT_METHOD !== 'Cash'">
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'CreditCard'">
          <a-form-model-item :label="$t('accountSetting.creditCardToken')" prop="CREDIT_CARD_TOKEN">
            <a-input-search
              v-containsSqlInjection
              v-model.trim="formData.CREDIT_CARD_TOKEN"
              :placeholder="$t('common.inputPlaceholder')"
              enter-button="Token"
              @search="handleTokenSearch"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'CreditCard'">
          <a-form-model-item :label="$t('accountSetting.cardName')" prop="CARD_NAME">
            <a-input
              v-containsSqlInjection
              v-model.trim="formData.CARD_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === 'DirectDebit'">
          <a-form-model-item :label="$t('accountSetting.bankNo')" prop="BANK_NO">
            <a-input
              v-containsSqlInjection
              v-model.trim="formData.BANK_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import config from '../config';
  import {
    queryCreateCustomerEnum,
    getCreditCardTokenUrl,
    getCreditCardToken,
  } from '@/api/accountSetting';

  export default {
    name: 'payMentMethod',
    data() {
      return {
        DirectDebitRules: config.DirectDebitRules,
        CreditCardRules: config.CreditCardRules,
        payMentMethodTabList: config.payMentMethodTabList,
        formRules: config.CashRules,
        cardTypeList: [],
        cardCountryList: [],
        formData: {
          PAYMENT_METHOD: 'Cash',
          CARD_COUNTRY: '',
          CARD_TYPE: '',
          EXPIRATION_DATE: '',
          CREDIT_CARD_TOKEN: '',
          CARD_NAME: '',
          BANK_NO: '',
        },
      };
    },
    watch: {
      'formData.PAYMENT_METHOD'(newVal) {
        if (newVal === 'Cash') {
          this.formRules = this.CashRules;
        } else if (newVal === 'CreditCard') {
          this.formRules = this.CreditCardRules;
        } else if (newVal === 'DirectDebit') {
          this.formRules = this.DirectDebitRules;
        }
      },
    },
    mounted() {
      this.getCardCountryList();
      this.getCardTypeList();
      // 编辑，回填数据
      // if (this.$route.query?.type === 'update' || this.$route.query?.type === 'copy') {
      //   this.handleInitUpdateAccount();
      // }
    },
    methods: {
      // 支付方式更改
      handlePaymentChange(value) {
        this.formData = {
          PAYMENT_METHOD: value,
          CARD_COUNTRY: '',
          CARD_TYPE: '',
          EXPIRATION_DATE: '',
          CREDIT_CARD_TOKEN: '',
          CARD_NAME: '',
          BANK_NO: '',
        };

        if (value === 'CreditCard') {
          this.getCardCountryList();
        } else if (value === 'DirectDebit') {
          this.getCardCountryList('DIRECT_DEBIT_CARD_COUNTRY');
        }
      },

      // TODO调用接口获取token，循环请求
      async handleTokenSearch() {
        try {
          const res = await getCreditCardTokenUrl();
          const { OPP_REF_ID, REDIRECT_URL } = res?.DATA?.[0];
          const windowFeatuers =
            'margin=auto,width=600,height=400,resizable=yes,scrollbars=yes,status=1';
          window.open(REDIRECT_URL, '_blank', windowFeatuers);
          this.handleGetCreditCardToken(OPP_REF_ID);
        } catch (error) {
          console.log(error);
        }
      },

      async handleGetCreditCardToken(OPP_REF_ID) {
        let params = OPP_REF_ID;
        try {
          const res = await getCreditCardToken({ params });
          const { OPP_REF_ID, CREDIT_CARD_TOKEN } = res?.RSP?.DATA?.[0];
          this.formData['CREDIT_CARD_TOKEN'] = OPP_REF_ID;
        } catch (error) {
          console.log(error);
        }
      },

      // 处理该组件数据，用于接口入参
      handleReturnData() {
        let flag = true;
        this.$refs.formDataRef.validate(valid => {
          if (!valid) {
            flag = false;
          }
        });
        return flag ? this.formData : flag;
      },

      // 查cardCountry枚举数据
      // TODO: type接口入参需调整
      async getCardCountryList(type = 'CREDIT_CARD_CARD_COUNTRY') {
        const params = {
          'CODE_TYPE': type,
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.cardCountryList = res.DATA || [];
        } catch (error) {
          console.log(error);
        }
      },

      // 查cardType枚举数据
      async getCardTypeList() {
        const params = {
          'CODE_TYPE': 'ACCT_CARD_TYPE',
        };
        try {
          let res = await queryCreateCustomerEnum(params);
          this.cardTypeList = res.DATA || [];
        } catch (error) {
          console.log(error);
        }
      },

      // 修改和复制账户回填数据
      // handleInitUpdateAccount() {
      //   const accountData = this.$store.state.account.accountData;
      //   const paymentMethodData = {
      //     PAYMENT_METHOD: accountData?.PAYMENT_METHOD,
      //     CARD_COUNTRY: accountData?.CARD_COUNTRY,
      //     CARD_TYPE: accountData?.CARD_TYPE,
      //     EXPIRATION_DATE: accountData?.EXPIRATION_DATE,
      //     CREDIT_CARD_TOKEN: accountData?.CREDIT_CARD_TOKEN,
      //     CARD_NAME: accountData?.CARD_NAME,
      //     BANK_NO: accountData?.BANK_NO,
      //   };
      //   this.formData = paymentMethodData;
      // },
    },
  };
</script>

<style scoped lang="less">
  .selectedTabList {
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
    .item {
      position: relative;
      height: 40px;
      line-height: 40px;
      border-radius: 2px;
      margin-right: 10px;
      padding: 0 10px;
      cursor: pointer;
    }
    .active {
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 80%;
        height: 2px;
        background: #01408e;
        text-align: center;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-top-color: #01408e;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
</style>
