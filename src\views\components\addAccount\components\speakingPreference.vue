/** speakingPreference */
<template>
  <div class="speakingPreference">
    <!-- 多选 -->
    <div class="checkContent">
      <span class="checkOption label">{{ $t('accountSetting.speakingPreference') }} :&nbsp;</span>
      <a-checkbox-group :options="speakOptions" v-model="selectedList" :disabled="isDisabled" />
    </div>
  </div>
</template>

<script>
  import config from '../config';
  export default {
    name: 'speakingPreference',
    props: {
      isDisabled: {
        type: Boolean,
        default: false,
      },
      selectedArray: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      selectedArray: {
        handler(newValue, oldValue) {
          if (newValue) {
            this.selectedList = newValue;
          }
        },
        immediate: true,
      },
    },
    data() {
      return {
        speakOptions: config.speakOptions,
        selectedList: [],
      };
    },
    mounted() {},
    methods: {
      // 详情接口回显数据
      setValue(obj) {
        this.selectedList = obj.SPEAKING_PREFERENCE ? obj.SPEAKING_PREFERENCE.split(',') : [];
      },
    },
  };
</script>

<style lang="less" scoped>
  .speakingPreference {
    .checkContent {
      margin-top: 20px;
      .checkOption {
      }
      /deep/ .ant-checkbox + span {
        color: #000;
      }
    }
  }
</style>
