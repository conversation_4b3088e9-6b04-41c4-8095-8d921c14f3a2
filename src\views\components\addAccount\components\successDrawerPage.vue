<template>
  <div class="archived-page">
    <div class="tipMainPosition">
      <div class="modal-container1">
        <div class="icon-container">
          <i class="iconfont icon-wancheng" style="color: #2dcb31"></i>
        </div>
        <div class="title">{{ messageTitleTips }}</div>
        <div class="content">{{ subText }}</div>
      </div>
    </div>
    <!-- 底部定位按钮 -->
    <div class="fixedBottomPartPlaceholder">
      <div class="fixedBottomPart">
        <a-button type="primary" class="btn submitBtn" @click="handleConfirm">
          {{ $t('common.buttonOk') }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'successDrawerPage',
    props: {
      messageTitleTips: {
        type: String,
        default: '',
      },
      subText: {
        type: String,
        default: '',
      },
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      handleConfirm() {
        this.$emit('handleConfirm');
      },
    },
  };
</script>

<style scoped lang="less">
  .archived-page {
    position: absolute;
    top: 0;
    bottom: 50px; // TODO底部高度样式变量
    left: 0;
    right: 0;
    padding: 10px 15px 15px 15px;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    z-index: 99;
    .tipMainPosition {
      width: 100%;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80vh;
      .modal-container1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        .icon-container {
          width: 60px;
          height: 60px;
          i {
            font-size: 60px;
          }
        }
        .title {
          margin-top: 60px !important;
          height: 33px;
          font-family: PingFangSC;
          color: #000000;
          font-weight: 400;
          font-size: 24px;
          letter-spacing: 0;
          margin: 20px;
        }
        .content {
          height: 22px;
          font-family: PingFangSC-Regular;
          color: #666666;
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0;
          margin-bottom: 5px;
        }
      }
    }
  }
  .fixedBottomPartPlaceholder .fixedBottomPart {
    overflow: hidden;
  }
</style>
