/** switchs 五个开关 */
<template>
  <div class="switchs">
    <div class="switchContent">
      <div class="switchItem">
        <span class="label">{{ $t('accountSetting.customerVerified') }} :&nbsp;</span>
        <!-- <a-switch
          :checked="form.CUSTOMER_VERIFIED == 'Y'"
          :checked-children="$t('common.yes')"
          :un-checked-children="$t('common.no')"
          disabled
        /> -->
        <span v-if="form.CUSTOMER_VERIFIED == 'Y'" class="iconfont icon-Yesbiaoqian"></span>
        <span v-else class="iconfont icon-Nobiaoqian"></span>
      </div>
      <div class="switchItem">
        <span class="label">{{ $t('accountSetting.writtenApprovalRequired') }} :&nbsp;</span>
        <a-select
          v-model="form.WRITTEN_APPROVAL_REQUIRED"
          :placeholder="$t('common.selectPlaceholder')"
          allowClear
          disabled
          class="selectItem"
        >
          <a-select-option v-for="item in typeList" :key="item.CODE_NAME" :value="item.CODE_VALUE">
            {{ item.CODE_NAME }}
          </a-select-option>
        </a-select>
      </div>
      <div class="switchItem">
        <span class="label">{{ $t('accountSetting.specialHandling') }} :&nbsp;</span>
        <!-- <a-switch
          :checked="form.SPECIAL_HANDLING == 'Y'"
          :checked-children="$t('common.yes')"
          :un-checked-children="$t('common.no')"
          disabled
        /> -->
        <span v-if="form.SPECIAL_HANDLING == 'Y'" class="iconfont icon-Yesbiaoqian"></span>
        <span v-else class="iconfont icon-Nobiaoqian"></span>
      </div>

      <div class="switchItem">
        <span class="label">{{ $t('accountSetting.windingUpBankruptcy') }} :&nbsp;</span>
        <!-- <a-switch
          :checked="form.WINDING_UP_BANKRUPTCY == 'Y'"
          :checked-children="$t('common.yes')"
          :un-checked-children="$t('common.no')"
          disabled
        /> -->
        <span v-if="form.WINDING_UP_BANKRUPTCY == 'Y'" class="iconfont icon-Yesbiaoqian"></span>
        <span v-else class="iconfont icon-Nobiaoqian"></span>
      </div>
      <div class="switchItem">
        <span class="label">Risky :&nbsp;</span>
        <!-- <a-switch
          :checked="form.RISKY == 'Y'"
          :checked-children="$t('common.yes')"
          :un-checked-children="$t('common.no')"
          disabled
        /> -->
        <span v-if="form.RISKY == 'Y'" class="iconfont icon-Yesbiaoqian"></span>
        <span v-else class="iconfont icon-Nobiaoqian"></span>
      </div>
    </div>
  </div>
</template>

<script>
  import { queryCreateCustomerEnum } from '@/api/accountSetting';
  export default {
    name: 'switchs',
    props: {
      detail: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      detail: {
        handler(newValue, oldValue) {
          if (newValue) {
            this.form = newValue;
          }
        },
        immediate: true,
      },
    },
    data() {
      return {
        typeList: [],
        form: {
          CUSTOMER_VERIFIED: 'N',
          WRITTEN_APPROVAL_REQUIRED: undefined,
          SPECIAL_HANDLING: 'N',
          WINDING_UP_BANKRUPTCY: 'N',
          RISKY: 'N',
        },
      };
    },
    computed: {},
    mounted() {
      this.getTypeList();
    },
    methods: {
      // 查枚举数据
      async getTypeList() {
        const params = {
          'CODE_TYPE': 'WRITTEN_APPROVAL_REQUIRED',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.typeList = res.DATA || [];
        } catch (error) {
          console.log(error);
        }
      },
      // 详情接口回显数据
      setValue(obj) {
        this.form = {
          CUSTOMER_VERIFIED: obj.CUSTOMER_VERIFIED || 'N',
          WRITTEN_APPROVAL_REQUIRED: obj.WRITTEN_APPROVAL_REQUIRED || undefined,
          SPECIAL_HANDLING: obj.SPECIAL_HANDLING || 'N',
          WINDING_UP_BANKRUPTCY: obj.WINDING_UP_BANKRUPTCY || 'N',
          RISKY: obj.RISKY || 'N',
        };
      },
    },
  };
</script>

<style lang="less" scoped>
  .switchs {
    .switchContent {
      display: flex;
      align-items: center;
      margin: 20px 0;
      .selectItem {
        width: 150px;
      }
      .switchItem {
        margin-right: 30px;
        .label {
        }
        .ant-switch {
          width: 50px;
        }
      }
    }
  }
  /deep/ .ant-select-selection {
    width: 100px;
  }
</style>
