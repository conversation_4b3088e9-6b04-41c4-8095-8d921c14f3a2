import that from '@/main.js';

let obj = {
  HKTBillContactColumns: [
    {
      scopedSlots: { title: 'creditType', customRender: 'type' },
    },
    {
      scopedSlots: { title: 'creditName', customRender: 'name' },
    },
    {
      title: that.$t('fulfillmentInfo.staffId'), // Email
      scopedSlots: { customRender: 'staffId' },
    },
    {
      title: that.$t('fulfillmentInfo.contactPhone'), // Contact
      scopedSlots: { customRender: 'contact' },
    },
    {
      title: that.$t('fulfillmentInfo.email'), // Email
      scopedSlots: { customRender: 'email' },
    },
    {
      title: that.$t('customerVerify.Action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
  emailFormRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(that.$t('accountSetting.emailCannotBeEmpty')));
          } else {
            const emails = value
              .split(',') // 按逗号分割字符串
              .map(email => email.trim()) // 去掉每个元素的前后空格
              .filter(email => email); // 过滤掉空字符串;
            if (emails.length > 3) {
              callback(new Error(that.$t('accountSetting.numberOfEmail')));
            } else {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    BILL_CONTACT: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_RECIPIENT: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  formRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_CONTACT: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    // BILL_RECIPIENT: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: false, trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          const emails = value
            .split(',') // 按逗号分割字符串
            .map(email => email.trim()) // 去掉每个元素的前后空格
            .filter(email => email); // 过滤掉空字符串;;
          if (emails.length > 3) {
            callback(new Error(that.$t('accountSetting.numberOfEmail')));
          } else {
            if (value) {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
          }
          callback();
        },
        trigger: 'blur',
      },
    ],
  },
  standardAddressFormRules: {
    ADDRESS_EN: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  checkFormRules: {
    BILL_MEDIA: [
      {
        type: 'array',
        required: true,
        message: that.$t('common.cannotBeEmpty'),
        trigger: 'change',
      },
    ],
    WAIVED_PAPER_BILL_FEE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    INCLUDE_ODD_CENTS_IN_BILL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    DISPLAY_ZERO_BILL_ITEMS: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    BILL_ADDRESS_TYPE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
    BILL_FREQUENCY: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
    BILL_LANGUAGE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
  },
  addressFormRules: {
    ADDRESS_EN: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    ADDRESS_LINE4_EN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
  },
  updateRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(that.$t('accountSetting.emailCannotBeEmpty')));
          } else {
            const emails = value
              .split(',') // 按逗号分割字符串
              .map(email => email.trim()) // 去掉每个元素的前后空格
              .filter(email => email); // 过滤掉空字符串;;
            if (emails.length > 3) {
              callback(new Error(that.$t('accountSetting.numberOfEmail')));
            } else {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    BILL_DAY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  billFrequenceOption: [
    { label: 'Month', value: 'Month' },
    { label: 'Quarter', value: 'Quarter' },
    { label: 'Year', value: 'Year' },
  ],
  billAddressOption: [
    { label: 'Standard', value: 'Standard' },
    { label: 'Non-standard', value: 'Non-standard' },
  ],
  payMentMethodTabList: [
    {
      value: 'Cash',
      label: that.$t('accountSetting.cash'),
    },
    {
      value: 'CreditCard',
      label: that.$t('accountSetting.creditCardAutopay'),
    },
    {
      value: 'DirectDebit',
      label: that.$t('accountSetting.directDebit'),
    },
  ],
  cardFormRules: {
    CARD_COUNTRY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    CARD_TYPE: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    EXPIRATION_DATE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
    CREDIT_CARD_TOKEN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    CARD_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  debitFormRules: {
    CARD_COUNTRY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    CREDIT_CARD_TOKEN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    CARD_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BANK_NO: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  cashRules: {
    PAYMENT_METHOD: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
  },
  CreditCardRules: {
    PAYMENT_METHOD: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    CARD_COUNTRY: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    CARD_TYPE: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    EXPIRATION_DATE: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    CREDIT_CARD_TOKEN: [{ required: true, message: that.$t('common.noNull'), trigger: 'blur' }],
    CARD_NAME: [{ required: true, message: that.$t('common.noNull'), trigger: 'blur' }],
  },
  DirectDebitRules: {
    PAYMENT_METHOD: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    CARD_COUNTRY: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
    CREDIT_CARD_TOKEN: [{ required: true, message: that.$t('common.noNull'), trigger: 'blur' }],
    CARD_NAME: [{ required: true, message: that.$t('common.noNull'), trigger: 'blur' }],
    BANK_NO: [{ required: true, message: that.$t('common.noNull'), trigger: 'blur' }],
  },
  emailUpdateFormRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(that.$t('accountSetting.emailCannotBeEmpty')));
          } else {
            const emails = value
              .split(',') // 按逗号分割字符串
              .map(email => email.trim()) // 去掉每个元素的前后空格
              .filter(email => email); // 过滤掉空字符串;
            if (emails.length > 3) {
              callback(new Error(that.$t('accountSetting.numberOfEmail')));
            } else {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    BILL_DAY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  speakOptions: [
    {
      label: that.$t('accountSetting.cantonese'),
      value: 'Cantonese', // 广东话
    },
    {
      label: that.$t('accountSetting.mandarin'),
      value: 'Mandarin', // 普通话
    },
    {
      label: that.$t('accountSetting.english'),
      value: 'English', // 英语
    },
  ],
};

export const cashRules = {
  PAYMENT_METHOD: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
};

export default obj;
