<!-- 账户编辑内容 -->
<template>
  <div class="page">
    <!-- 可编辑表单 -->
    <a-form-model :model="form" :rules="formRules" ref="form">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.accountName')" prop="ACCOUNT_NAME">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.ACCOUNT_NAME"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.LOB')" prop="LOB">
            <a-select
              v-model="form.LOB"
              :placeholder="$t('common.selectPlaceholder')"
              @change="handleLOBChange"
              allowClear
            >
              <a-select-option v-for="item in lobList" :key="item.LOB_NAME" :value="item.LOB">
                {{ item.LOB_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.productFamily')" prop="PRODUCT_FAMILY">
            <a-select
              v-model="form.PRODUCT_FAMILY"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option
                v-for="item in productFamilyList"
                :key="item.PRODUCT_FAMILY"
                :value="item.PRODUCT_FAMILY"
              >
                {{ item.PRODUCT_FAMILY_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billContact')">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.BILL_CONTACT"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.billRecipient')">
            <a-input
              v-containsSqlInjection
              v-model.trim="form.BILL_RECIPIENT"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.billEmail')">
            <a-input
              v-validateEmailMore
              v-model.trim="form.BILL_EMAIL"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-form-model :model="checkForm" layout="inline" :rules="checkFormRules" ref="checkFormRef">
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billLanguage')" prop="BILL_MEDIA">
            <a-checkbox-group v-model="checkForm.BILL_MEDIA">
              <a-checkbox value="Paper" name="type">{{ $t('accountSetting.paper') }}</a-checkbox>
              <a-checkbox value="EBill" name="type"> {{ $t('accountSetting.email') }} </a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billLanguage')">
            <a-checkbox-group v-model="checkForm.BILL_LANGUAGE">
              <a-checkbox value="English" name="type">
                {{ $t('accountSetting.english') }}
              </a-checkbox>
              <a-checkbox value="Traditional Chinese" name="type">
                {{ $t('accountSetting.traditionalChinese') }}
              </a-checkbox>
            </a-checkbox-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.billFrequence')">
            <a-radio-group
              :options="billFrequenceOption"
              v-model="checkForm.BILL_FREQUENCY"
            ></a-radio-group>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.waivedPaperBillFee')"
            prop="WAIVED_PAPER_BILL_FEE"
          >
            <a-switch
              v-model="checkForm.WAIVED_PAPER_BILL_FEE"
              :checked-children="$t('accountSetting.yes')"
              :un-checked-children="$t('accountSetting.no')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.includeOddCentsInBill')"
            prop="INCLUDE_ODD_CENTS_IN_BILL"
          >
            <a-switch
              v-model="checkForm.INCLUDE_ODD_CENTS_IN_BILL"
              :checked-children="$t('accountSetting.yes')"
              :un-checked-children="$t('accountSetting.no')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('accountSetting.displaybill')"
            prop="DISPLAY_ZERO_BILL_ITEMS"
          >
            <a-switch
              :checked="checkForm.DISPLAY_ZERO_BILL_ITEMS"
              :checked-children="$t('accountSetting.yes')"
              :un-checked-children="$t('accountSetting.no')"
            />
            <a-popover>
              <template slot="content">
                <ul style="color: #333333">
                  <li>{{ $t('accountSetting.displayBillTip1') }}</li>
                  <li>{{ $t('accountSetting.displayBillTip2') }}</li>
                  <li>{{ $t('accountSetting.displayBillTip3') }}</li>
                </ul>
              </template>
              <a-icon
                type="info-circle"
                style="color: #0072ff; font-size: 16px; margin-left: 6px"
              />
            </a-popover>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-divider dashed />
      <a-row>
        <a-col :span="12">
          <a-form-model-item :label="$t('accountSetting.billAddressType')" prop="BILL_ADDRESS_TYPE">
            <a-radio-group
              :options="billAddressOption"
              v-model="checkForm.BILL_ADDRESS_TYPE"
            ></a-radio-group>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-form-model :model="addressForm" :rules="addressFormRules" ref="addressFormRef">
      <a-row>
        <a-col :span="12">
          <a-form-model-item :colon="false" prop="ADDRESS_EN">
            <template v-slot:label>
              <span style="margin-right: 10px">{{ $t('accountSetting.billAddress') }}</span>
              <a-button type="primary" size="small" @click="handleOpenSplitAddress">{{
                $t('accountSetting.splitAddress')
              }}</a-button>
            </template>
            <a-input
              v-containsSqlInjection
              v-model.trim="addressForm.ADDRESS_EN"
              :placeholder="$t('common.inputPlaceholder')"
              style="margin-bottom: 10px"
            />
            <a-input
              v-containsSqlInjection
              v-model.trim="addressForm.ADDRESS_ZH"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-model-item
            :label="$t('accountSetting.envelopeDisplay')"
            ref="ADDRESS_LINE4_EN"
            prop="ADDRESS_LINE4_EN"
          >
            <a-row :gutter="[16, 16]">
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE1_EN"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE1_ZH"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
            </a-row>
            <a-row :gutter="[16, 16]">
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE2_EN"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE2_ZH"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
            </a-row>
            <a-row :gutter="[16, 16]">
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE3_EN"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE3_ZH"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
            </a-row>
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE4_EN"
                  :placeholder="$t('common.inputPlaceholder')"
                  @blur="() => $refs.ADDRESS_LINE4_EN.onFieldBlur()"
                  @change="() => $refs.ADDRESS_LINE4_EN.onFieldChange()"
                />
              </a-col>
              <a-col :span="12"
                ><a-input
                  v-containsSqlInjection
                  v-model.trim="addressForm.ADDRESS_LINE4_ZH"
                  :placeholder="$t('common.inputPlaceholder')"
              /></a-col>
            </a-row>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- Address Mapping 弹窗 -->
    <a-modal
      :title="$t('accountSetting.addressMapping')"
      width="80%"
      destroyOnClose
      :footer="null"
      :visible="selectAddressVisible"
      @cancel="selectAddressCancel"
    >
      <SelectAddress @cancel="selectAddressCancel" @ok="handleAddressOK" />
    </a-modal>

    <TipsPopWindow v-if="tipsVisible" :visible="tipsVisible" @Ok="handleTipsOk" :text="tipsText" />
  </div>
</template>

<script>
  import config from './config';
  import { getLobList, getProductFamilyList } from '@/utils/utils';
  import { splitAddress } from '@/api/common';
  import SelectAddress from '@/views/components/selectAddress/index.vue';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';

  export default {
    name: 'editAccountInfo',
    components: {
      SelectAddress,
      TipsPopWindow,
    },
    data() {
      return {
        form: {
          ACCOUNT_NAME: '',
          LOB: undefined,
          PRODUCT_FAMILY: undefined,
          BILL_CONTACT: '',
          BILL_RECIPIENT: '',
          BILL_EMAIL: '',
        },
        checkForm: {
          BILL_MEDIA: [],
          BILL_LANGUAGE: [],
          BILL_FREQUENCY: '',
          BILL_ADDRESS_TYPE: 'Standard',
          WAIVED_PAPER_BILL_FEE: true,
          INCLUDE_ODD_CENTS_IN_BILL: false,
          DISPLAY_ZERO_BILL_ITEMS: true,
        },
        addressForm: {
          ADDRESS_EN: '',
          ADDRESS_ZH: '',
          ADDRESS_LINE1_EN: '',
          ADDRESS_LINE1_ZH: '',
          ADDRESS_LINE2_EN: '',
          ADDRESS_LINE2_ZH: '',
          ADDRESS_LINE3_EN: '',
          ADDRESS_LINE3_ZH: '',
          ADDRESS_LINE4_EN: '',
          ADDRESS_LINE4_ZH: '',
          PO_BOX: '',
          NO_STANDARD_ADDRESS_LINE1_EN: '',
          NO_STANDARD_ADDRESS_LINE2_EN: '',
          NO_STANDARD_ADDRESS_LINE3_EN: '',
          NO_STANDARD_ADDRESS_LINE4_EN: '',
        },
        visible: false,
        formRules: config.formRules,
        checkFormRules: config.checkFormRules,
        addressFormRules: config.addressFormRules,
        updateRules: config.updateRules,
        billFrequenceOption: config.billFrequenceOption,
        billAddressOption: config.billAddressOption,
        lobList: [],
        productFamilyList: [],
        selectAddressVisible: false,
        tipsVisible: false,
        tipsText: this.$t('customerVerify.tipsText1'),
        tipsMessage: '',
      };
    },
    async mounted() {
      // 获取lob枚举数据
      this.lobList = await getLobList();
    },
    methods: {
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },
      // LOB切换
      async handleLOBChange(value) {
        this.productFamilyList = await getProductFamilyList({ LOB: value });
        this.form.PRODUCT_FAMILY = '';
      },
      handleOpenSplitAddress() {
        this.selectAddressVisible = true;
      },
      handleCancel() {
        this.visible = false;
      },
      // 标准地址切割回填
      async handleConfirmAddress(row) {
        if (!Object.keys(row).length > 0) {
          this.tipsText = this.$t('customerVerify.tipsText2');
          this.tipsVisible = true;
          return;
        }
        try {
          const res = await splitAddress({ BID: row.BID });
          const detailsEN = res.DATA?.[0]?.['MAIL_DETAILS_EN'];
          const detailsZH = res.DATA?.[0]?.['MAIL_DETAILS_ZH'];
          this.addressForm['ADDRESS_EN'] = res.DATA?.[0]?.['DETAILS_EN']?.['ADDRESS'];
          this.addressForm['ADDRESS_ZH'] = res.DATA?.[0]?.['DETAILS_ZH']?.['ADDRESS'];
          this.addressForm['ADDRESS_LINE1_EN'] = detailsEN?.[0];
          this.addressForm['ADDRESS_LINE2_EN'] = detailsEN?.[1];
          this.addressForm['ADDRESS_LINE3_EN'] = detailsEN?.[2];
          this.addressForm['ADDRESS_LINE4_EN'] = detailsEN?.[3];
          this.addressForm['ADDRESS_LINE1_ZH'] = detailsZH?.[0];
          this.addressForm['ADDRESS_LINE2_ZH'] = detailsZH?.[1];
          this.addressForm['ADDRESS_LINE3_ZH'] = detailsZH?.[2];
          this.addressForm['ADDRESS_LINE4_ZH'] = detailsZH?.[3];
          this.selectAddressCancel();
        } catch (error) {
          this.$message.error(error);
        }
      },
      handleReturnAddress() {
        this.visible = false;
      },
      // media切换，多选包含Paper，则Waived Paper Bill Fee开关设置为true
      handleBillMediaChange(arr) {
        const flag = arr.findIndex(x => x === 'Paper');
        if (flag !== -1) {
          this.checkForm.WAIVED_PAPER_BILL_FEE = true;
        }
      },
      // 整理表单数据，统一返回给父组件
      handleReturnFormData() {
        let flag = true;
        this.$refs.form.validate(valid => {
          if (!valid) {
            flag = false;
          }
        });
        this.$refs.checkFormRef.validate(valid => {
          if (!valid) {
            flag = false;
          }
        });
        this.$refs.addressFormRef.validate(valid => {
          if (!valid) {
            flag = false;
          }
        });

        // 校验不通过，返回提示
        if (!flag) {
          this.$message.warn('必填字段不能为空或格式错误');
          return false;
        }

        // 校验通过，整理数据
        const params = {
          ...this.form,
          ...this.handleCheckFormField(this.checkForm),
          ...this.handleAddressForm(),
        };

        return params;
      },
      // 标准地址和非标准地址处理
      handleAddressForm() {
        let data = {};
        if (this.checkForm.BILL_ADDRESS_TYPE === 'Standard') {
          data = {
            PO_BOX: '', // 该字段测试
            ADDRESS_EN: this.addressForm.ADDRESS_EN,
            ADDRESS_ZH: this.addressForm.ADDRESS_ZH,
            ADDRESS_LINE1_EN: this.addressForm.ADDRESS_LINE1_EN,
            ADDRESS_LINE1_ZH: this.addressForm.ADDRESS_LINE1_ZH,
            ADDRESS_LINE2_EN: this.addressForm.ADDRESS_LINE2_EN,
            ADDRESS_LINE2_ZH: this.addressForm.ADDRESS_LINE2_ZH,
            ADDRESS_LINE3_EN: this.addressForm.ADDRESS_LINE3_EN,
            ADDRESS_LINE3_ZH: this.addressForm.ADDRESS_LINE3_ZH,
            ADDRESS_LINE4_EN: this.addressForm.ADDRESS_LINE4_EN,
            ADDRESS_LINE4_ZH: this.addressForm.ADDRESS_LINE4_ZH,
          };
        } else {
          data = {
            PO_BOX: this.addressForm.PO_BOX,
            ADDRESS_LINE1_EN: this.addressForm.NO_STANDARD_ADDRESS_LINE1_EN,
            ADDRESS_LINE2_EN: this.addressForm.NO_STANDARD_ADDRESS_LINE2_EN,
            ADDRESS_LINE3_EN: this.addressForm.NO_STANDARD_ADDRESS_LINE3_EN,
            ADDRESS_LINE4_EN: this.addressForm.NO_STANDARD_ADDRESS_LINE4_EN,
          };
        }
        return data;
      },
      // 更新和复制账户
      handleUpdateInitData() {
        const accountData = this.$store.state.account.accountData;

        // 更新账户增加ACCT_ID、BILL_DAY两个字段
        if (this.$route.query?.type === 'update') {
          this.form = {
            ACCOUNT_NAME: accountData.ACCOUNT.ACCOUNT_NAME,
            LOB: accountData.ACCOUNT.LOB,
            PRODUCT_FAMILY: accountData.ACCOUNT.PRODUCT_FAMILY,
            BILL_CONTACT: accountData.BILL.BILL_CONTACT,
            BILL_RECIPIENT: accountData.BILL.BILL_RECIPIENT,
            BILL_EMAIL: accountData.BILL.BILL_EMAIL,
            BILL_DAY: accountData.ITEM?.[0]?.['ATTR_VALUE'],
            ACCT_ID: accountData.ACCOUNT.ACCT_ID,
          };
        } else if (this.$route.query?.type === 'copy') {
          this.form = {
            ACCOUNT_NAME: accountData.ACCOUNT_NAME,
            LOB: accountData.LOB,
            PRODUCT_FAMILY: accountData.PRODUCT_FAMILY,
            BILL_CONTACT: accountData.BILL.BILL_CONTACT,
            BILL_RECIPIENT: accountData.BILL.BILL_RECIPIENT,
            BILL_EMAIL: accountData.BILL.BILL_EMAIL,
          };
        }

        // 判断是标准地址还是非标准地址
        if (accountData.BILL.BILL_ADDRESS_TYPE === 'Standard') {
          this.addressForm = {
            ADDRESS_EN: accountData.BILL.ADDRESS_EN,
            ADDRESS_ZH: accountData.BILL.ADDRESS_ZH,
            ADDRESS_LINE1_EN: accountData.BILL.ADDRESS_LINE1_EN,
            ADDRESS_LINE1_ZH: accountData.BILL.ADDRESS_LINE1_ZH,
            ADDRESS_LINE2_EN: accountData.BILL.ADDRESS_LINE2_EN,
            ADDRESS_LINE2_ZH: accountData.BILL.ADDRESS_LINE2_ZH,
            ADDRESS_LINE3_EN: accountData.BILL.ADDRESS_LINE3_EN,
            ADDRESS_LINE3_ZH: accountData.BILL.ADDRESS_LINE3_ZH,
            ADDRESS_LINE4_EN: accountData.BILL.ADDRESS_LINE4_EN,
            ADDRESS_LINE4_ZH: accountData.BILL.ADDRESS_LINE4_ZH,
          };
        } else {
          this.addressForm = {
            PO_BOX: accountData.BILL.PO_BOX,
            NO_STANDARD_ADDRESS_LINE1_EN: accountData.BILL.ADDRESS_LINE1_EN,
            NO_STANDARD_ADDRESS_LINE2_EN: accountData.BILL.ADDRESS_LINE2_EN,
            NO_STANDARD_ADDRESS_LINE3_EN: accountData.BILL.ADDRESS_LINE3_EN,
            NO_STANDARD_ADDRESS_LINE4_EN: accountData.BILL.ADDRESS_LINE4_EN,
          };
        }

        this.checkForm = {
          BILL_MEDIA:
            accountData.BILL.BILL_MEDIA === 'All'
              ? ['Paper', 'EBill']
              : [accountData.BILL.BILL_MEDIA],
          BILL_LANGUAGE:
            accountData.BILL.BILL_LANGUAGE === 'All'
              ? ['Traditional Chinese', 'English']
              : [accountData.BILL.BILL_LANGUAGE],
          BILL_FREQUENCY: accountData.ITEM?.[1]?.['ATTR_VALUE'],
          BILL_ADDRESS_TYPE: accountData.BILL.BILL_ADDRESS_TYPE,
          WAIVED_PAPER_BILL_FEE: accountData.BILL.WAIVED_PAPER_BILL_FEE === '1' ? true : false,
          INCLUDE_ODD_CENTS_IN_BILL:
            accountData?.BILL.INCLUDE_ODD_CENTS_IN_BILL === '1' ? true : false,
          DISPLAY_ZERO_BILL_ITEMS: accountData.BILL.DISPLAY_ZERO_BILL_ITEMS === '1' ? true : false,
        };
      },

      // 处理checkForm数据，多选如果选一个，则返回选择的字符串，全选则返回“All”字符串
      handleCheckFormField(formObj) {
        return {
          ...formObj,
          WAIVED_PAPER_BILL_FEE: formObj.WAIVED_PAPER_BILL_FEE ? '1' : '0',
          INCLUDE_ODD_CENTS_IN_BILL: formObj.INCLUDE_ODD_CENTS_IN_BILL ? '1' : '0',
          DISPLAY_ZERO_BILL_ITEMS: formObj.DISPLAY_ZERO_BILL_ITEMS ? '1' : '0',
          BILL_MEDIA: this.handleCheckFormArr(formObj['BILL_MEDIA']),
          BILL_LANGUAGE: this.handleCheckFormArr(formObj['BILL_LANGUAGE']),
        };
      },

      // 处理数组arr，如果长度为1，返回aff[0],如果长度为2，返回“All”字符串
      handleCheckFormArr(arr) {
        // 非数组，直接返回
        if (!Array.isArray(arr)) {
          return arr;
        }
        if (arr.length === 1) {
          return arr[0];
        } else if (arr.length === 2) {
          return 'All';
        }
        return '';
      },

      handleAddressOpen() {
        this.selectAddressVisible = true;
      },
      selectAddressCancel() {
        this.selectAddressVisible = false;
      },
      handleTipsOk() {
        this.tipsVisible = false;
      },

      handleAddressOK(data) {
        // 新增地址返回为字符串，选址为对象
        if (typeof data === 'string') {
          this.newAddressMappingOk(data);
        } else {
          // TODO
          return;
        }
        this.selectAddressVisible = false;
      },
      // todo ------------tanwl3 -------------------新增地址部分待完成，以下逻辑有问题（12.12）
      newAddressMappingOk(val) {
        this.$set(this.$refs.AddressMappingFormRef.form, 'InstallationAddress', val);
        this.$set(this.$refs.AddressMappingFormRef.form, 'SBNo', undefined);
        this.$refs.AddressMappingFormRef.$refs.ruleForm.validateField('InstallationAddress');
        this.setInstallationAddress({
          // BID: val.BID,
          InstallationAddress: val,
          SBNo: undefined,
        });
        setTimeout(() => {
          this.selectAddressCancel();
        }, 0);
      },
    },
  };
</script>

<style scoped lang="less">
  .flexContent {
    display: flex;
  }
  .ant-divider-horizontal {
    margin: 10px 0;
  }
  // a-form-model 组件
  // .commonFormModel {
  //   // 上下分表单间距
  //   .ant-form-item-label {
  //     margin-bottom: -8px;
  //   }
  //   // 表单标签和控件上下布局，避免间距过大，margin-bottom重置为0
  //   .ant-form-item {
  //     margin-bottom: 0;
  //   }
  //   // 在<col>里面的按钮 置右边
  //   .textAlignRight {
  //     text-align: right;
  //   }
  //   // 单独一行的按钮 置右边
  //   .textAlignRightMargin {
  //     text-align: right;
  //     margin: 10px 0;
  //   }
  // }
</style>
