<template>
  <div class="archived-page">
    <!-- 客户信息 -->
    <Customer />
    <a-divider />

    <!-- 编辑账户信息 -->
    <EditAccountInfo ref="accountRef" />
    <a-divider />

    <!-- 支付方式 -->
    <PayMentMethod ref="paymentMethodRef" />
    <a-divider />

    <!-- HKT账单内容 -->
    <HKTBillContact ref="hktBillContactRef" />

    <!-- 底部固定按钮 -->
    <div class="footer-tool-bar">
      <footer-tool-bar>
        <div>
          <a-button ghost type="primary" @click="handleCancel" class="reset-button">{{
            $t('common.buttonCancel')
          }}</a-button>
          <a-button type="primary" :loading="loading" @click="onConfirm" class="search-button">
            {{ $t('common.buttonConfirm') }}
          </a-button>
        </div>
      </footer-tool-bar>
    </div>
    <!-- 新增用户成功 -->
    <a-drawer
      title=""
      placement="right"
      :closable="false"
      :visible="successPageVisible"
      @close="successPageVisible = false"
      :width="width"
    >
      <successDrawerPage
        :subText="subText"
        :messageTitleTips="messageTitleTips"
        @handleConfirm="handleConfirm"
      />
    </a-drawer>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import Customer from './components/customer.vue';
  import EditAccountInfo from './components/editAccountInfo.vue';
  import PayMentMethod from './payMentMethod.vue';
  import HKTBillContact from './components/HKTBillContact.vue';
  import FooterToolBar from '@/components/footerToolbar';
  import successDrawerPage from './components/successDrawerPage.vue';
  import { addAccount, virtualUser } from '@/api/accountSetting';
  export default {
    name: 'addAccount',
    components: {
      Customer,
      EditAccountInfo,
      PayMentMethod,
      HKTBillContact,
      FooterToolBar,
      successDrawerPage,
    },
    props: {},
    data() {
      return {
        width: '100%',
        successPageVisible: false,
        messageTitleTips: '',
        subText: '',
        loading: false,
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      // ...mapState('macd', {
      //   transferCustInfo: state => state.transferCustInfo,
      // }),
    },
    methods: {
      // 确定操作账户
      async onConfirm() {
        // 表单校验不通过
        if (!this.$refs.accountRef.handleReturnFormData()) {
          return;
        }
        if (!this.$refs.paymentMethodRef.handleReturnData()) {
          return;
        }
        if (!this.$refs.hktBillContactRef.returnContactList()) {
          return;
        }

        const a = '';

        // TODO
        let params = {
          CUST_ID: (this.transferCustInfo && this.transferCustInfo.CUSTOMER_NO) || this.custId,
          ...this.$refs.accountRef.handleReturnFormData(),
          ...this.$refs.paymentMethodRef.handleReturnData(),
          ACCOUNT_CONTACT_LIST: this.$refs.hktBillContactRef.returnContactList(),
          UPDATE_DEPART_ID: this.$store.state.app?.userInfo?.DEPART_ID || '',
          UPDATE_STAFF_ID: this.$store.state.app?.userInfo?.STAFF_ID || '',
        };
        // update = 修改， 默认新增
        this.loading = true;
        try {
          const res = await addAccount(params);
          this.messageTitleTips = this.$t('accountSetting.successSubmitTips');
          this.subText = this.$t('accountSetting.newAccountNo') + res.DATA[0];
          this.successPageVisible = true;
          // 调用生成纸质账单虚拟用户
          this.setVirtualUserParams(res.DATA[0]);
          this.$emit('confirm'); // idd0060新增加的确定回调时间
        } catch (error) {
          console.log(error);
        }
        this.loading = false;
      },
      async setVirtualUserParams(ACCOUNT_NO) {
        const data = this.$refs.accountRef.handleReturnFormData();
        const { ACCOUNT_BILL } = data;
        console.log(ACCOUNT_BILL, 'ACCOUNT_BILL');

        var params = {};
        // 1、账户新建时，勾选的是纸质账单，且Waived paper bill fee=No时，需生成收费订单；
        //勾选的是纸质账单，且Waived paper bill fee=No时，需生成收费订单；
        if (ACCOUNT_BILL.BILL_MEDIA.includes('1') && ACCOUNT_BILL.WAIVED_PAPER_BILL_FEE == '0') {
          params = {
            'ACCOUNT_NO': ACCOUNT_NO,
            'ACCOUNT_NAME': data.ACCOUNT_NAME,
            'WAIVE_FEE': false,
            'CUST_ID': (this.transferCustInfo && this.transferCustInfo.CUSTOMER_NO) || this.custId,
            'TRADE_STAFF_ID': this.$store.state.app.userInfo.STAFF_ID,
            'TRADE_STAFF_NAME': this.$store.state.app.userInfo.STAFF_NAME,
            'DEPART_ID': this.$store.state.app.userInfo.DEPART_ID,
          };
          await virtualUser(params);
        }
      },
      handleCancel() {
        this.$emit('cancel');
      },
      // 新建用户成功确认
      handleConfirm() {
        this.handleCancel();
        this.successPageVisible = false;
      },
    },
  };
</script>
<style lang="less" scoped>
  .queryAddressBtn {
    padding: 0px 12px;
    margin-left: 10px;
    height: 22px;
  }
  .tokenBtn {
    padding: 0px 8px;
    border-radius: 2px;
  }
  .textArea {
    height: 60px !important;
  }
  .input-bottom-margin {
    margin-bottom: 20px;
  }
  /deep/ .ant-row-flex {
    margin-right: 0 !important;
  }

  .search-container {
    .b-btns {
      padding-right: 0 !important;
      .btns-container {
        height: 100%;
        padding-bottom: 18px;
        display: flex;
        align-items: end;
        justify-content: end;
      }
    }
  }

  /deep/ .checkBoxStyle {
    display: flex;
    .ant-form-item-label {
      width: 120px;
      display: flex;
      justify-content: end;
      margin-right: 15px;
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
  /deep/.ant-btn {
    border-radius: 2px !important;
  }
  /deep/.ant-form-item-label > label {
    display: flex;
    align-items: center;
  }
  .footer-tool-bar {
    height: 50px;

    button + button {
      margin-left: 10px;
    }
  }
</style>
