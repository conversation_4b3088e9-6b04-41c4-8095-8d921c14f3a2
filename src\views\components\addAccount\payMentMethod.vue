<template>
  <div class="page">
    <div class="com-title">{{ $t('accountSetting.paymentMethod') }}</div>
    <a-form-model :model="formData" :rules="cashRules" ref="formDataRef">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.payment')" prop="PAYMENT_METHOD">
            <a-select
              v-model="formData.PAYMENT_METHOD"
              :placeholder="$t('common.inputPlaceholder')"
              @change="handlePaymentChange"
            >
              <a-select-option v-for="item in payMentMethodTabList" :key="item.DATA_ID">{{
                item.DATA_NAME
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD !== '1'">
          <a-form-model-item :label="$t('accountSetting.cardCountry')" prop="CARD_COUNTRY">
            <a-select
              v-model="formData.CARD_COUNTRY"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in cardCountryList" :key="item.DATA_ID">{{
                item.DATA_NAME
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '2'">
          <a-form-model-item :label="$t('accountSetting.cardType')" prop="CARD_TYPE">
            <a-select
              v-model="formData.CARD_TYPE"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in cardTypeList" :key="item.DATA_ID">{{
                item.DATA_NAME
              }}</a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '2'">
          <a-form-model-item :label="$t('accountSetting.expirationDate')" prop="EXPIRATION_DATE">
            <a-date-picker
              valueFormat="MMDD"
              style="width: 100%"
              v-model="formData.EXPIRATION_DATE"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '3'">
          <a-form-model-item :label="$t('accountSetting.creditCardToken')" prop="CREDIT_CARD_TOKEN">
            <a-input-search
              v-model="formData.CREDIT_CARD_TOKEN"
              :placeholder="$t('common.inputPlaceholder')"
              enter-button="Token"
              @search="handleTokenSearch"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '3'">
          <a-form-model-item :label="$t('accountSetting.cardName')" prop="CARD_NAME">
            <a-input
              v-model="formData.CARD_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="16" v-if="formData.PAYMENT_METHOD !== '1'">
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '2'">
          <a-form-model-item :label="$t('accountSetting.creditCardToken')" prop="CREDIT_CARD_TOKEN">
            <a-input-search
              v-model="formData.CREDIT_CARD_TOKEN"
              :placeholder="$t('common.inputPlaceholder')"
              enter-button="Token"
              @search="handleTokenSearch"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '2'">
          <a-form-model-item :label="$t('accountSetting.cardName')" prop="CARD_NAME">
            <a-input
              v-model="formData.CARD_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" v-if="formData.PAYMENT_METHOD === '3'">
          <a-form-model-item :label="$t('accountSetting.bankNo')" prop="BANK_NO">
            <a-input
              v-model="formData.BANK_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import { cashRules } from './config';
  import { getCreditCardTokenUrl, getCreditCardToken, qryStaticList } from '@/api/accountSetting';

  export default {
    data() {
      return {
        payMentMethodTabList: [],
        cardTypeList: [],
        cardCountryList: [],
        formData: {
          PAYMENT_METHOD: '1',
          CARD_COUNTRY: '',
          CARD_TYPE: '',
          EXPIRATION_DATE: '',
          CREDIT_CARD_TOKEN: '',
          CARD_NAME: '',
          BANK_NO: '',
        },
        cashRules,
      };
    },
    mounted() {
      this.handleInitPaymentMethod();
      // 编辑，回填数据
      if (this.$route.query?.type === 'update' || this.$route.query?.type === 'copy') {
        this.handleInitUpdateAccount();
      }
    },
    methods: {
      // 请求支付方式\cardType枚举
      async handleInitPaymentMethod() {
        let res = await this.handleStaticList('PAYMENT_METHOD_TYPE');
        this.payMentMethodTabList = res || [];
        console.log('res = this.payMentMethodTabList', res, this.payMentMethodTabList);

        res = await this.handleStaticList('CARD_TYPE');
        this.cardTypeList = res || [];
      },

      // 枚举请求方法统一封装
      async handleStaticList(TYPE_ID) {
        const res = await qryStaticList({ TYPE_ID });
        return res?.DATA;
      },

      // 支付方式更改
      handlePaymentChange(value) {
        this.formData = {
          PAYMENT_METHOD: value,
          CARD_COUNTRY: '',
          CARD_TYPE: '',
          EXPIRATION_DATE: '',
          CREDIT_CARD_TOKEN: '',
          CARD_NAME: '',
          BANK_NO: '',
        };

        if (value === '2') {
          this.getCardCountryList();
        } else if (value === '3') {
          this.getCardCountryList('DIRECT_DEBIT_TYPE');
        }
      },

      // TODO调用接口获取token，循环请求
      async handleTokenSearch() {
        const res = await getCreditCardTokenUrl();
        if (res?.STATUS === '0000') {
          const { OPP_REF_ID, REDIRECT_URL } = res?.RSP?.DATA?.[0];
          const windowFeatuers =
            'margin=auto,width=600,height=400,resizable=yes,scrollbars=yes,status=1';
          window.open(REDIRECT_URL, '_blank', windowFeatuers);
          this.handleGetCreditCardToken(OPP_REF_ID);
        }
      },

      async handleGetCreditCardToken(OPP_REF_ID) {
        const res = await getCreditCardToken({ OPP_REF_ID });
        if (res?.STATUS === '0000') {
          const { OPP_REF_ID, CREDIT_CARD_TOKEN } = res?.RSP?.DATA?.[0];
          this.formData['CREDIT_CARD_TOKEN'] = OPP_REF_ID;
        }
      },

      // 处理该组件数据，用于接口入参
      handleReturnData() {
        let flag = true;
        this.$refs.formDataRef.validate(valid => {
          if (!valid) {
            flag = false;
          }
        });
        return flag ? this.formData : flag;
      },

      // 查cardCountry枚举数据
      async getCardCountryList(TYPE_ID = 'CREDIT_CARD_AUTOPAY_TYPE') {
        let res = await this.handleStaticList(TYPE_ID);
        console.log('res = ', res, res.STATUS, res.RSP);
        if (res?.STATUS === '0000') {
          this.cardCountryList = res || [];
        }
      },

      // 修改和复制账户回填数据
      handleInitUpdateAccount() {
        const accountData = this.$store.state.account.accountData;
        const paymentMethodData = {
          PAYMENT_METHOD: accountData?.PAYMENT_METHOD,
          CARD_COUNTRY: accountData?.CARD_COUNTRY,
          CARD_TYPE: accountData?.CARD_TYPE,
          EXPIRATION_DATE: accountData?.EXPIRATION_DATE,
          CREDIT_CARD_TOKEN: accountData?.CREDIT_CARD_TOKEN,
          CARD_NAME: accountData?.CARD_NAME,
          BANK_NO: accountData?.BANK_NO,
        };
        this.formData = paymentMethodData;
      },
    },
  };
</script>

<style scoped lang="less">
  .selectedTabList {
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
    .item {
      position: relative;
      height: 40px;
      line-height: 40px;
      border-radius: 2px;
      margin-right: 10px;
      padding: 0 10px;
      cursor: pointer;
    }
    .active {
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 80%;
        height: 2px;
        background: #01408e;
        text-align: center;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-top-color: #01408e;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
</style>
