<template>
  <div>
    <PopWindow
      :visible="visible"
      :title="$t('comp.AttachmentUploading')"
      @cancel="handleCancel"
      :bodyStyle="bodyStyle"
      :footer="true"
      modalWidth="420px"
    >
      <template #Content>
        <div class="search-container">
          <a-form-model
            :model="formData"
            :colon="false"
            :rules="formRules"
            layout="vertical"
            ref="searchForm"
          >
            <a-row :gutter="24" justify="start" type="flex">
              <a-col :span="18" flex="flex-start">
                <a-form-model-item
                  :label="$t('common.type')"
                  prop="ATTACHMENT_TYPE"
                  labelAlign="left"
                >
                  <a-select
                    v-model="formData.ATTACHMENT_TYPE"
                    :placeholder="$t('common.selectPlaceholder')"
                    allowClear
                  >
                    <a-select-option
                      v-for="item in attchmentTypeData"
                      :value="item.DATA_ID"
                      :key="item.DATA_ID"
                    >
                      {{ item.DATA_NAME }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24" justify="start" type="flex">
              <a-col :span="24" flex="flex-start">
                <a-form-model-item
                  :label="$t('comp.AttachmentRemark')"
                  prop="REMARK"
                  labelAlign="left"
                >
                  <a-textarea
                    v-model="formData.REMARK"
                    :autoSize="{ minRows: 3, maxRows: 6 }"
                    :max-length="200"
                    :placeholder="$t('common.inputPlaceholder')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
          <a-divider dashed />
          <!-- 文件后缀名可以是：【msg】、【eml】 【txt】 【doc】【docx】 【xls】 【xlsx】 【csv】【 ppt】 【pptx】 【pdf  -->
          <a-upload
            :multiple="false"
            accept=".msg,.eml,.txt,.doc,.docx,.docx,.xls,.xlsx,.csv,.ppt,.pptx,.pdf"
            :showUploadList="true"
            list-type="text"
            :file-list="fileList"
            :before-upload="file => beforeUpload(file)"
            @change="handleChange"
          >
            <div class="flexCenter" v-show="fileList.length == 0">
              <div class="listAdd upload">+ {{ $t('common.upload') }}</div>
            </div>
          </a-upload>
        </div>
      </template>
      <template #footer>
        <a-button class="modal-button-cancel" @click="handleCancel">{{
          $t('common.buttonCancel')
        }}</a-button>
        <a-button
          type="primary"
          :disabled="uploadLoading"
          :loading="uploadLoading"
          @click="handleOk"
          class="moadl-button-Ok"
          >{{ $t('common.buttonConfirm') }}</a-button
        >
      </template>
    </PopWindow>
  </div>
</template>
<script>
  import PopWindow from '@/components/popWindow';
  import { queryParamList, uploadFile } from '@/api/common';
  import { mapState } from 'vuex';
  import { getCurrentTime } from '@/utils/utils';
  export default {
    name: 'addAttachmentUpload',
    components: {
      PopWindow,
    },
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
    },
    data() {
      return {
        uploadLoading: false,
        formData: { ATTACHMENT_TYPE: '', REMARK: '' },
        formRules: {
          ATTACHMENT_TYPE: [
            {
              required: true,
              message: 'Please Select AttachmentType',
              trigger: 'blur',
            },
          ],
        },
        bodyStyle: {
          height: '360px',
          padding: '10px',
        },
        attchmentTypeData: [],
        fileList: [],
        attachmentInfo: {}, // 上传的附件信息
      };
    },
    async created() {
      this.queryattchmentTypeData();
    },
    methods: {
      // 获取附件文件类型数据
      queryattchmentTypeData() {
        queryParamList({ TYPE_ID: 'OSCA_ATTACHMENT_TYPE' })
          .then(res => {
            this.attchmentTypeData = res.DATA || [];
          })
          .catch(() => {});
      },
      // 是否可接受的文件上传类型
      isAcceptFile(file) {
        // 文件类型判断
        const extension = file.name.match(/\.(\w+)$/)[1];
        const isAcceptFile = [
          'msg',
          'eml',
          'txt',
          'doc',
          'docx',
          'docx',
          'xls',
          'xlsx',
          'csv',
          'ppt',
          'pptx',
          'pdf',
        ].includes(extension);
        return isAcceptFile;
      },
      // 上传文件
      beforeUpload(file) {
        if (file) {
          //文件限制在10MB内
          if (file.size > 10 * 1024 * 1024) {
            this.$message.error(this.$t('quotation.FileSizeLimit'));
            return false;
          }
          if (!this.isAcceptFile(file)) {
            this.$message.error(this.$t('quotation.FileTypeLimit'));
            return false;
          }
          this.handleUpload(file);
        }
        return false;
      },
      // 附件更改
      handleChange(info) {
        if (info.fileList.length > 0 && info.fileList[0].size > 10 * 1024 * 1024) {
          return;
        }
        if (!this.isAcceptFile(info.fileList[0])) {
          return;
        }
        this.fileList = info.fileList;
      },
      // 上传接口
      async handleUpload(file) {
        let formData = new FormData();
        formData.append('file', file);
        this.uploadLoading = true;
        const currentTime = await getCurrentTime();
        const res = await uploadFile(formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        this.$message.success(this.$t('common.uploadSuccess'));
        this.uploadLoading = false;
        this.attachmentInfo.SERVER_TYPE = this.formData.ATTACHMENT_TYPE;
        this.attachmentInfo.FILE_TYPE = '11'; //01-欢迎信，11-OSCA报价单附件
        this.attachmentInfo.FILE_NAME = file.name;
        this.attachmentInfo.FILE_DESC = this.formData.REMARK;
        this.attachmentInfo.FILE_URL = res?.DATA[0] || '';
        this.attachmentInfo.OPER_STAFF_ID = this.userInfo.STAFF_ID;
        this.attachmentInfo.OPER_STAFF_NAME = this.userInfo.STAFF_NAME;
        this.attachmentInfo.OPER_TIME = currentTime;
        this.attachmentInfo.MODIFY_TAG = '0';
      },
      //校验
      validate() {
        let validateFlag = true;
        this.$refs.searchForm.validate(valid => {
          validateFlag = valid;
        });
        return validateFlag;
      },
      // 取消操作
      handleCancel() {
        this.$emit('upLoadCancel');
      },
      // 上传弹窗确定按钮
      handleOk() {
        this.attachmentInfo.SERVER_TYPE = this.formData.ATTACHMENT_TYPE;
        const obj = this.attchmentTypeData.find(
          item => item.DATA_ID == this.formData.ATTACHMENT_TYPE,
        );
        this.attachmentInfo.SERVER_TYPE_NAME = obj?.DATA_NAME || '';
        if (!this.validate()) {
          return;
        }
        if (!this.attachmentInfo.FILE_URL) {
          this.$message.info(this.$t('comp.UploadAttchmentTips'));
          return;
        }
        this.$emit('uploadComplete', this.attachmentInfo);
      },
    },
  };
</script>
<style lang="less" scoped>
  .listAdd {
    margin-top: 10px;
    width: 100%;
    height: 40px;
    background-color: #ffffff;
    border: 1px dashed #0076ff;
    color: #0076ff;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }
  .flexCenter {
    width: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .upload {
    height: 32px;
    line-height: 32px;
    width: 210px;
  }
  .textArea {
    min-height: 82px;
  }
  /deep/ .ant-modal {
    width: 420px !important;
  }
  /deep/.ant-divider-dashed {
    margin: 0px 0 !important;
  }
</style>
