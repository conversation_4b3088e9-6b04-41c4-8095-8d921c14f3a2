import that from '@/main.js';
export default {
  customerContactListColumns: [
    {
      title: that.$t('fulfillmentInfo.seq'),
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: that.$t('fulfillmentInfo.title'),
      scopedSlots: { customRender: 'customerTitle' },
      width: 120,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
    },
    {
      title: that.$t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
    },
    {
      title: that.$t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
};
