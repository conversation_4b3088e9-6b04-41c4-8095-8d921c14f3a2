<template>
  <div class="customerContact">
    <div class="secondLevel-header-title">{{ $t('fulfillmentInfo.customerContactList') }}</div>
    <a-table
      :columns="columns"
      :data-source="filterDataList"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
      :scroll="{ x: 1600 }"
    >
      <!-- 自定义标题 -->
      <!-- type -->
      <template slot="customTitle_participantsType">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.type') }}
      </template>
      <!-- name -->
      <template slot="customTitle_name">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.name') }}
      </template>
      <!-- contactPhone -->
      <template slot="customTitle_contactPhone">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.contactPhone') }}
      </template>

      <span slot="participantsType" slot-scope="record, index">
        <a-select
          :value="record.PARTICIPANTS_TYPE"
          :placeholder="$t('common.selectPlaceholder')"
          allowClear
          style="width: 100%"
          @change="onChange($event, record, 'PARTICIPANTS_TYPE')"
        >
          <a-select-option
            v-for="item in customerParticipantsType"
            :key="item.CODE_NAME"
            :value="item.CODE_VALUE"
          >
            {{ item.CODE_NAME }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="customerTitle" slot-scope="record, index">
        <a-select
          @change="onChange($event, record, 'TITLE')"
          v-model="record.TITLE"
          :placeholder="$t('common.selectPlaceholder')"
          style="width: 100%"
        >
          <a-select-option
            v-for="item in titleList"
            :key="item.CODE_VALUE"
            :value="item.CODE_VALUE"
            >{{ item.CODE_NAME }}</a-select-option
          >
        </a-select>
      </span>
      <span slot="name" slot-scope="record, index">
        <a-input
          v-containsSqlInjection
          v-model.trim="record.NAME"
          :maxLength="40"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'NAME')"
        />
      </span>
      <span slot="contactPhone" slot-scope="record, index">
        <a-input
          v-validate-number
          v-model="record.CONTACT_PHONE"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'CONTACT_PHONE')"
        />
      </span>
      <span slot="mobile" slot-scope="record, index">
        <a-input
          v-validate-number
          v-model="record.MOBILE"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'MOBILE')"
        />
      </span>
      <span slot="email" slot-scope="record, index">
        <a-input
          v-validateEmail
          v-model.trim="record.EMAIL"
          :placeholder="$t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'EMAIL')"
        />
      </span>
      <span slot="action" slot-scope="record, index">
        <RPopover @onConfirm="onDelete(record)" :content="$t('common.deleteConfirm')">
          <template slot="button">
            <i class="iconfont icon-shanchu"></i>
          </template>
        </RPopover>
      </span>
    </a-table>
    <div class="listAdd" @click="onAdd">+ {{ $t('common.add') }}</div>
    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  // MODIFY_TAG 0:新增 1:删除 2:更改 (-1:前端自定义标识，用来区分没有修改过)
  // id 唯一标识
  // old 区分旧数据
  // 新增 加上标识即可
  // 更改 a: 改动过的加上标识  b:但是改动过又改回原数据的，恢复为-1,即没修改过
  // 删除 a: 删除加上标识      b: 新增的数据又删除，则直接删除掉这条数据
  import { mapState, mapGetters } from 'vuex';
  import { validateHKTPhone, validateEmail } from '@/utils/utils.js';
  import config from './config';
  import RPopover from '@/components/Rpopover';
  import { queryCreateCustomerEnum, qryCustContactInfo } from '@/api/fulfillmentInfo';
  import MessageModal from '@/components/messageModal';

  export default {
    name: 'addCustomerContact',
    components: {
      RPopover,
      MessageModal,
    },
    data() {
      return {
        columns: config.customerContactListColumns,
        customerParticipantsType: [],
        dataList: [],
        originalDataList: [],
        tipsVisible: false,
        tipsMessage: '',
        titleList: [],
      };
    },

    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      ...mapGetters('macd', ['getCustomerContactList']),
      // 过滤掉已删除的项
      filterDataList() {
        return this.dataList.filter(item => item.MODIFY_TAG != 1);
      },
    },
    mounted() {
      this.getDataList();
      this.getTypeList();
    },
    methods: {
      // 查列表数据
      async getDataList() {
        let params = {
          CUST_ID: this.custId,
          CONTACT_TYPE: '0',
          PAGE_NUM: 1,
          PAGE_SIZE: 999,
        };
        try {
          const res = await qryCustContactInfo(params);
          let list = (res.DATA[0]?.LIST || []).map((item, index) => {
            return {
              ...item,
              id: index + 1,
              old: true,
              MODIFY_TAG: '-1',

              CREATE_STAFF_ID: '1',
              CREATE_STAFF_NAME: '1',
              UPDATE_STAFF_ID: '1',
              UPDATE_STAFF_NAME: '1',
            };
          });
          this.dataList = list;
          this.originalDataList = JSON.parse(JSON.stringify(list));

          if (this.$route.name === 'fulfillmentInfo') {
            this.hanldeInitMACDData();
          }
        } catch (error) {
          console.log(error);
        }
      },

      // 初始化macd数据
      hanldeInitMACDData() {
        if (!this.getHKTContactList || !this.getCustomerContactList.length) return;
        this.dataList = this.getCustomerContactList;
      },

      // 查枚举数据
      async getTypeList() {
        const params = {
          CODE_TYPE: 'PARTICIPANTS_TYPE_CUSTOMER',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.customerParticipantsType = res.DATA;
          const res2 = await queryCreateCustomerEnum({ 'CODE_TYPE': 'TITLE_TYPE' });
          this.titleList = res2.DATA;
        } catch (error) {
          console.log(error);
        }
      },
      // 新增
      onAdd() {
        this.dataList = this.filterDataList;
        this.dataList.push({
          CUST_ID: this.custId,
          id: this.dataList.length + 1,
          CONTACT_TYPE: '0',
          PARTICIPANTS_TYPE: undefined,
          TITLE: undefined,
          NAME: '',
          CONTACT_PHONE: '',
          MOBILE: '',
          EMAIL: '',
          MODIFY_TAG: '0', // 0:新增 1:删除 2:更改
          // 这四个字段，创建的时候都有，但是修改的时候只有update的两个
          CREATE_STAFF_ID: '1',
          CREATE_STAFF_NAME: '1',
          UPDATE_STAFF_ID: '1',
          UPDATE_STAFF_NAME: '1',
        });
      },

      // 删除
      onDelete(record, index) {
        // 旧数据打上标识，新增后又删除的，直接删除数据
        if (record.old) {
          record.MODIFY_TAG = '1';
          // 过滤掉已删除的项并重新赋值给 this.dataList
          this.dataList = this.dataList.filter(item => item.MODIFY_TAG != 1);
        } else {
          let index = this.dataList.findIndex(item => item.id === record.id);
          if (index !== -1) {
            this.dataList.splice(index, 1);
          }
        }
        if (this.dataList) {
          this.dataList = this.dataList.map((item, index) => {
            return {
              ...item,
              id: index + 1,
            };
          });
        }
      },
      // 修改 (判断原来的数据和修改后的数据是否一致)
      onChange(value, record, key) {
        // 校验PARTICIPANTS_TYPE不能重复选
        // if (key == 'PARTICIPANTS_TYPE') {
        //   let bool = this.filterDataList.some(item => value && item.PARTICIPANTS_TYPE === value);
        //   if (bool) {
        //     this.tipsMessage = this.$t('fulfillmentInfo.repeatChoose');
        //     this.tipsVisible = true;
        //     return;
        //   }
        // }
        if (key == 'PARTICIPANTS_TYPE') {
          record.PARTICIPANTS_TYPE = value;
        }
        let obj = this.originalDataList.find(item => item.id === record.id);
        // 没有找到，说明是新增的数据
        if (!obj) return;
        if (value === obj[key]) {
          // 未修改(注意：-1值是前端定义的，用作于后面过滤掉，不需要入参的数据)
          record.MODIFY_TAG = '-1';
        } else {
          record.MODIFY_TAG = '2';
        }
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
        throw console.log('阻断继续往下执行');
      },
      // 组件过滤后输出的数据
      returnDataList() {
        return this.dataList.filter(item => item.MODIFY_TAG !== '-1');
      },
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          if (this.filterDataList.length < 1) {
            this.showTipModal(this.$t('fulfillmentInfo.customerContactValidTips'));
          } else {
            const participantsType = this.filterDataList.find(
              item => item.PARTICIPANTS_TYPE == '01',
            );
            if (!participantsType) {
              this.showTipModal(this.$t('fulfillmentInfo.customerContactValidTips'));
            }
          }
          // 校验 (只校验0-新增和2-更改的数据) 是否为空/格式是否正确
          // 0:新增 1:删除 2:更改 -1:未修改过
          let list = this.dataList.filter(item => ['0', '2'].includes(item.MODIFY_TAG));
          list.forEach(item => {
            if (!item.PARTICIPANTS_TYPE) {
              this.showTipModal(this.$t('fulfillmentInfo.customerContactTypeRequired'));
            } else if (!item.NAME) {
              this.showTipModal(this.$t('fulfillmentInfo.customerContactNameRequired'));
            } else if (!item.CONTACT_PHONE) {
              this.showTipModal(this.$t('fulfillmentInfo.customerContactPhoneRequired'));
            }
            // else if (!validateHKTPhone(item.CONTACT_PHONE)) {
            //   this.showTipModal(this.$t('fulfillmentInfo.customerContactPhoneIncorrectFormat'));
            // }else if (item.MOBILE && !validateHKTPhone(item.MOBILE)) {
            //   this.showTipModal(this.$t('fulfillmentInfo.customerContactMobileIncorrectFormat'));
            // }
            else if (item.EMAIL && !validateEmail(item.EMAIL)) {
              this.showTipModal(this.$t('fulfillmentInfo.emailIncorrectFormat'));
            } else {
              console.log('校验完成');
            }
          });
          resolve();
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .customerContact {
    margin-bottom: 20px;
    .listAdd {
      margin-top: 10px;
      width: 100%;
      height: 40px;
      background-color: #ffffff;
      border: 1px dashed #0076ff;
      color: #0076ff;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
    /deep/ .ant-input[disabled] {
      color: #999;
    }
    .required {
      color: #f5222d;
      font-size: 14px;
    }
  }
</style>
