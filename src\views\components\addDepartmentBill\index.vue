<template>
  <PopWindow
    :visible="visible"
    :title="title"
    @cancel="handleCancel"
    :bodyStyle="bodyStyle"
    :footer="true"
    modalWidth="720px"
  >
    <template #Content>
      <div class="search-container">
        <a-form-model ref="ruleForm" :model="ruleForm" :rules="rules" :colon="false">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-model-item :label="$t('accountSetting.departmentName')" prop="departmentName">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="ruleForm.departmentName"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <!-- <a-col :span="8">
              <a-form-model-item :label="$t('accountSetting.serviceType')" prop="serviceType">
                <a-select
                  v-model="ruleForm.serviceType"
                  :placeholder="$t('common.selectPlaceholder')"
                >
                  <a-select-option
                    v-for="item in serviceTypeOptions"
                    :key="item.CODE_VALUE"
                    :value="item.CODE_VALUE"
                  >
                    {{ item.CODE_NAME }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col> -->
          </a-row>
          <!-- <a-row :gutter="16">
            <a-col :span="24">
              <a-form-model-item :label="$t('accountSetting.remark')" class="textAreaBorder">
                <a-textarea
                  class="textArea"
                  v-model="ruleForm.remark"
                  :placeholder="$t('common.inputPlaceholder')"
                  :rows="3"
                />
              </a-form-model-item>
            </a-col>
          </a-row> -->
        </a-form-model>
      </div>
    </template>
    <template #footer>
      <a-button @click="handleCancel">{{ $t('common.buttonCancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.buttonConfirm') }}</a-button>
    </template>
  </PopWindow>
</template>

<script>
  import { mapState } from 'vuex';
  import PopWindow from '@/components/popWindow';
  import { addDepartmentBill } from '@/api/accountSetting';
  import { queryStaticData } from '@/api/customerVerify';
  export default {
    components: {
      PopWindow,
    },
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        bodyStyle: {
          // height: "420px",
          padding: '10px',
          overflow: 'auto',
        },
        ruleForm: {
          departmentName: '',
          serviceType: undefined,
          remark: '',
        },
        rules: {
          departmentName: [{ required: true, message: 'Please Enter！' }],
          serviceType: [{ required: true, message: 'Please Select！' }],
        },
        serviceTypeOptions: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
    },
    mounted() {
      // this.queryDeptBillServiceTypeFunc();
    },
    methods: {
      handleCancel() {
        this.$emit('cancel');
      },
      // 新增数据
      async handleSubmit() {
        let params = {
          DEPARTMENT_NAME: this.ruleForm.departmentName ?? '',
          // SERVICE_TYPE: this.ruleForm.serviceType ?? '',
          CUST_ID: this.custId,
        };
        try {
          await addDepartmentBill(params);
          this.$emit('ok', params);
        } catch (error) {
          console.log('新增失败' + error);
        }
      },
      handleOk() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.handleSubmit();
          } else {
            return false;
          }
        });
      },
      // 查询部门账单下拉选项
      // async queryDeptBillServiceTypeFunc() {
      //   try {
      //     const res = await queryStaticData({ CODE_TYPE: 'DEPARTMENT_BILL_TYPE' });
      //     this.serviceTypeOptions = res?.DATA ?? [];
      //   } catch (error) {
      //     console.log(error);
      //   }
      // },
    },
  };
</script>

<style lang="less" scoped>
  .textAreaBorder {
    width: 100%;
    .textArea {
      height: 100px !important;
    }
  }
</style>
