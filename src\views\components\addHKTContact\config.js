import that from '@/main.js';
export default {
  HKTContactListColumns: [
    {
      title: that.$t('fulfillmentInfo.seq'),
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
      width: 150,
    },
    {
      scopedSlots: { title: 'customTitle_staffID', customRender: 'staffID' },
      width: 200,
    },
    {
      title: that.$t('fulfillmentInfo.salesCode'),
      scopedSlots: { customRender: 'salesCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_agentCode', customRender: 'agentCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_agentName', customRender: 'agentName' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_orderSaleType', customRender: 'orderSaleType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
      width: 200,
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 80,
    },
  ],
};
