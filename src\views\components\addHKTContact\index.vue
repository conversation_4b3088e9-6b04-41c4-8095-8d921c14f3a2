<template>
  <div class="customerContact">
    <div class="secondLevel-header-title">
      {{ $t('fulfillmentInfo.HKTContact') }}
    </div>
    <a-table
      :columns="columns"
      :data-source="filterDataList"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
      :scroll="{ x: 1600 }"
    >
      <!-- 自定义表头标题 -->
      <!--  participantsType -->
      <template slot="customTitle_participantsType">
        <span class="required">*</span>
        {{ $t('fulfillmentInfo.type') }}
      </template>
      <!-- name -->
      <template slot="customTitle_name" slot-scope="record, index">
        <!-- 注：如果type=3且只有一条数据，不需要显示星号必填 -->
        <span
          class="required"
          v-if="!(getTypeValueList.length == 1 && getTypeValueList.includes('03'))"
          >*</span
        >
        {{ $t('fulfillmentInfo.name') }}
      </template>
      <!-- staffID -->
      <template slot="customTitle_staffID">
        <span
          class="required"
          v-if="!(getTypeValueList.length == 1 && getTypeValueList.includes('03'))"
          >*</span
        >
        {{ $t('fulfillmentInfo.staffId') }}
      </template>
      <!-- contactPhone -->
      <template slot="customTitle_contactPhone">
        <span
          class="required"
          v-if="!(getTypeValueList.length == 1 && getTypeValueList.includes('03'))"
          >*</span
        >
        {{ $t('fulfillmentInfo.contactPhone') }}
      </template>
      <!-- agentCode -->
      <template slot="customTitle_agentCode">
        <span class="required" v-if="getTypeValueList.includes('03')">*</span>
        {{ $t('fulfillmentInfo.agentCode') }}
      </template>
      <!-- agentName -->
      <template slot="customTitle_agentName">
        <span class="required" v-if="getTypeValueList.includes('03')">*</span>
        {{ $t('fulfillmentInfo.agentName') }}
      </template>
      <!-- orderSaleType -->
      <template slot="customTitle_orderSaleType">
        <span class="required" v-if="getTypeValueList.includes('01')">*</span>
        {{ $t('fulfillmentInfo.orderSalesType') }}
      </template>

      <!-- 自定义表格内容 -->
      <span slot="participantsType" slot-scope="record, index">
        <a-select
          :value="record.PARTICIPANTS_TYPE"
          :placeholder="$t('common.selectPlaceholder')"
          style="min-width: 150px; width: 100%"
          @change="onChange($event, record, 'PARTICIPANTS_TYPE')"
        >
          <a-select-option
            v-for="item in HKTParticipantsType"
            :key="item.CODE_NAME"
            :value="item.CODE_VALUE"
          >
            {{ item.CODE_NAME }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="staffID" slot-scope="record, index">
        <div class="inputSearch">
          <a-input
            v-containsSqlInjection
            class="input"
            v-model.trim="record.STAFF_ID"
            :placeholder="record.STAFF_ID_DISABLED ? '' : $t('common.inputPlaceholder')"
            @change="onChange($event.target.value, record, 'STAFF_ID')"
            :disabled="record.STAFF_ID_DISABLED"
          />
          <i class="searchIcon iconfont icon-sousuo" @click="clickChange(record, 'STAFF_ID')"></i>
        </div>
      </span>
      <span slot="name" slot-scope="record, index">
        <a-input
          v-containsSqlInjection
          v-model.trim="record.NAME"
          :placeholder="record.NAME_DISABLED ? '' : $t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'NAME')"
          :disabled="record.NAME_DISABLED"
        />
      </span>
      <span slot="salesCode" slot-scope="record, index">
        <div class="inputSearch">
          <a-input
            v-containsSqlInjection
            class="input"
            v-model.trim="record.SALES_CODE"
            :placeholder="record.SALES_CODE_DISABLED ? '' : $t('common.inputPlaceholder')"
            @change="onChange($event.target.value, record, 'SALES_CODE')"
            :disabled="record.SALES_CODE_DISABLED"
          />
          <i class="searchIcon iconfont icon-sousuo" @click="clickChange(record, 'SALES_CODE')"></i>
        </div>
      </span>
      <span slot="agentCode" slot-scope="record, index">
        <div class="inputSearch">
          <a-input
            v-containsSqlInjection
            class="input"
            v-model.trim="record.AGENT_CODE"
            :placeholder="record.AGENT_CODE_DISABLED ? '' : $t('common.inputPlaceholder')"
            @change="onChange($event.target.value, record, 'AGENT_CODE')"
            :disabled="record.AGENT_CODE_DISABLED"
          />
          <i
            class="searchIcon iconfont icon-sousuo"
            @click="agentCodeAndNameQuery(record, 'AGENT_CODE')"
          ></i>
        </div>
      </span>
      <span slot="agentName" slot-scope="record, index">
        <div class="inputSearch">
          <a-input
            v-containsSqlInjection
            class="input"
            v-model.trim="record.AGENT_NAME"
            :placeholder="record.AGENT_NAME_DISABLED ? '' : $t('common.inputPlaceholder')"
            @change="onChange($event.target.value, record, 'AGENT_NAME')"
            :disabled="record.AGENT_NAME_DISABLED"
          />
          <i
            class="searchIcon iconfont icon-sousuo"
            @click="agentCodeAndNameQuery(record, 'AGENT_NAME')"
          ></i>
        </div>
      </span>
      <span slot="orderSaleType" slot-scope="record, index">
        <a-select
          v-model="record.ORDER_SALES_TYPE"
          :placeholder="record.ORDER_SALES_TYPE_DISABLED ? '' : $t('common.selectPlaceholder')"
          allowClear
          style="width: 100%"
          @change="onChange($event, record, 'ORDER_SALES_TYPE')"
          :disabled="record.ORDER_SALES_TYPE_DISABLED"
        >
          <a-select-option
            v-for="item in orderSalesTypeList"
            :key="item.CODE_NAME"
            :value="item.CODE_VALUE"
          >
            {{ item.CODE_NAME }}
          </a-select-option>
        </a-select>
      </span>
      <span slot="contactPhone" slot-scope="record, index">
        <a-input
          v-validateHKTPhone
          v-model="record.CONTACT_PHONE"
          :placeholder="record.CONTACT_PHONE_DISABLED ? '' : $t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'CONTACT_PHONE')"
          :disabled="record.CONTACT_PHONE_DISABLED"
        />
      </span>
      <span slot="mobile" slot-scope="record, index">
        <a-input
          v-validateHKTPhone
          v-model="record.MOBILE"
          :placeholder="record.MOBILE_DISABLED ? '' : $t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'MOBILE')"
          :disabled="record.MOBILE_DISABLED"
        />
      </span>
      <span slot="email" slot-scope="record, index">
        <a-input
          v-validateEmail
          v-model.trim="record.EMAIL"
          :placeholder="record.EMAIL_DISABLED ? '' : $t('common.inputPlaceholder')"
          @change="onChange($event.target.value, record, 'EMAIL')"
          :disabled="record.EMAIL_DISABLED"
        />
      </span>
      <span slot="action" slot-scope="record, index">
        <RPopover @onConfirm="onDelete(record)" :content="$t('common.deleteConfirm')">
          <template slot="button">
            <i class="iconfont icon-shanchu"></i>
          </template>
        </RPopover>
      </span>
    </a-table>
    <div class="listAdd" @click="onAdd">+ {{ $t('common.add') }}</div>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  // MODIFY_TAG 0:新增 1:删除 2:更改 (-1:前端自定义标识，用来区分没有修改过)
  // id 唯一标识
  // old 区分旧数据
  // 新增 加上标识即可
  // 更改 a: 改动过的加上标识  b:但是改动过又改回原数据的，恢复为-1,即没修改过
  // 删除 a: 删除加上标识      b: 新增的数据又删除，则直接删除掉这条数据
  import config from './config';
  import { mapState, mapGetters } from 'vuex';
  import { validateHKTPhone, validateEmail } from '@/utils/utils.js';
  import { staffQuery, infoQuery } from '@/api/common';
  import { queryCreateCustomerEnum, qryCustContactInfo } from '@/api/fulfillmentInfo';
  import RPopover from '@/components/Rpopover';
  import MessageModal from '@/components/messageModal';
  export default {
    name: 'addHKTContact',
    components: {
      RPopover,
      MessageModal,
    },
    data() {
      return {
        columns: config.HKTContactListColumns,
        HKTParticipantsType: [],
        dataList: [],
        originalDataList: [],
        orderSalesTypeList: [],
        tipsVisible: false,
        tipsMessage: '',
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      // ...mapGetters('macd', ['getHKTContactList']),
      // 过滤掉已删除的项
      filterDataList() {
        return this.dataList.filter(item => item.MODIFY_TAG != 1);
      },
      // 获取PARTICIPANTS_TYPE值的数组 eg: ['01','02',...]
      getTypeValueList() {
        return this.filterDataList.map(item => item.PARTICIPANTS_TYPE);
      },
    },
    mounted() {
      this.getTypeList();
      this.qryCustContactInfoFunc();
      this.getorderSalesTypeList();
    },
    methods: {
      clearSpecificItem(index) {
        this.dataList = this.filterDataList;
        if (index >= 0 && index < this.dataList.length) {
          // 使用 Vue.set 确保响应式更新
          this.$set(this.dataList, index, {
            ...this.dataList[index],
            NAME: '',
            STAFF_ID: '',
            SALES_CODE: '',
            AGENT_CODE: '',
            AGENT_NAME: '',
            ORDER_SALES_TYPE: undefined,
            CONTACT_PHONE: '',
            MOBILE: '',
            EMAIL: '',
          });
        }
      },

      // 初始化macd数据
      hanldeInitMACDData() {
        if (!this.getHKTContactList || !this.getHKTContactList.length) return;
        this.dataList = this.getHKTContactList;
      },

      // 查枚举数据
      async getTypeList() {
        const params = {
          CODE_TYPE: 'PARTICIPANTS_TYPE_HKT',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.HKTParticipantsType = res.DATA;
        } catch (error) {
          console.log(error);
        }
      },
      // 查枚举数据
      async getorderSalesTypeList() {
        const params = {
          CODE_TYPE: 'ORDER_SALES_TYPE',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.orderSalesTypeList = res.DATA || [];
        } catch (error) {
          console.log(error);
        }
      },
      // 查询HKT联系人
      async qryCustContactInfoFunc() {
        let parmas = {
          CUST_ID: this.custId,
          CONTACT_TYPE: '1',
          PAGE_NUM: 1,
          PAGE_SIZE: 999,
        };
        try {
          const res = await qryCustContactInfo(parmas);
          let list = (res.DATA[0]?.LIST || []).map((item, index) => {
            return {
              ...item,

              // 加标识
              id: index + 1,
              old: true,
              MODIFY_TAG: '-1',

              // 是否允许填写
              NAME_DISABLED: item.PARTICIPANTS_TYPE == '03',
              STAFF_ID_DISABLED: item.PARTICIPANTS_TYPE == '03',
              SALES_CODE_DISABLED: item.PARTICIPANTS_TYPE == '03',
              AGENT_CODE_DISABLED: item.PARTICIPANTS_TYPE != '03',
              AGENT_NAME_DISABLED: item.PARTICIPANTS_TYPE != '03',
              ORDER_SALES_TYPE_DISABLED: item.PARTICIPANTS_TYPE != '01',
              CONTACT_PHONE_DISABLED: item.PARTICIPANTS_TYPE == '03',
              MOBILE_DISABLED: item.PARTICIPANTS_TYPE == '03',
              EMAIL_DISABLED: item.PARTICIPANTS_TYPE == '03',

              // 工号信息
              CREATE_STAFF_ID: '1',
              CREATE_STAFF_NAME: '1',
              UPDATE_STAFF_ID: '1',
              UPDATE_STAFF_NAME: '1',
            };
          });
          this.dataList = list;
          this.originalDataList = JSON.parse(JSON.stringify(list));

          if (this.$route.name === 'fulfillmentInfo') {
            this.hanldeInitMACDData();
          }
        } catch (error) {
          console.log(error, 'error');
        }
      },
      // 新增
      onAdd() {
        this.dataList = this.filterDataList;
        this.dataList.push({
          CUST_ID: this.custId,
          id: this.dataList.length + 1,
          CONTACT_TYPE: '1',
          PARTICIPANTS_TYPE: undefined,
          NAME: '',
          STAFF_ID: '',
          SALES_CODE: '',
          AGENT_CODE: '',
          AGENT_NAME: '',
          ORDER_SALES_TYPE: undefined,
          CONTACT_PHONE: '',
          MOBILE: '',
          EMAIL: '',
          MODIFY_TAG: '0', // 0:新增 1:删除 2:更改
          // 这四个字段，创建的时候都有，但是修改的时候只有update的两个
          CREATE_STAFF_ID: '1',
          CREATE_STAFF_NAME: '1',
          UPDATE_STAFF_ID: '1',
          UPDATE_STAFF_NAME: '1',
        });
      },
      // 点击搜索
      async clickChange(record, key) {
        // 置灰 不可点击
        if (key == 'STAFF_ID' && record.STAFF_ID_DISABLED) return;
        if (key == 'SALES_CODE' && record.SALES_CODE_DISABLED) return;

        // 未输入内容 不可点击
        if (!record.SALES_CODE && !record.STAFF_ID) return;

        // 未选择类型 不可点击
        if (!record.PARTICIPANTS_TYPE) {
          this.tipsMessage = this.$t('fulfillmentInfo.HKTTypeRequired');
          this.tipsVisible = true;
          return;
        }
        const params = {
          STAFF_ID: record.STAFF_ID,
          SALES_CODE: record.SALES_CODE,
        };
        try {
          const res = await staffQuery(params);
          let obj = res.DATA[0]?.STAFF_INFO || {};
          // 搜索的结果不可修改
          this.updateRecord(record, {
            STAFF_ID: obj.STAFF_ID,
            STAFF_ID_DISABLED: key == 'SALES_CODE' && Boolean(obj.STAFF_ID),

            SALES_CODE: obj.SALES_CODE,
            SALES_CODE_DISABLED: key == 'STAFF_ID' && Boolean(obj.SALES_CODE),

            NAME: obj.STAFF_NAME,
            NAME_DISABLED: Boolean(obj.STAFF_ID),

            CONTACT_PHONE: obj.CONTACT_PHONE,
            CONTACT_PHONE_DISABLED: Boolean(obj.CONTACT_PHONE),

            MOBILE: obj.CONTACT_PHONE,
            MOBILE_DISABLED: Boolean(obj.CONTACT_PHONE),

            EMAIL: obj.EMAIL,
            EMAIL_DISABLED: Boolean(obj.EMAIL),
          });
          this.onChange(obj.STAFF_ID, record, 'STAFF_ID');
          this.onChange(obj.SALES_CODE, record, 'SALES_CODE');
          this.onChange(obj.STAFF_NAME, record, 'NAME');
          this.onChange(obj.CONTACT_PHONE, record, 'CONTACT_PHONE');
          this.onChange(obj.CONTACT_PHONE, record, 'MOBILE');
          this.onChange(obj.EMAIL, record, 'EMAIL');
        } catch (error) {
          console.log(error);
        }
      },
      async agentCodeAndNameQuery(record, key) {
        // 置灰 不可点击
        if (key == 'AGENT_NAME' && record.AGENT_NAME_DISABLED) return;
        if (key == 'AGENT_CODE' && record.AGENT_CODE_DISABLED) return;

        // 未输入内容 不可点击
        if (!record.AGENT_NAME && !record.AGENT_CODE) return;

        // 未选择类型 不可点击
        if (!record.PARTICIPANTS_TYPE) {
          this.tipsMessage = this.$t('fulfillmentInfo.HKTTypeRequired');
          this.tipsVisible = true;
          return;
        }

        const parmas = {
          AGENT_NAME: record.AGENT_NAME,
          AGENT_CODE: record.AGENT_CODE,
        };
        try {
          const res = await infoQuery(parmas);
          const obj = res.DATA[0] ?? {};
          this.updateRecord(record, {
            AGENT_CODE: obj.AGENT_CODE,
            AGENT_CODE_DISABLED: key == 'AGENT_NAME' && Boolean(obj.AGENT_CODE),

            AGENT_NAME: obj.AGENT_NAME,
            AGENT_NAME_DISABLED: key == 'AGENT_CODE' && Boolean(obj.AGENT_NAME),
          });
          this.onChange(obj.AGENT_CODE, record, 'AGENT_CODE');
          this.onChange(obj.AGENT_NAME, record, 'AGENT_NAME');
        } catch (error) {
          console.log(error);
        }
      },
      // 删除
      onDelete(record, index) {
        // 旧数据打上标识，新增后又删除的，直接删除数据
        if (record.old) {
          record.MODIFY_TAG = '1';
          // 过滤掉已删除的项并重新赋值给 this.dataList
          this.dataList = this.dataList.filter(item => item.MODIFY_TAG != 1);
        } else {
          let index = this.dataList.findIndex(item => item.id === record.id);
          if (index !== -1) {
            this.dataList.splice(index, 1);
          }
        }
        if (this.dataList) {
          this.dataList = this.dataList.map((item, index) => {
            return {
              ...item,
              id: index + 1,
            };
          });
        }
      },
      // 修改 (判断原来的数据和修改后的数据是否一致)
      onChange(value, record, key) {
        // 校验PARTICIPANTS_TYPE不能重复选
        if (key == 'PARTICIPANTS_TYPE') {
          // 筛选出当前选中的 PARTICIPANTS_TYPE 值为 01 或 02 的记录
          const filteredList = this.filterDataList.filter(
            item => item !== record && ['01', '02'].includes(item.PARTICIPANTS_TYPE),
          );
          // 判断新值是否为 01 且列表中已存在相同值
          if (
            ['01'].includes(value) &&
            filteredList.some(item => item.PARTICIPANTS_TYPE === value)
          ) {
            this.tipsMessage = this.$t('fulfillmentInfo.repeatChoose');
            this.tipsVisible = true;
            return;
          }
          record.PARTICIPANTS_TYPE = value;
          // 是否允许填写
          this.isDisabled(value, record, key);
          const index = record.id - 1;
          this.clearSpecificItem(index);
        }

        let obj = this.originalDataList.find(item => item.id === record.id);
        // 没有找到，说明是新增的数据
        if (!obj) return;
        if (value === obj[key]) {
          // 未修改(注意：-1值是前端定义的，用作于后面过滤掉，不需要入参的数据)
          record.MODIFY_TAG = '-1';
        } else {
          record.MODIFY_TAG = '2';
        }
      },
      // 是否允许填写
      isDisabled(value, record, key) {
        this.updateRecord(record, {
          NAME_DISABLED: value == '03',
          STAFF_ID_DISABLED: value == '03',
          SALES_CODE_DISABLED: value == '03',
          AGENT_CODE_DISABLED: value != '03',
          AGENT_NAME_DISABLED: value != '03',
          ORDER_SALES_TYPE_DISABLED: value != '01',
          CONTACT_PHONE_DISABLED: value == '03',
          MOBILE_DISABLED: value == '03',
          EMAIL_DISABLED: value == '03',
        });
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
        throw console.log('阻断继续往下执行');
      },
      // 等于03
      equal_to_03(item) {
        if (!item.AGENT_CODE) {
          this.showTipModal(this.$t('fulfillmentInfo.HKTAgentCodeRequired'));
        }
        if (!item.AGENT_NAME) {
          this.showTipModal(this.$t('fulfillmentInfo.HKTAgentNameRequired'));
        }
      },
      // 不等于03
      not_equal_to_03(item) {
        if (!item.NAME) {
          this.showTipModal(this.$t('fulfillmentInfo.HKTNameRequired'));
        }
        if (!item.STAFF_ID) {
          this.showTipModal(this.$t('fulfillmentInfo.HKTStaffIdRequired'));
        }
        if (!item.CONTACT_PHONE_DISABLED) {
          // 校验必填
          if (!item.CONTACT_PHONE) {
            this.showTipModal(this.$t('fulfillmentInfo.HKTContactPhoneRequired'));
          }
          // 校验格式是否正确
          // if (!validateHKTPhone(item.CONTACT_PHONE)) {
          //   this.showTipModal(this.$t('fulfillmentInfo.HKTPhoneIncorrectFormat'));
          // }
        }
        // 可填 && 有值 && 格式不对
        // if (!item.MOBILE_DISABLED && item.MOBILE && !validateHKTPhone(item.MOBILE)) {
        //   this.showTipModal(this.$t('fulfillmentInfo.HKTMobileIncorrectFormat'));
        // }
        // 可填 && 有值 && 格式不对
        if (!item.EMAIL_DISABLED && item.EMAIL && !validateEmail(item.EMAIL)) {
          this.showTipModal(this.$t('fulfillmentInfo.HKTEmailRequired'));
        }
      },
      // 更新record对象里面的数据
      updateRecord(record, updates) {
        Object.keys(updates).forEach(key => {
          this.$set(record, key, updates[key]);
        });
      },
      // 组件过滤后输出的数据
      returnDataList() {
        return this.dataList.filter(item => item.MODIFY_TAG !== '-1');
      },

      // todo -----------------------------------------------------  tanwl3 -------  请勿重复选择和校验必填国际化！！！
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          if (this.filterDataList.length < 1) {
            this.showTipModal(this.$t('fulfillmentInfo.HKTContactValidTips'));
          }
          // 校验 (只校验0-新增和2-更改的数据) 是否为空/格式是否正确
          // 0:新增 1:删除 2:更改 -1:未修改过
          let list = this.dataList.filter(item => ['0', '2'].includes(item.MODIFY_TAG));
          list.forEach(item => {
            // PARTICIPANTS_TYPE 为空校验
            if (!item.PARTICIPANTS_TYPE) {
              this.showTipModal(this.$t('fulfillmentInfo.HKTTypeRequired'));
            }
            // PARTICIPANTS_TYPE = 03 的时候校验
            if (item.PARTICIPANTS_TYPE == '03') {
              this.equal_to_03(item);
            }
            // PARTICIPANTS_TYPE != 03 的时候校验
            if (item.PARTICIPANTS_TYPE != '03') {
              this.not_equal_to_03(item);
            }
            // PARTICIPANTS_TYPE = 01 的时候校验
            if (item.PARTICIPANTS_TYPE == '01' && !item.ORDER_SALES_TYPE) {
              this.showTipModal(this.$t('fulfillmentInfo.HKTOrderSaleTypeRequired'));
            }
          });
          resolve();
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .customerContact {
    margin-bottom: 20px;
    .inputSearch {
      position: relative;
      .input {
        padding-right: 30px;
      }
      .searchIcon {
        position: absolute;
        // right: 5px;
        left: 55%;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #666;
      }
    }
    .listAdd {
      margin-top: 10px;
      width: 100%;
      height: 40px;
      background-color: #ffffff;
      border: 1px dashed #0076ff;
      color: #0076ff;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
    }
    /deep/ .ant-table-placeholder {
      display: none;
    }
    /deep/ .ant-input[disabled] {
      color: #999;
    }
    .required {
      color: #f5222d;
      font-size: 14px;
    }
  }
</style>
