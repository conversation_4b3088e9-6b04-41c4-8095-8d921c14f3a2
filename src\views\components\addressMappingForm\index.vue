<template>
  <div class="header">
    <div class="secondLevel-header-title">
      {{ headerTitle }}
    </div>
    <a-form-model
      :model="form"
      v-bind="isCitinetType ? {} : layout"
      :colon="false"
      ref="ruleForm"
      :rules="isCitinetType ? {} : formRules"
    >
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="12" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.InstallationAddress')"
            prop="SB_ADDRESS"
            labelAlign="left"
            validateFirst
            :labelCol="{ span: 5 }"
            :wrapperCol="{ span: 18 }"
          >
            <a-input
              v-containsSqlInjection
              v-model.trim="form.masterAddress"
              :placeholder="$t('customerVerify.selectInquiry')"
              disabled
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.SB No')"
            prop="SB_NO"
            labelAlign="left"
            :labelCol="{ span: 3 }"
            :wrapperCol="{ span: 18 }"
          >
            <a-input
              disabled
              v-containsSqlInjection
              v-model.trim="form.SB_NO"
              :placeholder="$t('customerVerify.selectInquiry')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="4">
          <!-- 按钮组 -->
          <div class="button-group">
            <a-button ghost type="primary" @click="resetForm" class="reset-button btnDistance">
              {{ $t('common.buttonReset') }}
            </a-button>
            <a-button type="primary" @click="handleAddressOpen" class="search-button">
              {{ $t('common.buttonInquiry') }}
            </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- Address Mapping 弹窗 -->
    <a-modal
      :title="$t('customerVerify.addressMapping')"
      :width="880"
      destroyOnClose
      :footer="null"
      :visible="selectAddressVisible"
      @cancel="selectAddressCancel"
    >
      <SelectAddress
        @cancel="selectAddressCancel"
        @ok="handleConfirmAddress"
        :businessType="businessType"
      />
    </a-modal>
  </div>
</template>
<script>
  import SelectAddress from '@/views/components/selectAddress';
  import i18n from '@/i18n/index';
  export default {
    name: 'AddressMappingForm',
    components: {
      SelectAddress,
    },
    props: {
      isCitinetType: {
        type: Boolean,
        default: false,
      },
      resetGroupAddress: {
        type: Boolean,
        default: false,
      },
      businessType: {
        type: String,
        default: '',
      },
      headerTitle: {
        type: String,
        default: i18n.t('customerVerify.oscaProductAddressTitle'),
      },
    },
    data() {
      return {
        spinning: false,
        layout: {
          labelCol: { span: 4 },
          wrapperCol: { span: 20 },
        },
        form: {
          SB_ADDRESS: '',
          SB_NO: '',
          MODIFY_TAG: '0',
        },
        formRules: {
          SB_ADDRESS: [
            {
              required: true,
              message: 'Please Enter Installation Address',
              trigger: 'blur',
            },
          ],
        },
        selectAddressVisible: false,
      };
    },
    computed: {
      selectedAddress() {
        let selectedAddress = '';
        if (this.form.SB_ADDRESS) {
          const { EN_ADDR3, EN_ADDR1, SB_ADDRESS, status } = this.form;
          selectedAddress = SB_ADDRESS;
          //   status == 'new'
          //     ? `${SB_ADDRESS}`
          //     : `${EN_ADDR3 ? EN_ADDR3 : ''}, ${EN_ADDR1 ? EN_ADDR1 : ''}, ${SB_ADDRESS}`
          //         .split(',')
          //         .map(part => part.trim()) // 去除每个部分的前后空格
          //         .filter(part => part !== '') // 过滤掉空的部分
          //         .join(', '); // 使用逗号和空格重新连接;;
        }
        selectedAddress = selectedAddress?.split(',').map(item => item.trim());
        selectedAddress = [...new Set(selectedAddress)].join(',');
        return selectedAddress;
      },
    },
    methods: {
      resetForm() {
        if (this.resetGroupAddress) {
          this.$emit('resetGroupAddressFunc');
        } else {
          this.$refs.ruleForm.resetFields();
        }
      },
      // 选址 弹窗 关闭
      selectAddressCancel() {
        this.selectAddressVisible = false;
      },
      // 选址 弹窗 打开
      handleAddressOpen() {
        this.selectAddressVisible = true;
      },
      // 选址 确认回调
      handleConfirmAddress(data) {
        // OSCA - new Address Data新格式
        // const newFormatData = this.formatAddressData(data);
        this.form = {
          BID: data.masterAddress?.BID ?? '',
          GEOSEQ: data.GEOSEQ,
          FLOORVALUE: data.FLOORVALUE,
          FLATVALUE: data.FLATVALUE,
          ADDRESS_2N_CODE: data.BW2NCODE,
          ADDRESS_2N_TAG: data.CONTAINBW2N,
          SB_ADDRESS: data.SB_ADDR,
          masterAddress:
            data.status == 'normal' ? data.masterAddress.DETAILS_EN.ADDRESS : data.SB_ADDR,
          GEOSEQOfMasterAddress: data.masterAddress?.GEOSEQ ?? '',
          SB_NO: data.SB_NO,
          EN_ADDR1: data.EN_ADDR1,
          ZH_ADDR1: data.ZH_ADDR1,
          EN_ADDR3: data.EN_ADDR3,
          ZH_ADDR3: data.ZH_ADDR3,
          EN_ADDR4: data.EN_ADDR4,
          ZH_ADDR4: data.ZH_ADDR4,
          EN_ADDR8: data.EN_ADDR8,
          ZH_ADDR8: data.ZH_ADDR8,
          EN_ADDR11: data.EN_ADDR11,
          ZH_ADDR11: data.ZH_ADDR11,
          EN_ADDR12: data.EN_ADDR12,
          ZH_ADDR12: data.ZH_ADDR12,
          EN_ADDR13: data.EN_ADDR13,
          ZH_ADDR13: data.ZH_ADDR13,
          status: data.status,
          MODIFY_TAG: '0',
          // newFormatData,
        };
        console.log(this.form);
        // 校验
        setTimeout(() => {
          this.$refs.ruleForm.validateField('SB_ADDRESS');
        }, 0);

        // 关闭弹窗
        this.selectAddressCancel();
      },
      // 格式化地址数据
      //     {
      //  "PRODUCT_ID": "80001", //产品ID
      //   "ATTR_CODE": "stander_address_id", // 标准地址id
      //   "ATTR_VALUE": "1002" //标准地址id的值
      // }
      //   formatAddressData(addressInfo) {
      //     console.log(addressInfo, 'addressInfo');

      //     const result = [];
      //     const base = {
      //       stander_address_id: '',
      //       stander_address: '',
      //       sb_no: '',
      //       service_no: '',
      //     };
      //     const detail = {
      //       region: '',
      //       district: '',
      //       street: '',
      //       estate: '',
      //       building: '',
      //       floor: '',
      //       flat: '',
      //       unit: '',
      //       room: '',
      //       lot_number: '',
      //     };
      //     if (addressInfo.status === 'new') {
      //       base.stander_address = addressInfo.SB_ADDR;

      //       detail.region = addressInfo.EN_ADDR13;
      //       detail.district = addressInfo.EN_ADDR12;
      //       detail.street = addressInfo.EN_ADDR11;
      //       detail.estate = addressInfo.EN_ADDR8;
      //       detail.building = addressInfo.EN_ADDR4;
      //       detail.floor = addressInfo.EN_ADDR3;
      //       detail.flat = addressInfo.EN_ADDR1;
      //       detail.unit = addressInfo.EN_ADDR1;
      //       detail.room = addressInfo.EN_ADDR1;
      //       detail.lot_number = addressInfo.LOT_NUMBER;
      //     } else {
      //       const EN_DETAILS = addressInfo.masterAddress.DETAILS_EN;

      //       base.stander_address_id = addressInfo.BID;
      //       base.stander_address =
      //         EN_DETAILS && EN_DETAILS.ADDRESS !== ''
      //           ? EN_DETAILS.ADDRESS
      //           : addressInfo.masterAddress.SB_ADDR;
      //       base.sb_no = addressInfo.SB_NO;
      //       // #TODO：暂不清楚用哪个属性
      //       // base.service_no = addressInfo. ;

      //       detail.region = EN_DETAILS.ADDR13;
      //       detail.district = EN_DETAILS.ADDR12;
      //       detail.street = EN_DETAILS.ADDR11;
      //       detail.estate = EN_DETAILS.ADDR8;
      //       detail.building = EN_DETAILS.ADDR4;
      //       detail.floor = EN_DETAILS.ADDR3;
      //       detail.flat = EN_DETAILS.ADDR1;
      //       detail.unit = EN_DETAILS.ADDR1;
      //       detail.room = EN_DETAILS.ADDR1;
      //       detail.lot_number = addressInfo.LOT_NUMBER;
      //     }
      //     // 合并属性
      //     const mergeAddress = {
      //       ...base,
      //       ...detail,
      //     };
      //     for (const key in mergeAddress) {
      //       if (
      //         Object.prototype.hasOwnProperty.call(mergeAddress, key) &&
      //         mergeAddress[key] &&
      //         mergeAddress[key] !== ''
      //       ) {
      //         result.push({
      //           PRODUCT_ID: '', // 产品ID - 提交时候添加
      //           ATTR_CODE: key, // 属性代码
      //           ATTR_VALUE: mergeAddress[key] || '', // 属性值
      //         });
      //       }
      //     }
      //     console.log(result, 'result');

      //     return result;
      //   },
    },
  };
</script>
<style lang="less" scoped>
  header {
    background-color: #fff;
    box-sizing: border-box;
    // padding: 10px 22px 0 22px;

    .ant-col-6 {
      display: flex;
    }

    /deep/ .ant-form-item-label {
      width: auto !important;
      line-height: 32px !important;
    }

    .b-btns {
      justify-content: flex-end;
    }

    // button + button {
    //   margin-left: 10px;
    // }
  }
  .btnStyle {
    display: flex !important;
    align-items: end !important;
    justify-content: end !important;
    padding-bottom: 12px !important;
  }
  .btnDistance {
    // margin-right: 10px;
  }
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
</style>
