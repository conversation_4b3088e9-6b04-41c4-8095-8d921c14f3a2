<template>
  <div class="messageModal">
    <a-modal
      :visible="visible"
      :zIndex="1001"
      centered
      :closable="closable"
      :footer="null"
      :width="450"
      :height="330"
      :maskClosable="false"
      @cancel="cancel"
    >
      <div class="content">
        <!-- <template #title>
        <div class="header-title">
          <i class="iconfont icon-tishi" />
          <div class="headermessage">{{ $t('common.prompt') }}</div>
        </div>
      </template> -->
        <i class="iconfont icon-tishi" />
        <div class="headermessage">{{ $t('quotation.ConfirmProceed') }}</div>
        <div class="message">{{ $t('quotation.ApproveInfo') }}</div>
        <div class="flex">
          <span class="approveContent" v-for="(item, index) in approveFlows" :key="index">
            【{{ item }}】
          </span>
        </div>

        <div>
          <span class="approveInfo">{{ $t('quotation.Approver') }} </span>
          <span class="message" v-for="(item, index) in approvers" :key="index">
            {{ index > 0 ? '/' : '' }}{{ item }}
          </span>
        </div>
        <div class="btnRow">
          <a-button v-show="displayCancelBtn" ghost type="primary" @click="cancel">{{
            $t('common.buttonCancel')
          }}</a-button>
          <a-button :loading="loading" type="primary" @click="confirm">{{
            $t('common.buttonConfirm')
          }}</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
  export default {
    name: 'MessageModal',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      // 触发的审批流程
      approveFlows: {
        type: Array,
        default() {
          return [];
        },
      },
      // 审批者信息
      approvers: {
        type: Array,
        default() {
          return [];
        },
      },
      loading: {
        type: Boolean,
        default: false,
      },
      displayCancelBtn: {
        type: Boolean,
        default: false,
      },
      closable: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      cancel() {
        this.$emit('cancel');
      },
      confirm() {
        this.$emit('confirm');
      },
    },
  };
</script>

<style scoped lang="less">
  .content {
    width: 100%;
    height: 100%;
    text-align: center;
    padding: 20px 0 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    .iconfont {
      font-size: 60px;
    }
    .headermessage {
      color: #000000;
      font-size: 24px;
      font-weight: 700;
    }
    .message {
      margin-top: 15px;
      font-size: 14px;
      color: #373d41;
      text-align: center;
      line-height: 22px;
      white-space: pre-line; /* 添加这行支持message处理换行标签 */
    }
    .flex {
      display: flex;
    }
    .approveContent {
      font-size: 14px;
      color: #0072ff;
      text-align: center;
      line-height: 22px;
      font-weight: 400;
    }
    .approveInfo {
      font-size: 14px;
      color: #373d41;
      text-align: center;
      line-height: 22px;
      font-weight: 500;
    }
    .btnRow {
      margin-top: 32px;
      display: flex;
      justify-content: center;
    }
    .btnRow button:first-child {
      margin-right: 20px !important;
    }
  }
  :deep(.ant-modal-body) {
    padding: 0 10px !important;
    width: 450px;
    height: 330px;
  }
  :deep(.ant-modal-header) {
    padding: 0 10px !important;
    border-bottom: 0px;
  }
  :deep(.ant-modal-content) {
    border-radius: 8px !important;
  }
</style>
