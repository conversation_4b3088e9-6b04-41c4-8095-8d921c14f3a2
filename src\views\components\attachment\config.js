import that from '@/main.js';
export default {
  attachmentListColumns: [
    {
      title: that.$t('common.seq'),
      dataIndex: 'id',
      key: 'id',
      width: 200,
    },
    {
      title: that.$t('common.type'),
      dataIndex: 'SERVER_TYPE_NAME',
      key: 'SERVER_TYPE_NAME',
      width: 200,
    },
    {
      title: that.$t('comp.AttachmentName'),
      dataIndex: 'FILE_NAME',
      key: 'FILE_NAME',
      width: 200,
    },
    {
      title: that.$t('comp.AttachmentRemark'),
      dataIndex: 'FILE_DESC',
      key: 'FILE_DESC',
      width: 360,
    },
    {
      title: that.$t('comp.UploadTime'),
      dataIndex: 'OPER_TIME',
      key: 'OPER_TIME',
      width: 200,
    },
    {
      title: that.$t('comp.UploadUser'),
      dataIndex: 'OPER_STAFF_NAME',
      key: 'OPER_STAFF_NAME',
      width: 200,
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      className: 'action-column',
    },
  ],
};
