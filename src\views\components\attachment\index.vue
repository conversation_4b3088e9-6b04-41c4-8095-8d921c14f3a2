/** Document */
<template>
  <div class="document">
    <!-- Attachment -->
    <div class="secondLevel-header-title">
      {{ $t('comp.Attachment') }}
    </div>
    <a-table
      :columns="columns"
      :data-source="dataList"
      :rowKey="(record, index) => `${record.FILE_URL}`"
      :pagination="false"
    >
      <span slot="action" slot-scope="record, index">
        <span
          v-if="record.FILE_URL && showBtnControl.includes('download')"
          class="iconfont icon-xiazai action-icon"
          @click="downAttchment(record)"
        ></span>
        <Rpopover @onConfirm="onDelete(record)" :content="$t('common.deleteConfirm')">
          <template slot="button">
            <a
              v-if="showBtnControl.includes('delete') && judgeOwner(record)"
              class="iconfont icon-shanchu action-icon"
            ></a>
          </template>
        </Rpopover>
      </span>
    </a-table>

    <div v-if="dataList && dataList.length < limits && ifShowUpload" class="listAdd" @click="onAdd">
      + {{ $t('common.add') }}
    </div>

    <AddAttachmentUpload
      v-if="addAttachmentVisible"
      :visible="addAttachmentVisible"
      ref="addAttachmentUploadRef"
      @upLoadCancel="upLoadCancel"
      @uploadComplete="uploadComplete"
    />
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  import AddAttachmentUpload from '@/views/components/addAttachmentUpload/index.vue';
  import Rpopover from '@/components/Rpopover';
  import config from './config';
  import { downloadFile } from '@/api/common';
  export default {
    name: 'attachment',
    components: {
      AddAttachmentUpload,
      Rpopover,
    },
    props: {
      fileList: {
        type: Array,
        default: () => [],
      },
      limits: {
        type: Number,
        default: 5,
      },
      // 是否展示上传按钮
      ifShowUpload: {
        type: Boolean,
        default: true,
      },
      // 操作列 - 按钮展示 - download(下载)/delete(删除)
      actionBtnShowType: {
        type: String,
        default: 'download,delete',
      },
    },
    data() {
      return {
        columns: config.attachmentListColumns,
        originalDataList: [],
        addAttachmentVisible: false,
        dataList: [],
      };
    },
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
      showBtnControl() {
        console.log(this.actionBtnShowType.split(','));
        return this.actionBtnShowType.split(',');
      },
    },
    watch: {
      fileList: {
        handler(newValue, oldValue) {
          if (newValue) {
            this.addDataId(newValue);
          }
        },
        immediate: true,
        deep: true,
      },
    },
    mounted() {},
    methods: {
      // 增加标识
      addDataId(datas) {
        if (datas) {
          this.dataList = datas.map((item, index) => {
            return {
              ...item,
              id: index + 1,
            };
          });
        }
      },
      // 新增
      onAdd() {
        this.addAttachmentVisible = true;
      },
      // 附件上传取消
      upLoadCancel() {
        this.addAttachmentVisible = false;
      },
      // 附件上传完成
      uploadComplete(data) {
        this.upLoadCancel();
        this.dataList = [...this.dataList, data];
        this.addDataId(this.dataList);
      },
      // 删除
      onDelete(record) {
        // 新增后删除的，直接删除数据
        let index = this.dataList.findIndex(item => item.id === record.id);
        if (index !== -1) {
          this.dataList.splice(index, 1);
        }
        this.addDataId(this.dataList);
      },
      // 下载附件
      downAttchment(record) {
        window.location.href = downloadFile(
          '?fileId=' + record.FILE_URL + '&fileName=' + record.FILE_NAME,
        );
      },
      // 判断是否为本人的文件
      judgeOwner(record) {
        console.log('judgeOwner', this.userInfo, record);
        if (this.userInfo.STAFF_ID && record.OPER_STAFF_ID) {
          return this.userInfo.STAFF_ID === record.OPER_STAFF_ID;
        } else if (this.userInfo.STAFF_NAME && record.OPER_STAFF_NAME) {
          return this.userInfo.STAFF_NAME === record.OPER_STAFF_NAME;
        }
        return false;
      },
    },
  };
</script>

<style lang="less" scoped>
  .document {
    /deep/ .ant-table-placeholder {
      display: none;
    }
    .icon-shanchu {
      margin-left: 15px;
    }

    /deep/.ant-calendar-picker-clear,
    .ant-calendar-picker-icon {
      left: 65%;
    }
    /deep/.ant-calendar-picker-icon {
      left: 65%;
    }
  }
  .action-icon {
    font-size: 12px;
    line-height: 12px;
    cursor: pointer;
  }
  .listAdd {
    margin-top: 10px;
    width: 100%;
    height: 40px;
    background-color: #ffffff;
    border: 1px dashed #0076ff;
    color: #0076ff;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }

  /deep/.ant-divider-dashed {
    margin: 0px 0 !important;
  }

  .document {
    /deep/ .ant-table-thead > tr > th {
      height: 40px;
    }
  }
</style>
