<template>
  <div class="details-product-tree">
    <template v-for="(item, key) in currentProduct">
      <TableComponent
        :key="activeKey + '_' + key"
        :ifExpand="item.ifExpand"
        :columns="columns"
        :data-source="item.list"
        :expandIconColumnIndex="0"
        :expand-title="item.title"
        @handleExpandWhole="handleExpandWhole"
      ></TableComponent>
    </template>
  </div>
</template>

<script>
  import TableComponent from './tableComponent.vue';
  export default {
    name: 'CollapseProductTree',
    props: {
      treeList: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      TableComponent,
    },
    data() {
      return {
        activeKey: '',
        currentProduct: {
          mainProduct: {
            title: this.$t('comp.MainProduct'),
            list: [],
            total: 0,
            ifExpand: true,
          },
          addition: {
            title: this.$t('comp.AdditionalProduct'),
            list: [],
            total: 0,
            ifExpand: false,
          },
          equipment: {
            title: this.$t('comp.Equipment'),
            list: [],
            total: 0,
            ifExpand: false,
          },
          premium: {
            title: this.$t('comp.Premium'),
            list: [],
            total: 0,
            ifExpand: false,
          },
        },
        columns: [
          {
            dataIndex: 'NAME',
            width: 350,
            ellipsis: true,
            slots: { title: 'expandTitle' },
            scopedSlots: { customRender: 'productTypeName' },
          },
          {
            title: this.$t('customerVerify.type'),
            dataIndex: 'type',
            key: 'type',
            width: 100,
          },
          {
            title: this.$t('customerVerify.Charge'),
            scopedSlots: { customRender: 'charge' },
            width: 200,
          },
          {
            title: this.$t('customerVerify.Period'),
            scopedSlots: { customRender: 'period' },
            width: 200,
          },
          {
            title: this.$t('customerVerify.Action'),
            scopedSlots: { customRender: 'action' },
            fixed: 'right',
            width: 100,
            className: 'action-column',
          },
        ],
      };
    },
    methods: {
      // 设置 - 当前产品树
      setCurrentProduct(key) {
        console.log(this.treeList, key, 'this.treeList - key');
        if (!this.treeList || Object.keys(this.treeList).length == 0) {
          return;
        }
        // #TODO - 优化处理方式
        this.currentProduct['mainProduct'].list = JSON.parse(
          JSON.stringify(this.treeList.mainProduct),
        );
        this.currentProduct['addition'].list = JSON.parse(JSON.stringify(this.treeList.addition));
        this.currentProduct['equipment'].list = JSON.parse(JSON.stringify(this.treeList.equipment));
        this.currentProduct['premium'].list = JSON.parse(JSON.stringify(this.treeList.premium));
      },
      // 展示/收缩 - 表格
      handleExpandWhole(title) {
        for (const key in this.currentProduct) {
          if (Object.prototype.hasOwnProperty.call(this.currentProduct, key)) {
            const ele = this.currentProduct[key];

            if (ele.title === title) {
              ele.ifExpand = !ele.ifExpand;
            } else {
              ele.ifExpand = false;
            }
          }
        }
      },
    },
  };
</script>

<style lang="less" scoped></style>
