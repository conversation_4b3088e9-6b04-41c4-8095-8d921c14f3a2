<template>
  <div class="collapse-table" :class="ifExpand ? 'expanded' : ''">
    <a-table
      size="small"
      v-if="dataSource.length"
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      :expandIconColumnIndex="expandIconColumnIndex"
      :expandIcon="expandIcon"
      expandRowByClick
      :rowClassName="() => 'no-select'"
      :rowKey="(record, index) => `${record.key}`"
      :expandedRowKeys="expandedRowKeys"
      @expand="(expanded, record) => handleExpand(expanded, record)"
    >
      <span slot="expandTitle">
        <img alt="" :src="ifExpand ? icon_down : icon_up" class="svg" @click.stop="expandWhole" />
        {{ expandTitle }} ({{ selectedNum }})
      </span>
      <span slot="charge" slot-scope="text, record">
        {{ getCharge(record) }}
      </span>
      <span slot="period" slot-scope="text, record">
        {{ getPeriod(record) }}
      </span>
      <span v-if="!record.NAME.includes('CCP')" slot="action" slot-scope="text, record">
        <em
          v-if="record.HAS_ATTR == '1'"
          :class="['iconfont', iconfontStatusObj['detail']]"
          @click.stop="attributeEditOpen(record)"
        />
      </span>
    </a-table>
    <!-- 属性编辑 弹窗-->
    <a-modal
      v-model="attributeEditVisible"
      :title="$t('customerVerify.attributeDetail')"
      width="420px"
      :maskClosable="false"
      :footer="null"
      @cancel="attributeEditCancel"
    >
      <AttributeEdit
        ref="AttributeEdit"
        @cancel="attributeEditCancel"
        @confirm="attributeEditConfirm"
        :attributeEditDisabled="true"
      />
    </a-modal>
  </div>
</template>

<script>
  import AttributeEdit from '@/views/components/productTreeList/attributeEdit.vue';
  import config from '@/views/components/productTreeList/common/config';
  import icon_up from '@/assets/images/up.svg';
  import icon_down from '@/assets/images/down.svg';
  export default {
    name: 'ProductTreeTableComponent',
    components: {
      AttributeEdit,
    },
    props: {
      columns: {
        type: Array,
        required: true,
      },
      dataSource: {
        type: Array,
        required: true,
      },
      expandTitle: String,
      expandIconColumnIndex: {
        type: Number,
        default: 1,
      },
      ifExpand: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        icon_up,
        icon_down,
        iconfontStatusObj: {
          'edit': 'icon-xiugai', // 编辑图标
          'edited': 'icon-xiugai isEdited', // 编辑过变灰色图标
          'detail': 'icon-xiangqingmingxi', // 详情图标
        },
        attributeEditVisible: false,
        expandedRowKeys: [],
      };
    },
    computed: {
      // 汇总 - 已选的元素数量
      selectedNum() {
        function countLeafNodes(list) {
          let count = 0;
          list.forEach(item => {
            if (item.children && item.children.length > 0) {
              count += countLeafNodes(item.children);
            } else {
              count += 1;
            }
          });
          return count;
        }
        return countLeafNodes(this.dataSource || []);
      },
    },
    watch: {
      dataSource: {
        immediate: true,
        handler(newVal) {
          this.expandedRowKeys = this.getAllParentKeys(newVal);
        },
      },
    },
    methods: {
      // 获取所有有 children 的节点 key
      getAllParentKeys(list) {
        let keys = [];
        function traverse(arr) {
          arr.forEach(item => {
            if (item.children && item.children.length > 0) {
              keys.push(item.key); // 或你自定义的 rowKey
              traverse(item.children);
            }
          });
        }
        traverse(list || []);
        return keys;
      },
      handleExpand(expanded, record) {
        const key = record.key; // 或你自定义的 rowKey
        if (expanded) {
          // 展开时添加 key
          if (!this.expandedRowKeys.includes(key)) {
            this.expandedRowKeys = [...this.expandedRowKeys, key];
          }
        } else {
          // 收起时移除 key
          this.expandedRowKeys = this.expandedRowKeys.filter(k => k !== key);
        }
      },
      expandIcon(props) {
        if (props.record?.children && props.record?.children.length > 0) {
          if (props.expanded) {
            return <img src={this.icon_down} class="svg" alt="" />;
          }
          return <img src={this.icon_up} class="svg" alt="" />;
        }
        return <span></span>;
      },
      createRowKey(record, index) {
        if (record.DISCNT_CODE) return `d-${record.DISCNT_CODE}`;
        else if (record.PACKAGE_ID) return `pk-${record.PACKAGE_ID}`;
        else if (record.PRODUCT_ID) return `p-${record.PRODUCT_ID}`;
        return index;
      },
      // 展示费用信息
      getCharge(record) {
        // console.log('charge', record);
        return config.getCharge(record);
      },
      getStandardMRC(record) {
        return config.getStandardMRC(record);
      },
      // 展示合约期
      getPeriod(record) {
        return config.getPeriod(record);
      },
      // 属性编辑 弹窗 打开
      attributeEditOpen(record) {
        this.rebateContractPeriodDisabled(record);
        this.attributeEditVisible = true;

        // 先打开弹窗，才能再获取弹窗里面的组件实例，故settimeout
        setTimeout(() => {
          // 编辑进来才做数据回显
          if (this.$refs.AttributeEdit) {
            this.$refs.AttributeEdit.getElements(record);
          }
        }, 0);
      },
      rebateContractPeriodDisabled(record) {
        // 判断是否为Rebate元素
        const isRebate = record?.DISCNT_ITEM?.some(
          x => x.ATTR_CODE === 'price_type' && x.ATTR_VALUE === 'Rebate',
        );
        if (isRebate) {
          let rebateInterfaceElementList = record.interfaceElementList || [];
          rebateInterfaceElementList.forEach(item => {
            if (item.elementCode == 'contract_period') {
              item.modifyRightCode = 'false';
            }
          });
        }
      },
      // 属性编辑 弹窗 打开
      attributeEditConfirm() {
        this.attributeEditVisible = true;
      },
      // 属性编辑 弹窗 关闭
      attributeEditCancel() {
        this.attributeEditVisible = false;
      },
      // 展示/收缩 - 整体
      expandWhole() {
        this.$emit('handleExpandWhole', this.expandTitle);
      },
    },
  };
</script>
<style lang="less" scoped>
  // 新增禁止选择样式
  /deep/ .no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    td {
      cursor: default !important;
      line-height: 24px;
    }
  }
  .svg {
    width: 14px;
    height: 14px;
    margin-right: 7px;
  }
  .collapse-table {
    max-height: 41px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    &.expanded {
      max-height: 2000px; /* 设置一个足够大的值 */
    }
  }
  .collapse-table {
    /deep/ .ant-table-thead {
      height: 40px;
      background: #f0f2f5;
      color: #73777a;
      line-height: 22px;
      font-weight: 500;
    }
  }
</style>
