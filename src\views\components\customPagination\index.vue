<template>
  <div class="custom-pagination">
    <a-button icon="left" :disabled="currentPage === 1" @click="handlePrevPage" />
    <div class="current-page">
      {{ currentPage }}
    </div>
    <a-button icon="right" :disabled="!morePage" @click="handleNextPage" />
  </div>
</template>

<script>
  export default {
    name: 'CustomPagination',
    props: {
      currentPage: {
        type: Number,
        default: 1,
      },
      morePage: {
        type: Boolean,
        default: false,
      },
    },
    methods: {
      // 上一页
      handlePrevPage() {
        let prevPage = this.currentPage - 1 === 0 ? 1 : this.currentPage - 1;
        this.$emit('pagination-change', prevPage);
      },
      // 下一页
      handleNextPage() {
        let nextPage = this.currentPage + 1;
        this.$emit('pagination-change', nextPage);
      },
    },
  };
</script>

<style lang="less" scoped>
  .custom-pagination {
    margin-top: 18px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .current-page {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
    }
  }
</style>
