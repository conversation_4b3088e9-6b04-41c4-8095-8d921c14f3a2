import that from '@/main.js';
export default {
  columns: [
    {
      title: that.$t('orderSummary.seq'),
      dataIndex: 'id',
      key: 'id',
      showFn: isDetail => {
        return !isDetail;
      },
    },
    // {
    //   title: that.$t('fulfillmentInfo.title'),
    //   dataIndex: 'TITLE',
    //   key: 'TITLE',
    // },
    {
      title: that.$t('fulfillmentInfo.title'),
      scopedSlots: { customRender: 'CUSTOMER_TITLE' },
      showFn: () => {
        return true;
      },
    },
    {
      title: that.$t('orderSummary.type'),
      scopedSlots: { customRender: 'PARTICIPANTS_TYPE' },
      showFn: () => {
        return true;
      },
    },
    // {
    //   title: 'Staff ID',
    //   dataIndex: 'STAFF_ID',
    //   key: 'STAFF_ID',
    // },
    {
      title: that.$t('orderSummary.name'),
      dataIndex: 'NAME',
      key: 'NAME',
      showFn: () => {
        return true;
      },
    },
    {
      title: that.$t('orderSummary.contactPhone'),
      dataIndex: 'CONTACT_PHONE',
      key: 'CONTACT_PHONE',
      showFn: () => {
        return true;
      },
    },
    {
      title: that.$t('orderSummary.mobile'),
      dataIndex: 'MOBILE',
      key: 'MOBILE',
      showFn: () => {
        return true;
      },
    },
    {
      title: that.$t('orderSummary.email'),
      dataIndex: 'EMAIL',
      key: 'EMAIL',
      showFn: () => {
        return true;
      },
    },
  ],
};
