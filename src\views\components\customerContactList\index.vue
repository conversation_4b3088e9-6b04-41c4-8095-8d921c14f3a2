<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('orderSummary.customerContactList') }}</div>
    <a-table :columns="tableColumns" :data-source="datas" :pagination="false">
      <span slot="PARTICIPANTS_TYPE" slot-scope="record, index">
        {{ changeTypeLabel(record) }}
      </span>
      <span slot="CUSTOMER_TITLE" slot-scope="record, index"> {{ changeTitleLabel(record) }} </span>
    </a-table>
    <a-divider />
  </div>
</template>
<script>
  import config from './config';
  import { queryCreateCustomerEnum } from '@/api/fulfillmentInfo';
  export default {
    props: {
      datas: {
        type: Array,
        default: () => [],
      },
      isDetail: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        columns: config.columns,
        participantsType: [],
        titleList: [],
      };
    },
    mounted() {
      this.getTypeList();
    },
    computed: {
      tableColumns() {
        const { isDetail } = this;
        return config.columns.filter(item => item.showFn(isDetail));
      },
    },
    methods: {
      // 查枚举数据
      async getTypeList() {
        const params = {
          CODE_TYPE: 'PARTICIPANTS_TYPE_CUSTOMER',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.participantsType = res.DATA;
          const res2 = await queryCreateCustomerEnum({ 'CODE_TYPE': 'TITLE_TYPE' });
          this.titleList = res2.DATA;
        } catch (error) {
          console.log(error);
        }
      },
      changeTypeLabel(record) {
        return this.participantsType?.find(item => item.CODE_VALUE === record.PARTICIPANTS_TYPE)
          ?.CODE_NAME;
      },
      changeTitleLabel(record) {
        return (
          this.titleList?.find(item => item.CODE_VALUE === record.TITLE)?.CODE_NAME ||
          this.titleList?.find(item => item.CODE_NAME === record.TITLE)?.CODE_NAME
        );
      },
    },
  };
</script>
