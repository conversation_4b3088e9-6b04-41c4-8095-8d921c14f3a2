<template>
  <a-popover v-if="isOverflow" placement="top">
    <template #content>
      <div>{{ text }}</div>
    </template>
    <div ref="contents" class="ellipsis-tooltip">{{ text }}</div>
  </a-popover>
  <div v-else ref="contents" class="ellipsis-tooltip">{{ text }}</div>
</template>

<script>
  export default {
    name: 'EllipsisPopover',
    props: {
      text: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        isOverflow: false,
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.checkOverflow();
      });
      window.addEventListener('resize', this.checkOverflow);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.checkOverflow);
    },
    methods: {
      checkOverflow() {
        const el = this.$refs.contents;
        if (el) {
          this.isOverflow = el.scrollWidth > el.offsetWidth;
        }
      },
    },
  };
</script>

<style scoped>
  .ellipsis-tooltip {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
