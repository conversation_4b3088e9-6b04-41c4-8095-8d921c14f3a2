<template>
  <!-- 可以支撑table组件描述过长展示tooltip的功能，有需要用的可以直接把text传进去 -->
  <a-tooltip :title="isOverflow ? text : null">
    <div ref="content" class="ellipsis-tooltip">
      <slot>{{ text }}</slot>
    </div>
  </a-tooltip>
</template>

<script>
  export default {
    name: 'EllipsisTooltip',
    props: {
      text: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        isOverflow: false,
      };
    },
    mounted() {
      this.checkOverflow();
      window.addEventListener('resize', this.checkOverflow);
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.checkOverflow);
    },
    methods: {
      checkOverflow() {
        const el = this.$refs.content;
        if (el) {
          this.isOverflow = el.scrollWidth > el.offsetWidth;
        }
      },
    },
  };
</script>

<style scoped>
  .ellipsis-tooltip {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
