<template>
  <div class="stepBox">
    <a-steps labelPlacement="vertical">
      <a-step
        disabled
        v-for="(item, index) in flowDatas"
        :key="index"
        :status="item.status"
        :title="item.flowNames"
      >
        <template #icon v-if="item.status == 'finish' || item.status == 'process'">
          <a class="iconfont" :class="getClassName(item, index)" />
        </template>
      </a-step>
    </a-steps>
    <a-divider />
  </div>
</template>

<script>
  export default {
    name: 'flowSteps',
    props: {
      orderStatus: {
        type: String,
        default: '',
      },
    },
    watch: {
      orderStatus: {
        handler(val) {
          // S0-草稿 S1-报价单审批中 S2-报价单审批通过 S3-报价单审批拒绝 S4-AF单审批中 S5-AF单审批通过 S6-AF单审批拒绝 S7-AF单完成 S8-取消
          if (this.orderStatus === 'S0') {
            //Quotation 【3-Submit Offer Pre-Approval】之前
            this.dealFlowDatas(0);
          } else if (this.orderStatus === 'S1' || this.orderStatus === 'S3') {
            //Offer Approval【3-Submit Offer Pre-Approval】之后，至【4-Offer Pre-Approval】审批通过之前
            this.dealFlowDatas(1);
          } else if (
            this.orderStatus === 'S2' ||
            this.orderStatus === 'S4' ||
            this.orderStatus === 'S6'
          ) {
            //AF Endorsment 【4-Offer Pre-Approval】审批通过之后，至【7-AF 签署审批（AF Endorsment Approval）】审批通过之前
            this.dealFlowDatas(2);
          } else if (this.orderStatus === 'S5') {
            //Verify AF 【7-AF 签署审批（AF Endorsment Approval）】审批通过之后，至【9-Submit Order】提交订单之前
            this.dealFlowDatas(3);
          } else if (this.orderStatus === 'S7') {
            //AF Complete【9-Submit Order】提交订单之后
            this.dealFlowDatas(4);
          }
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        flowDatas: [
          { flowNames: this.$t('quotation.PreAFCreation'), status: 'finish' },
          { flowNames: this.$t('quotation.PreAFApproval'), status: 'finish' },
          { flowNames: this.$t('quotation.AFEndorsment'), status: 'finish' },
          { flowNames: this.$t('quotation.AFVerification'), status: 'finish' },
          { flowNames: this.$t('quotation.AFCompletion'), status: 'finish' },
        ],
      };
    },
    methods: {
      // 获取图标名称
      getClassName(item, index) {
        const icons = [
          'icon-a-Pre-AFCreation',
          'icon-a-Pre-AFApproval',
          'icon-a-AFEndorsement',
          'icon-a-AFVerification',
          'icon-a-AFCompletion',
        ];
        let className = '';
        if (item.status == 'finish') {
          className = 'icon-yiwanchengbuzhou color';
        } else if (item.status == 'process') {
          className = 'processColor ' + icons[index];
        } else {
          className = 'color';
        }
        return className;
      },
      dealFlowDatas(current) {
        console.log('this.flowDatas', this.flowDatas);
        this.flowDatas.forEach((item, index) => {
          current < index
            ? (item.status = 'wait')
            : current == index
            ? (item.status = 'process')
            : (item.status = 'finish');
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .stepBox {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }
  /deep/ .ant-steps {
    width: 800px;
  }
  /deep/.ant-steps-item-title {
    font-size: 14px;
    color: #7a7a82;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  /deep/
    .ant-steps-item-process
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-title {
    color: #0072ff;
  }
  /deep/.ant-steps-item-icon > .ant-steps-icon {
    color: #7a7a82;
  }
  /deep/.ant-steps-item-icon {
    color: #7a7a82;
  }
  /deep/.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after {
    background-color: #0072ff;
  }
  .processColor {
    font-size: 30px;
    color: #0072ff;
  }
  .color {
    font-size: 30px;
    color: #7a7a82;
  }
</style>
