<template>
  <div :class="prefixCls" :style="{ width: barWidth, transition: '0.3s all' }">
    <div class="left-text" :class="[currentNodeEnv]">
      <EllipsisPopover :text="productMrcOtcData.MRCtitle" class="title" />
      <span>{{ productMrcOtcData.MRCtotal | numFilter }}</span>
      <span class="interval-line">|</span>
      <EllipsisPopover :text="productMrcOtcData.OTCtitle" class="title" />
      <span>{{ productMrcOtcData.OTCtotal | numFilter }}</span>
      <span class="interval-line" v-if="ifShowTotalPeriod">|</span>
      <EllipsisPopover
        :text="productMrcOtcData.TotalMRCAllPeriodTitle"
        class="title"
        v-if="ifShowTotalPeriod"
      />
      <span class="title-val-price" v-if="ifShowTotalPeriod">{{
        productMrcOtcData.TotalMRCAllPeriod | numFilter
      }}</span>
    </div>
    <div class="right-text"><slot></slot></div>
  </div>
</template>

<script>
  import EllipsisPopover from '@/views/components/ellipsisPopover';
  import { TAG_VALUE_FTG } from '@/views/components/productTreeList/common/sign';
  export default {
    name: 'FooterToolBarPrice',
    components: {
      EllipsisPopover,
    },
    props: {
      prefixCls: {
        type: String,
        default: 'ant-pro-footer-toolbar-price',
      },
      resetBarWidth: {
        type: String,
        default: undefined,
      },
      productMrcOtcData: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      barWidth() {
        return this.resetBarWidth || '100%';
      },
      currentNodeEnv() {
        console.log(process.env.NODE_ENV);
        return process.env.NODE_ENV === 'development' ? 'ml-200' : '';
      },
      ifShowTotalPeriod() {
        console.log(this.productMrcOtcData, 'productMrcOtcData');
        return this.productMrcOtcData.TotalMRCAllPeriod !== TAG_VALUE_FTG;
      },
    },
    filters: {
      numFilter(value) {
        let val = 0;
        // 千分符
        if (value != 0) {
          val = Number(value).toLocaleString('en-US');
        }
        // 添加 - 单位
        val = `$${val}`;
        return val;
      },
    },
  };
</script>

<style lang="less" scoped>
  .ant-pro-footer-toolbar-price {
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: 9;
    width: 100%;
    height: 80px;
    padding: 0 22px;
    line-height: 80px;
    background: #fff;
    box-shadow: 0 -1px 15px #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-text {
      font-size: 14px;
      color: #373d41;
      font-weight: 400;
      display: flex;
      align-items: center;
      height: 100%;

      &.ml-200 {
        margin-left: 200px;
      }

      .title {
        line-height: 20px;
        &::after {
          content: ':';
          margin: 0 5px;
        }
      }
      .title-val-price {
        font-size: 18px;
        color: #e60017;
        line-height: 25px;
        height: 25px;
        display: inline-block;
      }
      .interval-line {
        // margin: 0 31px;
        width: 62px;
        text-align: center;
        color: rgba(158, 158, 158, 1);
      }
    }
    .right-text {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      flex-wrap: nowrap;
    }
  }
</style>
