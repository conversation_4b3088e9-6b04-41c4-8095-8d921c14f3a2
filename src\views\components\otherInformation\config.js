import that from '@/main.js';

export const OASISSerialNo = that => (rule, value, callback) => {
  // if (value === '') {
  //   callback(new Error(that.$t('fulfillmentInfo.notEnteredOASISSerialNo')));
  // } else {
  //   const regex = /^[a-zA-Z0-9]{2}[0-9]{2}-[0-9]{8}$/;
  //   if (!regex.test(value)) {
  //     callback(new Error(that.$t('fulfillmentInfo.OASISSerialNoError')));
  //   } else {
  //     callback();
  //   }
  // }
  if (value) {
    const regex = /^[a-zA-Z0-9]{2}[0-9]{2}-[0-9]{8}$/;
    if (!regex.test(value)) {
      callback(new Error(that.$t('fulfillmentInfo.OASISSerialNoError')));
    } else {
      callback();
    }
  } else {
    callback();
  }
};

export const welcomeLetter = (that, isEmailAddressShow) => (rule, value, callback) => {
  if (value) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(value)) {
      callback(new Error(that.$t('fulfillmentInfo.errorEmailTips')));
    } else {
      callback();
    }
  } else if (isEmailAddressShow) {
    callback(that.$t('common.noNull'));
  } else {
    callback();
  }
};

export const welcomeLetterCustomerHotlineNumber =
  (that, isEmailAddressShow) => (rule, value, callback) => {
    if (value) {
      const phoneRegex = /^\d{1,12}$/;
      if (!phoneRegex.test(value)) {
        callback(new Error(that.$t('fulfillmentInfo.errorPhoneTips')));
      } else {
        callback();
      }
    } else if (isEmailAddressShow) {
      callback(that.$t('common.noNull'));
    } else {
      callback();
    }
  };

export default {
  rules: isEmailAddressShow => ({
    OASISSerialNo: [{ required: false, validator: OASISSerialNo(that), trigger: 'change' }],
    welcomeLetter: [
      {
        required: isEmailAddressShow,
        validator: welcomeLetter(that, isEmailAddressShow),
        trigger: 'blur',
      },
    ],
    welcomeLetterCustomerHotlineNumber: [
      {
        required: isEmailAddressShow,
        validator: welcomeLetterCustomerHotlineNumber(that, isEmailAddressShow),
        trigger: 'blur',
      },
    ],
    orderRemark: [{ required: true, message: that.$t('common.noNull'), trigger: 'change' }],
  }),
};
