<template>
  <div class="otherInformation">
    <div class="secondLevel-header-title">{{ $t('fulfillmentInfo.otherInformation') }}</div>
    <a-form-model
      :model="ruleForm"
      v-bind="{}"
      :colon="false"
      layout="vertical"
      ref="ruleForm"
      :rules="rules"
    >
      <a-row :gutter="24" justify="start" type="flex">
        <!-- OASISSerialNo -->
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('fulfillmentInfo.OASISSerialNo')" prop="OASISSerialNo">
            <a-input
              v-model.trim="ruleForm.OASISSerialNo"
              @keyup="handleInputOASISSerialNo"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <!-- Billing Customer Reference -->
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('fulfillmentInfo.billingCustomerReference')">
            <a-input
              :maxLength="8"
              v-model.trim="ruleForm.billingCustomerReference"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInputBillingCustomerReference"
            />
          </a-form-model-item>
        </a-col>
        <!-- Project -->
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('fulfillmentInfo.project')" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model.trim="ruleForm.project"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <!-- OPPID -->
        <a-col :span="6" flex="flex-start" v-if="showSRD">
          <a-form-model-item :label="$t('fulfillmentInfo.OPPID')" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model.trim="ruleForm.OPPID"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <!-- Welcome Letter -->
        <!-- AI帮写 -->
        <a-col :span="6" v-if="showSRD && PRODUCT_TYPE != '300019'">
          <a-form-model-item prop="welcomeLetter">
            <template v-slot:label>
              <a-checkbox
                @change="switchChange"
                ref="welcomeLetterCheckbox"
                :id="`welcome-letter-checkbox-${_uid}`"
                :checked="checkBoxValue"
              />
              <label :for="`welcome-letter-checkbox-${_uid}`" @click.stop.prevent>
                <span style="margin: 0 10px">{{ $t('fulfillmentInfo.welcomeLetter') }}</span>
              </label>
            </template>
            <a-input
              v-validateEmail
              v-model.trim="ruleForm.welcomeLetter"
              :placeholder="$t('common.inputPlaceholder')"
              :disabled="!isEmailAddressShow"
            />
          </a-form-model-item>
        </a-col>
        <!-- Welcome Letter Customer Hotline Number -->
        <a-col :span="6" v-if="showSRD && PRODUCT_TYPE != '300019'">
          <a-form-model-item
            :label="$t('fulfillmentInfo.welcomeLetterCustomerHotlineNumber')"
            prop="welcomeLetterCustomerHotlineNumber"
          >
            <a-input
              v-validate-number="12"
              v-model="ruleForm.welcomeLetterCustomerHotlineNumber"
              :placeholder="$t('common.inputPlaceholder')"
              :disabled="!isEmailAddressShow"
            />
          </a-form-model-item>
        </a-col>
        <!-- 是否同时新开Hunting for Citinet Group -->
        <a-col :span="12" v-show="this.$route.path == '/UCS/citinetInstall'">
          <a-form-model-item>
            <template v-slot:label>
              <a-checkbox v-model="ruleForm.isNewOpenHuntingForCitinet" />
            </template>
            <span>{{ $t('fulfillmentInfo.newHuntingForCitinetGroup') }}</span>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row>
        <!-- Order Remark -->
        <a-col :span="18">
          <a-form-model-item
            :label="$t('fulfillmentInfo.orderRemark')"
            prop="orderRemark"
            class="textAreaBorder"
          >
            <a-textarea
              class="textArea"
              v-model="ruleForm.orderRemark"
              :placeholder="$t('common.inputPlaceholder')"
              :rows="3"
              :max-length="1000"
              @input="handleInput"
            />
            <!-- <span class="word-limit-hint">当前输入({{ currentLength }}/2000)</span> -->
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
  import { mapState, mapGetters } from 'vuex';
  import config from './config';
  export default {
    name: 'otherInformation',
    props: {
      getCustContactListDatas: {
        type: Function,
        required: true,
      },
      showSRD: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        ruleForm: {
          OASISSerialNo: '',
          welcomeLetter: '',
          welcomeLetterCustomerHotlineNumber: '10088',
          isNewOpenHuntingForCitinet: false,
        },
        isEmailAddressShow: false,
        currentLength: 0, // 当前输入的字数
        checkBoxValue: false,
        PRODUCT_TYPE: '',
      };
    },
    computed: {
      ...mapState('quotation', {
        // productFamily: state => state.productFamily,
        productType: state => state.productType,
      }),
      ...mapState('macd', {
        orderList: state => state.orderList,
      }),
      ...mapGetters('macd', ['getHKTOtherInfo']),
      rules() {
        // 动态生成校验规则
        return config.rules(this.isEmailAddressShow);
      },
    },
    mounted() {
      this.PRODUCT_TYPE = this.productType.value; // this.orderList[0]?.PRODUCT_TYPE;
    },
    methods: {
      handleInputBillingCustomerReference(event) {
        // 使用正则表达式过滤掉中文字符
        this.ruleForm.billingCustomerReference = event.target.value.replace(/[\u4e00-\u9fa5]/g, '');
      },
      handleInputOASISSerialNo(event) {
        const value = event.target.value; // 获取当前输入值
        if (value.length === 4 && event.key !== 'Backspace' && event.key !== 'Delete') {
          // 检查长度为3时添加字符或进行其他操作
          this.ruleForm.OASISSerialNo = value + '-'; // 更新绑定的数据以显示格式化后的内容给用户看，但不完全覆盖用户的原始输入值。如果需要完全覆盖，可以考虑使用setTimeout或nextTick。
        } else {
          // 否则不做任何处理或重置为原始值等。这里不做额外处理，保持原始行为。
          this.ruleForm.OASISSerialNo = value; // 直接更新原始输入值，保持用户的每次输入。如果需要阻止某些输入，可以在这里处理。例如：阻止非数字输入等。
        }
      },
      // handleInput() {
      //   // 更新当前输入的字数
      //   this.currentLength = this.ruleForm.orderRemark.length;
      // },
      handleInput(event) {
        // 获取当前输入值
        let value = event.target.value;
        // 使用正则表达式替换多个连续空格为单个空格
        // 替换多个连续空格为单个空格，但保留换行符
        // value = value.replace(/ {2,}/g, ' ');
        // 去掉开头的空格（不影响换行）
        if (value.startsWith(' ')) {
          value = value.trimStart();
        }

        // 如果输入值的第一个字符是空格，去掉它
        if (value.startsWith(' ')) {
          value = value.substring(1);
        }
        // 更新 formData 中对应的字段
        this.$set(this.ruleForm, 'orderRemark', value);
        // 同时更新原生输入框的值，防止用户看到多余空格
        event.target.value = value;
      },

      // 初始化macd数据
      hanldeInitMACDData() {
        console.log('this.getHKTOtherInfo = ', this.getHKTOtherInfo);
        const data = this.getHKTOtherInfo;
        this.ruleForm = {
          OASISSerialNo: data.OASISSerialNo,
          billingCustomerReference: data.billingCustomerReference,
          project: data.project,
          OPPID: data.OPPID,
          welcomeLetter: data.welcomeLetter,
          welcomeLetterCustomerHotlineNumber: data.welcomeLetterCustomerHotlineNumber || '10088',
          orderRemark: data.orderRemark,
        };
        if (data?.welcomeLetter) {
          this.isEmailAddressShow = true;
          this.checkBoxValue = true;
        }
      },

      // 是否需要 修改Admin中的letter邮箱信息
      switchChange(e) {
        this.checkBoxValue = e.target.checked;
        this.$refs.ruleForm.clearValidate(['welcomeLetter', 'welcomeLetterCustomerHotlineNumber']);
        this.isEmailAddressShow = e.target.checked;
        if (e.target.checked) {
          let CUS_ARRAY = this.getCustContactListDatas();
          if (Array.isArray(CUS_ARRAY)) {
            const admin = CUS_ARRAY.find(item => item.PARTICIPANTS_TYPE === '01');
            if (admin) {
              this.ruleForm.welcomeLetter = admin.EMAIL;
            }
          }
        } else {
          this.ruleForm.welcomeLetter = '';
          this.ruleForm.welcomeLetterCustomerHotlineNumber = '10088';
        }
      },
      // 校验必填
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.ruleForm.validate(valid => {
            if (valid) {
              // 验证通过，调用 resolve
              resolve();
            } else {
              // 验证失败，调用 reject 并传递错误信息
              // reject(new Error('OASIS Serial No 字段验证失败'));
            }
          });
        });
      },
    },
  };
</script>
<style lang="less" scoped>
  .word-limit-hint {
    font-size: 12px;
    color: #999;
    // margin-top: 4px;
  }
  .welcome-letter {
    height: 30px;
    display: flex;
    align-items: center;
    .switch-module {
      display: flex;
      align-items: center;
      margin-right: 10px;
      .switch-word {
        margin-left: 10px;
        font-size: 14px;
      }
    }
  }
  .dateIcon {
    width: 16px;
    height: 16px;
    margin-top: -8px;
  }

  .textAreaBorder {
    width: 100%;
    .textArea {
      height: 100px !important;
    }
  }
  .otherInformation {
    /deep/.ant-form-vertical .ant-form-explain {
      background-color: #fff !important;
      z-index: 999 !important;
      margin-top: 2px;
    }
  }
  /deep/.ant-form-vertical .ant-form-explain {
    background-color: #fff !important;
    z-index: 999 !important;
    margin-top: 2px;
  }
  /deep/.has-error .ant-form-explain {
    background-color: #fff !important;
    z-index: 999 !important;
    margin-top: 2px;
  }
</style>
