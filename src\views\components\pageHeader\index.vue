<template>
  <div class="page-header">
    <!-- 顶部返回按钮 -->
    <div class="return-btn flex-row title-flex" @click="handleReturn">
      <a-icon type="left" class="icon" />
      <span style="padding-right: 20px">{{ $t('common.return') }}</span>
      <span class="page-info">{{ prePageInfo }}&nbsp;</span>
    </div>
  </div>
</template>

<script>
  import { getUrlParams } from '@/utils/utils';
  export default {
    name: 'pageHeader',
    props: {
      prePageInfo: { type: String, default: '' },
    },
    methods: {
      handleReturn() {
        const { path } = getUrlParams();
        if (path) {
          window.location.href = path;
        } else {
          this.$router.customBack();
        }
      },
    },
  };
</script>

<style lang="less">
  .page-header {
    .return-btn {
      margin-bottom: 10px;
      cursor: pointer;
      font-size: 14px;
      color: #0076ff;
      .icon {
        margin-right: 5px;
      }
    }

    .page-info {
      font-size: 14px;
      color: #333333;
      font-weight: 500;
    }
  }
  .title-flex {
    align-items: center;
  }
</style>
