// 字段 - 标记
export const FIELD_SIGN = {
  FIELD_POINTS: 'redeemablePoints',
  FIELD_LINE_NUMBER: 'noOfLinesInstalled',
  FIELD_MRC_TOTAL: 'totalMRCForAllLine',
  FIELD_OTC_TOTAL: 'totalOTCForAllLine',
  FIELD_MRC_PERIOD_TOTAL: 'totalMRCForAllLinePeriod',
};

// 格式化 - 标记
export const FORMATTER_SIGN = {
  FORMATTER_LOCALE: 'locale', // 千分符
  FORMATTER_UNIT: 'unit', // 加入单位 - 可能会需要兼容多个单位，目前$
};

export const FIELD_CONFIG = [
  {
    key: FIELD_SIGN.FIELD_POINTS,
    label: 'quotation.NoOfPremiumPoints',
    class: 'tag-red tag-f18',
  },
  {
    key: FIELD_SIGN.FIELD_LINE_NUMBER,
    label: 'quotation.NoOfLinesInstalled',
    class: 'tag-red tag-f18',
  },
  {
    key: FIELD_SIGN.FIELD_MRC_TOTAL,
    label: 'quotation.TotalMRCForAllLine',
    class: '',
    formatter: [FORMATTER_SIGN.FORMATTER_LOCALE, FORMATTER_SIGN.FORMATTER_UNIT],
  },
  {
    key: FIELD_SIGN.FIELD_OTC_TOTAL,
    label: 'quotation.TotalOTCForAllLine',
    class: '',
    formatter: [FORMATTER_SIGN.FORMATTER_LOCALE, FORMATTER_SIGN.FORMATTER_UNIT],
  },
  {
    key: FIELD_SIGN.FIELD_MRC_PERIOD_TOTAL,
    label: 'quotation.TotalMRCForAllLinePeriod',
    class: 'tag-red tag-f18',
    formatter: [FORMATTER_SIGN.FORMATTER_LOCALE, FORMATTER_SIGN.FORMATTER_UNIT],
  },
];
