<template>
  <div class="product-count">
    <template v-for="(field, index) in fieldConfigList">
      <span class="product-count-title" :key="field.key + '-title'"> {{ $t(field.label) }} : </span>
      <span
        class="product-count-content"
        :class="[field.class, index != fieldConfigList.length - 1 ? 'has-line' : '']"
        :key="field.key + '-content'"
      >
        {{
          field.formatter && field.formatter.length != 0
            ? formatterField(productCount[field.key], field.formatter)
            : productCount[field.key]
        }}
      </span>
    </template>
  </div>
</template>

<script>
  import { FIELD_CONFIG, FIELD_SIGN, FORMATTER_SIGN } from './config.js';
  export default {
    name: 'productChargeTotal',
    props: {
      // 全部产品的费用
      productCount: {
        type: Object,
        default: () => {
          return {
            [FIELD_SIGN.FIELD_POINTS]: 0,
            [FIELD_SIGN.FIELD_LINE_NUMBER]: 0,
            [FIELD_SIGN.FIELD_MRC_TOTAL]: 0,
            [FIELD_SIGN.FIELD_OTC_TOTAL]: 0,
            [FIELD_SIGN.FIELD_MRC_PERIOD_TOTAL]: 0,
          };
        },
      },
      fields: {
        type: Array,
        default: () => [
          FIELD_SIGN.FIELD_POINTS,
          FIELD_SIGN.FIELD_LINE_NUMBER,
          FIELD_SIGN.FIELD_MRC_TOTAL,
          FIELD_SIGN.FIELD_OTC_TOTAL,
          FIELD_SIGN.FIELD_MRC_PERIOD_TOTAL,
        ],
      },
    },
    data() {
      return {};
    },

    computed: {
      fieldConfigList() {
        return FIELD_CONFIG.filter(item => this.fields.includes(item.key));
      },
    },
    mounted() {
      console.log(this.productCount, this.fieldConfigList, 'productCount');
    },
    methods: {
      formatterField(value = 0, formatterArr) {
        let beforeResult = '';
        let content = '';
        let afterResult = '';
        // #TODO：可能会需要兼容很多种情况
        for (let i = 0; i < formatterArr.length; i++) {
          const ele = formatterArr[i];
          if (ele === FORMATTER_SIGN.FORMATTER_LOCALE) {
            content = Number(value).toLocaleString('en-US');
          } else if (ele === FORMATTER_SIGN.FORMATTER_UNIT) {
            beforeResult = '$' + beforeResult;
          }
        }
        return `${beforeResult}${content}${afterResult}`;
      },
    },
  };
</script>

<style lang="less" scoped>
  .product-count {
    background: #e5f0ff;
    height: 40px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow: auto;
    padding: 0 20px;
    margin-top: 11px;
    &-title {
      font-size: 14px;
      color: #373d41;
      line-height: 25px;
      font-weight: 500;
      padding-right: 3px;
    }
    &-content {
      font-size: 14px;
      color: #373d41;
      line-height: 20px;
      font-weight: 400;
      padding-right: 60px;
      position: relative;
      &.tag-red {
        color: #e60017;
      }
      &.tag-f18 {
        font-size: 18px;
      }
      &.has-line {
        &::after {
          content: '';
          width: 1px;
          height: 20px;
          background: #9e9e9e;
          position: absolute;
          right: 30px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
</style>
