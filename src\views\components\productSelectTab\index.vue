<template>
  <div class="product-select-tab">
    <div
      v-for="(item, index) in list"
      :key="item.id"
      :class="[
        'select-tab-item',
        activeKey === index ? 'active' : '',
        showChecked ? 'has-check' : '',
      ]"
      @click="handleTabClick(item, index)"
    >
      {{ index + 1 }} - {{ item.name }}
      <a
        v-if="showChecked && item.checked !== undefined"
        class="tab-check iconfont icon-lym-shengxiaokaoshi"
        :class="item.checked ? 'checked' : ''"
        @click.stop="handleChecked(item)"
      ></a>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ProductSelectTab',
    props: {
      list: {
        type: Array,
        default: () => [],
      },
      // 是否展示勾选
      showChecked: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        activeKey: 0,
      };
    },
    computed: {},
    mounted() {},
    methods: {
      handleTabClick(item, index) {
        this.activeKey = index;
        this.$emit('change', { ...item, index });
      },
      // #TODO 待补充，是否加入点击,用于适配AF单
      handleChecked(item) {
        this.$emit('checkChange', item);
      },
    },
  };
</script>

<style lang="less" scoped>
  .product-select-tab {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .select-tab-item {
    background: #e9e9e9;
    border-radius: 11px;
    font-size: 14px;
    color: #333333;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
    min-width: 130px;
    height: 22px;
    padding: 0 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
    &:last-child {
      margin-right: 0;
    }
    &.active {
      background: #d2e6ff;
      border: 1px solid rgba(0, 114, 255, 1);
      color: #0072ff;
    }
    &.has-check {
      padding: 0 25px;
    }
  }
  .tab-check {
    font-size: 14px;
    color: #333;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 14px;
    &.checked {
      color: #0072ff;
    }
  }
</style>
