

# 目录结构

```javascript
|-- components
    |-- README.md  # 说明文档
    |-- additionalProduct.vue  # 附加产品树列表
    |-- attributeEdit.vue  # 附加产品树修改逻辑
    |-- mainProduct.vue  # 主产品树列表
    |-- tabList.vue  # 产品树Tab页逻辑
    |-- taxRateExchange.vue  # 产品树税率弹窗文件
	|-- common
		|-- config.js  # 产品树配置
		|-- utils.js  # 产品树通用业务逻辑工具类
    |-- mixin
        |-- index.js # 主产品和附加产品的混入文件
    |-- nextStepValidate  
        |-- nextStepValidate.js  # 产品树选择完成后下一步完整校验逻辑
    |-- validate
        |-- dependence.js  # 部分依赖/完全依赖校验
		|-- index.js  # 产品树逻辑校验入口
		|-- maxmin.js  # 最大最小值校验
		|-- mrcAndRebate.js  # mrc和rebate属性校验
        |-- mutex.js  # 互斥校验
        |-- utils.js  # 校验工具类

```

# 产品选择逻辑

## 1、主产品

主产品只能选择一个，新开场景中必须要选择一个主产品才能继续进行

## 2、 附加产品

其余Tab标签都是附加产品，如设备产品，高端产品，是通过附加产品列表通过特定值过滤出来展示在对应标签页。

附加产品可以选择多个。



# 初始化查询状态
1、所有主产品进入页面是默认未选择，并处于收起状态
2、选中某个主产品时，根据必选项配置，自动选中必选项并展开Product和所有必选项所在的Package
3、取消选中某个主产品时，给出提示，确认后收起此产品，并自动取消所有选中的package和元素，重置修改过的属性值为默认值。必选项不允许取消选中。

FORCE_TAG + DEFAULT_TAG
1、必选1 默认1（选中+置灰，不可操作）
2、非必选0 默认1（默认选中状态，可取消勾选）
3、非必选0 非默认0（未选中状态，可勾选）





# 产品校验

所有校验都是基于同产品内部的包和元素校验，选择的多个附加产品，产品与产品之间不进行对应业务校验。

## 1、互斥校验

### 规则

包与包之间、同包/不同包元素之间，因为配置会存在互斥关系。

勾选包/元素的时候会触发校验互斥校验逻辑，如果包/元素 A与B存在互斥关系，并且A已经选中了，则勾选B的时候会提示与A互斥，不能勾选B。如果取消勾选A，则能正常勾选B。



## 2、最大最小值校验

### 规则

勾选包/元素的时候，会校验包/元素会校验产品的配置的包/元素最大最小值，当超过最大值时，则不允许勾选，如果取消勾选小于最小值时则不允许取消。



## 3、部分依赖

### 规则

包/元素之间，因为配置会存在部分依赖关系。部分依赖校验规则又区分为单项部分依赖和多项部分依赖。

- 单项部分依赖： 勾选包/元素的时候，如果存在部分依赖关系并且是单项部分依赖，则勾选当前项的同时，将其单项的部分依赖包/元素也进行勾选。如A部分依赖B，则勾选A的时候会将B也进行勾选。如果勾选后取消B，在选择产品阶段是不进行校验的，后续我们有统一的步骤进行校验。
- 多项部分依赖： 勾选包/元素的时候，如果存在部分依赖关系并且是多项部分依赖，则勾选当前项的同时，提示和多项包/元素存在部分依赖关系，让用户手动进行勾选。如A部分依赖B、C、D，在勾选A的时候A会变成选中状态同时会弹窗提示，需要用户手动勾选B、C、或者D其中的一个。



## 4、 完全依赖

### 规则

包/元素之间，因为配置会存在完全依赖关系。

当勾选包/元素的时候，会弹窗提示进行二次确认：点击确定会自动选中完全依赖项，否则会取消当前勾选，如果点击确定则勾选中所有完全依赖项，如果点击否则全不勾选。

当取消勾选包/元素的时候，会弹窗提示进行二次确认：点击确定会自动取消完全依赖项，否则会保持当前勾选，如果点击确定则取消所有完全依赖项，如多点击否则保持现有勾选状态。



## 5、Mrc和Rebate校验

### 规则

- 勾选Rebate的前提是对应的Mrc一定是已选中状态
- 三种类型的Rebate之间是互斥，只能选择一个
- 如果对MRC的entfee进行了修改，则不能再选择Rebate
- Rebate的合约期与对应MRC对齐，因此Rebate的合约期默认取MRC的合约期并置灰，不能修改



# 下一步校验



由于初始化查询状态中的处理，加上配置关系的错乱，存在选择商品勾选完成后，但是产品树的数据并不符合规则的一些情况

如：

- 产品配置A，B都是默认选中状态，但是实际上A、B又存在互斥关系，这种在初始化产品树数据逻辑的时候是没有进行校验出来的
- 如在单项部分依赖中，A依赖B，我们将A、B都选中了，但是用户手动取消了B，是没有做任何限制的，这也会导致产品树数据不对
- 等等其他一系列错误场景

所以在下一步校验中，我们重新针对整个选中的产品树数据，进行了完整的详细的规则校验，可以理解为是产品选择校验的超集，不仅包含产品选择的所有校验，目前还有重复元素下单校验、产品属性必填校验。

后续新增相关校验逻辑的时候，这里是必须要进行代码添加改造的。





# 结语



目前产品树的目录结构和代码已经进行了部分调整和封装，开发人员参考目录结构和其他校验方法在对应位置添加即可。

待改进：产品树中mainProduct.vue和additionalProduct.vue文件中，有高度重合的代码，这个中间讨论过，梳理好代码逻辑，提取出一致代码，以mixin的形式混入到这两个文件中，避免未来附加产品需要同步主产品代码的问题。