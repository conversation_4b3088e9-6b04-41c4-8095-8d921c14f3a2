<template>
  <div class="attributeEdit">
    <a-spin :spinning="spinning">
      <a-form-model :model="form" layout="vertical" class="form" :rules="rules" ref="ruleForm">
        <div
          v-for="item in interfaceElementList"
          :key="item.intfElementId"
          :style="{ order: item.showOrder }"
        >
          <a-form-model-item
            :label="item.intfElementLabel"
            :prop="item.elementCode"
            v-if="item.showOrder !== 99999999"
          >
            <!-- 输入框 -->
            <a-input
              :type="passwordDesensitization == item.elementCode ? 'password' : 'text'"
              v-if="item.intfElementTypeCode === '0'"
              v-model="form[item.elementCode]"
              :placeholder="$t('common.inputPlaceholder')"
              :disabled="isDisabled || item.modifyRightCode == 'false'"
            />

            <!-- 下拉选择框 -->
            <a-select
              v-if="['1', '4', '5', '6'].includes(item.intfElementTypeCode)"
              v-model="form[item.elementCode]"
              :placeholder="$t('common.selectPlaceholder')"
              :disabled="isDisabled || item.modifyRightCode == 'false'"
            >
              <a-select-option
                v-for="(iitem, iindex) of item.selectInitialDataList || []"
                :value="iitem.enumFieldCode"
                :key="iindex"
                >{{ iitem.enumFieldName }}</a-select-option
              >
            </a-select>

            <!-- 日期选择框 -->
            <a-date-picker
              v-if="item.intfElementTypeCode === '2'"
              v-model="form[item.elementCode]"
              :placeholder="$t('common.selectPlaceholder')"
              style="width: 100%"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              :disabled="isDisabled || item.modifyRightCode == 'false'"
            />

            <!-- 多选框 -->
            <a-checkbox-group
              v-if="item.intfElementTypeCode === '11'"
              v-model="form[item.elementCode]"
              :disabled="isDisabled || item.modifyRightCode == 'false'"
            >
              <!-- 这里可以根据实际情况动态生成选项 -->
              <a-checkbox
                v-for="(iitem, iindex) of item.selectInitialDataList || []"
                :value="iitem.enumFieldCode"
                :key="iindex"
                >{{ iitem.enumFieldName }}</a-checkbox
              >
            </a-checkbox-group>
          </a-form-model-item>
        </div>
      </a-form-model>

      <!-- 底部按钮 -->
      <a-space class="flex-right" v-if="interfaceElementList.length">
        <a-button @click="cancel">{{ $t('common.buttonCancel') }}</a-button>
        <a-button type="primary" @click="confirm" v-if="!isDisabled">{{
          $t('common.buttonConfirm')
        }}</a-button>
      </a-space>
    </a-spin>
  </div>
</template>

<script>
  import { getElements } from '@/api/customerVerify';
  import { validateAlphanumeric, validateEmail, validateIsNumber } from '@/utils/utils';
  import { mapState } from 'vuex';
  // interfaceElementList，每一项的字段解释
  // showOrder == 99999999 隐藏属性
  // modifyRightCode TRUE 就是可以改 FALSE就是不可以
  export default {
    name: 'AttributeEdit',
    // 新装Citinet选号页面使用了provide
    inject: {
      elementCode: {
        from: 'elementCode', // 注入的 key
        default: '', // 默认值
      },
      // 订单详情页面 密码展示需要脱敏
      passwordDesensitization: {
        from: 'passwordDesensitization', // 注入的 key
        default: '', // 默认值
      },
    },
    props: {
      attributeEditDisabled: {
        type: Boolean,
        default: false,
      },
      data: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        form: {},
        rules: {},
        interfaceElementList: [],
        spinning: false,
        record: {},
        isDisabled: false,
      };
    },
    watch: {
      // 属性编辑 表单内容是否全部禁用
      attributeEditDisabled: {
        handler(newVal) {
          this.isDisabled = newVal;
        },
        deep: true,
        immediate: true,
      },
    },
    mounted() {},
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
    },
    methods: {
      getElements(record) {
        // 存储当前record，弹窗确认按钮的时候返回父组件，用作记录判断点击的是哪一项
        this.record = record;

        if (record.interfaceElementList) {
          // 如果已经点击确认编辑过的数据，则不需要调接口初始化，直接获取存储好的数据
          this.getInterfaceElementList(record, record.interfaceElementList);
        } else {
          this.getElementsApi(record);
        }
      },
      // 获取元素属性接口
      getElementsApi(record) {
        const params = {
          areaCode: '212',
          cityId: this.userInfo.CITY_CODE,
          deptId: this.userInfo.DEPART_ID,
          epachyId: this.userInfo.EPARCHY_CODE,
          loginEpachyId: '0010',
          loginProvinceId: '11',
          pageData: true,
          provinceId: this.userInfo.PROVINCE_CODE,
          staffId: this.userInfo.STAFF_ID,
          tradeEpachyId: '0010',
        };
        const data = [
          record.DISCNT_CODE ? `DISCNT_${record.DISCNT_CODE}` : `SERVICE_${record.SERVICE_ID}`,
        ]; // 入参格式： ['DISCNT_91600060'] ['SERVICE_91600060']
        this.spinning = true;
        getElements(data, params).then(res => {
          this.spinning = false;
          const obj = res.DATA[0][data[0]]; // 输出格式:：res.DATA[0].DISCNT_91600060

          this.getInterfaceElementList(record, obj.interfaceElement, obj);
        });
      },
      // 数据处理
      getInterfaceElementList(record, list, obj) {
        console.log(record, 'record', list, 'list');
        this.interfaceElementList = list.map(item => {
          // obj有值，说明是接口返回的，针对selectInitialData 对象是否含有elementCode值，将返回同级的selectInitialData下拉选择框数据，放到对应的item里面
          if (
            obj &&
            Object.prototype.hasOwnProperty.call(obj.selectInitialData, item.elementCode)
          ) {
            item.selectInitialDataList = obj.selectInitialData[item.elementCode];
          }

          // 是否有attributeList字段，将用户已订购的属性和全量的进行匹配，回显数据
          if (record.attributeList?.length) {
            record.attributeList.forEach(iitem => {
              if (item.elementCode == iitem.CRM_ATTR_CODE || item.elementCode == iitem.ATTR_CODE) {
                item.isOrdered = '1'; // 标识已订购
                item.orderedValue = iitem.CRM_ATTR_VALUE || iitem.ATTR_VALUE; // 存储已订购的值
                this.$set(
                  this.form,
                  item.elementCode,
                  iitem.CRM_ATTR_VALUE || iitem.ATTR_VALUE || undefined,
                );
              }
            });
          } else {
            if (!record.interfaceElementList) {
              // 获取接口返回的数据，form初始化表单数据
              this.$set(this.form, item.elementCode, item.intfElementInitValue || undefined);
            }
          }

          // 针对不同类型组件，设置校验规则,这里只有输入框需要校验
          if (item.intfElementTypeCode === '0') {
            this.$set(this.rules, item.elementCode, [
              {
                required: item.intfElementCanNull == '1',
                trigger: 'change',
                validator: (rule, value, callback) => {
                  if (item.intfElementCanNull == '1' && !value) {
                    // 必填 没值（提示）
                    callback(new Error(`${item.intfElementLabel} ${this.$t('common.noNull')}`));
                  } else if (item.intfElementCanNull == '1' && value) {
                    // 必填 有值（校验）
                    this.isValidator(item, rule, value, callback);
                  } else if (item.intfElementCanNull == '0' && value) {
                    // 非必填 有值（校验）
                    this.isValidator(item, rule, value, callback);
                  } else if (item.intfElementCanNull == '0' && !value) {
                    // 非必填 没值（不校验）
                    callback(); // 校验通过
                  }
                },
              },
            ]);
          } else if (['1', '4', '5', '6'].includes(item.intfElementTypeCode)) {
            this.$set(this.rules, item.elementCode, [
              {
                required: item.intfElementCanNull == '1',
                trigger: 'change',
                validator: (rule, value, callback) => {
                  if (item.intfElementCanNull == '1' && !value) {
                    // 必填 没值（提示）
                    callback(new Error(`${item.intfElementLabel} ${this.$t('common.noNull')}`));
                  } else {
                    callback(); // 校验通过
                  }
                },
              },
            ]);
          }

          // 针对OSCA新需求积分累计 - 主产品的BASIC_PRICE的MRC可调整
          // 暂时识别标记：record - MAIN_DISCNT_TYPE === 1,
          if (
            record.MAIN_DISCNT_TYPE === '1' &&
            list.filter(i => i.intfElementLabel === 'MRC').length
          ) {
            item.modifyRightCode = 'true'; // 允许修改
          }
          return item;
        });

        // 获取存储过的数据，form初始化表单数据
        if (record.interfaceElementList) {
          record.interfaceElementList.forEach(item => {
            this.$set(this.form, item.elementCode, item[item.elementCode] || undefined);
          });
        }
      },
      // 校验
      isValidator(item, rule, value, callback) {
        if (item.regType == 'isalnum') {
          // isalnum 字母或者数字
          this.regType_isalnum(item, value, callback);
        } else if (item.regType == 'isdouble') {
          // isdouble 纯数字
          this.regType_isdouble(item, value, callback);
        } else if (item.regType == 'isemail') {
          // isemail 邮件
          this.regType_isemail(item, value, callback);
        } else {
          callback(); // 校验通过
        }
      },
      // isalnum 字母或者数字
      regType_isalnum(item, value, callback) {
        if (item.regSubType == '1') {
          // isalnum 字母或数字
          if (!validateAlphanumeric(value)) {
            callback(new Error(this.$t('common.inputLetterNumber')));
          }

          // 1 固定长度
          this.limitLength(item, value, callback);
        } else if (item.regSubType == '2') {
          // isalnum 字母或数字
          if (!validateAlphanumeric(value)) {
            callback(new Error(this.$t('common.inputLetterNumber')));
          }

          // 2 范围长度
          this.rangeLength(item, value, callback);
        } else {
          // 4 不限制
          callback(); // 校验通过
        }
      },
      // isdouble 纯数字
      regType_isdouble(item, value, callback) {
        if (item.regSubType == '3') {
          // isdouble 纯数字
          if (!validateIsNumber(value)) {
            callback(new Error(this.$t('common.pureNumbers')));
          }

          // 3 范围数字
          this.rangeNumber(item, value, callback);
        } else if (item.regSubType == '5') {
          // 5 长度范围数字
          this.lengthRangeNumber(item, value, callback);
        } else {
          // 4 不限制
          callback(); // 校验通过
        }
      },
      // isemail 邮箱格式
      regType_isemail(item, value, callback) {
        if (validateEmail(value)) {
          callback(); // 校验通过
        } else {
          callback(new Error(this.$t('fulfillmentInfo.emailIncorrectFormat')));
        }
      },
      // 固定长度
      limitLength(item, value, callback) {
        if (!item.regExpr) callback(); // 校验通过

        if (Number(item.regExpr) === value.length) {
          callback(); // 校验通过
        } else {
          callback(new Error(`${this.$t('common.limitLength', { length: item.regExpr })}`));
        }
      },
      // 范围长度
      rangeLength(item, value, callback) {
        if (!item.regExpr) callback(); // 校验通过

        if (this.isRange('length', item.regExpr, value)) {
          callback(); // 校验通过
        } else {
          callback(
            new Error(
              this.$t('common.rangeLength', { rangeLength: item.regExpr.replace('|', ' - ') }),
            ),
          );
        }
      },
      // 范围数字
      rangeNumber(item, value, callback) {
        if (!item.regExpr) callback(); // 校验通过

        if (this.isRange('number', item.regExpr, value)) {
          callback(); // 校验通过
        } else {
          callback(
            new Error(
              this.$t('common.rangeNumber', { rangeNumber: item.regExpr.replace('|', ' - ') }),
            ),
          );
        }
      },
      // 长度范围数字
      lengthRangeNumber(item, value, callback) {
        if (!item.regExpr) callback(); // 校验通过

        if (this.isValidLengthNumber(item.regExpr, value)) {
          callback(); // 校验通过
        } else {
          callback(
            new Error(
              this.$t('common.rangeNumber', { rangeNumber: item.regExpr.replace('|', ' - ') }),
            ),
          );
        }
      },
      // 检验是否在两个值的范围内
      isRange(type, str, value) {
        const { leftValue, rightValue } = this.splitStr(str);
        if (type == 'number') {
          // 获取 1|100 的两个值出来,是否在这个范围内
          return Number(leftValue) <= Number(value) && Number(value) <= Number(rightValue);
        } else if (type == 'length') {
          // 获取 1|100 的两个值出来,值的长度是否在这个范围内
          return Number(leftValue) <= value.length && value.length <= Number(rightValue);
        }
      },
      // 分割字符串 例如：1|100
      splitStr(str) {
        // 使用 split 方法根据 '|' 分割字符串
        const parts = str.split('|');

        // 获取左右两边的值
        const leftValue = parts[0]; // '1'
        const rightValue = parts[1]; // '100'
        return {
          leftValue,
          rightValue,
        };
      },
      // 判断配置的前后数字长度是否一致，输入格式：000-999、000-9999
      // 1、如果一致，则固定位数且在这个范围内的
      // 2、如果不一致，则表示3-4位，在这个范围里面的
      isValidLengthNumber(str, value) {
        try {
          let { leftValue, rightValue } = this.splitStr(str);

          // 校验是否是 n 位数字
          let lengthDigits = false;

          if (leftValue.length === rightValue.length) {
            lengthDigits = new RegExp(`^\\d{${leftValue.length}}$`).test(value);
          } else {
            lengthDigits = new RegExp(`^\\d{${leftValue.length},${rightValue.length}}$`).test(
              value,
            );
          }

          if (!lengthDigits) return false;

          // 将字符串转换为数字，并判断范围
          const num = parseInt(value, 10);
          return num >= Number(leftValue) && num <= Number(rightValue);
        } catch (err) {
          console.log('isValidLengthNumber', err);
          return true;
        }
      },
      // 关闭
      cancel() {
        this.$emit('cancel');
      },
      // 删除符号（.）
      deleteDot(value) {
        if (typeof value === 'string' && value.endsWith('.')) {
          value = value.slice(0, -1); // 去除最后一位
        }
        return value;
      },
      // 确认
      confirm() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            const interfaceElementList = this.interfaceElementList.map(item => {
              Object.keys(this.form).forEach(iitem => {
                if (item.elementCode == iitem) {
                  let value = this.form[iitem];
                  // 如果 this.form[iitem] 是字符串，并且最后一位是 '.', 则去除
                  value = this.deleteDot(value);

                  item[item.elementCode] = value;
                }
              });
              return item;
            });
            this.$emit('confirm', this.record, interfaceElementList);
          } else {
            console.log('校验不通过', valid);
          }
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .attributeEdit {
    display: flex;
    flex-direction: column;
    .form {
      padding: 0 20px 20px;
      min-height: 200px;
      flex: 1;
      max-height: 600px;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    .flex-right {
      display: flex;
      justify-content: right;
      margin: 10px 0;
    }
  }
</style>
