import that from '@/main.js';
import {
  TAG_KEY_PRICING,
  TAG_KEY_VAS,
  TAG_KEY_RENT_FEE,
  TAG_KEY_OTC_FEE,
  TAG_KEY_REBATE_FEE,
  TAG_KEY_REDEEMED_POINTS,
  TAG_KEY_CONTRACT_PERIOD,
} from './sign';
export default {
  columns: [
    {
      scopedSlots: { title: 'customTitle_productName', customRender: 'name' },
      ellipsis: true,
      width: 400,
    },
    {
      title: that.$t('customerVerify.type'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: that.$t('customerVerify.Charge'),
      scopedSlots: { customRender: 'charge' },
      width: 200,
    },
    {
      title: that.$t('customerVerify.Period'),
      scopedSlots: { customRender: 'period' },
      width: 200,
    },
    {
      title: that.$t('customerVerify.Action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 100,
      className: 'action-column',
    },
  ],
  tabList: [
    {
      value: '1',
      title: that.$t('customerVerify.MainProduct'),
      component: 'MainProduct',
      comp: 'MainProduct',
      count: 0,
    },
    {
      value: '2',
      title: that.$t('customerVerify.AdditionalProduct'),
      component: 'AdditionalProduct',
      // productTypeCode: '300011', // 修改做法：300013和300010 都不是的，就是附加产品
      comp: 'AdditionalProduct',
      count: 0,
    },
    {
      value: '3',
      title: that.$t('customerVerify.Equipment'),
      component: 'EquipmentProduct',
      productTypeCode: '300010', // 用于区分类型
      comp: 'AdditionalProduct',
      count: 0,
    },
    {
      value: '4',
      title: that.$t('customerVerify.Premium'),
      component: 'PremiumProduct',
      productTypeCode: '300013', // 用于区分类型
      comp: 'AdditionalProduct',
      count: 0,
    },
  ],
  // 展示费用信息
  // interfaceElementList 字段是编辑确认后，存储下来的属性数据
  // elementTypeList 里面的两项分别代表Pricing（资费）和服务（Vas）
  // typeList 里面的三个字段是根据attributeList的ATTR_CODE字段 或者 interfaceElementList里的elementCode字段进行匹配出来展示
  // attributeList MACD业务初始化查询已订购的产品数据的时候，默认展示attributeList属性数据。编辑后再展示interfaceElementList属性数据
  getCharge: record => {
    const elementTypeList = [TAG_KEY_PRICING, TAG_KEY_VAS];
    const typeList = [TAG_KEY_RENT_FEE, TAG_KEY_OTC_FEE, TAG_KEY_REBATE_FEE];
    let str = '';
    if (elementTypeList.includes(record.type)) {
      if (record.interfaceElementList) {
        typeList.forEach(item => {
          const obj = record.interfaceElementList.find(iitem => iitem.elementCode === item);
          if (obj) {
            let value = obj[obj.elementCode];
            let rebateFeeSymbol = obj.elementCode == TAG_KEY_REBATE_FEE && value != '0' ? '-' : '';
            str = value ? `${obj.intfElementLabel} : ${rebateFeeSymbol} $ ${value}` : ''; // 输出：MRC:$189.8 、 Rebate:-$100
          }
        });
      } else if (record.attributeList) {
        typeList.forEach(item => {
          const obj = record.attributeList.find(
            iitem => iitem.CRM_ATTR_CODE === item || iitem.ATTR_CODE === item,
          );
          const intfElementLabelObj = {
            rent_fee: 'MRC',
            otc_fee: 'OTC',
            rebate_fee: 'Rebate',
          };
          if (obj) {
            let code = obj.CRM_ATTR_CODE || obj.ATTR_CODE;
            let value = obj.CRM_ATTR_VALUE || obj.ATTR_VALUE;
            let rebateFeeSymbol = code == TAG_KEY_REBATE_FEE && value != '0' ? '-' : '';
            str = value ? `${intfElementLabelObj[code]} : ${rebateFeeSymbol} $ ${value}` : ''; // 输出：MRC:$189.8 、 Rebate:-$100
          }
        });
      } else {
        str = '';
      }
    }
    return str;
  },
  getStandardMRC: record => {
    const elementTypeList = [TAG_KEY_PRICING, TAG_KEY_VAS];
    if (elementTypeList.includes(record.type)) {
      console.log(record);
      if (record.interfaceElementList) {
        var rent_fee = record.interfaceElementList.find(
          f => f.elementCode == TAG_KEY_RENT_FEE,
        )?.intfElementInitValue;
        return rent_fee ? 'MRC:$' + rent_fee : '';
      }
    }
  },
  // 展示合约期
  // 展示合约期（逻辑同上）
  getPeriod: record => {
    const elementTypeList = [TAG_KEY_PRICING, TAG_KEY_VAS];
    if (elementTypeList.includes(record.type)) {
      if (record.interfaceElementList) {
        let obj = {};
        record.interfaceElementList.forEach(item => {
          if (item.elementCode === TAG_KEY_CONTRACT_PERIOD) {
            obj = item.selectInitialDataList.find(
              iitem => iitem.enumFieldCode === item[item.elementCode],
            );
          }
        });
        return obj?.enumFieldName || '';
      } else if (record.attributeList) {
        const obj = record.attributeList.find(
          iitem =>
            iitem.CRM_ATTR_CODE === TAG_KEY_CONTRACT_PERIOD ||
            iitem.ATTR_CODE === TAG_KEY_CONTRACT_PERIOD,
        );
        if (obj) {
          let value = obj.CRM_ATTR_VALUE || obj.ATTR_VALUE;
          if (value) {
            return value == 'FTG' ? 'FTG' : `${value} months`; // 输出：`12 months`
          }
          return '';
        }
      } else {
        return '';
      }
    }
  },
  // 展示积分
  getRedeemedPoints(record) {
    const elementTypeList = [TAG_KEY_PRICING, TAG_KEY_VAS];
    if (elementTypeList.includes(record.type)) {
      if (record.interfaceElementList) {
        // #TODO: 这里的逻辑需要确认，是否是直接取redeemed_points字段
        var redeemed_points = record.interfaceElementList.find(
          f => f.elementCode == TAG_KEY_REDEEMED_POINTS,
        )?.intfElementInitValue;
        return redeemed_points ? `${redeemed_points}Pt` : '';
      }
    }
  },
  defaultFunctions: {
    isalnum: value => /^[a-zA-Z0-9]+$/.test(value),
    isalpha: value => /^[a-zA-Z]+$/.test(value),
    isdigit: value => /^\d+$/.test(value),
    islower: value => /^[a-z]+$/.test(value),
    isupper: value => /^[A-Z]+$/.test(value),
    isinteger: value => Number.isInteger(Number(value)),
    isdouble: value => !isNaN(parseFloat(value)),
    ispinteger: value => Number.isInteger(Number(value)) && Number(value) > 0,
    isemail: value => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    thelength: (value, length) => value.length === Number(length),
    minlength: (value, length) => value.length >= Number(length),
    maxlength: (value, length) => value.length <= Number(length),
    maxvalue: (value, max) => Number(value) <= Number(max),
    minvalue: (value, min) => Number(value) >= Number(min),
    isipaddr: value => /^(?:\d{1,3}\.){3}\d{1,3}$/.test(value),
    isTwoDecimal: value => /^\d+(\.\d{1,2})?$/.test(value),
  },
  generateAlphabetList: () => {
    const alphabetList = [];
    for (let i = 65; i <= 90; i++) {
      alphabetList.push({
        name: String.fromCharCode(i),
        isActive: false,
      });
    }
    return alphabetList;
  },
};
