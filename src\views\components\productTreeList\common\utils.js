import that from '@/main.js';
// 输出类似 "0042" 或 "9876" 四位数字
export const generateFourDigitNumber = function (length = 4) {
  const num = Math.floor(Math.random() * 10000); // 生成 0 到 9999 的随机数
  return num.toString().padStart(length, '0'); // 补零到四位
};

// Rebate输入的值不能大于MRC输入的值
export const rebateMappingMrc = function (list) {
  try {
    list.forEach(item => {
      item.children.forEach(iitem => {
        iitem.children.forEach(iiitem => {
          (iiitem.interfaceElementList || []).forEach(iiiitem => {
            // 判断哪个是Rebate
            if (iiiitem.elementCode == 'rebate_fee') {
              // 找到Rebate对应的MRC（对应的关联关系）
              const obj = (iiitem.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
              if (obj) {
                // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
                const element = iitem?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
                if (element) {
                  // 找到对应的MRC的值是多少
                  const mrcObj = (element?.interfaceElementList || []).find(
                    x => x.elementCode == 'rent_fee',
                  );
                  if (mrcObj) {
                    // 将Rebate和MRC进行比较
                    if (Number(iiiitem[iiiitem.elementCode]) > Number(mrcObj[mrcObj.elementCode])) {
                      throw that.$t('macd.rebateFeeMoreThanMrcFee', {
                        rebateElement: iiitem.DISCNT_NAME,
                        mrcElement: element.DISCNT_NAME,
                      });
                    }
                  }
                }
              }
            }
          });
        });
      });
    });
  } catch (err) {
    return err;
  }
};

// Rebate输入的值不能大于MRC输入的值
export const rebateValidate = function (list) {
  try {
    list.forEach(item => {
      item.children.forEach(iitem => {
        iitem.children.forEach(iiitem => {
          (iiitem.interfaceElementList || []).forEach(iiiitem => {
            // 判断哪个是Rebate
            if (iiiitem.elementCode == 'contract_period') {
              // 找到Rebate对应的MRC（对应的关联关系）
              const obj = (iiitem.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
              if (obj) {
                // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
                const element = iitem?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
                if (element) {
                  // 找到对应的MRC的值是多少
                  const mrcObj = (element?.interfaceElementList || []).find(
                    x => x.elementCode == 'contract_period',
                  );
                  if (mrcObj) {
                    // 将Rebate和MRC进行比较
                    if (iiiitem[iiiitem.elementCode] !== mrcObj[mrcObj.elementCode]) {
                      throw that.$t('macd.rebatePeriodEqualMrcPeriod', {
                        rebateElement: iiitem.DISCNT_NAME,
                        mrcElement: element.DISCNT_NAME,
                      });
                    }
                  }
                }
              }
            }
          });
        });
      });
    });
  } catch (err) {
    return err;
  }
};

// 修改rebate中的合约期的值要和mrc的合约值相等
export const updateRebatePeriod = function (list, record, productKey) {
  try {
    return list.map(item => {
      if (item.key !== productKey) return item;

      return {
        ...item,
        children: item.children.map(iitem => ({
          ...iitem,
          children: iitem.children.map(iiitem => ({
            ...iiitem,
            interfaceElementList: (iiitem.interfaceElementList || []).map(iiiitem => {
              // 判断哪个是Rebate
              if (iiiitem.elementCode !== 'contract_period') return iiiitem;

              // 找到Rebate对应的MRC（对应的关联关系）
              const obj = (iiitem.DISCNT_ITEM || []).find(
                x => x.ATTR_CODE === 'rebate_mapping_mrc',
              );

              if (
                !obj ||
                !(obj.DISCNT_CODE === record.DISCNT_CODE || obj.SERVICE_ID === record.SERVICE_ID)
              ) {
                return iiiitem;
              }

              // 找到Rebate对应关联关系的MRC的元素
              const element = iitem?.children.find(x => x.DISCNT_CODE === obj.ATTR_VALUE);
              if (!element) return iiiitem;

              // 找到对应的MRC的值
              const mrcObj = (element?.interfaceElementList || []).find(
                x => x.elementCode === 'contract_period',
              );
              if (!mrcObj) return iiiitem;

              // 更新 Rebate 的值为 MRC 的值
              return {
                ...iiiitem,
                [iiiitem.elementCode]: mrcObj[mrcObj.elementCode],
              };
            }),
          })),
        })),
      };
    });
  } catch (err) {
    return err;
  }
};

// 修改rebate中的合约期的值要和mrc的合约值相等
// export const updateRebatePeriod = function (list, record, productKey) {
//   try {
//     list.forEach(item => {
//       if (item.key === productKey) {
//         item.children.forEach(iitem => {
//           iitem.children.forEach(iiitem => {
//             (iiitem.interfaceElementList || []).forEach(iiiitem => {
//               // 判断哪个是Rebate
//               if (iiiitem.elementCode == 'contract_period') {
//                 // 找到Rebate对应的MRC（对应的关联关系）
//                 const obj = (iiitem.DISCNT_ITEM || []).find(
//                   x => x.ATTR_CODE == 'rebate_mapping_mrc',
//                 );
//                 if (
//                   obj &&
//                   (obj.DISCNT_CODE === record.DISCNT_CODE || obj.SERVICE_ID === record.SERVICE_ID)
//                 ) {
//                   // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
//                   const element = iitem?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
//                   if (element) {
//                     // 找到对应的MRC的值是多少
//                     const mrcObj = (element?.interfaceElementList || []).find(
//                       x => x.elementCode == 'contract_period',
//                     );
//                     if (mrcObj) {
//                       // 将Rebate和MRC进行比较
//                       if (iiiitem[iiiitem.elementCode] !== mrcObj[mrcObj.elementCode]) {
//                         iiiitem[iiiitem.elementCode] = mrcObj[mrcObj.elementCode];
//                         that.$set(iiiitem, iiiitem.elementCode, mrcObj[mrcObj.elementCode]);
//                       }
//                     }
//                   }
//                 }
//               }
//             });
//           });
//         });
//       }
//     });
//     console.log('222', list);
//   } catch (err) {
//     return err;
//   }
// };

/**
 * 判断不同包下是否选中同一元素
 * @param {Array} selectedList 原始数据
 * @returns NULL | String 返回错误元素名称或者NULL
 */
export const checkDuplicateOrder = selectedList => {
  const elementMap = new Map();
  // 递归检查所有子项
  const checkChildren = items => {
    for (const item of items) {
      if (item.checked) {
        // 检查元素级别
        if (['Vas', 'Pricing'].includes(item.type)) {
          const elementKey = item.DISCNT_CODE || item.SERVICE_ID;
          if (elementKey) {
            if (elementMap.has(elementKey)) {
              // 找到重复项
              return item.NAME;
            }
            elementMap.set(elementKey, item);
          }
        }

        // 递归检查子项
        if (item.children && item.children.length > 0) {
          const result = checkChildren(item.children);
          if (result) return result;
        }
      }
    }
    return null;
  };

  for (const product of selectedList) {
    if (product.type === 'Product') {
      const error = checkChildren([product]);
      if (error) return error;
    }
  }
  return null;
};

/**
 * 判断是否为MRC或Rebate
 * @param {Object} record - 当前记录
 * @returns {boolean} - 是否为MRC或Rebate
 */
const isRebateOrMrc = function (record) {
  return record?.DISCNT_ITEM?.some(
    item =>
      item.ATTR_CODE === 'price_type' &&
      (item.ATTR_VALUE === 'Rebate' || item.ATTR_VALUE === 'MRC'),
    false,
  );
};

/**
 * 检查是否为相同的MRC或Rebate元素
 * @param {Object} item1 - 第一个元素
 * @param {Object} item2 - 第二个元素
 * @returns {boolean} - 是否为相同元素
 */
const isSameMrcOrRebate = (item1, item2) => {
  return (
    (item1.DISCNT_CODE && item1.DISCNT_CODE === item2.DISCNT_CODE) ||
    (item1.SERVICE_ID && item1.SERVICE_ID === item2.SERVICE_ID)
  );
};

/**
 * 获取元素所属的包
 * @param {Array} items - 产品树数据
 * @param {Object} target - 目标元素
 * @returns {Object|null} - 所属的包
 */
const findPackage = (items, target) => {
  for (const item of items) {
    if (item.children) {
      for (const pkg of item.children) {
        if (pkg.children && pkg.children.some(child => isSameMrcOrRebate(child, target))) {
          return pkg;
        }
      }
    }
  }
  return null;
};

/**
 * 查找所有相同的MRC/Rebate元素
 * @param {Array} items - 产品树数据
 * @param {Object} target - 目标元素
 * @returns {Array} - 相同元素列表
 */
const findSameElements = (items, target) => {
  const result = [];
  const searchItems = list => {
    for (const item of list) {
      if (isSameMrcOrRebate(item, target)) {
        result.push(item);
      }
      if (item.children) {
        searchItems(item.children);
      }
    }
  };
  searchItems(items);
  return result;
};

/**
 * @des: 递归检查每个选中的项的依赖关系
 * @param items  选中的产品数据
 * @param checkedData 过滤出来选中的数据
 * @return 弹窗Modal或NULL
 */
export const checkDependencies = (items, checkedData) => {
  for (const item of items) {
    // 检查完全依赖
    if (item.checked && item.FULL_RELY_RECORD && item.FULL_RELY_RECORD.length > 0) {
      // 获取所有依赖项ID
      const allDependencyIds = item.FULL_RELY_RECORD.map(rely => rely.ID);

      // 过滤出属于当前产品的依赖项ID
      const dependencyIds = allDependencyIds.filter(id => isIdExistInProduct(items, id));

      // 如果过滤后没有依赖项，说明所有依赖都不属于当前产品，跳过检查
      if (dependencyIds.length === 0) continue;

      // 如果是MRC或Rebate元素
      if (isRebateOrMrc(item)) {
        // 找到所有相同的MRC/Rebate元素
        const sameElements = findSameElements(items, item);
        // 找到当前元素所在的包
        const currentPackage = findPackage(items, item);

        // 如果存在相同元素且当前元素在某个包内
        if (sameElements.length > 1 && currentPackage) {
          // 获取当前包内的依赖项
          const currentPackageDependencies = dependencyIds.filter(id => {
            const dep = findItemById(items, id);
            return dep && findPackage(items, dep) === currentPackage;
          });

          // 检查当前包内的依赖项是否都被选中
          const packageDependenciesChecked = currentPackageDependencies.every(id =>
            findCheckedDependency(checkedData, [id]),
          );

          // 如果当前包内的依赖未满足，返回错误提示
          if (!packageDependenciesChecked) {
            const packageDependencyNames = item.FULL_RELY_RECORD.filter(rely =>
              currentPackageDependencies.includes(rely.ID),
            ).map(rely => rely.NAME);

            return that.$t('common.validateDependenceAllTip', {
              name: packageDependencyNames.join(', '),
              currentName: item.NAME,
            });
          }

          // 如果当前包内依赖都满足了，跳过其他包中相同元素的检查
          continue;
        }
      }

      // 对于非MRC/Rebate元素或没有相同元素的情况，执行常规依赖检查
      const dependencyNames = item.FULL_RELY_RECORD.filter(rely =>
        dependencyIds.includes(rely.ID),
      ).map(rely => rely.NAME);

      const allDependenciesChecked = dependencyIds.every(id =>
        findCheckedDependency(checkedData, [id]),
      );

      if (!allDependenciesChecked) {
        return that.$t('common.validateDependenceAllTip', {
          name: dependencyNames.join(', '),
          currentName: item.NAME,
        });
      }
    }

    // 递归检查子项
    if (item.children && item.children.length > 0) {
      const childError = checkDependencies(item.children, checkedData);
      if (childError) return childError;
    }
  }
  return null;
};

/**
 * 根据ID查找元素
 * @param {Array} items - 产品树数据
 * @param {string} id - 目标ID
 * @returns {Object|null} - 找到的元素
 */
const findItemById = (items, id) => {
  for (const item of items) {
    if (item.PACKAGE_ID === id || item.DISCNT_CODE === id || item.SERVICE_ID === id) {
      return item;
    }
    if (item.children) {
      const found = findItemById(item.children, id);
      if (found) return found;
    }
  }
  return null;
};

/**
 * @des: 校验互斥关系
 * @param checkedData 过滤出来的选中数据
 * @return 返回互斥对象
 */
export const validateMutexData = checkedData => {
  // 递归检查每个选中项的互斥关系
  const checkMutex = items => {
    for (const item of items) {
      // 只检查选中的项
      if (item.checked && item.MUTEX_RECORD && item.MUTEX_RECORD.length > 0) {
        // 遍历互斥记录
        const mutexFound = [];
        for (const mutexItem of item.MUTEX_RECORD) {
          // 在已选中的数据中查找互斥项
          const findMutexItem = data => {
            for (const checkedItem of data) {
              // 检查当前项
              if (checkedItem.checked) {
                const checkedId =
                  checkedItem.type === 'Package'
                    ? checkedItem.PACKAGE_ID
                    : checkedItem.DISCNT_CODE || checkedItem.SERVICE_ID;

                if (checkedId === mutexItem.ID && checkedItem.checked) {
                  return checkedItem;
                }
              }

              // 递归检查子项
              if (checkedItem.children && checkedItem.children.length > 0) {
                const found = findMutexItem(checkedItem.children);
                if (found) return found;
              }
            }
            return null;
          };

          // 在当前项的数据中查找互斥项，而不是整个checkedData
          const foundItem = findMutexItem(items);
          if (foundItem) {
            mutexFound.push(foundItem.NAME);
          }
        }

        if (mutexFound.length > 0) {
          return {
            current: item.NAME,
            mutex: mutexFound.join(', '),
          };
        }
      }

      // 递归检查子项
      if (item.children && item.children.length > 0) {
        const childError = checkMutex(item.children);
        if (childError) return childError;
      }
    }
    return null;
  };

  // 按产品级别分别检查互斥关系
  for (const product of checkedData) {
    if (product.type === 'Product') {
      const error = checkMutex([product]);
      if (error) return error;
    }
  }
  return null;
};

/**
 * @des: 判断ID是否存在于产品数据中
 * @param productData 产品数据
 * @param id 需要查找的ID
 * @return 布尔值
 */
export const isIdExistInProduct = (productData, id) => {
  let exists = false;
  const checkItem = items => {
    for (const item of items) {
      if (item.PACKAGE_ID === id || item.DISCNT_CODE === id || item.SERVICE_ID === id) {
        exists = true;
        return;
      }
      if (item.children) {
        checkItem(item.children);
        if (exists) return;
      }
    }
  };
  checkItem(productData);
  return exists;
};

/**
 * @des: 在已选中的数据中查找依赖项
 * @param items  选中的数据
 * @param ids 依赖项的ID数组
 * @return 布尔值
 */
export const findCheckedDependency = (items, ids) => {
  let found = false;
  const searchItems = list => {
    for (const listItem of list) {
      if (
        (listItem.PACKAGE_ID && ids.includes(listItem.PACKAGE_ID)) ||
        (listItem.DISCNT_CODE && ids.includes(listItem.DISCNT_CODE)) ||
        (listItem.SERVICE_ID && ids.includes(listItem.SERVICE_ID))
      ) {
        if (listItem.checked) {
          found = true;
          return;
        }
      }
      if (listItem.children) {
        searchItems(listItem.children);
      }
    }
  };
  searchItems(items);
  return found;
};

/**
 * 过滤出 checked 为 true 的数据
 * @param {Array} data 原始数据
 * @returns {Array} 过滤后的数据
 */
export const filterCheckedData = function (data) {
  return data
    .filter(item => item.checked) // 过滤出 checked 为 true 的节点
    .map(item => {
      // 如果节点有 children，递归处理
      if (item.children && item.children.length > 0) {
        return {
          ...item, // 保留节点的其他属性
          children: filterCheckedData(item.children), // 递归过滤 children
        };
      }
      return item; // 如果没有 children，直接返回节点
    });
};

// 主产品，产品树状结构，将勾选的筛选出来，过滤掉未勾选的
// 1、父级勾选了，但是子级一个都没勾选，过滤掉
// 2、子级勾选了，但是父级没有勾选，过滤掉
export const getMainProductTreeSelectedList = function (treeList) {
  return JSON.parse(JSON.stringify(treeList)).filter(item => {
    // 如果当前项没有item.FORCE_TAG === '1' 或者 没有 checked=true，直接返回 false
    if (!(item.FORCE_TAG === '1' || item.checked)) return false;

    // 如果有children，递归过滤children
    if (item.children) {
      item.children = getMainProductTreeSelectedList(item.children);
      // 如果过滤后没有children，返回 false
      if (item.children.length === 0) return false;
    }

    // 返回符合条件的节点
    return true;
  });
};

/**
 * 校验整个 productData 数组
 * @param {Array} productData 产品数据数组
 * @returns {boolean} 是否所有数据都校验通过
 */
export const validateProductData = function (productData) {
  try {
    // 遍历每一条数据
    for (const product of productData) {
      validateSelection(product);
    }
  } catch (err) {
    return err;
  }
};

// 属性需要编辑确认
export const attributeMustEditConfim = function (list) {
  try {
    list.forEach(item => {
      item.children.forEach(iitem => {
        iitem.children.forEach(iiitem => {
          // 元素勾选上的 且 有属性的
          if (iiitem.checked && iiitem.HAS_ATTR == '1') {
            // 属性必填但是值为空的
            let isRequiredBool = (iiitem?.interfaceElementList || []).some(
              x => x.intfElementCanNull == '1' && !x[x.elementCode],
            );
            if (isRequiredBool) {
              // 提示需要编辑确认
              throw that.$t('customerVerify.attributeMustEditConfim', {
                productName: item.NAME,
                packageName: iitem.NAME,
                elementName: iiitem.NAME,
              });
            }
          }
        });
      });
    });
  } catch (err) {
    return err;
  }
};

/**
 * 校验当前层级及其子层级的勾选数量是否符合 MIN_NUMBER 和 MAX_NUMBER 的限制
 * @param {Object} node 当前节点
 * @returns {boolean} 是否校验通过
 */
function validateSelection(node) {
  // 如果当前节点没有 children，直接返回 true
  // if (!node.children || node.children.length === 0) {
  //   return true;
  // }
  if (!node.children) {
    node.children = [];
  }

  // 获取 MIN_NUMBER 和 MAX_NUMBER
  const min = parseInt(node.MIN_NUMBER, 10);
  const max = parseInt(node.MAX_NUMBER, 10);

  // 如果 MIN_NUMBER 或 MAX_NUMBER 为 -1，表示没有限制
  const hasLimit = min !== -1 || max !== -1;

  if (hasLimit) {
    // 计算当前层级已勾选的项数
    const selectedCount = node.children.filter(child => child.checked).length;

    // 校验选择的项数是否符合 MIN_NUMBER 和 MAX_NUMBER 的限制
    if (min !== -1 && selectedCount < min) {
      throw that.$t('customerVerify.cannotLessThan', {
        name: node.NAME,
        num: min,
        selectedCount: selectedCount,
      });
      // `${node.NAME} 至少需要选择 ${min} 项，当前选择了 ${selectedCount} 项。`;
    }
    if (max !== -1 && selectedCount > max) {
      throw that.$t('customerVerify.cannotMoreThan', {
        name: node.NAME,
        num: max,
        selectedCount: selectedCount,
      });
      // `${node.NAME} 最多只能选择 ${max} 项，当前选择了 ${selectedCount} 项。`;
    }
  }

  // 递归校验每个子节点
  for (const child of node.children) {
    validateSelection(child);
  }
}

// 处理产品数据(密码)
export const dealProductData_elementAttr_PSWD = function (
  productInfo,
  item,
  currentSelectData,
  field,
) {
  let list = JSON.parse(JSON.stringify(productInfo[field])) || [];
  if (field === 'MainProduct') {
    return deal_elementAttr_PSWD(list, item.value, currentSelectData);
  } else if (field === 'AdditionalProduct') {
    let arr = list.filter(item => item.checked) || [];
    return deal_elementAttr_PSWD(arr, item.value, currentSelectData);
  }
};

// 处理属性数据(密码)
export const deal_elementAttr_PSWD = function (list, value, currentSelectData) {
  return list.map(item => {
    return {
      ...item,
      children: item.children.map(iitem => {
        return {
          ...iitem,
          children: iitem.children.map(iiitem => {
            if (iiitem.interfaceElementList?.length) {
              iiitem.interfaceElementList = iiitem.interfaceElementList.map(x => {
                // 当前号码设置的产品的PSWD不变，只修改其他号码的PSWD
                if (x.elementCode == 'PSWD' && value != currentSelectData.value) {
                  x[x.elementCode] = generateFourDigitNumber();
                }
                return x;
              });
            }
            return iiitem;
          }),
        };
      }),
    };
  });
};

// 处理单个产品类型（MainProduct或AdditionalProduct）的函数
const processProductType = function (products) {
  return products.map(item => ({
    ...item,
    children: (item?.children || []).map(iitem => ({
      ...iitem,
      children: (iitem?.children || []).map(iiitem => {
        const hasHuntingPilot = iiitem?.SERV_ITEM?.some(
          servItem => servItem.ATTR_CODE === 'hunting_pilot' && servItem.ATTR_VALUE === '1',
        );
        return {
          ...iiitem,
          checked: hasHuntingPilot && iiitem.checked ? false : iiitem.checked,
        };
      }),
    })),
  }));
};

// //复制选中商品数据到全部号码时判断选中元素是否符合要求，如果不符合则将选中状态改为false
export const dealSelectNumberProduct = function (list) {
  return list.map(item => {
    if (!item.isPilot) {
      // 分别处理MainProduct和AdditionalProduct
      if (item.MainProduct) {
        item.MainProduct = processProductType(item.MainProduct);
      }
      if (item.AdditionalProduct) {
        item.AdditionalProduct = processProductType(item.AdditionalProduct);
      }
    }
    return item;
  });
};

// MRC的值发生修改了，则不能勾选rebate
export const mrcUpdateNotChooseRebate = function (list) {
  try {
    list.forEach(item => {
      item.children.forEach(iitem => {
        iitem.children.forEach(iiitem => {
          (iiitem.interfaceElementList || []).forEach(iiiitem => {
            // 判断哪个是Rebate
            if (iiiitem.elementCode == 'rebate_fee') {
              // 找到Rebate对应的MRC（对应的关联关系）
              const obj = (iiitem.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
              if (obj) {
                // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
                const element = iitem?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
                if (element) {
                  // MRC元素是否勾选
                  if (element.checked) {
                    // 找到对应的MRC的值是多少
                    const mrcObj = (element?.interfaceElementList || []).find(
                      x => x.elementCode == 'rent_fee',
                    );
                    if (mrcObj) {
                      // 对比mrc的值是否修改过
                      if (mrcObj[mrcObj.elementCode] != mrcObj.intfElementInitValue) {
                        throw that.$t('macd.mrcValueUpdateNotChooseRebateNext', {
                          mrcName: element.NAME,
                          rebateName: iiitem.NAME,
                        });
                      }
                    }
                  } else {
                    throw that.$t('macd.needToChooseMrc', {
                      name: element.NAME,
                    });
                  }
                } else {
                  throw that.$t('macd.needToChooseMrc', {
                    name: obj.ATTR_VALUE || '',
                  });
                }
              }
            }
          });
        });
      });
    });
  } catch (err) {
    return err;
  }
};

//非主号选择特殊元素的时候，提示元素只能被主号订购
export const onlyBeSelectedByMainNumber = function (list) {
  try {
    list.forEach(item => {
      item.children.forEach(iitem => {
        iitem.children.forEach(iiitem => {
          // 检查 SERV_ITEM 数组
          if (iiitem.SERV_ITEM && Array.isArray(iiitem.SERV_ITEM)) {
            const hasHuntingPilot = iiitem.SERV_ITEM.some(
              servItem => servItem.ATTR_CODE === 'hunting_pilot' && servItem.ATTR_VALUE === '1',
            );
            if (hasHuntingPilot) {
              throw that.$t('common.cannotSelectNext', {
                name: iiitem.DISCNT_NAME || iiitem.SERVICE_NAME,
              });
            }
          }
        });
      });
    });
  } catch (e) {
    return e;
  }
};

// 元素排序 升序
export const elementSort = function (list) {
  try {
    return list.sort((a, b) => {
      return Number(a.ITEM_INDEX) - Number(b.ITEM_INDEX);
    });
  } catch (err) {
    return list;
  }
};

// 处理主产品或者附加产品元素的mrc值更改为默认值
export const processProducts = products => {
  return products.map(product => {
    const newProduct = { ...product };
    // 处理产品本身
    if (
      (newProduct.type === 'Pricing' || newProduct.type === 'Vas') &&
      newProduct.interfaceElementList
    ) {
      newProduct.interfaceElementList = newProduct.interfaceElementList.map(element => {
        if (
          element.elementCode === 'rent_fee' &&
          element.intfElementInitValue !== element.rent_fee
        ) {
          return { ...element, rent_fee: element.intfElementInitValue };
        }
        return { ...element };
      });
    }

    // 递归处理子产品
    if (newProduct.children && newProduct.children.length > 0) {
      newProduct.children = processProducts(newProduct.children);
    }
    return newProduct;
  });
};

//查找产品树某一项的父级
export const findParent = (data, record) => {
  for (const item of data) {
    if (item.children && item.children.some(child => child.key === record.key)) {
      return item;
    }
    if (item.children && item.children.length > 0) {
      const found = findParent(item.children, record);
      if (found) {
        return found;
      }
    }
  }
  return null;
};
