<template>
  <div class="productTreeList">
    <a-spin :spinning="spinning">
      <a-table
        :columns="getTableColumns"
        :data-source="productTreeListFilter()"
        :customRow="customRow"
        :expandedRowKeys="expandedRowKeys"
        :rowKey="(record, index) => `${record.key}`"
        class="productTreeTable"
        :pagination="false"
        :scroll="{ x: 1000, y: 500 }"
      >
        <!-- 自定义标题 产品名称 -->
        <template slot="customTitle_productName">
          {{ $t('customerVerify.MainProduct') }}
        </template>
        <span slot="expandIcon" slot-scope="props" class="expand-icon">
          <img
            src="@/assets/images/up.svg"
            alt=""
            v-if="!props.expanded && props.record?.children"
            class="arrow"
          />
          <img
            src="@/assets/images/down.svg"
            alt=""
            v-if="props.expanded && props.record?.children"
            class="arrow"
          />
        </span>
        <span slot="name" slot-scope="text, record">
          <a-checkbox
            @click="checkboxChange(record, $event)"
            :checked="record.checked"
            :disabled="checkboxDisabled || getIsDisabled(record)"
            v-if="checkboxShow"
            :class="['checkbox', ['Pricing', 'Vas'].includes(record.type) ? 'checkboxRight' : '']"
          />
          {{ record.NAME }}
        </span>
        <span slot="standardMRC" slot-scope="text, record">
          {{ getStandardMRC(record) }}
        </span>
        <span slot="charge" slot-scope="text, record">
          {{ getCharge(record) }}
        </span>
        <span slot="period" slot-scope="text, record">
          {{ getPeriod(record) }}
        </span>

        <span v-if="!record.NAME.includes('CCP')" slot="action" slot-scope="text, record">
          <i
            v-if="record.HAS_ATTR == '1'"
            :class="['iconfont', iconfontStatusObj[getAttributeClass(record)]]"
            @click.stop="attributeEditOpen(record)"
          />
        </span>
        <!-- 先判断ccp_tag 是否为1 ，1显示编辑，0之后再判断HAS_ATTR是否为1 才显示编辑-->
        <span v-else slot="action" slot-scope="text, record">
          <span v-for="(item, index) in record.DISCNT_ITEM || []" :key="index">
            <span v-if="item.ATTR_CODE == 'ccp_tag'">
              <i
                v-if="item.ATTR_VALUE == '1' || (item.ATTR_VALUE == '0' && record.HAS_ATTR == '1')"
                :class="['iconfont', iconfontStatusObj[getAttributeClass(record)]]"
                @click.stop="attributeEditOpen(record)"
              />
            </span>
          </span>
        </span>
      </a-table>
    </a-spin>

    <!-- 属性编辑 弹窗-->
    <a-modal
      v-model="attributeEditVisible"
      :title="
        attributeEditDisabled
          ? $t('customerVerify.attributeDetail')
          : $t('customerVerify.attributeEdit')
      "
      width="420px"
      :maskClosable="false"
      :footer="null"
      @cancel="attributeEditCancel"
    >
      <AttributeEdit
        ref="AttributeEdit"
        @cancel="attributeEditCancel"
        @confirm="attributeEditConfirm"
        :attributeEditDisabled="attributeEditDisabled"
      />
    </a-modal>
    <!-- 税率弹窗 -->
    <a-modal
      v-model="taxRateExchangeDisabled"
      @cancel="taxRateExchangeCancel"
      :maskClosable="false"
      :footer="null"
    >
      <TaxRateExchange
        ref="TaxRateExchange"
        @confirm="taxRateExchangeConfirm"
        @cancel="taxRateExchangeCancel"
        :taxRateExchangeDisabled="taxRateExchangeDisabled"
        :recordData="recordData"
        :attributeEditDisabled="attributeEditDisabled"
      />
    </a-modal>
    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      closable
      @cancel="tipsVisible = false"
      @confirm="messageModalConfirm"
    />
  </div>
</template>

<script>
  // import { defprodandmutex, getElements } from '@/api/customerVerify';
  import MessageModal from '@/components/messageModal';
  import { mapState } from 'vuex';
  import AttributeEdit from './attributeEdit';
  import config from './common/config';
  import { elementSort, generateFourDigitNumber, updateRebatePeriod } from './common/utils';
  import TaxRateExchange from './taxRateExchange.vue';
  import { validateProductAllRules } from './validate';
  import { mixins } from '@/views/components/productTreeList/mixin';
  export default {
    name: 'MainProduct',
    components: {
      MessageModal,
      AttributeEdit,
      TaxRateExchange,
    },
    // 新装Citinet选号页面使用了provide
    inject: {
      elementCode: {
        from: 'elementCode', // 注入的 key
        default: '', // 默认值
      },
    },
    props: {
      // 过滤查询的产品名称
      filterProductName: {
        type: String,
        default: '',
      },
      // 主产品列表
      mainProductList: {
        type: Array,
        default: () => [],
      },
      // 展开的key值列表
      mainProductExpandedRowKeys: {
        type: Array,
        default: () => [],
      },
      // 多选框是否全部禁用
      mainProductCheckboxDisabled: {
        type: Boolean,
        default: false,
      },
      // 是否显示编辑按钮
      mainProductEditBtnShow: {
        type: Boolean,
        default: true,
      },
      // 是否显示多选框
      mainProductCheckboxShow: {
        type: Boolean,
        default: true,
      },
      // 属性编辑 表单内容是否全部禁用
      mainProductAttributeEditDisabled: {
        type: Boolean,
        default: false,
      },
      // 加载中样式
      mainProductSpinning: {
        type: Boolean,
        default: false,
      },
      // 编辑按钮是否做 修改过变颜色
      mainProductEditBtnChangeColorBool: {
        type: Boolean,
        default: false,
      },
      //是否显示Standard MRC表头---续约模块需要传true，默认false
      isMRCFee: {
        type: Boolean,
        default: false,
      },
      //是否为hunting中的主号标识，如果是则操作产品树的时候需要服务标签判断校验
      isHuntingInstallPilot: {
        type: Boolean,
        default: true,
      },
    },
    mixins: [mixins],
    data() {
      return {
        currentSelectedMainProduct: {},
        needCallBackFunBool: false,
        lastSelectedMainProduct: {},
        isMainProduct: true,
      };
    },
    watch: {
      // 过滤查询的产品名称
      filterProductName: {
        handler(newVal) {
          this.productName = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 主产品列表
      mainProductList: {
        handler(newVal) {
          if (newVal) {
            this.productTreeList = newVal;
          }
        },
        deep: true,
        immediate: true,
      },
      // 展开的key值列表
      mainProductExpandedRowKeys: {
        handler(newVal) {
          if (newVal) {
            this.expandedRowKeys = newVal;
          }
        },
        deep: true,
        immediate: true,
      },
      // 多选框是否全部禁用
      mainProductCheckboxDisabled: {
        handler(newVal) {
          this.checkboxDisabled = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 是否显示编辑按钮
      mainProductEditBtnShow: {
        handler(newVal) {
          this.editBtnShow = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 是否显示多选框
      mainProductCheckboxShow: {
        handler(newVal) {
          this.checkboxShow = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 属性编辑 表单内容是否全部禁用
      mainProductAttributeEditDisabled: {
        handler(newVal) {
          this.attributeEditDisabled = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 加载中样式
      mainProductSpinning: {
        handler(newVal) {
          this.spinning = newVal;
        },
        deep: true,
        immediate: true,
      },
      // 编辑按钮是否做 修改过变颜色
      mainProductEditBtnChangeColorBool: {
        handler(newVal) {
          this.editBtnChangeColorBool = newVal;
        },
        deep: true,
        immediate: true,
      },
      isMRCFee: {
        handler(newVal) {
          if (newVal) {
            // this.columns.splice(2, 0, {
            //   title: this.$t('customerVerify.standardMRC'),
            //   scopedSlots: { customRender: 'standardMRC' },
            //   key: 'standardMRC',
            //   dataIndex: 'standardMRC',
            //   width: 200,
            // });
          }
        },
        deep: true,
        immediate: true,
      },
    },
    methods: {
      // 主产品只能选一个,取消或者切换时 提示
      cancelOrChangeMainProduct(record) {
        const lastProductObj = this.productTreeList.find(item => item.checked);
        let currentProductId = record.key.split('-')[0];
        const productObj = this.productTreeList.find(item => item.ID == currentProductId);
        if (lastProductObj && lastProductObj?.ID != productObj.ID) {
          this.needCallBackFunBool = true;
          this.tipsMessage = this.$t('customerVerify.tipsText5');
          this.tipsVisible = true;
          // 存储当前选中的主产品
          this.lastSelectedMainProduct = lastProductObj;
          this.currentSelectedMainProduct = productObj;
          throw '拦截且提示';
        } else {
          this.lastSelectedMainProduct = productObj;
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .productTreeList {
    /deep/ .productTreeTable tbody tr:hover {
      cursor: pointer;
    }
    .arrow {
      width: 12px;
      height: 12px;
    }
    .checkbox {
      margin: 0 6px;
    }
    .checkboxRight {
      margin-left: 25px;
    }
    .isEdited {
      color: #333;
    }
  }
</style>
