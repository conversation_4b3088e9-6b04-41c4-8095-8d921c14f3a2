import config from '@/views/components/productTreeList/common/config';
import { mapState } from 'vuex';
import { validateProductAllRules } from '@/views/components/productTreeList/validate';
import { defprodandmutex, getElements } from '@/api/customerVerify';
import {
  elementSort,
  generateFourDigitNumber,
  updateRebatePeriod,
} from '@/views/components/productTreeList/common/utils';
import { debounce } from '@/utils/utils';
export const mixins = {
  data() {
    return {
      columns: config.columns,
      spinning: false,
      productTreeList: [],
      expandedRowKeys: [],
      tipsVisible: false,
      tipsMessage: '',
      currentSelectedMainProduct: {},
      attributeEditVisible: false,
      defaultExpandAllRows: false,
      checkboxDisabled: false,
      checkboxShow: false,
      editBtnShow: false,
      attributeEditDisabled: false,
      editBtnChangeColorBool: false,
      iconfontStatusObj: {
        'edit': 'icon-xiugai', // 编辑图标
        'edited': 'icon-xiugai isEdited', // 编辑过变灰色图标
        'detail': 'icon-xiangqingmingxi', // 详情图标
      },
      productName: '',
      taxRateExchangeDisabled: false,
      recordData: {
        edit: 0,
      },
    };
  },
  computed: {
    ...mapState('app', {
      userInfo: state => state.userInfo,
    }),
    // 根据isMRCFee动态计算表格列配置
    getTableColumns() {
      const cols = this.isMRCFee
        ? [
            ...this.columns.slice(0, 2),
            {
              title: this.$t('customerVerify.standardMRC'),
              scopedSlots: { customRender: 'standardMRC' },
              key: 'standardMRC',
              dataIndex: 'standardMRC',
              width: 200,
            },
            ...this.columns.slice(2),
          ]
        : this.columns;
      // 如果是礼品【300013】，period去掉
      if (this.productTypeCode == '300013') {
        const filterCol = cols.filter(x => x.title != this.$t('customerVerify.Period'));
        filterCol.splice(filterCol.length - 1, 0, {
          title: this.$t('comp.RedeemedPoints'),
          scopedSlots: { customRender: 'redeemedPoints' },
          width: 200,
        });
        return filterCol;
      }
      return cols;
    },
  },
  mounted() {
    // 使用防抖，确保只在最后一次变更后 500ms 触发
    const debouncedHandler = debounce(newVal => {
      console.log(newVal, '是否为最后的渲染');
      this.$nextTick(() => {
        this.watchNewestProductTree();
      });
    }, 500);
    this.unHandlerWatch = this.$watch(
      () => this.productTreeList, // 可以监听计算属性或函数返回值
      debouncedHandler,
      { deep: true },
    );
  },
  watch: {},
  methods: {
    // 监听最新 - productTreeList，执行对应所需的操作
    async watchNewestProductTree(extra = {}) {
      console.log('猜猜我触发了没');
      this.$emit('getCurrentSelectedData', { ...extra });
    },
    // 过滤产品列表
    productTreeListFilter() {
      let list = this.productTreeList.filter(x => {
        return x.NAME.includes(this.productName);
      });
      return list;
    },
    // 展示费用信息
    getCharge(record) {
      return config.getCharge(record);
    },
    getStandardMRC(record) {
      return config.getStandardMRC(record);
    },
    // 展示合约期
    getPeriod(record) {
      return config.getPeriod(record);
    },
    // 展示积分
    getRedeemedPoints(record) {
      return config.getRedeemedPoints(record);
    },
    // 点击整行
    customRow(record, rowIndex) {
      return {
        on: {
          click: () => {
            this.handleExpand(record);
          },
        },
      };
    },
    handleExpand(record) {
      console.log('点击整行', record);

      // 产品
      if (record.type == 'Product') {
        if (this.expandedRowKeys.includes(record.key)) {
          // 收起
          this.removeKey(record);
        } else {
          // 展开
          this.expandedRowKeys.push(record.key);
          // 只有当type=Product并且没有children数据才去请求数据
          if (record.children.length === 0) {
            this.getChildrenList(record);
          }
        }
      }

      // 包
      if (record.type == 'Package') {
        if (this.expandedRowKeys.includes(record.key)) {
          // 收起
          this.removeKey(record);
        } else {
          // 展开
          this.expandedRowKeys.push(record.key);
        }
      }

      // 元素
      if (['Vas', 'Pricing'].includes(record.type)) {
        if (this.expandedRowKeys.includes(record.key)) {
          // 收起
          this.removeKey(record);
        } else {
          // 展开
          this.expandedRowKeys.push(record.key);
        }
      }
    },
    // 收起
    removeKey(record) {
      this.expandedRowKeys = Array.from(new Set(this.expandedRowKeys));
      const index = this.expandedRowKeys.findIndex(item => item === record.key);
      if (index !== -1) this.expandedRowKeys.splice(index, 1);
    },
    // 勾选/取消勾选
    checkboxChange(record, e) {
      console.log('勾选/取消勾选', record);
      try {
        // 手动阻止事件冒泡
        e && e.stopPropagation();

        validateProductAllRules(record, this.productTreeList, this);

        // 主产品只能选一个,取消或者切换时 提示
        if (this.isMainProduct) {
          this.cancelOrChangeMainProduct(record);
        }

        // 点击勾选框时
        this.checkBoxClick(record);

        // 更新当前勾选状态
        this.$set(record, 'checked', !record.checked);
      } catch (error) {
        console.log('checkboxChange error', error);
      }
    },
    // 点击勾选框时
    checkBoxClick(record) {
      // 产品
      if (record.type == 'Product') {
        if (record.checked) {
          // 取消勾选
          this.product_unChecked(record);
        } else {
          // 勾选中
          this.product_checked(record);
        }
      }

      // 包
      if (record.type == 'Package') {
        if (record.checked) {
          // 取消勾选
          this.package_unChecked(record);
        } else {
          // 勾选中
          this.package_checked(record);
        }
      }

      // 元素
      if (['Vas', 'Pricing'].includes(record.type)) {
        if (record.checked) {
          // 取消勾选
          this.element_unChecked(record);
        } else {
          // 勾选中
          this.element_checked(record);
        }
      }
    },
    // 产品 取消勾选
    product_unChecked(record) {
      // 取消勾选
      this.productTreeList = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.ID == record.ID) {
          this.removeKey(item); // 产品收起
          return {
            ...item,
            checked: false,
            children: item.children.map(iitem => {
              this.removeKey(iitem); // 包收起
              return {
                ...iitem,
                checked: false,
                children: iitem.children.map(iiitem => {
                  return {
                    ...iiitem,
                    checked: false,
                    interfaceElementList: '', // 属性清空
                  };
                }),
              };
            }),
          };
        }
        return item;
      });
    },
    // 产品 勾选中
    product_checked(record) {
      // 1、只有当type=Product并且没有children数据才去请求数据
      if (record.children.length === 0) {
        this.expandedRowKeys.push(record.key);
        this.getChildrenList(record);
        return;
      }

      // 2、有children了则直接处理
      let attrLoadOfElementList = [];
      let list = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.ID == record.ID) {
          this.expandedRowKeys.push(record.key);
          return {
            ...item,
            checked: true,
            children: item.children.map(iitem => {
              // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
              if (this.isChecked(iitem)) {
                this.expandedRowKeys.push(iitem.key);
                iitem.children.forEach(x => {
                  if (this.isChecked(x) && x.HAS_ATTR == '1') attrLoadOfElementList.push(x);
                });
              }
              let packageChecked = this.isChecked(iitem);
              return {
                ...iitem,
                checked: packageChecked,
                children: iitem.children.map(iiitem => {
                  return {
                    ...iiitem,
                    checked: packageChecked ? this.isChecked(iiitem) : false,
                  };
                }),
              };
            }),
          };
        }
        return item;
      });

      // 查属性数据
      this.getElementsApi(list, attrLoadOfElementList, record.ID);
    },
    // 包 取消勾选
    package_unChecked(record) {
      let parent = this.findParent(record);
      this.productTreeList = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.ID == parent.ID) {
          return {
            ...item,
            children: item.children.map(iitem => {
              if (iitem.PACKAGE_ID == record.PACKAGE_ID) {
                this.removeKey(iitem); // 包收起
                return {
                  ...iitem,
                  checked: false,
                  children: iitem.children.map(iiitem => {
                    return {
                      ...iiitem,
                      checked: false,
                      interfaceElementList: '', // 属性清空
                    };
                  }),
                };
              }
              return iitem;
            }),
          };
        }
        return item;
      });
    },
    // 包 勾选中
    package_checked(record) {
      let parent = this.findParent(record);
      if (parent.checked) {
        // 1、父级产品已经勾选了
        let attrLoadOfElementList = [];
        let list = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
          if (item.ID == parent.ID) {
            return {
              ...item,
              children: item.children.map(iitem => {
                if (iitem.PACKAGE_ID == record.PACKAGE_ID) {
                  this.expandedRowKeys.push(iitem.key);
                  return {
                    ...iitem,
                    checked: true,
                    children: iitem.children.map(iiitem => {
                      // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
                      if (this.isChecked(iiitem) && iiitem.HAS_ATTR == '1') {
                        attrLoadOfElementList.push(iiitem);
                      }
                      let elementChecked = this.isChecked(iiitem);
                      return {
                        ...iiitem,
                        checked: elementChecked ? this.isChecked(iiitem) : false,
                      };
                    }),
                  };
                }
                return iitem;
              }),
            };
          }
          return item;
        });

        // 查属性数据
        this.getElementsApi(list, attrLoadOfElementList, parent.ID, record.PACKAGE_ID);
      } else {
        // 2、父级产品没有勾选（自动勾选并且当前包也勾选上）
        let attrLoadOfElementList = [];
        let list = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
          if (item.ID == parent.ID) {
            return {
              ...item,
              checked: true,
              children: item.children.map(iitem => {
                // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
                if (this.isChecked(iitem) || iitem.PACKAGE_ID == record.PACKAGE_ID) {
                  this.expandedRowKeys.push(iitem.key);
                  iitem.children.forEach(x => {
                    if (this.isChecked(x) && x.HAS_ATTR == '1') attrLoadOfElementList.push(x);
                  });
                }
                let packageChecked =
                  iitem.PACKAGE_ID == record.PACKAGE_ID ? true : this.isChecked(iitem);
                return {
                  ...iitem,
                  checked: packageChecked,
                  children: iitem.children.map(iiitem => {
                    return {
                      ...iiitem,
                      checked: packageChecked ? this.isChecked(iiitem) : false,
                    };
                  }),
                };
              }),
            };
          }
          return item;
        });

        // 查属性数据
        this.getElementsApi(list, attrLoadOfElementList, parent.ID);
      }
    },
    // 元素 取消勾选
    element_unChecked(record) {
      // 如果这个包下面的全部元素都为空，则取消包的选择，如果产品下的包都取消了，则全部都取消选择（未做）
      record.interfaceElementList = '';
    },
    // 元素 勾选中
    element_checked(record) {
      let packageRecord = this.findParent(record);
      let productRecord = this.findParent(packageRecord);
      // 判断产品是否勾选，包是否勾选
      if (productRecord.checked && packageRecord.checked) {
        // 产品勾选、包勾选情况
        this.productCheckedPackageChecked(productRecord, packageRecord, record);
      } else if (productRecord.checked && !packageRecord.checked) {
        // 产品勾选、包未勾选情况
        this.productCheckedPackageUnchecked(productRecord, packageRecord, record);
      } else if (!productRecord.checked && !packageRecord.checked) {
        // 产品未勾选、包未勾选情况
        this.productUncheckedPackageUnchecked(productRecord, packageRecord, record);
      } else if (!productRecord.checked && packageRecord.checked) {
        console.log('正常情况下，不存在这种情况');
      }
    },
    // 产品勾选、包勾选情况
    productCheckedPackageChecked(productRecord, packageRecord, record) {
      // 查属性数据
      if (record.HAS_ATTR == '1') {
        this.getElementsApi(
          this.productTreeList,
          [record],
          productRecord.ID,
          packageRecord.PACKAGE_ID,
        );
      }
    },
    // 产品勾选、包未勾选情况
    productCheckedPackageUnchecked(productRecord, packageRecord, record) {
      let attrLoadOfElementList = [];
      let list = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.ID == productRecord.ID) {
          return {
            ...item,
            children: item.children.map(iitem => {
              if (iitem.PACKAGE_ID == packageRecord.PACKAGE_ID) {
                return {
                  ...iitem,
                  checked: true,
                  children: iitem.children.map(iiitem => {
                    // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
                    let currentElement = record.DISCNT_CODE || record.SERVICE_ID;
                    let bool = (iiitem.DISCNT_CODE || iiitem.SERVICE_ID) == currentElement;
                    // 必选和默认的、包括勾选的元素，如果有属性的都要添加进去
                    if (
                      (this.isChecked(iiitem) && iiitem.HAS_ATTR == '1') ||
                      (bool && record.HAS_ATTR == '1')
                    ) {
                      attrLoadOfElementList.push(iiitem);
                    }

                    let elementChecked = this.isChecked(iiitem);
                    return {
                      ...iiitem,
                      checked: bool ? true : elementChecked ? this.isChecked(iiitem) : false,
                    };
                  }),
                };
              }
              return iitem;
            }),
          };
        }
        return item;
      });
      // 查属性数据
      this.getElementsApi(list, attrLoadOfElementList, productRecord.ID, packageRecord.PACKAGE_ID);
    },
    // 产品未勾选、包未勾选情况
    productUncheckedPackageUnchecked(productRecord, packageRecord, record) {
      let attrLoadOfElementList = [];
      let list = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.ID == productRecord.ID) {
          return {
            ...item,
            checked: true,
            children: item.children.map(iitem => {
              // 必选和默认的、包括勾选的包，都要添加进去
              if (this.isChecked(iitem) || iitem.PACKAGE_ID == packageRecord.PACKAGE_ID) {
                this.expandedRowKeys.push(iitem.key);
                iitem.children.forEach(x => {
                  if (this.isChecked(x) && x.HAS_ATTR == '1') attrLoadOfElementList.push(x);
                });
              }
              let packageChecked =
                iitem.PACKAGE_ID == packageRecord.PACKAGE_ID ? true : this.isChecked(iitem);
              return {
                ...iitem,
                checked: packageChecked,
                children: iitem.children.map(iiitem => {
                  // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
                  let currentElement = record.DISCNT_CODE || record.SERVICE_ID;
                  let bool = (iiitem.DISCNT_CODE || iiitem.SERVICE_ID) == currentElement; // 勾选的元素也要勾上
                  // 必选和默认的、包括勾选的元素，如果有属性的都要添加进去
                  if (
                    (this.isChecked(iiitem) && iiitem.HAS_ATTR == '1') ||
                    (bool && record.HAS_ATTR == '1')
                  ) {
                    attrLoadOfElementList.push(iiitem);
                  }

                  let elementChecked = this.isChecked(iiitem);
                  return {
                    ...iiitem,
                    checked: bool ? true : packageChecked ? elementChecked : false,
                  };
                }),
              };
            }),
          };
        }
        return item;
      });

      // 查属性数据
      this.getElementsApi(list, attrLoadOfElementList, productRecord.ID);
    },
    // 查找当前项的父级
    findParent(record) {
      return this.findParentRecursive(this.productTreeList, record);
    },
    // 递归查找父级
    findParentRecursive(data, record) {
      for (const item of data) {
        if (item.children && item.children.some(child => child.key === record.key)) {
          return item;
        }
        if (item.children && item.children.length > 0) {
          const found = this.findParentRecursive(item.children, record);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    // 提示弹窗 确认回调
    messageModalConfirm() {
      if (this.needCallBackFunBool) {
        // 折叠取消上一个产品
        this.product_unChecked(this.lastSelectedMainProduct);

        // 展开勾选当前勾选产品
        setTimeout(() => {
          this.productTreeList = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
            if (item.ID == this.currentSelectedMainProduct.ID) {
              item.checked = true;
            }
            return item;
          });
          this.product_checked({ ...this.currentSelectedMainProduct, checked: true });
        }, 100);
      }

      this.needCallBackFunBool = false;
      this.tipsVisible = false;
    },
    // 获取主产品下的数据
    async getChildrenList(record) {
      try {
        const params = {
          EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
          STAFF_ID: this.userInfo.STAFF_ID,
          PRODUCT_ID: record.ID,
          TRADE_TYPE_CODE: '10',
          PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
          DEFAULT_TAG: '0',
        };

        this.spinning = true;
        const res = await defprodandmutex(params);
        this.spinning = false;
        const product = res?.DATA[0] || {};
        const packageList = res?.DATA[0]?.PACKAGES || [];
        let attrLoadOfElementList = [];

        const list = this.productTreeList.map(item => {
          if (item.ID === record.ID) {
            item.children = packageList.map(iitem => {
              let discntsList = [];
              let servicesList = [];
              const productPackageKey = `${item.ID}-${iitem.PACKAGE_ID}`;
              // 判断产品是否已勾选
              let packageChecked = record.checked ? this.isChecked(iitem) : false;
              // 包下的资费
              if (iitem.TD_B_DISCNTS && iitem.TD_B_DISCNTS.length) {
                discntsList = this.transferDiscnts(
                  packageChecked,
                  iitem.TD_B_DISCNTS,
                  productPackageKey,
                );
              }

              // 包下的服务
              if (iitem.TD_B_SERVICES && iitem.TD_B_SERVICES.length) {
                servicesList = this.transferServices(
                  packageChecked,
                  iitem.TD_B_SERVICES,
                  productPackageKey,
                );
              }

              // 选择产品后，产品下必选包、默认包 默认打勾，且自动展开，非必选非默认的包自动收缩，不展开； 默认包可以取消选择，但必选包不能取消
              if (record.checked && this.isChecked(iitem)) {
                this.expandedRowKeys.push(productPackageKey);
                [...discntsList, ...servicesList].forEach(x => {
                  if (this.isChecked(x) && x.HAS_ATTR == '1') attrLoadOfElementList.push(x);
                });
              }

              // 包
              return {
                key: productPackageKey,
                type: 'Package',
                checked: packageChecked,
                NAME: iitem.PACKAGE_NAME,
                children: elementSort([...discntsList, ...servicesList]),
                ...iitem,
              };
            });
            item.MIN_NUMBER = product.MIN_NUMBER;
            item.MAX_NUMBER = product.MAX_NUMBER;
          }
          return item;
        });
        // 查属性数据
        this.getElementsApi(list, attrLoadOfElementList, record.ID);
      } catch (err) {
        this.removeKey(record);
        this.spinning = false;
      }
    },
    // 包下的资费
    transferDiscnts(packageChecked, list, productPackageKey) {
      return list.map(iiitem => {
        return {
          key: `${productPackageKey}-${iiitem.DISCNT_CODE}`,
          type: 'Pricing',
          checked: packageChecked ? this.isChecked(iiitem) : false,
          NAME: iiitem.DISCNT_NAME,
          ...iiitem,
        };
      });
    },
    // 包下的服务
    transferServices(packageChecked, list, productPackageKey) {
      return list.map(iiitem => {
        return {
          key: `${productPackageKey}-${iiitem.SERVICE_ID}`,
          type: 'Vas',
          checked: packageChecked ? this.isChecked(iiitem) : false,
          NAME: iiitem.SERVICE_NAME,
          ...iiitem,
        };
      });
    },
    // 是否选中
    isChecked(item) {
      return item.FORCE_TAG == '1' || item.DEFAULT_TAG == '1';
    },
    // 获取元素属性接口
    getElementsApi(list, attrLoadOfElementList, ID, PACKAGE_ID, ELEMENT_ID) {
      const params = {
        areaCode: '212',
        cityId: this.userInfo.CITY_CODE,
        deptId: this.userInfo.DEPART_ID,
        epachyId: this.userInfo.EPARCHY_CODE,
        loginEpachyId: '0010',
        loginProvinceId: '11',
        pageData: true,
        provinceId: this.userInfo.PROVINCE_CODE,
        staffId: this.userInfo.STAFF_ID,
        tradeEpachyId: '0010',
      };
      // 入参
      let data = attrLoadOfElementList.map(x => {
        return x.DISCNT_CODE ? `DISCNT_${x.DISCNT_CODE}` : `SERVICE_${x.SERVICE_ID}`;
      });
      this.spinning = true;
      getElements(data, params).then(res => {
        this.spinning = false;
        const obj = res.DATA[0];
        if (PACKAGE_ID) {
          this.dealPackageList(list, obj, ID, PACKAGE_ID, ELEMENT_ID);
        } else {
          this.dealProductList(list, obj, ID, PACKAGE_ID, ELEMENT_ID);
        }
      });
    },
    dealProductList(list, obj, ID, PACKAGE_ID, ELEMENT_ID) {
      let newList = JSON.parse(JSON.stringify(list)).map(x => {
        if (x.ID == ID) {
          return {
            ...x,
            children: x.children.map(xx => {
              return {
                ...xx,
                children: xx.children.map(xxx => {
                  // 增加属性数据
                  if (xxx.DISCNT_CODE) {
                    let y = obj[`DISCNT_${xxx.DISCNT_CODE}`];
                    if (y) {
                      // [x] 表示 当前的产品数据
                      let contractPeriod = this.getMrcElementItem(xx, xxx) || '';
                      xxx.interfaceElementList = this.getInterfaceElementList(
                        y.interfaceElement,
                        y,
                        contractPeriod,
                      );
                    }
                  }
                  // 增加属性数据
                  if (xxx.SERVICE_ID) {
                    let y = obj[`SERVICE_${xxx.SERVICE_ID}`];
                    if (y) {
                      xxx.interfaceElementList = this.getInterfaceElementList(
                        y.interfaceElement,
                        y,
                      );
                    }
                  }
                  return xxx;
                }),
              };
            }),
          };
        }
        return x;
      });
      // 赋值展示
      this.productTreeList = newList;
    },
    dealPackageList(list, obj, ID, PACKAGE_ID, ELEMENT_ID) {
      let newList = JSON.parse(JSON.stringify(list)).map(x => {
        if (x.ID == ID) {
          return {
            ...x,
            children: x.children.map(xx => {
              if (xx.PACKAGE_ID == PACKAGE_ID) {
                return {
                  ...xx,
                  children: xx.children.map(xxx => {
                    // 增加属性数据
                    if (xxx.DISCNT_CODE) {
                      let y = obj[`DISCNT_${xxx.DISCNT_CODE}`];
                      if (y) {
                        // [x] 表示 当前的产品数据
                        let contractPeriod = this.getMrcElementItem(xx, xxx) || '';
                        xxx.interfaceElementList = this.getInterfaceElementList(
                          y.interfaceElement,
                          y,
                          contractPeriod,
                        );
                      }
                    }
                    // 增加属性数据
                    if (xxx.SERVICE_ID) {
                      let y = obj[`SERVICE_${xxx.SERVICE_ID}`];
                      if (y) {
                        xxx.interfaceElementList = this.getInterfaceElementList(
                          y.interfaceElement,
                          y,
                        );
                      }
                    }
                    return xxx;
                  }),
                };
              }
              return xx;
            }),
          };
        }
        return x;
      });
      // 赋值展示
      this.productTreeList = newList;
    },
    // 数据处理
    getInterfaceElementList(list, obj, contractPeriod) {
      return list.map(item => {
        // obj有值，说明是接口返回的，针对selectInitialData 对象是否含有elementCode值，将返回同级的selectInitialData下拉选择框数据，放到对应的item里面
        if (obj && Object.prototype.hasOwnProperty.call(obj.selectInitialData, item.elementCode)) {
          item.selectInitialDataList = obj.selectInitialData[item.elementCode];
        }
        item[item.elementCode] = item.intfElementInitValue || undefined;

        // 合约期保持和MRC的一致，且不可修改
        if (item.elementCode == 'contract_period' && contractPeriod) {
          this.$set(item, 'contract_period', contractPeriod);
          this.$set(item, 'modifyRightCode', 'false');
        }

        // 与this.elementCode值一样，且该属性值为空的时候（注：不为空说明生成了或者填写过了）
        if (item.elementCode == 'PSWD' && !item[item.elementCode]) {
          // 自动生成0000-9999之间的四位数字
          this.$set(item, item.elementCode, generateFourDigitNumber());
        }
        return item;
      });
    },
    // 找MRC对应这项元素出来
    getMrcElementItem(list, xxx) {
      // 找到Rebate对应的MRC（对应的关联关系）
      const obj = (xxx.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
      if (obj) {
        // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
        const element = list?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
        if (element) {
          // 找到对应的MRC的值是多少
          const mrcObj = (element?.interfaceElementList || []).find(
            x => x.elementCode == 'contract_period',
          );
          if (mrcObj) {
            return mrcObj[mrcObj.elementCode];
          }
        }
      }
    },
    // 属性编辑 弹窗 打开
    attributeEditOpen(record) {
      let parent = this.findParent(record);
      let isCCP = parent.NAME.includes('CCP');
      if (isCCP) {
        console.log('打开税率弹窗');
        let num = 0;
        this.recordData = {
          ...record,
          edit: num + 1,
        };
        this.taxRateExchangeDisabled = true;
      } else {
        if (['edit', 'edited'].includes(this.getAttributeClass(record))) {
          this.attributeEditDisabled = false;
        } else {
          this.attributeEditDisabled = true;
        }
        this.attributeEditVisible = true;
      }

      // 先打开弹窗，才能再获取弹窗里面的组件实例，故settimeout
      setTimeout(() => {
        // 编辑进来才做数据回显
        if (this.$refs.AttributeEdit) {
          this.$refs.AttributeEdit.getElements(record);
        }
        if (this.$refs.TaxRateExchange) {
          this.$refs.TaxRateExchange.getElements(record);
        }
      }, 0);
    },
    // 对比两个数组的字段的值是否一致
    compareArrays(arr1, arr2) {
      // 遍历数组，逐个比较指定字段的值
      for (let i = 0; i < arr1.length; i++) {
        if (arr1[i][arr1[i]['elementCode']] !== arr2[i][arr2[i]['elementCode']]) {
          return true; // 如果发现不一致，返回 true
        }
      }

      // 如果全部一致，返回 false
      return false;
    },
    // 获取属性的类名
    getAttributeClass(record) {
      // 如果不显示编辑按钮，直接返回详情类名
      if (!this.editBtnShow) {
        return 'detail';
      }

      // 不可编辑，直接返回详情类名
      if (record?.DIS_EDIT_TAG === '1') {
        return 'detail';
      }

      // 如果当前行没有被选中，返回详情类名
      if (!record.checked) {
        return 'detail';
      }

      // 如果编辑按钮需要变色，并且当前行被编辑过，返回带灰色的编辑状态的类名
      if (this.editBtnChangeColorBool && this.isEdited(record)) {
        return 'edited';
      }

      // 默认情况下，返回普通编辑按钮的类名
      return 'edit';
    },
    // 判断是否编辑过
    isEdited(record) {
      if (record.interfaceElementList && record.interfaceElementListBackup) {
        return this.compareArrays(record.interfaceElementList, record.interfaceElementListBackup);
      }
      return false;
    },
    // 是否置灰
    getIsDisabled(record) {
      let parent = this.findParent(record);
      if (parent) {
        if (parent?.checked) {
          // 只有父级勾选上了，当前项才置灰
          return record.FORCE_TAG == '1';
        }
      } else {
        return record.FORCE_TAG == '1';
      }
    },
    // 属性编辑 弹窗 关闭
    attributeEditCancel() {
      this.attributeEditVisible = false;
    },
    // 属性编辑 弹窗 确认回调
    attributeEditConfirm(record, interfaceElementList) {
      // 说明: record.key = '***********'; 通过层级拼接：111是产品层级  222是包层级 333是元素层级
      // 1. 获取第一个 - 符号前面的数据
      const productKey = record.key.split('-')[0];
      // 2. 获取第二个 - 符号前面的数据
      const packageKey = record.key.split('-').slice(0, 2).join('-');
      // 赋值给对应的项
      this.productTreeList = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.key == productKey) {
          item.children = item.children.map(iitem => {
            if (iitem.key === packageKey) {
              iitem.children = iitem.children.map(iiitem => {
                if (iiitem.key === record.key) {
                  iiitem.interfaceElementList = JSON.parse(JSON.stringify(interfaceElementList));
                  // 存储原始数据，用于判断编辑过变更颜色
                  if (!iiitem.interfaceElementListBackup) {
                    iiitem.interfaceElementListBackup = JSON.parse(
                      JSON.stringify(interfaceElementList),
                    );
                  }
                }
                return iiitem;
              });
            }
            return iitem;
          });
        }
        return item;
      });

      setTimeout(() => {
        this.productTreeList = updateRebatePeriod(this.productTreeList, record, productKey);

        // 触发 - 更新事件，需补充补充当前的record数据
        this.$nextTick(() => {
          this.watchNewestProductTree(
            record.MAIN_DISCNT_TYPE === '1' ? { MAIN_PRODUCT_MRC_CHANGE: record } : {},
          );
        });
        this.attributeEditVisible = false;
      }, 0);
    },
    // 税率 弹窗 关闭
    taxRateExchangeCancel() {
      this.taxRateExchangeDisabled = false;
      console.log(this.taxRateExchangeDisabled, ' this.taxRateExchangeDisabled');
    },
    taxRateExchangeConfirm(record, interfaceElementList) {
      // 说明: record.key = '***********'; 通过层级拼接：111是产品层级  222是包层级 333是元素层级
      // 1. 获取第一个 - 符号前面的数据
      const productKey = record.key.split('-')[0];
      // 2. 获取第二个 - 符号前面的数据
      const packageKey = record.key.split('-').slice(0, 2).join('-');
      console.log(productKey, packageKey, 'packageKey');
      this.productTreeList = JSON.parse(JSON.stringify(this.productTreeList)).map(item => {
        if (item.key == productKey) {
          item.children = item.children.map(iitem => {
            if (iitem.key === packageKey) {
              iitem.children = iitem.children.map(iiitem => {
                if (iiitem.key === record.key) {
                  iiitem.interfaceElementList = JSON.parse(JSON.stringify(interfaceElementList));
                  // 存储原始数据，用于判断编辑过变更颜色
                  if (!iiitem.interfaceElementListBackup) {
                    iiitem.interfaceElementListBackup = JSON.parse(
                      JSON.stringify(interfaceElementList),
                    );
                  }
                }
                return iiitem;
              });
            }
            return iitem;
          });
        }
        return item;
      });
      this.taxRateExchangeDisabled = false;
      console.log(this.productTreeList, ' this.productTreeList ');
    },
  },
  beforeDestroy() {
    if (this.unHandlerWatch) {
      this.unHandlerWatch(); // 手动停止监听
    }
  },
};
