import {
  filterCheckedData,
  getMainProductTreeSelectedList,
  attributeMustEditConfim,
  validateProductData,
  rebateMappingMrc,
  rebateValidate,
  checkDuplicateOrder,
  checkDependencies,
  validateMutexData,
  mrcUpdateNotChooseRebate,
  onlyBeSelectedByMainNumber,
} from '@/views/components/productTreeList/common/utils';
import that from '@/main.js';
// 规则映射对象
const RULES_MAP = {
  0: 'maxMinCheck', // 最大最小值校验
  1: 'productAttributeEdit', //产品属性校验
  2: 'mutexValidation', //互斥校验
  3: 'dependencyValidation', //依赖校验
  4: 'rebateMappingMrc', //rebate费用项和mrc费用项校验
  5: 'rebateValidate', //rebate合约和mrc合约校验
  6: 'mainProductValidate', // 主产品校验， huntingCitinet中不需要这个逻辑
  7: 'checkDuplicateOrder', //重复元素下单
  8: 'mrcUpdateNotChooseRebate', // MRC的值发生修改了，则不能勾选rebate
};

// 完整校验规则， rules传递格式 [0， 1， 3]
export const validateProductTreeRules = function (
  productInfo,
  rules = Object.keys(RULES_MAP).map(Number),
  instance,
) {
  const ruleNames = rules.map(rule => RULES_MAP[rule]);
  //主产品
  const selectedMainProduct = (productInfo?.MainProduct || []).filter(item => item.checked);
  //附加产品
  const selectedAdditionalProduct = (productInfo?.AdditionalProduct || []).filter(
    item => item.checked,
  );
  //选中的主产品
  const checkedMainProduct = filterCheckedData(JSON.parse(JSON.stringify(selectedMainProduct)));

  // 已选择的产品数据 其实和checkedMainProduct基本相同，filterCheckedData只考虑了checked的情况，getMainProductTreeSelectedList考虑了children子集合为空的情况
  const mainProductTreeSelectedList = getMainProductTreeSelectedList(selectedMainProduct);
  console.log(9999, mainProductTreeSelectedList);

  //选中的附加产品
  const checkedAdditionalProduct = filterCheckedData(
    JSON.parse(JSON.stringify(selectedAdditionalProduct)),
  );

  // 选中的附加产品
  const additionalProductTreeSelectedList =
    getMainProductTreeSelectedList(selectedAdditionalProduct);

  /**
   * 主产品必要校验规则， 无需配置项 huntingCitinet中不需要这个逻辑 通过配置项解决
   * 1、是否选择主产品
   * 2、主产品是否只选择了一个
   * 3、主产品必须有包，元素的数据
   * */
  // 是否勾选主产品
  if (ruleNames.includes('mainProductValidate')) {
    if (selectedMainProduct.length === 0) {
      return that.$t('customerVerify.selectMainProd');
    }
    if (selectedMainProduct.length > 1) {
      return that.$t('customerVerify.selectMainProdOnlyOne');
    }
    if (mainProductTreeSelectedList.length === 0) {
      return that.$t('customerVerify.selectMainProdMustHasPackageElement');
    }
  }

  /**
   * 非必要校验规则，需要配置项
   * */
  // 最大最小值校验
  if (ruleNames.includes('maxMinCheck')) {
    const errorMain = validateProductData(checkedMainProduct);
    if (errorMain) return errorMain;
    const error = validateProductData(checkedAdditionalProduct);
    if (error) return error;
  }

  //属性校验
  if (ruleNames.includes('productAttributeEdit')) {
    const errorMain = attributeMustEditConfim(mainProductTreeSelectedList);
    if (errorMain) return errorMain;

    const error = attributeMustEditConfim(additionalProductTreeSelectedList);
    if (error) return error;
  }

  //互斥校验
  if (ruleNames.includes('mutexValidation')) {
    const errorMain = validateMutexData(checkedMainProduct);
    if (errorMain) {
      return that.$t('common.mutexNextStepTips', {
        name: errorMain.current,
        target: errorMain.mutex,
      });
    }
    const error = validateMutexData(checkedAdditionalProduct);
    if (error) {
      return that.$t('common.mutexNextStepTips', { name: error.current, target: error.mutex });
    }
  }

  //依赖校验
  if (ruleNames.includes('dependencyValidation')) {
    const errorMain = checkDependencies(selectedMainProduct, checkedMainProduct);
    if (errorMain) return errorMain;

    const error = checkDependencies(selectedAdditionalProduct, checkedAdditionalProduct);
    if (error) return error;
  }

  //  Rebate输入的值不能大于MRC输入的值
  if (ruleNames.includes('rebateMappingMrc')) {
    const errorMain = rebateMappingMrc(mainProductTreeSelectedList);
    if (errorMain) return errorMain;

    const error = rebateMappingMrc(additionalProductTreeSelectedList);
    if (error) return error;
  }

  // 主产品Rebate合约期要喝MRC的合约期相等
  if (ruleNames.includes('rebateValidate')) {
    const errorMain = rebateValidate(mainProductTreeSelectedList);
    if (errorMain) return errorMain;

    const error = rebateValidate(additionalProductTreeSelectedList);
    if (error) return error;
  }

  if (ruleNames.includes('checkDuplicateOrder')) {
    const errorMain = checkDuplicateOrder(checkedMainProduct);
    if (errorMain) return that.$t('common.duplicateCheckedTips', { name: errorMain });

    const error = checkDuplicateOrder(checkedAdditionalProduct);
    if (error) return that.$t('common.duplicateCheckedTips', { name: error });
  }

  //  MRC的值发生修改了，则不能勾选rebate
  if (ruleNames.includes('mrcUpdateNotChooseRebate')) {
    const errorMain = mrcUpdateNotChooseRebate(mainProductTreeSelectedList);
    if (errorMain) return errorMain;

    const error = mrcUpdateNotChooseRebate(additionalProductTreeSelectedList);
    if (error) return error;
  }

  // 如果是hunting并且号码为非主号的时候，校验所选元素是否有 判断attrCode = hunting_pilot  且 value =1 的，代表该服务只能被hunting pilot号码订购
  if (instance && !instance.isHuntingInstallPilot) {
    const errorMain = onlyBeSelectedByMainNumber(mainProductTreeSelectedList);
    if (errorMain) return errorMain;

    const error = onlyBeSelectedByMainNumber(additionalProductTreeSelectedList);
    if (error) return error;
  }
  return null;
};
