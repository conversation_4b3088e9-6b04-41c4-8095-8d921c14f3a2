<template>
  <div>
    <!-- 搜索条件 -->
    <!-- <a-form-model :model="form" :colon="false" v-if="isShowSearch">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-model-item :label="$t('common.productName')">
            <a-input v-model.trim="form.productName" :placeholder="$t('common.inputPlaceholder')" />
          </a-form-model-item>
        </a-col>
        <a-col :span="1">
          <a-form-model-item label=" " class="textAlignRight">
            <a-button type="primary" @click="handleQuery()">{{
              $t('common.buttonInquiry')
            }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model> -->

    <!-- 已选中的tab列表 -->
    <div class="tabList">
      <div
        :class="['item', currentSelectedTabItem.value == item.value ? 'active' : '']"
        v-for="item of tabList"
        :key="item.value"
        @click="selectedTabChange(item)"
      >
        {{ item.title }}
        (<span class="tab-count">{{ item.count }}</span
        >)
      </div>
    </div>

    <!-- 切换步骤对应的组件 -->
    <keep-alive>
      <component
        :key="currentSelectedTabItem.value"
        :is="currentSelectedTabItem.comp"
        :title="currentSelectedTabItem.title"
        :productTypeCode="currentSelectedTabItem.productTypeCode"
        :ref="currentSelectedTabItem.component"
        :filterProductName="filterProductName"
        :mainProductList="mainProductList"
        :mainProductExpandedRowKeys="mainProductExpandedRowKeys"
        :mainProductCheckboxDisabled="mainProductCheckboxDisabled"
        :mainProductEditBtnShow="mainProductEditBtnShow"
        :mainProductCheckboxShow="mainProductCheckboxShow"
        :mainProductAttributeEditDisabled="mainProductAttributeEditDisabled"
        :mainProductSpinning="mainProductSpinning"
        :mainProductEditBtnChangeColorBool="mainProductEditBtnChangeColorBool"
        :isMRCFee="isMRCFee"
        :additionalProductList="additionalProductList"
        :additionalProductExpandedRowKeys="additionalProductExpandedRowKeys"
        :additionalProductCheckboxDisabled="additionalProductCheckboxDisabled"
        :additionalProductEditBtnShow="additionalProductEditBtnShow"
        :additionalProductCheckboxShow="additionalProductCheckboxShow"
        :additionalProductAttributeEditDisabled="additionalProductAttributeEditDisabled"
        :additionalProductSpinning="additionalProductSpinning"
        :isHuntingInstallPilot="isHuntingInstallPilot"
        @getCurrentSelectedData="handleTreeSelectedData"
      />
    </keep-alive>

    <!-- MainProduct & Premium -->
    <!-- IDD没有安装线路和积分 productType.value != '300007' -->
    <div class="score-part" v-show="showScorePart">
      <span class="score-part-title main"> {{ scorePartTitle }}</span>
      <template v-if="currentSelectedTabItem.component === TAG_KEY_MAIN_PRODUCT">
        <div class="score-part-title sub red-star">
          {{ $t('customerVerify.NoOfLinesInstalled') }}
        </div>
        <a-input-number
          v-model="lineInstallNumber"
          :min="0"
          :precision="0"
          @change="calculateMrcOtcData"
          class="reset-input-number"
        />
      </template>
      <template v-if="currentSelectedTabItem.component === 'PremiumProduct'">
        <div class="score-part-title sub" style="margin-left: 14.5px">
          {{ $t('customerVerify.NoOfPremiumPoints') }}
        </div>
        <a-input
          v-model="selectedRedeemablePoints"
          read-only
          :class="[
            Number(selectedRedeemablePoints.slice(0, -2)) > redeemablePoints ? 'exceed' : '',
          ]"
        />
      </template>
    </div>

    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  import MessageModal from '@/components/messageModal';
  import AdditionalProduct from '../productTreeList/additionalProduct';
  import MainProduct from '../productTreeList/mainProduct';
  import config from './common/config';
  import { mapState } from 'vuex';
  import {
    TAG_KEY_PRICING,
    TAG_KEY_VAS,
    TAG_KEY_RENT_FEE,
    TAG_KEY_OTC_FEE,
    TAG_KEY_REBATE_FEE,
    TAG_KEY_REDEEMED_POINTS,
    TAG_KEY_CONTRACT_PERIOD,
    TAG_KEY_MAIN_PRODUCT,
    TAG_VALUE_FTG,
  } from './common/sign';
  import { FloatCalculator } from '@/utils/floatCalculator';
  import { waitForDataFnComplete } from '@/utils/utils';
  export default {
    name: 'ProductTreeListTabs',
    components: {
      MainProduct,
      AdditionalProduct,
      MessageModal,
    },
    props: {
      searchHidden: {
        // 是否隐藏 搜索条件
        type: Boolean,
        default: false,
      },
      productSelectedDetail: {
        // 详情展示 树形图
        type: Boolean,
        default: false,
      },
      currentTab: {
        type: String,
        default: '',
      },
      // 从哪个tab开始展示 四个tab
      // 1 第一个开始展示
      // 2 第二个tab开始展示，只显示三个tab
      // 4 第四个tab开始展示，即只显示第四个tab
      startFromWhichTabShow: {
        type: Number,
        default: 1,
      },
      // 是否展示搜索条件
      isShowSearch: {
        type: Boolean,
        default: true,
      },
      // 选中产品vas和price的数量
      tabCountList: {
        type: Array,
        default: () => [],
      },
      //线路安装数量
      lineInstallNum: {
        type: String,
        default: '',
      },
      // 主产品列表
      mainProductList: {
        type: Array,
        default: () => [],
      },
      mainProductExpandedRowKeys: {
        type: Array,
        default: () => [],
      },
      mainProductCheckboxDisabled: {
        type: Boolean,
        default: false,
      },
      mainProductEditBtnShow: {
        type: Boolean,
        default: true,
      },
      mainProductCheckboxShow: {
        type: Boolean,
        default: true,
      },
      mainProductAttributeEditDisabled: {
        type: Boolean,
        default: false,
      },
      mainProductSpinning: {
        type: Boolean,
        default: false,
      },
      mainProductEditBtnChangeColorBool: {
        type: Boolean,
        default: false,
      },
      isMRCFee: {
        type: Boolean,
        default: false,
      },

      // 附加产品列表
      additionalProductList: {
        type: Array,
        default: () => [],
      },
      additionalProductExpandedRowKeys: {
        type: Array,
        default: () => [],
      },
      additionalProductCheckboxDisabled: {
        type: Boolean,
        default: false,
      },
      additionalProductEditBtnShow: {
        type: Boolean,
        default: true,
      },
      additionalProductCheckboxShow: {
        type: Boolean,
        default: true,
      },
      additionalProductAttributeEditDisabled: {
        type: Boolean,
        default: false,
      },
      additionalProductSpinning: {
        type: Boolean,
        default: false,
      },
      /*
       * isHuntingInstallPilot涉及到特殊场景选择产品的时候元素校验问题
       * 默认为true：代表其他场景或者是Hunting场景下的主号标识，为true时不做规则校验
       * 为false时： 代表是Hunting场景下非主号标识，判断所选元素的属性SERV_ITEM,判断attrCode = hunting_pilot且 value =1 的时候提示此元素只能被主号订购
       * */
      isHuntingInstallPilot: {
        type: Boolean,
        default: true,
      },
    },

    data() {
      return {
        TAG_KEY_MAIN_PRODUCT,
        form: {},
        // 当前显示的tab
        currentSelectedTabItem: config.tabList[this.startFromWhichTabShow - 1],
        // 展示的tab内容，注：huntingForCitinet不展示主产品，只显示附加产品三个tab
        tabList: [],
        selectedMainProduct: {},
        isLoadApi: false,
        filterProductName: '',
        tipsVisible: false,
        tipsMessage: '',
        lineInstallNumber: 0, // 线装数量
        redeemablePoints: 0, // 礼品积分
        selectedRedeemablePoints: '0Pt', // 选中的礼品积分
        cacheTabListSelected: {}, // 缓存选中的tab选中数据
      };
    },
    computed: {
      // 数量&积分 - 标题
      scorePartTitle() {
        let str = '';
        if (this.currentSelectedTabItem.component === TAG_KEY_MAIN_PRODUCT) {
          str = `${this.$t('customerVerify.ProductQuantity')}: `;
        } else if (this.currentSelectedTabItem.component === 'PremiumProduct') {
          str = `${this.$t('customerVerify.RedeemablePoints')}: ${this.redeemablePoints}Pt`;
        }
        return str;
      },
      ...mapState('quotation', {
        productType: state => state.productType,
        isCORP: state => state.isCORP,
      }),
      // 是否展示积分或者安装线路
      showScorePart() {
        // IDD没有安装线路和积分
        if (this.productType.value == '300007') {
          return false;
        }
        if (this.currentSelectedTabItem.component === 'MainProduct') {
          return true;
        }
        if (this.currentSelectedTabItem.component === 'PremiumProduct' && !this.isCORP) {
          return true;
        }
        return false;
      },
    },
    watch: {
      // 设置当前显示的tab，号码选产品时，每次切换都重置为第一个tab
      currentTab: {
        handler(newVal) {
          this.$nextTick(() => {
            if (newVal) {
              this.selectedMainProduct = {};
              this.isLoadApi = false;
              this.currentSelectedTabItem = config.tabList[this.startFromWhichTabShow - 1];
              // this.startFromWhichTabShow
              //   ? config.tabList.filter(x => x.value != '1')
              //   : config.tabList;
            }
          });
        },
        deep: true,
        immediate: true,
      },
      tabCountList: {
        handler(newVal) {
          this.tabList = this.tabList.map((item, index) => {
            item.count = newVal[index];
            return item;
          });
        },
        deep: true,
        immediate: true,
      },
      lineInstallNum: {
        handler(newVal) {
          this.lineInstallNumber = Number(newVal);
        },
        deep: true,
        immediate: true,
      },
    },
    mounted() {
      this.tabList = this.filterTabList(this.startFromWhichTabShow);
      // 清空上次选品缓存的数据
      this.tabList = this.tabList.map(item => {
        return {
          ...item,
          expandedRowKeys: [],
          productTreeList: [],
        };
      });
    },
    methods: {
      // 过滤tabList value 从第value项开始展示
      filterTabList(value) {
        const index = config.tabList.findIndex(tab => tab.value === String(value));
        return index >= 0 ? config.tabList.slice(index) : config.tabList;
      },
      // 搜索条件查询
      // 主产品有分页，调接口查
      // 附加产品没有分页，前端做搜索即可
      handleQuery() {
        this.filterProductName = this.form.productName;
      },
      // tab切换
      selectedTabChange(item) {
        if (this.productSelectedDetail) {
          return (this.currentSelectedTabItem = item);
        }
        let newSelectedMainProduct = {};

        // 需要判断选了主产品才能选附加产品
        if (this.currentSelectedTabItem.component === TAG_KEY_MAIN_PRODUCT) {
          newSelectedMainProduct = this.$refs.MainProduct.productTreeList.find(x => x.checked);
          if (!newSelectedMainProduct) {
            this.tipsMessage = this.$t('customerVerify.selectMainProd');
            this.tipsVisible = true;
            return;
          }
        }

        // 每次切换都先存储上一个 tab 的组件数据
        this.tabList = this.tabList.map(x => {
          if (x.component === this.currentSelectedTabItem.component) {
            x.productTreeList = this.$refs[x.component].productTreeList || [];
            x.expandedRowKeys = this.$refs[x.component].expandedRowKeys || [];
          }
          return x;
        });

        // 切换到后面三个 tab，如果其中一个tab加载过，则不再请求接口，除非主产品ID发生变化
        if (item.component !== TAG_KEY_MAIN_PRODUCT) {
          // 检查主产品 ID 是否发生变化
          const isMainProductChanged = newSelectedMainProduct.ID
            ? this.selectedMainProduct.ID !== newSelectedMainProduct.ID
            : false;

          // 如果主产品 ID 发生变化，重置加载状态
          if (isMainProductChanged) {
            this.selectedMainProduct.ID = newSelectedMainProduct.ID;
            this.isLoadApi = false;
          }

          // 如果未加载过数据，则加载数据
          if (!this.isLoadApi) {
            this.isLoadApi = true; // 标记为已加载
            this.$emit('getAdditionalProduct', this.selectedMainProduct.ID); // 触发加载数据的逻辑
          }
        }

        // 切换 tab
        this.currentSelectedTabItem = item;
      },
      // 获取子组件数据
      getSubComponentProductTreeList() {
        let obj = {};
        this.tabList.forEach(item => {
          if (item.component == this.currentSelectedTabItem.component) {
            // 获取当前子组件最新的数据
            obj[item.component] =
              this.$refs[item.component]?.productTreeList || item.productTreeList;
            obj[item.component + 'ExpandedRowKeys'] =
              this.$refs[item.component]?.expandedRowKeys || item.expandedRowKeys;
          } else {
            // 获取切换后存储的子组件数据
            obj[item.component] = item.productTreeList;
            obj[item.component + 'ExpandedRowKeys'] = item.expandedRowKeys;
          }
        });
        let AdditionalProduct = this.additionalProductList.filter(
          item => !['300010', '300013'].includes(item.PRODUCT_TYPE_CODE),
        );
        let EquipmentProduct = this.additionalProductList.filter(
          item => item.PRODUCT_TYPE_CODE === '300010',
        );
        let PremiumProduct = this.additionalProductList.filter(
          item => item.PRODUCT_TYPE_CODE === '300013',
        );

        AdditionalProduct =
          obj.AdditionalProduct && obj.AdditionalProduct.length > 0
            ? obj.AdditionalProduct
            : AdditionalProduct;
        EquipmentProduct =
          obj.EquipmentProduct && obj.EquipmentProduct.length > 0
            ? obj.EquipmentProduct
            : EquipmentProduct;
        PremiumProduct =
          obj.PremiumProduct && obj.PremiumProduct.length > 0 ? obj.PremiumProduct : PremiumProduct;

        return {
          MainProduct: obj.MainProduct || [],
          AdditionalProduct: [
            ...(AdditionalProduct || []),
            ...(EquipmentProduct || []),
            ...(PremiumProduct || []),
          ],
        };
      },
      // 处理已选数据
      async handleTreeSelectedData(extra) {
        const arr = await this.countSelectedNode(
          this.$refs[this.currentSelectedTabItem.component].productTreeListFilter(),
        );
        // 缓存选中的tab选中数据
        this.cacheTabListSelected[this.currentSelectedTabItem.component] = arr;
        // 更新当前选中的tab数量
        this.updateTabCount(arr);
        // 同步计算OTC和MRC费用
        // #TODO - 目前问题场景：新增或者编辑下，重新回显的底部栏总价值值不对。
        // 出现原因：产品树切换Tab栏才会加载附件产品的数据，其次问题 - 附加产品/设备/积分，共用同个组件，同样导致计算也会失误【#TODO：可能存在隐患，等待解决方案】。
        // 暂时方案：通过判断已保存的TabList的count的值与已缓存的cacheTabListSelected中是否有缓存对应组件【AdditionalProduct、EquipmentProduct、PremiumProduct】做对比
        // 如果值对不上就是存在问题，需要使用getSubComponentProductTreeList拿到全数据，并把装载到cacheTabListSelected，在这基础上的做计算。
        // 如果是对的上，就按原来的已实现的方式计算。
        const cacheTabSelectedTags = Object.keys(this.cacheTabListSelected);
        const haveSelectedTabs = this.tabList.filter(x => x.count != 0);
        if (haveSelectedTabs.length === cacheTabSelectedTags.length) {
          this.calculateMrcOtcData();
        } else {
          let tabAdditionCount = 0;
          haveSelectedTabs.forEach(v => (tabAdditionCount += Number(v.count)));
          this.calculateMrcOtcDataBySubComponent(tabAdditionCount);
        }
        // 如果编辑的是主产品的MRC费用，则需要计算更新积分。【目前判断条件：MAIN_PRODUCT_MRC_CHANGE（MRC价格发生变化）】
        if (extra.MAIN_PRODUCT_MRC_CHANGE) {
          this.calculateCanUsePremiumPoints(extra.MAIN_PRODUCT_MRC_CHANGE);
        }
        // 当前是礼品页面 - 计算所选积分
        if (this.currentSelectedTabItem.component === 'PremiumProduct') {
          this.$nextTick(() => {
            this.calculateSelectedPremiumPoints(arr);
          });
        }
      },

      // 统计元素所选数量
      async countSelectedNode(treeData) {
        let result = await this.findCheckedItems(treeData);
        console.log('result', result);
        return result;
      },
      // 递归树
      findCheckedItems(treeData) {
        const checkedItems = [];

        function traverse(node) {
          if (
            node.checked === true &&
            (node.type === TAG_KEY_VAS || node.type === TAG_KEY_PRICING)
          ) {
            checkedItems.push(node);
          }

          if (node.children && node.children.length > 0) {
            node.children.forEach(child => traverse(child));
          }
        }

        treeData.forEach(item => traverse(item));

        return checkedItems;
      },
      // 同步更新tabCount数量
      updateTabCount(arr = []) {
        this.tabList[this.currentSelectedTabItem.value - 1].count = arr.length;
      },
      // 计算MRC和OTC费用
      calculateMrcOtcData() {
        // 如果没有填写线路数量，则不计算
        if (
          this.lineInstallNumber === '' ||
          isNaN(this.lineInstallNumber) ||
          this.lineInstallNumber == 0
        ) {
          return false;
        }

        // MRC费用总额
        // 注：如有免费赠送 3 个月的情况，此项只需要计算展示正常月费的金额即可。
        let MRCtotal = 0;

        // OTC费用总额
        let OTCtotal = 0;

        // 【Total MRC For All Line Period】=【Total MRC For All Line】*【Main Product Period】
        // 注：费用需按实际最终支付的费用进行计算。即需要将免费的金额进行减免
        // let rebateTotal = 0;
        let TotalMRCAllPeriod = 0;

        console.log('cacheTabListSelected', this.cacheTabListSelected);
        const typeList = [TAG_KEY_RENT_FEE, TAG_KEY_OTC_FEE, TAG_KEY_REBATE_FEE];

        for (const key in this.cacheTabListSelected) {
          if (Object.hasOwnProperty.call(this.cacheTabListSelected, key)) {
            const element = this.cacheTabListSelected[key];
            for (let i = 0; i < element.length; i++) {
              const item = element[i];
              // 处理 Price类型
              if (item.type === TAG_KEY_PRICING && item.interfaceElementList) {
                const foundItems = item.interfaceElementList.find(x =>
                  typeList.includes(x.elementCode),
                );
                const foundPeriod = item.interfaceElementList.find(
                  x => x.elementCode === TAG_KEY_CONTRACT_PERIOD,
                );
                // 当前item的费用
                const costTotal =
                  Number(foundItems[foundItems.elementCode] || 0) * this.lineInstallNumber;
                // 当前周期
                let costPeriod = 1;
                // 判断 - period是否为FTG, 如果为FTG，则总值不参与计算且显示都要隐藏
                if (foundPeriod) {
                  if (!isNaN(foundPeriod[TAG_KEY_CONTRACT_PERIOD])) {
                    costPeriod = Number(foundPeriod[TAG_KEY_CONTRACT_PERIOD]);
                  } else {
                    costPeriod = TAG_VALUE_FTG;
                  }
                }
                // MRC费用总额
                if (foundItems.elementCode === TAG_KEY_RENT_FEE) {
                  MRCtotal += costTotal;
                  // 总价 - 同步计算
                  TotalMRCAllPeriod =
                    costPeriod != TAG_VALUE_FTG
                      ? TotalMRCAllPeriod + costTotal * costPeriod
                      : costPeriod;
                }
                // OTC费用总额
                if (foundItems.elementCode === TAG_KEY_OTC_FEE) {
                  OTCtotal += costTotal;
                }
                // 减免后的费用
                if (foundItems.elementCode === TAG_KEY_REBATE_FEE) {
                  // 这里需要处理的是 是否选择前三或后三个月免费，或者是全周期减免
                  // 前三 - free_months_previous， 后三 - free_months_after
                  // #TODO 还需要留意其他rebate是否同样会有前三后三属性返回，后面等其他产品数据验证
                  if (item.DISCNT_ITEM && item.DISCNT_ITEM.length) {
                    const ifHaveThreeMonth = item.DISCNT_ITEM.filter(
                      discntItem =>
                        discntItem.ATTR_CODE === 'free_months_after' ||
                        discntItem.ATTR_CODE === 'free_months_previous',
                    );
                    if (ifHaveThreeMonth.length > 0) {
                      // ifHaveThreeMonth/free_months_previous中的ATTR_VALUE的值不为0，证明就是选择了对应的三月免费
                      // 否则，就是选择全周期减免
                      const isHaveThree = ifHaveThreeMonth.filter(
                        monthItem => monthItem.ATTR_VALUE == 0,
                      );
                      if (isHaveThree.length !== 2) {
                        const rebateMonthValue = Number(
                          ifHaveThreeMonth[0].ATTR_VALUE != '0'
                            ? ifHaveThreeMonth[0].ATTR_VALUE
                            : ifHaveThreeMonth[1].ATTR_VALUE,
                        );
                        // MRC - 价格做对应的调整
                        MRCtotal = FloatCalculator.subtract(MRCtotal, costTotal);
                        if (costPeriod !== TAG_VALUE_FTG) {
                          const calcPeriod =
                            costPeriod <= rebateMonthValue
                              ? costPeriod
                              : costPeriod - rebateMonthValue;
                          // 主产品【涉及到rebate和免月份】计算公式：（主产品价格 - 免月份的rebate）* （主产品周期 - 免月份的值）
                          // 减去 rebate
                          TotalMRCAllPeriod = FloatCalculator.subtract(
                            TotalMRCAllPeriod,
                            costTotal * calcPeriod,
                          );
                          // 再减去 主价格 * 减免的月份月， 因为前面已经计算了主价格 *周期了
                          const MAIN_PRODUCT_MRC = this.getMainProductMrcPrice(
                            this.cacheTabListSelected[TAG_KEY_MAIN_PRODUCT],
                          );
                          TotalMRCAllPeriod = FloatCalculator.subtract(
                            TotalMRCAllPeriod,
                            MAIN_PRODUCT_MRC * rebateMonthValue * this.lineInstallNumber,
                          );
                        } else {
                          TotalMRCAllPeriod = TAG_VALUE_FTG;
                        }
                        continue;
                      }
                    }
                  }
                  // MRC - 价格做对应的调整
                  MRCtotal = FloatCalculator.subtract(MRCtotal, costTotal);
                  // 总价格 - 做对应的调整
                  TotalMRCAllPeriod =
                    costPeriod != TAG_VALUE_FTG
                      ? FloatCalculator.subtract(TotalMRCAllPeriod, costTotal * costPeriod)
                      : costPeriod;
                }
              }
            }
          }
        }

        this.$emit('getProductMrcOtcData', { MRCtotal, OTCtotal, TotalMRCAllPeriod });
      },
      // 补全cache缓存数据，后计算总价
      async calculateMrcOtcDataBySubComponent(noCacheSelectedNum) {
        try {
          const allData = await waitForDataFnComplete(
            () => this.getSubComponentProductTreeList(),
            data => {
              return data['AdditionalProduct'].length >= noCacheSelectedNum;
            },
            15, // 最多尝试15次
            200, // 每次间隔200ms
          );
          // 默认主产品已经加载在cache，忽略处理
          // const mainArr = await this.countSelectedNode(allData.MainProduct);
          for (let j = 0; j < this.cacheTabListSelected['MainProduct'].length; j++) {
            const ele = this.cacheTabListSelected['MainProduct'][j];
            // 计算 - 可用积分
            this.calculateCanUsePremiumPoints(ele);
          }

          const cacheTabSelectedTags = Object.keys(this.cacheTabListSelected);
          const noCacheButSelectedTabs = this.tabList.filter(
            x => x.count != 0 && !cacheTabSelectedTags.includes(x.component),
          );
          console.log('====================================');
          console.log(allData, noCacheButSelectedTabs);
          console.log('====================================');
          if (noCacheButSelectedTabs.length != 0) {
            for (let i = 0; i < noCacheButSelectedTabs.length; i++) {
              const ele = noCacheButSelectedTabs[i];
              if (ele.component === 'AdditionalProduct') {
                let AdditionalProduct = await this.countSelectedNode(
                  allData.AdditionalProduct.filter(
                    item => !['300010', '300013'].includes(item.PRODUCT_TYPE_CODE),
                  ),
                );
                this.cacheTabListSelected['AdditionalProduct'] = AdditionalProduct;
              } else if (ele.component === 'EquipmentProduct') {
                let EquipmentProduct = await this.countSelectedNode(
                  allData.AdditionalProduct.filter(item => item.PRODUCT_TYPE_CODE === '300010'),
                );
                console.log(EquipmentProduct);
                this.cacheTabListSelected['EquipmentProduct'] = EquipmentProduct;
              } else if (ele.component === 'PremiumProduct') {
                let PremiumProduct = await this.countSelectedNode(
                  allData.AdditionalProduct.filter(item => item.PRODUCT_TYPE_CODE === '300013'),
                );
                // 计算 - 已选积分
                this.calculateSelectedPremiumPoints(PremiumProduct);
                console.log(PremiumProduct);
                this.cacheTabListSelected['PremiumProduct'] = PremiumProduct;
              }
            }
          }
          console.log(this.cacheTabListSelected, 'getSubComponentProductTreeList - arr');
          this.calculateMrcOtcData();
        } catch (e) {
          // this.$message.error('数据加载超时，请重试');
          // #TODO - 兜底处理
          this.calculateMrcOtcData();
        }
      },
      // 计算可用礼品积分
      // 注：说明：销售报价比标准定价每增加【$30】，可获得【0.5pt】的礼品可兑换积分。小于$30,不算分。
      // 例：trunc(（$300-200）/$30）*0.5=1.5pt
      calculateCanUsePremiumPoints(record) {
        console.log('record', record);
        const PREMIUM_RULE_VALUE = 30;
        if (record.interfaceElementList && record.interfaceElementList.length > 0) {
          const foundItems = record.interfaceElementList.find(
            x => x.elementCode === TAG_KEY_RENT_FEE && x.intfElementLabel === 'MRC',
          );
          if (foundItems) {
            this.redeemablePoints =
              Math.floor(
                (foundItems.rent_fee - foundItems.intfElementInitValue) / PREMIUM_RULE_VALUE,
              ) * 0.5;
          }
        }
      },
      // 统计已选积分
      calculateSelectedPremiumPoints(pointsList) {
        // const pointsList = this.cacheTabListSelected['PremiumProduct'];
        let count = 0;
        for (let i = 0; i < pointsList.length; i++) {
          const ele = pointsList[i];
          console.log('calculateSelectedPremiumPoints', ele);
          if (ele.checked && ele.type === TAG_KEY_PRICING && ele.interfaceElementList) {
            const foundItems = ele.interfaceElementList.find(x =>
              [TAG_KEY_REDEEMED_POINTS].includes(x.elementCode),
            );
            count += Number(foundItems[TAG_KEY_REDEEMED_POINTS]);
          }
        }
        console.log('count', count);
        this.selectedRedeemablePoints = `${count}Pt`;
      },
      // 辅助 - 获取传入的主产品数组的主价格
      getMainProductMrcPrice(arr) {
        let result = 0;
        if (arr && arr.length) {
          const mainObj = arr.find(
            x => x.MAIN_DISCNT_TYPE == 1 && x.type === TAG_KEY_PRICING && x.interfaceElementList,
          );
          if (mainObj) {
            result = mainObj.interfaceElementList.find(
              x => x.elementCode === TAG_KEY_RENT_FEE,
            )?.rent_fee;
          }
        }
        return Number(result);
      },
    },
  };
</script>
<style lang="less" scoped>
  .tabList {
    display: flex;
    margin-bottom: 20px;
    .item {
      position: relative;
      height: 40px;
      line-height: 40px;
      border-radius: 2px;
      padding: 0 10px;
      cursor: pointer;
    }
    .active {
      font-weight: bolder;
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 80%;
        height: 2px;
        background: #01408e;
        text-align: center;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-top-color: #01408e;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  /deep/ .ant-checkbox-input {
    width: 16px !important;
  }
  .tab-count {
    color: #0072ff;
  }
  .score-part {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 20px;
    &-title {
      font-size: 14px;
      color: #373d41;
      text-align: center;
      font-weight: 400;
      padding-right: 6px;
      &.main {
        font-family: SimSun, sans-serif;
      }
      &.sub {
        background-color: #e9e9e9;
        display: inline-block;
        height: 32px;
        text-align: center;
        line-height: 32px;
        padding: 0 10px;
      }
      &.red-star {
        &::before {
          display: inline-block;
          margin-right: 4px;
          color: #f5222d;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
          content: '*';
        }
      }
    }
    .ant-input {
      width: 68px;
      text-align: center;
      &.exceed {
        color: #e60017;
      }
    }
  }

  .reset-input-number {
    width: 68px;
    /deep/ .ant-input-number-handler-wrap {
      display: none;
    }
    /deep/ .ant-input-number-input {
      text-align: center;
    }
  }
</style>
