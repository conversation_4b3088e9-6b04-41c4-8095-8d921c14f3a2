<template>
  <div class="taxRateExchange">
    <a-modal
      :title="$t('taxRateExchange.title')"
      :visible="taxRateExchangeDisabled"
      class="edit-modal"
      width="60%"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="content">
        <div class="item">
          <div class="label">{{ $t('taxRateExchange.Country') }} :</div>
          <div class="value">
            <a-input
              v-containsSqlInjection
              v-model.trim="country"
              class="pilot"
              placeholder="Please Enter"
            ></a-input>
          </div>
        </div>
        <div class="item">
          <a-button type="primary" @click="handleInquiry" class="serch-button">
            {{ $t('common.buttonInquiry') }}
          </a-button>
          <!-- <a-button ghost type="primary" @click="Import" class="reset-button">
            {{ $t('common.buttonImport') }}
          </a-button> -->
        </div>
      </div>
      <a-divider />
      <div>
        <p class="common-custom-title">{{ $t('taxRateExchange.selectedList') }}</p>
        <div class="chooseTag" style="display: flex; flex-wrap: wrap">
          <div>
            <a-tag
              v-for="(item, index) in countryList"
              v-show="item.checked"
              style="margin-bottom: 5px"
              :key="index"
              :closable="!isDisabled"
              @close="e => handleCloseTag(e, item)"
            >
              <span style="padding: 0 10px">{{ item.DESTINATION }}</span>
              <a-input
                prefix="$"
                style="width: 100px"
                :default-value="item.PRICE"
                :formatter="value => `$ ${value}`"
                :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                v-model="item.PRICE"
                @change="changePrice(item)"
                :disabled="isDisabled"
                @blur="blur(item)"
              />
            </a-tag>
          </div>
        </div>
      </div>
      <a-divider />
      <div>
        <div class="checkDiv">
          <div
            style="width: 30px"
            v-for="(item, index) in letterList"
            :key="index"
            :class="[
              item.name == letterCheck
                ? 'tabChecked common-custom-title'
                : 'tabInit common-custom-title',
            ]"
            @click="handleCheck(item.name)"
          >
            <span class="line">{{ item.name }}</span>
          </div>
        </div>
        <div>
          <a-checkbox-group @change="change" v-model="selectList">
            <a-checkbox
              v-for="(item, index) in checkList"
              :key="index"
              name="type"
              :value="item.NCC_CNTY"
              :checked="item.checked"
              :disabled="isDisabled"
              @change="onSelectChange"
            >
              {{ item.DESTINATION }}（${{ item.tempPRICE }}）
            </a-checkbox>
          </a-checkbox-group>
        </div>
      </div>
      <template slot="footer">
        <a-button class="modal-button-cancel button" @click="handleCancel">{{
          $t('common.buttonCancel')
        }}</a-button>
        <a-button
          class="moadl-button-Ok button"
          v-if="!isDisabled"
          type="primary"
          @click="confirm"
          >{{ $t('common.buttonConfirm') }}</a-button
        >
      </template>
    </a-modal>
  </div>
</template>

<script>
  import {
    queryCppRatePlanApi,
    querySelectedCppRatePlanApi,
    querySelectedCppRatePlanByOrderApi,
  } from '@/api/accountSetting';
  import config from './common/config';
  import { mapState } from 'vuex';

  export default {
    name: 'taxRateExchange',
    props: {
      taxRateExchangeDisabled: {
        type: Boolean,
        default: false,
      },
      recordData: {
        type: Object,
        default: () => {},
      },
      attributeEditDisabled: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      // ...mapState('macd', {
      //   selectedOrderList: state => state.selectedOrderList,
      //   orderList: state => state.orderList,
      // }),
      // ...mapState('orderQuery', {
      //   orderDetail: state => state.orderDetail,
      // }),
    },
    data() {
      return {
        selectList: [],
        checkList: [],
        letterCheck: 'A',
        letterList: [],
        country: '', // 搜索框
        form: {},
        confirmList: [],
        record: {},
        isDisabled: false,
        countryList: [], //全部的税率列表
        taxRateList: [], //过滤后的税率列表
      };
    },
    watch: {
      recordData: {
        deep: true,
        immediate: false,
        async handler(newVal, oldVal) {
          if (newVal.DISCNT_CODE !== oldVal.DISCNT_CODE) {
            this.selectList = [];
          }
          if (newVal.edit === 1 && newVal.DISCNT_CODE !== oldVal.DISCNT_CODE) {
            await this.queryCppRatePlan(this.recordData);
            this.querySelectedCppRatePlan(newVal);
          }
        },
      },
      // 属性编辑 表单内容是否全部禁用
      attributeEditDisabled: {
        handler(newVal) {
          this.isDisabled = newVal;
        },
        deep: true,
        immediate: true,
      },
    },
    async mounted() {
      this.letterList = config.generateAlphabetList();
      if (!this.attributeEditDisabled) {
        await this.queryCppRatePlan(this.recordData);
        this.querySelectedCppRatePlan(this.recordData);
      } else if (this.attributeEditDisabled) {
        await this.queryCppRatePlan(this.recordData);
        this.querySelectedCppRatePlan(this.recordData);
        this.getElements(this.recordData);
      }
    },
    methods: {
      blur(tag) {
        // 正则表达式：匹配小数或正数
        const regex = /^(0*[1-9]\d*(\.\d+)?|0*\.\d*[1-9]\d*)$/;
        // 获取当前输入值
        let value = tag.PRICE;
        // 去除前缀和格式化
        value = value.replace(/\$\s?|(,*)/g, '');
        // 验证是否为小数或正数
        if (!regex.test(value)) {
          // 如果不符合正则表达式，则清空输入框
          tag.PRICE = '';
          this.$message.error(this.$t('taxRateExchange.Prompt'));
        }
        this.countryList.map((item, index) => {
          if (item.NCC_CNTY == tag.NCC_CNTY) {
            item.status = tag.status == 'original' ? 'edit' : 'add';
          }
        });
      },
      getElements(record) {
        // // 存储当前 record，弹窗确认按钮的时候返回父组件，用作记录判断点击的是哪一项
        this.record = record;
        if (record.interfaceElementList) {
          this.countryList = this.countryList.map(item => {
            record?.interfaceElementList?.map(iitem => {
              if (item.NCC_CNTY == iitem.NCC_CNTY) {
                item = { ...iitem };
              }
            });
            return item;
          });
        }
      },
      handleCloseTag(e, val) {
        e.preventDefault();
        this.countryList.map((item, index) => {
          if (item.NCC_CNTY == val.NCC_CNTY) {
            item.checked = !item.checked;
            item.status =
              ['original', 'edit'].includes(item.status) && !item.checked ? 'del' : 'add';
          }
        });
        const checkedItems = this.countryList.filter(item => item.checked === true);
        const checkedNCC_CNTYs = checkedItems.map(item => item.NCC_CNTY);
        this.selectList = checkedNCC_CNTYs;
      },
      handleInquiry() {
        this.checkList = this.countryList.filter(item => item.DESTINATION.includes(this.country));
      },
      // 调用接口查询出全部的税率
      queryCppRatePlan(newVal) {
        return new Promise((resolve, reject) => {
          const params = {
            'PLAN_CODE': 'UGM1',
          };
          queryCppRatePlanApi(params).then(res => {
            this.countryList = res.DATA[0].LIST.map(item => ({
              ...item,
              tempPRICE: item.PRICE,
              checked: false, // 初始化 checked 属性
            }));
            resolve();
          });
        });
      },
      // 调用接口，查询出已选的税率 利用status进行标记
      // status : 'original',//del删除，add新增，edit修改,original原数据
      querySelectedCppRatePlan(newVal) {
        console.log(this.reqParams);
        // oscaAddIddProductTree
        if (['changeIDD', 'oscaAddIddProductTree'].includes(this.$route.name)) {
          const params = {
            'USER_ID':
              // this.selectedOrderList[0]?.USER_ID ||
              // this.orderList[0]?.USER_ID ||
              this.$route.query?.userId || '9025032700908486', // 用户ID
            /*TODO*/
            'DIS_ITEM_ID': newVal.DISCNT_CODE || '9025022800089080',
          };
          querySelectedCppRatePlanApi(params).then(res => {
            const selectedData = res.DATA ?? [];
            selectedData &&
              selectedData.forEach(selectedItem => {
                this.countryList.map((item, index) => {
                  if (item.NCC_CNTY == selectedItem.CRM_ATTR_CODE) {
                    item.checked = true;
                    item.status = 'original';
                    item.elementCode = selectedItem.ATTR_CODE;
                    item.elementValue = selectedItem.ATTR_VALUE;
                    item.tempPRICE = item.PRICE;
                  }
                });
              });
            this.handleCheck(this.letterCheck);
          });
        } else {
          // const { numberInfoList } = this.orderDetail;
          // const params = {
          //   'ORDER_ID': numberInfoList[0]?.ORDER_ID || '',
          //   'ORDER_LINE_ID': numberInfoList[0]?.ORDER_LINE_ID || '',
          // };
          // querySelectedCppRatePlanByOrderApi(params).then(res => {
          //   const selectedData = res.DATA ?? [];
          //   selectedData &&
          //     selectedData.forEach(selectedItem => {
          //       this.countryList.map((item, index) => {
          //         if (item.NCC_CNTY == selectedItem.ATTR_CODE) {
          //           item.checked = true;
          //           item.status = 'original';
          //           item.elementCode = selectedItem.ATTR_CODE;
          //           item.elementValue = selectedItem.ATTR_VALUE;
          //           item.tempPRICE = item.PRICE;
          //         }
          //       });
          //     });
          //   this.handleCheck(this.letterCheck);
          // });
        }
      },

      onSelectChange(val) {
        this.countryList.map((item, index) => {
          if (item.NCC_CNTY == val.target.value) {
            item.checked = !item.checked;
            item.status =
              ['original', 'edit'].includes(item.status) && !item.checked ? 'del' : 'add';
            console.log('item=', item);
          }
        });
        const checkedItems = this.countryList.filter(item => item.checked === true);
        const checkedNCC_CNTYs = checkedItems.map(item => item.NCC_CNTY);
        this.selectList = checkedNCC_CNTYs;
      },
      change(val) {
        this.selectList = Array.from(new Set(this.selectList));
      },
      handleCheck(item) {
        const checkedItems = this.countryList.filter(item => item.checked === true);
        const checkedNCC_CNTYs = checkedItems.map(item => item.NCC_CNTY);
        this.selectList = checkedNCC_CNTYs;

        this.checkList = [];
        // 确保单词不为空，并获取首字母
        this.letterCheck = item;
        // 获取首字母并转换为小写进行比较
        this.countryList.forEach(word => {
          if (word.DESTINATION.charAt(0) == this.letterCheck) {
            this.checkList.push(word);
          }
        });
      },
      async handleCancel() {
        if (!this.confirmList[0]) {
          await this.queryCppRatePlan(this.recordData);
          this.querySelectedCppRatePlan(this.recordData);
        }
        this.$emit('cancel');
      },
      handleOk() {},
      // import() {},
      // 关闭
      cancel() {
        this.$emit('cancel');
      },
      changePrice(tag) {},
      // 确认
      confirm() {
        let delArr = [];
        let addArr = [];
        let editArr = [];
        this.countryList.map(item => {
          let a = item.NCC_CNTY.split(', ');
          item[item.NCC_CNTY] = item.PRICE;
          if (item.status == 'del') {
            a.map(iitem => {
              let arr = {
                ...item,
                elementCode: 'RATE_' + iitem,
                elementValue: item.PRICE,
                ['RATE_' + iitem]: item.PRICE,
                MODIFY_TAG: '1',
              };
              delArr.push(arr);
            });
          }
          if (item.status == 'add') {
            a.map(iitem => {
              let arr = {
                ...item,
                elementCode: 'RATE_' + iitem,
                elementValue: item.PRICE,
                ['RATE_' + iitem]: item.PRICE,
                MODIFY_TAG: '0',
              };
              addArr.push(arr);
            });
          }
          if (item.status == 'edit') {
            a.map(iitem => {
              let arr = {
                ...item,
                elementCode: 'RATE_' + iitem,
                elementValue: item.PRICE,
                ['RATE_' + iitem]: item.PRICE,
                MODIFY_TAG: '2',
              };
              editArr.push(arr);
            });
          }
        });
        this.confirmList = [...delArr, ...addArr, ...editArr];
        // this.$store.dispatch('macd/setTaxRate', this.confirmList);
        this.$emit('confirm', this.record, this.confirmList);
      },
    },
  };
</script>

<style lang="less" scoped>
  .content {
    display: flex;
    justify-content: space-between;
    .item {
      display: flex;
      align-items: center;
      .label {
        color: #333333;
        font-size: 14px;
        margin-right: 5px;
      }
    }
  }
  .ant-tag {
    padding: 0 !important;
    font-size: 12px;
    line-height: 20px;
    border: 1px solid #01408e;
    border-radius: 1px;
  }

  .tabChecked {
    .line {
      border-bottom: 1px solid black;
    }
  }
  .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0 !important;
    margin-bottom: 10px;
  }
  .moadl-button-Ok {
    width: 70px;
  }
  .title {
    margin-bottom: 5px !important;
  }
  .chooseTag {
    display: flex;
    flex-wrap: wrap;
  }
  .tagValue {
    background-color: #8abcdb80;
    padding: 1px 4px;
    border-radius: 20px;
    font-size: 10px;
  }
  .checkDiv {
    display: flex;
    overflow-x: scroll;
    margin-bottom: 10px;
  }
  .attributeEdit {
    display: flex;
    flex-direction: column;
    .form {
      padding: 0 20px 20px;
      min-height: 200px;
      flex: 1;
      max-height: 600px;
      overflow: auto;
      display: flex;
      flex-direction: column;
    }
    .flex-right {
      display: flex;
      justify-content: right;
      margin: 10px 0;
    }
  }
  .common-custom-title {
    &::before {
      display: none;
    }
  }
</style>
