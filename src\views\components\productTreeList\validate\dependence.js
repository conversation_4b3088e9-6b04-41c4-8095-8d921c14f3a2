import { getCurrentProduct, getCurrentProductId, getRecordType } from './utils';
import { findItemsByIds, updateProductTreeCheckedStatus } from '@/utils/utils';
import { findParent } from '@/views/components/productTreeList/common/utils';

export const dependenceValidate = (record, productTreeList, that, flag) => {
  // 判空情况
  if (!record[flag] || record[flag].length === 0) return;

  // 获取当前产品信息
  const currentProductId = getCurrentProductId(record);
  const currentProduct = getCurrentProduct(productTreeList, currentProductId);
  if (!currentProduct) return;

  // 找出当前产品对应的全部包
  const packageList = currentProduct.children || [];

  // 确定类型
  const type = getRecordType(record);
  if (!type) return;

  // 查找依赖项
  const dependenceObjs = getDependencyObjects(packageList, record, flag, type);
  if (dependenceObjs.length === 0) return;

  // 部分依赖处理
  if (flag === 'RELY_RECORD') {
    if (record.checked) return;

    if (dependenceObjs.length === 1) {
      handleSinglePartialDependency(that, record, dependenceObjs[0], currentProductId, flag);
    }

    const hasChecked = dependenceObjs.some(item => item.checked === true);
    if (!hasChecked) {
      handleMultiplePartialDependencies(that, record, currentProductId, flag);
    }
  }

  // 完全依赖处理
  if (flag === 'FULL_RELY_RECORD') {
    if (!record.checked) {
      const uncheckedDependencies = getDependencies(record, productTreeList, dependenceObjs, false);
      if (uncheckedDependencies.length > 0) {
        handleFullDependencyCheck(that, record, uncheckedDependencies, currentProductId, flag);
      }
    } else {
      const checkedDependencies = getDependencies(record, productTreeList, dependenceObjs, true);
      if (checkedDependencies.length > 0) {
        handleFullDependencyUncheck(that, record, checkedDependencies, currentProductId, flag);
      }
    }
  }
};

// 更新产品树状态的辅助函数
const updateTreeStatus = (that, currentProductId, updates) => {
  let updatedProductTreeList = JSON.parse(JSON.stringify(that.productTreeList));
  updates.forEach(({ target, checked }) => {
    updatedProductTreeList = updateProductTreeCheckedStatus(
      updatedProductTreeList,
      currentProductId,
      target,
      checked,
    );
  });
  return updatedProductTreeList;
};

// 处理部分依赖的单个依赖项
const handleSinglePartialDependency = (that, record, dependenceObj, currentProductId, flag) => {
  if (dependenceObj.checked) return;

  that.$confirm({
    content: that.$t('common.partialDependenceTip', {
      name: record[flag].map(item => item.NAME).join(', '),
    }),
    onOk: () => {
      that.productTreeList = updateTreeStatus(that, currentProductId, [
        { target: dependenceObj, checked: true },
        { target: record, checked: true },
      ]);
      that.checkBoxClick(record);
      setTimeout(() => {
        that.checkBoxClick(dependenceObj);
      }, 0);
    },
    onCancel() {
      // that.productTreeList = updateTreeStatus(that, currentProductId, [
      //   { target: record, checked: false },
      // ]);
    },
  });
  throw '1';
};

// 处理部分依赖的多个依赖项
const handleMultiplePartialDependencies = (that, record, currentProductId, flag) => {
  that.productTreeList = updateTreeStatus(that, currentProductId, [
    { target: record, checked: true },
  ]);
  that.checkBoxClick(record);
  that.tipsMessage = that.$t('common.partialDependenceTips', {
    name: record[flag].map(item => item.NAME).join(', '),
  });
  that.tipsVisible = true;
  throw '1';
};

// 处理完全依赖选中操作
const handleFullDependencyCheck = (that, record, uncheckedDependencies, currentProductId, flag) => {
  that.$confirm({
    content: that.$t('common.completeDependenceCheckTip', {
      name: uncheckedDependencies.map(item => item.NAME).join(', '),
    }),
    onOk: () => {
      const updates = [
        ...uncheckedDependencies.map(dep => ({ target: dep, checked: true })),
        { target: record, checked: true },
      ];
      that.productTreeList = updateTreeStatus(that, currentProductId, updates);
      parentCheckBoxClick(that, record, uncheckedDependencies);
    },
    onCancel() {
      const updates = [
        ...uncheckedDependencies.map(dep => ({ target: dep, checked: false })),
        { target: record, checked: false },
      ];
      that.productTreeList = updateTreeStatus(that, currentProductId, updates);
    },
  });
  throw '1';
};

// 处理完全依赖取消选中操作
const handleFullDependencyUncheck = (that, record, checkedDependencies, currentProductId, flag) => {
  that.$confirm({
    content: that.$t('common.completeDependenceCancelTip', {
      name: checkedDependencies.map(item => item.NAME).join(', '),
    }),
    onOk: () => {
      const updates = [
        ...checkedDependencies.map(dep => ({ target: dep, checked: false })),
        { target: record, checked: false },
      ];
      that.productTreeList = updateTreeStatus(that, currentProductId, updates);
    },
    onCancel() {
      that.productTreeList = updateTreeStatus(that, currentProductId, [
        { target: record, checked: true },
      ]);
      parentCheckBoxClick(that, record, checkedDependencies);
    },
  });
  throw '1';
};

// 获取依赖项
const getDependencyObjects = (packageList, record, flag, type) => {
  return findItemsByIds(
    packageList,
    record[flag].map(item => item.ID),
    type,
  );
};

const parentCheckBoxClick = function (that, record, dependencies) {
  // 先执行当前记录的校验
  that.checkBoxClick(record);

  // 然后依次执行所有依赖项的校验
  let index = 0;
  const executeCheck = () => {
    if (index < dependencies.length) {
      that.checkBoxClick(dependencies[index]);
      index++;
      setTimeout(executeCheck, 200);
    }
  };
  // 开始执行依赖项校验
  setTimeout(executeCheck, 200);
};

const isRebateOrMrc = function (record) {
  return record?.DISCNT_ITEM?.some(
    item =>
      item.ATTR_CODE === 'price_type' &&
      (item.ATTR_VALUE === 'Rebate' || item.ATTR_VALUE === 'MRC'),
    false,
  );
};

// 获取包内依赖项
const getPackageDependencies = (record, parentPackage, dependenceObjs, isChecked) => {
  if (!parentPackage?.children) return [];
  const packageChildrenKeys = parentPackage.children.map(child => child.key);
  // 判断是否为相同的MRC或Rebate
  const isSameMrcOrRebate = item => {
    // 如果不是MRC或Rebate，直接返回false
    if (!isRebateOrMrc(item)) return false;

    // 判断是否为Rebate元素
    const isRebate = item.DISCNT_ITEM?.some(
      x => x.ATTR_CODE === 'price_type' && x.ATTR_VALUE === 'Rebate',
    );

    if (isRebate) {
      // 找到rebate关联关系的Mrc
      const rebateMappingMrc = item.DISCNT_ITEM?.find(x => x.ATTR_CODE === 'rebate_mapping_mrc');
      // 如果找到关联的MRC，并且这个MRC的DISCNT_CODE和当前record的DISCNT_CODE相同，则过滤掉这个rebate的校验
      if (rebateMappingMrc && rebateMappingMrc.ATTR_VALUE === record.DISCNT_CODE) {
        return true;
      }
    }

    // 判断是否为相同元素（通过DISCNT_CODE或SERVICE_ID）
    return (
      (record.DISCNT_CODE && item.DISCNT_CODE === record.DISCNT_CODE) ||
      (record.SERVICE_ID && item.SERVICE_ID === record.SERVICE_ID)
    );
  };

  return dependenceObjs.filter(item => {
    const isInCurrentPackage = packageChildrenKeys.includes(item.key);
    const isSameElement = isSameMrcOrRebate(item);

    // 如果是当前包内的元素，正常检查
    if (isInCurrentPackage) {
      return item.checked === isChecked;
    }

    // 如果是其他包的元素，只有在不是相同MRC/Rebate的情况下才检查
    return !isSameElement && item.checked === isChecked;
  });
};

// 获取依赖项列表
//规则： 点击mrc或者rebate的时候，完全依赖只校验当前包下的rebate和mrc，针对其他包下面的完全依赖项，如果为mrc和rebate则不做校验，否则都校验
const getDependencies = (record, productTreeList, dependenceObjs, isChecked) => {
  const rebateOrMrc = isRebateOrMrc(record);
  if (rebateOrMrc) {
    const parentPackage = findParent(productTreeList, record);
    return getPackageDependencies(record, parentPackage, dependenceObjs, isChecked);
  }
  return dependenceObjs.filter(item => item.checked === isChecked);
};
