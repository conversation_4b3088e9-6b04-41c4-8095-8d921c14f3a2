import { dependenceValidate } from '@/views/components/productTreeList/validate/dependence';
import { mutexValidate } from '@/views/components/productTreeList/validate/mutex';
import { minMinValidate } from '@/views/components/productTreeList/validate/maxMin';
import { mrcAndRebateValidate } from '@/views/components/productTreeList/validate/mrcAndRebate';
import { huntingPilotValidate } from '@/views/components/productTreeList/validate/huntingPilot';

// 互斥限制
export const mutexLimit = function (record, productTreeList, that) {
  mutexValidate(record, productTreeList, that);
};

// 依赖限制
export const dependenceLimit = function (record, productTreeList, that, flag) {
  dependenceValidate(record, productTreeList, that, flag);
};

// 最大最小值限制
export const maxMinNumberLimit = function (record, that) {
  minMinValidate(record, that);
};

export const mrcAndRebateLimit = function (record, that) {
  mrcAndRebateValidate(record, that);
};

// 完整的校验规则
export const validateProductAllRules = function (record, productTreeList, that) {
  mutexValidate(record, productTreeList, that);
  dependenceValidate(record, productTreeList, that, 'RELY_RECORD');
  dependenceValidate(record, productTreeList, that, 'FULL_RELY_RECORD');
  minMinValidate(record, that);
  mrcAndRebateValidate(record, that);
  console.log(8888, that.isHuntingInstallPilot);
  if (!that.isHuntingInstallPilot) {
    huntingPilotValidate(record, that);
  }
};
