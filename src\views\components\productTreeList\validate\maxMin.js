export const minMinValidate = function (record, that) {
  // 产品层级没有限制
  if (record.type != 'Product') {
    // 获取父级
    const parent = that.findParent(record);

    // 获取 MIN_NUMBER 和 MAX_NUMBER
    const min = parseInt(parent.MIN_NUMBER, 10);
    const max = parseInt(parent.MAX_NUMBER, 10);

    // 如果 MIN_NUMBER 且 MAX_NUMBER 为 -1，表示没有限制
    const hasLimit = min !== -1 || max !== -1; // const hasLimit = !(min == -1 && max == -1);
    if (hasLimit) {
      const selectedCount = parent.children.filter(x => x.checked).length;
      // !record.checked   // 未勾选状态（准备勾选上前作判断）
      // max !== -1        // 有限制时才判断
      // selectedCount + 1 // 已选择的数量 + 1(准备选的一个)
      if (!record.checked && max !== -1 && selectedCount + 1 > max) {
        that.tipsMessage = that.$t('customerVerify.cannotMoreThan', {
          name: parent.NAME,
          num: max,
          selectedCount: selectedCount,
        });
        that.tipsVisible = true;
        throw '1';
      }
    }
  }
};
