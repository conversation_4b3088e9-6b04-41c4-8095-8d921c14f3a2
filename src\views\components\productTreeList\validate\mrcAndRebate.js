export const mrcAndRebateValidate = function (record, that) {
  // 情况一、如果修改了MRC的默认值，则Rebate不能勾选了
  if (record.type == 'Pricing') {
    // 找到Rebate对应的MRC（对应的关联关系）
    const obj = (record.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
    if (obj && record.DISCNT_CODE != obj.ATTR_VALUE) {
      const parentPackage = that.findParent(record);
      // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
      const mrcElement = parentPackage?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
      if (mrcElement) {
        // MRC元素是否勾选
        if (mrcElement.checked) {
          if (!record.checked) {
            // 找到哪项是mrc
            let mrcObj = (mrcElement?.interfaceElementList || []).find(
              x => x.elementCode == 'rent_fee',
            );
            if (mrcObj) {
              // 对比mrc的值是否修改过
              if (mrcObj[mrcObj.elementCode] != mrcObj.intfElementInitValue) {
                that.tipsMessage = that.$t('macd.mrcValueUpdateNotChooseRebate', {
                  name: mrcElement.NAME,
                });
                that.tipsVisible = true;
                throw '1';
              }
            }
          }
        } else {
          // 当前元素未勾选的状态下 才校验
          if (!record.checked) {
            // 情况二、不能只选择Rebate，而不选择对应的MRC，提示先去现在MRC
            that.tipsMessage = that.$t('macd.needToChooseMrc', {
              name: mrcElement.NAME,
            });
            that.tipsVisible = true;
            throw '1';
          }
        }
      } else {
        // 情况三、找不到MRC元素，不能勾选Rebate
        that.tipsMessage = that.$t('macd.needToChooseMrc', {
          name: obj.ATTR_VALUE || '',
        });
        that.tipsVisible = true;
        throw '1';
      }
    }
  }
};
