import { getCurrentProduct, getAllElements, getCurrentProductId } from './utils';

export const mutexValidate = (record, productTreeList, that) => {
  // 如果是取消勾选，不执行互斥校验
  if (record.checked) {
    return;
  }

  // 获取当前产品信息
  const currentProductId = getCurrentProductId(record);
  const currentProduct = getCurrentProduct(productTreeList, currentProductId);
  if (!currentProduct) return;

  // 获取包列表和元素列表
  const packageList = currentProduct.children || [];
  const elementList = getAllElements(packageList);

  // 获取互斥记录
  const mutexList = record.MUTEX_RECORD || [];
  if (mutexList.length === 0) return;

  // 根据类型执行相应的互斥检查
  if (record.type === 'Package') {
    checkPackageMutex(packageList, mutexList, that);
  } else if (['Pricing', 'Vas'].includes(record.type)) {
    checkElementMutex(elementList, mutexList, that);
  }
};

// 处理互斥冲突
const handleMutexConflict = (that, conflictItem) => {
  that.tipsMessage = that.$t('common.mutexTips', {
    name: conflictItem.NAME,
  });
  that.tipsVisible = true;
  throw '1';
};

// 检查包级别的互斥
const checkPackageMutex = (packageList, mutexList, that) => {
  for (const pkg of packageList) {
    for (const mutexItem of mutexList) {
      if (pkg.PACKAGE_ID == mutexItem.ID && pkg.checked) {
        handleMutexConflict(that, pkg);
      }
    }
  }
};

// 检查元素级别的互斥
const checkElementMutex = (elementList, mutexList, that) => {
  for (const element of elementList) {
    for (const mutexItem of mutexList) {
      if (
        (element.DISCNT_CODE == mutexItem.ID || element.SERVICE_ID == mutexItem.ID) &&
        element.checked
      ) {
        handleMutexConflict(that, element);
      }
    }
  }
};
