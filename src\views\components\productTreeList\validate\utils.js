import that from '@/main.js';

// 获取当前产品信息
export const getCurrentProduct = (productTreeList, currentProductId) => {
  const list = JSON.parse(JSON.stringify(productTreeList));
  return list.find(item => (item.ID || item.PRODUCT_ID) == currentProductId);
};

// 获取所有元素列表
export const getAllElements = packageList => {
  let elementList = [];
  packageList.forEach(pkg => {
    elementList = elementList.concat(pkg.children || []);
  });
  return elementList;
};

// 获取当前产品ID
export const getCurrentProductId = record => {
  return record.key.split('-')[0];
};

// 获取记录类型
export const getRecordType = record => {
  return ['Pricing', 'Vas'].includes(record.type)
    ? 'element'
    : record.type === 'Package'
    ? 'Package'
    : undefined;
};

// 校验每个号码的产品（主，附加产品）里面的元素的邮箱，需要每个号码的邮箱都不相同
export const emailUnique = numberList => {
  let emailList = [];
  numberList.forEach(item => {
    item.productList.forEach(product => {
      // 将所有元素遍历出来
      let elementList = getAllElements(product.children);
      // 找到哪些元素是有邮箱的
      let elementObj = elementList.find(element => ['7CAE', '7SLN', '7EFE'].includes(element.PSEF));
      // 找到对应的元素
      if (elementObj) {
        // 邮箱前缀
        let beforeAttrObj = (elementObj?.interfaceElementList || []).find(
          x => x.elementCode == 'COE1',
        );
        // 邮箱后缀
        let afterAttrObj = (elementObj?.interfaceElementList || []).find(
          x => x.elementCode == 'COE2',
        );
        // 找到对应元素的属性
        if (beforeAttrObj && afterAttrObj) {
          emailList.push({
            // 号码
            serviceNo: item.serviceNo,
            // 邮箱 由两个字段拼接成
            value: `${beforeAttrObj[beforeAttrObj.elementCode]}${
              afterAttrObj[afterAttrObj.elementCode]
            }`,
          });
        }
      }
    });
  });

  // 找出相同的邮箱的号码进行提示
  let sameList = findDuplicateValues(emailList);
  sameList.forEach(y => {
    if (y.value) {
      that.$message.error(`${y.serviceNos.join(',')}${that.$t('common.cannotSame')}`);
      throw '1';
    }
  });
};

// 找出哪些号码是有重复邮箱的数据
export const findDuplicateValues = data => {
  const groupedByValue = data.reduce((acc, item) => {
    if (!acc[item.value]) acc[item.value] = [];
    acc[item.value].push(item.serviceNo);
    return acc;
  }, {});

  return Object.entries(groupedByValue)
    .filter(([_, serviceNos]) => serviceNos.length > 1)
    .map(([value, serviceNos]) => ({ value, serviceNos }));
};
