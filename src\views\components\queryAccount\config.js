import that from '@/main.js';

export default {
  columns: [
    {
      title: 'Sel',
      width: 60,
      scopedSlots: { customRender: 'Sel' },
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_NO',
      key: 'ACCOUNT_NO',
      width: 170,
      ellipsis: true,
      scopedSlots: { customRender: 'ACCOUNT_NO' },
    },
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'ACCOUNT_NAME' },
    },
    {
      title: that.$t('accountSetting.productFamily'),
      dataIndex: 'PRODUCT_FAMILY_NAME',
      key: 'PRODUCT_FAMILY_NAME',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'PRODUCT_FAMILY_NAME' },
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'BILL_DAY' },
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA_NAME',
      key: 'BILL_MEDIA_NAME',
      width: 150,
      ellipsis: true,
      scopedSlots: { customRender: 'BILL_MEDIA_NAME' },
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD_NAME',
      key: 'PAYMENT_METHOD_NAME',
      width: 150,
      fixed: 'right',
      ellipsis: true,
      scopedSlots: { customRender: 'PAYMENT_METHOD_NAME' },
    },
  ],
};
