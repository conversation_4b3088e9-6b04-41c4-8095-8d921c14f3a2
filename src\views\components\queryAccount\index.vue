<template>
  <PopWindow
    :visible="visible"
    :title="$t('accountSetting.queryAccount')"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    :bodyStyle="bodyStyle"
    :footer="true"
    modalWidth="880px"
  >
    <template #Content>
      <div class="search-container">
        <a-form-model
          :model="form"
          v-bind="layout"
          :colon="false"
          ref="searchForm"
          class="commonFormModel"
        >
          <a-row :gutter="24" justify="start" type="flex">
            <a-col :span="6" flex="flex-start">
              <a-form-model-item :label="$t('accountSetting.billingAccountNo')">
                <a-input
                  v-validate-number="14"
                  v-model.trim="form.ACCOUNT_NO"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item :label="$t('accountSetting.accountName')">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="form.ACCOUNT_NAME"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item :label="$t('accountSetting.AGN')">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="form.AGN"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item :label="$t('accountSetting.billEmail')">
                <a-input
                  v-validateEmailMore
                  v-model.trim="form.BILL_EMAIL"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24" class="b-btns">
              <div class="btns-container">
                <a-button type="primary" class="search-button" @click="handleQueryAccount">{{
                  $t('common.buttonInquiry')
                }}</a-button>
              </div>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <div class="table-container" v-if="isMultiple">
        <a-table
          rowKey="ACCOUNT_NO"
          :columns="columns"
          :loading="loading"
          :data-source="tableData"
          :pagination="false"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onRowChange,
            onSelect: onRowSelect,
            onSelectAll: onRowSelectAll,
            getCheckboxProps: handleGetCheckboxProps,
          }"
        >
        </a-table>
      </div>

      <div class="table-container" v-else>
        <a-table
          rowKey="discntCode"
          :columns="columns"
          :loading="loading"
          :data-source="tableData"
          :pagination="false"
          :scroll="{ x: true }"
        >
          <span slot="Sel" slot-scope="text, record, index">
            <a-radio
              @change="handleRadioChange(record, index)"
              :value="index"
              :checked="radioValue == index"
            />
          </span>
          <span slot="ACCOUNT_NO" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
          <span slot="ACCOUNT_NAME" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
          <span slot="PRODUCT_FAMILY_NAME" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
          <span slot="BILL_DAY" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
          <span slot="BILL_MEDIA_NAME" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
          <span slot="PAYMENT_METHOD_NAME" slot-scope="text, record, index">
            <EllipsisPopover :text="text || ''" />
          </span>
        </a-table>
      </div>
    </template>
    <template #footer>
      <a-button class="modal-button-cancel" @click="handleCancel">
        {{ $t('common.buttonCancel') }}
      </a-button>
      <a-button type="primary" @click="handleOk" class="moadl-button-Ok">
        {{ $t('common.buttonConfirm') }}
      </a-button>
    </template>
  </PopWindow>
</template>
<script>
  import { qryAccountList } from '@/api/accountSetting';
  import PopWindow from '@/components/popWindow';
  import { tableOnRowSelect, tableOnRowSelectAll } from '@/utils/utils';
  import EllipsisPopover from '@/views/components/ellipsisPopover';
  import { mapState } from 'vuex';
  import config from './config';
  export default {
    name: 'queryAccount',
    components: { PopWindow, EllipsisPopover },
    props: {
      accountTableDataNos: {
        type: Array,
        default() {
          return [];
        },
      },
      isMultiple: {
        type: Boolean,
        default: false,
      },
      visible: {
        type: Boolean,
        default: false,
      },
      isNewInstallFlag: {
        type: Boolean,
        default: false,
      },
      CLASS_LEVEL: {
        type: Number,
        default: 3,
      },
      title: {
        type: String,
        default: '',
      },
      transferOwnershipCustId: {
        type: String,
        default: '',
      },
      accountType: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        form: {},
        loading: false, // 表格加载标识符
        columns: config.columns,
        tableData: [],
        confirmLoading: false,
        spinning: false,
        bodyStyle: {
          height: '350px',
          padding: '10px',
          overflow: 'auto',
        },
        layout: {},
        radioValue: 0,
        accountArr: {},
        selectedRows: [],
        selectedRowKeys: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
        // productFamily: state => state.productFamily,
      }),
      // ...mapState('macd', {
      //   selectedOrderList: state => state.selectedOrderList,
      // }),
      // ...mapState('orderQuery', {
      //   currentProductFamily: state => state.product_family,
      // }),
    },
    mounted() {
      this.handleQueryAccount();
    },
    methods: {
      handleRadioChange(row, index) {
        this.radioValue = index;
        this.accountArr = row;
      },
      handleCancel() {
        this.$emit('cancel');
      },
      handleOk() {
        console.log(this.selectedRows, 'this.selectedRows---------------111');

        if (this.selectedRows.length || Object.keys(this.accountArr).length > 0) {
          if (this.isMultiple) {
            this.$emit('Ok', this.selectedRows);
          } else {
            this.$emit('Ok', this.accountArr);
          }
        } else {
          this.$message.error(this.$t('accountSetting.pleaseChooseOneOfDatas'));
        }
      },
      getProductFamily() {
        if (this.accountType == 'newInstall') {
          return this.productFamily?.value;
        } else if (this.accountType == 'macd') {
          return this.selectedOrderList && this.selectedOrderList[0]?.PRODUCT_FAMILY;
        } else if (this.accountType == 'order') {
          return this.currentProductFamily;
        }
        return null;
      },
      // 查询账户数据
      async handleQueryAccount() {
        this.loading = true;
        this.radioValue = 0;
        this.accountArr = {};
        let parmas = {};

        console.log('this.transferOwnershipCustId=======>', this.transferOwnershipCustId);

        console.log('this.custId=======>', this.custId);

        // 过户 查询使用新客户的名称查询
        if (this.transferOwnershipCustId !== '') {
          parmas = {
            CUSTOMER_NO: this.transferOwnershipCustId,
            ...this.form,
          };
        } else {
          parmas = {
            CUSTOMER_NO: this.custId,
            ...this.form,
          };
        }
        let productFamily = this.getProductFamily();
        productFamily = 'UCS'; // TODO
        if (productFamily) {
          try {
            const res = await qryAccountList({
              ...parmas,
              PAGE_SIZE: 999,
              PAGE_NUM: 1,
            });
            if (res.DATA[0].LIST) {
              // 空账户阻断
              if (res?.DATA[0]?.LIST[0]?.ACCOUNT_NO) {
                this.tableData = res.DATA[0].LIST ?? [];
                if (this.tableData && productFamily) {
                  this.tableData = this.tableData.filter(item => {
                    return item.PRODUCT_FAMILY == productFamily;
                  });
                }
                this.accountArr = this.tableData[0];
              }
            } else {
              this.tableData = [];
              this.accountArr = {};
            }
          } catch (error) {
            console.log('查询失败', error);
          }
        }
        this.loading = false;
      },
      handleGetCheckboxProps(record) {
        if (this.accountTableDataNos.includes(record.ACCOUNT_NO)) {
          return {
            props: { disabled: true },
          };
        }
        return {
          props: { disabled: false },
          style: {},
        };
      },
      // 账户信息表格行点击事件
      onRowChange(selectedKeys) {
        this.selectedRowKeys = selectedKeys;
      },
      onRowSelect(record, selected) {
        this.selectedRows = tableOnRowSelect(this.selectedRows, 'discntCode', record, selected);
      },
      onRowSelectAll(selected, _, changeRows) {
        this.selectedRows = tableOnRowSelectAll(
          this.selectedRows,
          'discntCode',
          selected,
          changeRows,
        );
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .ant-row-flex {
    margin-right: 0 !important;
  }
  /deep/ .ant-form-item-label {
    margin-bottom: -5px !important;
  }
  .search-container {
    .b-btns {
      padding-right: 12px !important;
      .btns-container {
        height: 100%;
        padding-bottom: 18px;
        display: flex;
        align-items: end;
        justify-content: end;
      }
    }
  }

  .moadl-button-Ok {
    padding: 0;
  }
</style>
