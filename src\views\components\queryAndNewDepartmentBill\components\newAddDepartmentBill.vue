<template>
  <div>
    <a-form-model ref="ruleForm" :model="ruleForm" :rules="rules" :colon="false">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-model-item :label="$t('accountSetting.departmentalName')" prop="DEPARTMENT_NAME">
            <a-input
              v-model="ruleForm.DEPARTMENT_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput"
            />
          </a-form-model-item>
        </a-col>
        <!-- <a-col :span="8">
          <a-form-model-item :label="$t('accountSetting.serviceType')" prop="SERVICE_TYPE">
            <a-select v-model="ruleForm.SERVICE_TYPE" :placeholder="$t('common.selectPlaceholder')">
              <a-select-option
                v-for="item in serviceTypeOptions"
                :key="item.CODE_VALUE"
                :value="item.CODE_VALUE"
              >
                {{ item.CODE_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col> -->
      </a-row>
      <!-- <a-row :gutter="16">
        <a-col :span="24">
          <a-form-model-item :label="$t('accountSetting.remark')" class="textAreaBorder">
            <a-textarea
              class="textArea"
              v-model="ruleForm.REMARK"
              :placeholder="$t('common.inputPlaceholder')"
              :rows="3"
            />
          </a-form-model-item>
        </a-col>
      </a-row> -->
    </a-form-model>
    <!-- 底部按钮 -->
    <div class="footBtn">
      <a-button class="btn-space" @click="handleCancel">{{ $t('common.buttonCancel') }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.buttonConfirm') }}</a-button>
    </div>
  </div>
</template>
<script>
  import config from '../config';
  import { mapState } from 'vuex';
  import { addDepartmentBill } from '@/api/accountSetting';
  import { queryStaticData } from '@/api/customerVerify';
  export default {
    name: 'newAddDepartmentBill',
    data() {
      return {
        ruleForm: {
          SERVICE_TYPE: undefined,
        },
        rules: config.newAddDepartmentBillRules,
        // serviceTypeOptions: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
    },
    mounted() {
      // this.queryDeptBillServiceTypeFunc();
    },
    methods: {
      handleInput(event) {
        // 获取当前输入值
        let value = event.target.value;
        // 使用正则表达式替换多个连续空格为单个空格
        // 替换多个连续空格为单个空格，但保留换行符
        value = value.replace(/ {2,}/g, ' ');
        // 去掉开头的空格（不影响换行）
        if (value.startsWith(' ')) {
          value = value.trimStart();
        }

        // 如果输入值的第一个字符是空格，去掉它
        if (value.startsWith(' ')) {
          value = value.substring(1);
        }
        // 更新 formData 中对应的字段
        this.$set(this.ruleForm, 'DEPARTMENT_NAME', value);
        // 同时更新原生输入框的值，防止用户看到多余空格
        event.target.value = value;
      },
      handleCancel() {
        this.$emit('changePage', 'QueryDepartmentBillPage');
      },
      // 新增数据
      async handleSubmit() {
        let params = {
          ...this.ruleForm,
          CUST_ID: this.custId,
          CREATE_STAFF_ID: this.userInfo.STAFF_ID, // 登录人ID
          CREATE_STAFF_NAME: this.userInfo.STAFF_NAME, // 登陆人姓名
        };
        try {
          await addDepartmentBill(params);
          this.$emit('ok', params);
        } catch (error) {
          console.log('新增失败' + error);
        }
      },
      handleOk() {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            this.handleSubmit();
          } else {
            return false;
          }
        });
      },
      // // 查询部门账单下拉选项
      // async queryDeptBillServiceTypeFunc() {
      //   try {
      //     const res = await queryStaticData({ CODE_TYPE: 'DEPARTMENT_BILL_TYPE' });
      //     this.serviceTypeOptions = res?.DATA ?? [];
      //   } catch (error) {
      //     console.log(error);
      //   }
      // },
    },
  };
</script>
<style lang="less" scoped>
  .footBtn {
    display: flex;
    justify-content: end;
    padding: 12px 0;
    .btn-space {
      margin-right: 10px;
    }
  }
</style>
