<template>
  <div class="page">
    <div class="search-container">
      <a-form-model :model="form" v-bind="layout" :colon="false" layout="vertical" ref="searchForm">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-model-item :label="$t('accountSetting.departmentalName')">
              <a-input
                v-containsSqlInjection
                v-model.trim="form.DEPARTMENT_NAME"
                :placeholder="$t('common.inputPlaceholder')"
              />
            </a-form-model-item>
          </a-col>
          <!-- <a-col :span="8">
            <a-form-model-item :label="$t('accountSetting.serviceType')">
              <a-select v-model="form.SERVICE_TYPE" :placeholder="$t('common.selectPlaceholder')">
                <a-select-option
                  v-for="item in serviceTypeOptions"
                  :key="item.CODE_VALUE"
                  :value="item.CODE_VALUE"
                >
                  {{ item.CODE_NAME }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col> -->
          <a-col :span="16" class="b-btns">
            <div class="btns-container">
              <a-button type="primary" class="search-button" @click="handleQuery">
                {{ $t('common.buttonInquiry') }}
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-form-model>
      <div class="main-search-divider" style="margin-bottom: 10px"><a-divider dashe /></div>
      <a-table :columns="columns" :loading="loading" :data-source="tableData" :pagination="false">
        <span slot="Sel" slot-scope="text, record, index">
          <a-radio
            @change="handleRadioChange(record, index)"
            :value="index"
            :checked="radioValue == index"
          />
        </span>
      </a-table>
    </div>
    <!-- 底部按钮 -->
    <div class="footBtn">
      <a-button class="btn-space" @click="handleCancel">{{ $t('common.buttonCancel') }}</a-button>
      <a-button class="btn-space" @click="addDepartmentalBill">{{
        $t('accountSetting.addBillDepartment')
      }}</a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.buttonConfirm') }}</a-button>
    </div>
  </div>
</template>
<script>
  import config from '../config';
  import { mapState } from 'vuex';
  import { queryDepartmentBill } from '@/api/accountSetting';
  import { queryStaticData } from '@/api/customerVerify';
  export default {
    name: 'queryDepartmentBill',
    data() {
      return {
        form: {},
        loading: false, // 表格加载标识符
        columns: config.queryDepBillColumns,
        tableData: [],
        layout: {},
        radioValue: 0,
        // serviceTypeOptions: [],
        departmentBillObj: {},
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
      ...mapState('macd', {
        transferCustInfo: state => state.transferCustInfo,
      }),
    },
    mounted() {
      this.handleQuery();
      // this.queryDeptBillServiceTypeFunc();
    },
    methods: {
      handleOk() {
        this.$emit('ok', this.departmentBillObj);
      },
      // 部门账单查询
      async handleQuery() {
        this.departmentBillObj = {};
        this.loading = true;
        let params = {
          ...this.form,
          CUST_ID: this.custId,
          PAGE: this.pagination?.current ?? 1,
          PAGE_SIZE: this.pagination?.pageSize ?? 10,
        };

        // 过户使用新客户id查询
        if (this.$route.name === 'transferOwnership') {
          params.CUST_ID = this.transferCustInfo.CUSTOMER_NO;
        }

        try {
          const res = await queryDepartmentBill(params);
          this.tableData = res.DATA[0].LIST;
          this.departmentBillObj = this.tableData[0];
          this.radioValue = 0;
        } catch (error) {
          console.log(error);
        }
        this.loading = false;
      },
      // // 查询部门账单下拉选项
      // async queryDeptBillServiceTypeFunc() {
      //   try {
      //     const res = await queryStaticData({ CODE_TYPE: 'DEPARTMENT_BILL_TYPE' });
      //     this.serviceTypeOptions = res?.DATA ?? [];
      //   } catch (error) {
      //     console.log(error);
      //   }
      // },
      handleRadioChange(row, index) {
        this.radioValue = index;
        this.departmentBillObj = row;
      },
      handleCancel() {
        this.$emit('cancel');
      },
      addDepartmentalBill() {
        this.$emit('changePage', 'NewAddDepartmentBillPage');
      },
    },
  };
</script>
<style lang="less" scoped>
  .b-btns {
    .btns-container {
      height: 74px;
      padding-bottom: 12px;
      display: flex;
      align-items: end;
      justify-content: end;
    }
  }
  .footBtn {
    display: flex;
    justify-content: end;
    padding: 12px 0;
    .btn-space {
      margin-right: 10px;
    }
  }
  /deep/ .ant-table-tbody > tr > td {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
</style>
