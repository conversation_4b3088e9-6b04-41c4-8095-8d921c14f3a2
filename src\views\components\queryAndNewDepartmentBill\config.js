import that from '@/main.js';

export default {
  queryDepBillColumns: [
    {
      title: 'Sel',
      scopedSlots: { customRender: 'Sel' },
    },
    {
      title: that.$t('accountSetting.departmentalBill'),
      dataIndex: 'DEPARTMENT_INDEX',
      key: 'DEPARTMENT_INDEX',
      width: 150,
    },
    {
      title: that.$t('accountSetting.departmentalName'),
      dataIndex: 'DEPARTMENT_NAME',
      key: 'DEPARTMENT_NAME',
    },
    // {
    //   title: that.$t('accountSetting.serviceType'),
    //   dataIndex: 'SERVICE_TYPE',
    //   key: 'SERVICE_TYPE',
    // },
  ],
  newAddDepartmentBillRules: {
    DEPARTMENT_NAME: [{ required: true, message: 'Please Enter！' }],
    // SERVICE_TYPE: [{ required: true, message: 'Please Select！' }],
  },
};
