<template>
  <div class="page">
    <!-- 查询部门账单 -->
    <QueryDepartmentBill
      v-if="showModel == 'QueryDepartmentBillPage'"
      @ok="handleDepartmentBillInfo"
      @changePage="changeModelShow"
      @cancel="handleCancel"
    />

    <!-- 新增部门账单 -->
    <NewAddDepartmentBill
      v-else-if="showModel == 'NewAddDepartmentBillPage'"
      @ok="handleDepartmentBillInfo"
      @changePage="changeModelShow"
    />
  </div>
</template>
<script>
  import { mapState } from 'vuex';
  import QueryDepartmentBill from './components/queryDepartmentBill.vue';
  import NewAddDepartmentBill from './components/newAddDepartmentBill.vue';
  export default {
    components: {
      QueryDepartmentBill,
      NewAddDepartmentBill,
    },
    data() {
      return {
        showModel: 'QueryDepartmentBillPage',
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
      }),
    },
    methods: {
      handleCancel() {
        this.$emit('cancel');
      },
      handleDepartmentBillInfo(departmentBillObj) {
        this.$emit('confirm', departmentBillObj);
      },
      // 切换弹窗
      changeModelShow(showPageTitle) {
        if (showPageTitle == 'QueryDepartmentBillPage') {
          this.$emit('changeTilte', this.$t('accountSetting.queryDepartmentBill'));
        } else {
          this.$emit('changeTilte', this.$t('accountSetting.addBillDepartment'));
        }
        this.showModel = showPageTitle;
      },
    },
  };
</script>
<style lang="less" scoped>
  .b-btns {
    .btns-container {
      height: 74px;
      padding-bottom: 12px;
      display: flex;
      align-items: end;
      justify-content: end;
    }
  }
</style>
