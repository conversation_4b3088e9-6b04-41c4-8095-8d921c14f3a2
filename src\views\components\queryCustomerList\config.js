import i18n from '@/i18n/index';

// 客户识别-客户选择
export const columns1 = [
  {
    title: i18n.t('customerVerify.Sel'),
    dataIndex: 'Sel',
    key: 'Sel',
    // width: 60,
    // ellipsis: true,
    scopedSlots: { customRender: 'Sel' },
  },
  {
    title: i18n.t('customerVerify.CustomerName'),
    dataIndex: 'CUSTOMER_NAME',
    key: 'CUSTOMER_NAME',
    width: 250,
    ellipsis: true,
    scopedSlots: { customRender: 'CUSTOMER_NAME' },
  },
  {
    title: i18n.t('customerVerify.CustomerNo'),
    dataIndex: 'CUSTOMER_NO',
    key: 'CUSTOMER_NO',
    width: 180,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.DocumentType'),
    dataIndex: 'DOCUMENT_TYPE',
    key: 'DOCUMENT_TYPE',
    width: 150,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.DocumentNo'),
    dataIndex: 'DOCUMENT_NO',
    key: 'DOCUMENT_NO',
    width: 150,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.SalesTeam'),
    dataIndex: 'SALES_TEAM',
    key: 'SALES_TEAM',
    width: 150,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.SalesSubTeam'),
    dataIndex: 'SALES_SUB_TEAM',
    key: 'SALES_SUB_TEAM',
    fixed: 'right',
    width: 150,
    ellipsis: true,
    scopedSlots: { customRender: 'SALES_SUB_TEAM' },
  },
];

// 报价单填写，客户选择
export const columns2 = [
  {
    title: i18n.t('customerVerify.Sel'),
    dataIndex: 'Sel',
    key: 'Sel',
    width: 70,
    scopedSlots: { customRender: 'Sel' },
  },
  {
    title: i18n.t('customerVerify.CustomerName'),
    dataIndex: 'CUSTOMER_NAME',
    key: 'CUSTOMER_NAME',
    width: 160,
    ellipsis: true,
    scopedSlots: { customRender: 'CUSTOMER_NAME' },
  },
  {
    title: i18n.t('customerVerify.MarketSegment'),
    dataIndex: 'MARKET_SEGMENT', //市场细分
    key: 'MARKET_SEGMENT',
    width: 160,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.CustomerNo'),
    dataIndex: 'CUSTOMER_NO', //客户编码
    key: 'CUSTOMER_NO',
    width: 180,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.HKTCustomerNo'),
    dataIndex: 'DRAGON_CUSTOMER_NO',
    key: 'DRAGON_CUSTOMER_NO',
    width: 160,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.AM'),
    dataIndex: 'AM',
    key: 'AM',
    width: 160,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.ASM'),
    dataIndex: 'ASM',
    key: 'ASM',
    width: 160,
    ellipsis: true,
  },

  {
    title: i18n.t('customerVerify.SalesTeam'),
    dataIndex: 'SALES_TEAM',
    key: 'SALES_TEAM',
    width: 160,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.SalesSubTeam'),
    dataIndex: 'SALES_SUB_TEAM',
    key: 'SALES_SUB_TEAM',
    width: 150,
    ellipsis: true,
  },
  {
    title: i18n.t('customerVerify.LOB'),
    dataIndex: 'LOB_NAME',
    key: 'LOB_NAME',
    fixed: 'right',
    width: 150,
    ellipsis: true,
  },
];
