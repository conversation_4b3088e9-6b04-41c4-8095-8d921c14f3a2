<template>
  <PopWindow
    :visible="visible"
    :title="title"
    @cancel="handleCancel"
    :bodyStyle="bodyStyle"
    :footer="true"
    modalWidth="880px"
  >
    <template #Content>
      <div class="search-container">
        <a-form-model
          :model="formData"
          v-bind="layout"
          :colon="false"
          layout="vertical"
          ref="searchForm"
        >
          <a-row :gutter="24" justify="start" type="flex">
            <a-col :span="6" flex="flex-start">
              <a-form-model-item
                :label="$t('customerVerify.CustomerNo')"
                prop="CUSTOMER_NO"
                labelAlign="left"
              >
                <a-input
                  v-validate-number="20"
                  v-model="formData.CUSTOMER_NO"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item
                :label="$t('customerVerify.CustomerName')"
                prop="CUSTOMER_NAME"
                labelAlign="left"
              >
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.CUSTOMER_NAME"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item
                :label="$t('customerVerify.DragonCustomerNo')"
                prop="DRAGON_CUSTOMER_NO"
                labelAlign="left"
              >
                <a-input
                  v-containsSqlInjection
                  v-model="formData.DRAGON_CUSTOMER_NO"
                  :placeholder="$t('common.inputPlaceholder')"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" v-if="!isCustomerCertificate">
              <a-form-model-item :label="$t('customerVerify.LOB')" prop="LOB">
                <a-select
                  v-model="formData.LOB"
                  :placeholder="$t('common.selectPlaceholder')"
                  @change="handleLOBChange"
                  allowClear
                >
                  <a-select-option v-for="item in lobList" :key="item.LOB_NAME" :value="item.LOB">
                    {{ item.LOB_NAME }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="6" class="b-btns1" v-if="isCustomerCertificate">
              <div class="btns-container">
                <a-form-model-item>
                  <a-button
                    :loading="spinning"
                    type="primary"
                    @click="queryList('init')"
                    class="search-button"
                  >
                    {{ $t('common.buttonFilter') }}
                  </a-button>
                </a-form-model-item>
              </div>
            </a-col>
          </a-row>
          <a-row :gutter="24" justify="end" type="flex">
            <a-col :span="6" class="b-btns2" v-if="!isCustomerCertificate">
              <div class="btns-container">
                <a-form-model-item>
                  <a-button
                    :loading="spinning"
                    type="primary"
                    @click="queryList('init')"
                    class="search-button"
                  >
                    {{ $t('common.buttonFilter') }}
                  </a-button>
                </a-form-model-item>
              </div>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <!-- <div class="secondLevel-header-title">
        {{ $t('customerVerify.CustomerList') }}
      </div> -->
      <div class="table-container">
        <a-spin :spinning="spinning">
          <a-table
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            :rowKey="(record, index) => `${record.CUSTOMER_NO}`"
            :scroll="{ x: 1125, y: 210 }"
          >
            <span slot="Sel" slot-scope="text, record, index">
              <a-radio @change="handleRadioChange" :value="index" :checked="radioValue == index" />
            </span>
            <span slot="CUSTOMER_NAME" slot-scope="text, record">
              <a-tooltip placement="topLeft">
                <template slot="title">
                  <span>{{ text }}</span>
                </template>
                {{ text }}
              </a-tooltip>
            </span>
            <span slot="SALES_SUB_TEAM" slot-scope="text, record" class="active-button">
              {{ text }}
            </span>
            <span slot="LOB" slot-scope="text, record" class="active-button">
              {{ text }}
            </span>
            <template #footer>
              <div class="table-pagination">
                <div class="table-pagination-total">{{ pagination.total }} items</div>
                <a-pagination
                  :total="pagination.total"
                  :pageSizeOptions="pagination.pageSizeOptions"
                  @change="pagination.onChange"
                  showQuickJump
                  showSizeChanger
                  showQuickJumper
                  @showSizeChange="pagination.showSizeChange"
                  v-model="pagination.current"
                  :defaultPageSize="pagination.defaultPageSize"
                >
                  <template slot="buildOptionText" slot-scope="props">
                    <span>{{ props.value }}items/pages</span>
                  </template>
                </a-pagination>
              </div>
            </template>
          </a-table>
        </a-spin>
      </div>
    </template>
    <template #footer>
      <a-button class="modal-button-cancel" @click="handleCancel">{{
        $t('common.buttonCancel')
      }}</a-button>
      <a-button type="primary" @click="handleOk" class="moadl-button-Ok">{{
        $t('common.buttonConfirm')
      }}</a-button>
    </template>
  </PopWindow>
</template>

<script>
  import PopWindow from '@/components/popWindow';
  import { columns1, columns2 } from './config';
  import { getLobList } from '@/utils/utils';
  import { getCustListQryList } from '@/api/customerVerify';

  export default {
    name: 'queryCustomerList',
    components: { PopWindow },
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      // 查询参数
      queryData: {
        type: Object,
        default() {
          return {};
        },
      },
      // 是否客户识别弹窗
      isCustomerCertificate: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        spinning: false,
        bodyStyle: {
          maxHeight: '420px',
          padding: '10px',
          overflow: 'auto',
        },
        layout: {},
        formData: {},
        lobList: [],
        columns1,
        columns2,
        tableData: [],
        radioValue: 0,
        pageParam: {
          PAGE_SIZE: 10,
          PAGE_NUM: 1,
        },
        pagination: {
          current: 1,
          total: 10,
          pageSize: 10,
          defaultPageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: ['5', '10', '15', '20'],
          showQuickJumper: true,
          showSizeChange: (current, pageSize) => {
            this.pageParam.PAGE_SIZE = pageSize;
            this.pageParam.PAGE_NUM = 1;
          },
          onChange: pageNumber => {
            this.pageParam.PAGE_NUM = pageNumber;
          },
        },
      };
    },
    watch: {
      pageParam: {
        handler() {
          // 当选中节点发生变化时，更新选中树
          this.queryList();
        },
        deep: true,
      },
    },
    computed: {
      // 客户识别 查询客户列表展示字段 和 报价单查询客户列表展示字段不同
      columns() {
        if (this.isCustomerCertificate) {
          return this.columns1;
        }
        return this.columns2;
      },
    },
    async created() {
      // 获取lob枚举数据
      this.lobList = await getLobList();
      this.queryList();
    },
    async mounted() {},
    methods: {
      handleRadioChange(e) {
        this.radioValue = e.target.value;
        /* 通过当前字段id在表格数据中检索选中数据 */
      },
      // LOB切换
      async handleLOBChange(value) {},
      // 弹窗取消按钮
      handleCancel() {
        this.$emit('cancel');
      },
      // 弹窗确定客户
      handleOk() {
        this.$emit('Ok', this.tableData[this.radioValue]);
      },
      //请求列表参数处理
      processedForm(item) {
        let DOCUMENT_NO = '';
        // 创建一个处理后的数据副本
        if (item.DOCUMENT_TYPE === '0') {
          if (item.DOCUMENT_NO) {
            DOCUMENT_NO = item.DOCUMENT_NO.replaceAll('-', '');
          }
        } else {
          DOCUMENT_NO = item.DOCUMENT_NO;
        }
        return {
          ...item,
          DOCUMENT_NO: DOCUMENT_NO,
        };
      },
      //查询客户列表
      queryList(type) {
        if (type == 'init') {
          this.pageParam.PAGE_NUM = 1;
          this.pagination.current = 1;
        }
        let reqParams = {};
        const params = {
          QRY_TYPE: this.queryData.SERVICE_NO ? '0' : '1',
          ...this.queryData,
          ...this.pageParam,
          ...this.formData,
        };
        // 空值不传，作处理
        Object.keys(params).forEach(key => {
          if (params[key] !== '') {
            this.$set(reqParams, key, params[key]);
          }
        });
        let formParams = this.processedForm(reqParams);
        this.spinning = true;
        getCustListQryList(formParams)
          .then(res => {
            this.tableData = res.DATA[0].CUSTOMER_LIST;
            this.pagination.total = res.DATA[0]?.TOTAL_COUNT;
          })
          .finally(() => {
            this.spinning = false;
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .ant-row-flex {
    margin-right: 0 !important;
  }
  /deep/ .ant-btn-loading {
    width: 80px;
    padding-left: 6px !important;
  }

  .search-container {
    .b-btns1 {
      padding-right: 0 !important;
      .btns-container {
        height: 100%;
        display: flex;
        align-items: end;
      }
    }
    .b-btns2 {
      padding-right: 6px !important;
      display: flex;
      justify-content: flex-end;
      .btns-container {
        height: 100%;
        display: flex;
        align-items: end;
      }
    }
  }

  .moadl-button-Ok {
    padding: 0;
  }
</style>
