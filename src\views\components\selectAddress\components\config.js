import that from '@/main.js';

export default {
  bodyStyle: {
    height: '380px',
    padding: '10px',
    overflow: 'auto',
  },
  rules: {
    Region: [
      {
        required: true,
        message: that.$t('customerVerify.RegioRuleMessage'),
        trigger: 'change',
      },
    ],
    District: [
      {
        required: true,
        message: that.$t('customerVerify.DistrictRuleMessage'),
        trigger: 'change',
      },
    ],
    Street: [
      {
        required: true,
        message: that.$t('customerVerify.StreetRuleMessage'),
        trigger: 'blur',
      },
    ],
    Estate: [
      {
        required: true,
        message: that.$t('customerVerify.EstateRuleMessage'),
        trigger: 'blur',
      },
    ],
    Building: [
      {
        required: true,
        message: that.$t('customerVerify.BuildingRuleMessage'),
        trigger: 'blur',
      },
    ],
    Floor: [
      {
        required: true,
        message: that.$t('customerVerify.FloorRuleMessage'),
        trigger: 'blur',
      },
    ],
    LotNumber: [
      {
        required: true,
        message: that.$t('customerVerify.LotNumberRuleMessage'),
        trigger: 'blur',
      },
    ],
  },
};
