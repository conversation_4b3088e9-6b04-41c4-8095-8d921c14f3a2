<!-- 新增地址 -->
<template>
  <div class="form">
    <a-form-model :model="formData" :colon="false" layout="vertical" ref="Form" :rules="rules">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="12" flex="flex-start">
          <a-form-model-item :label="$t('customerVerify.region')" prop="Region" labelAlign="left">
            <a-select
              v-model="formData.Region"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              @change="handleRegionChange"
              labelInValue
            >
              <a-select-option v-for="item in RegionSelData" :value="item.CID" :key="item.CID">
                {{ item.EN_DESC }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.district')"
            prop="District"
            labelAlign="left"
          >
            <a-select
              v-model="formData.District"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              :disabled="DistrictStatus"
              labelInValue
            >
              <a-select-option v-for="item in DistrictSelData" :value="item.CID" :key="item.CID">
                {{ item.EN_DESC }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="12" flex="flex-start">
          <a-form-model-item :label="$t('customerVerify.street')" prop="Street" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model="formData.Street"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput($event, 'Street')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12" flex="flex-start">
          <a-form-model-item :label="$t('customerVerify.estate')" prop="Estate" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model="formData.Estate"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput($event, 'Estate')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="12" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.building')"
            prop="Building"
            labelAlign="left"
          >
            <a-input
              v-containsSqlInjection
              v-model="formData.Building"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput($event, 'Building')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="12" flex="flex-start">
          <a-form-model-item :label="$t('customerVerify.Floor')" prop="Floor" labelAlign="left">
            <a-input
              v-containsSqlInjection
              v-model="formData.Floor"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput($event, 'Floor')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="12" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.Flat_Unit_Room')"
            prop="Flat/Unit/Room"
            labelAlign="left"
          >
            <a-input-group compact>
              <a-select
                v-model="formData.flatSelectValue"
                :placeholder="$t('common.selectPlaceholder')"
                style="width: 40%"
              >
                <a-select-option
                  v-for="item in flatStaticList"
                  :value="item.value"
                  :key="item.value"
                >
                  <span :title="item.label">
                    {{ item.label }}
                  </span>
                </a-select-option>
              </a-select>
              <a-input
                v-containsSqlInjection
                v-model.trim="formData.FlatUnitRoom"
                style="width: calc(60% - 5px)"
                :placeholder="$t('common.inputPlaceholder')"
                @input="handleInput($event, 'FlatUnitRoom')"
              />
            </a-input-group>
          </a-form-model-item>
        </a-col>
        <a-col :span="12" flex="flex-start">
          <a-form-model-item
            :label="$t('customerVerify.LotNumber')"
            prop="LotNumber"
            labelAlign="left"
          >
            <a-input
              v-containsSqlInjection
              v-model="formData.LotNumber"
              :placeholder="$t('common.inputPlaceholder')"
              @input="handleInput($event, 'LotNumber')"
            />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import config from './config';
  import upConfig from '../config';
  import { searchRegion, searchDistrict } from '@/api/customerVerify';
  export default {
    name: 'NewAddressMapping',
    data() {
      return {
        confirmLoading: false,
        bodyStyle: config.bodyStyle,
        formData: {
          flatSelectValue: 'FLAT',
        },
        rules: config.rules,
        RegionSelData: [],
        DistrictSelData: [],
        DistrictStatus: true,
        flatStaticList: upConfig.flatStaticList,
      };
    },
    mounted() {
      this.getSelectDatas();
    },
    methods: {
      handleInput(event, fieldName) {
        // 获取当前输入值
        let value = event.target.value;

        // 使用正则表达式替换多个连续空格为单个空格
        value = value.replace(/\s+/g, ' ');

        // 如果输入值的第一个字符是空格，去掉它
        if (value.startsWith(' ')) {
          value = value.substring(1);
        }

        // 更新 formData 中对应的字段
        this.$set(this.formData, fieldName, value);

        // 同时更新原生输入框的值，防止用户看到多余空格
        event.target.value = value;
      },
      // Region下拉枚举
      getSelectDatas() {
        searchRegion().then(res => {
          this.RegionSelData = res.DATA;
        });
      },

      // 更改Region
      handleRegionChange(e) {
        searchDistrict({ CID: e.key }).then(res => {
          this.DistrictStatus = false;
          this.DistrictSelData = res.DATA;
        });
      },

      // 封装数组，父组件回调
      handleOk() {
        let installationAddress = '';
        let data = {};
        let flag = false;
        this.$refs.Form.validate(valid => {
          if (valid) {
            flag = true;

            const {
              Region,
              District,
              Street,
              Estate,
              Building,
              Floor,
              FlatUnitRoom,
              flatSelectValue,
              LotNumber,
            } = this.formData;
            // installationAddress = `${Region?.label},${
            //   District?.label
            // },${Street}, ${Estate}, ${Building}, ${Floor} ${FlatUnitRoom ? ',' + FlatUnitRoom : ''}`;
            installationAddress = `${FlatUnitRoom ? FlatUnitRoom + ',' : ''}, ${
              Floor ? ',' + Floor : ''
            } ${Building ? ',' + Building : ''},${Estate ? ',' + Estate : ''},${Street} ${
              District?.label ? ',' + District?.label : ''
            },${Region?.label},${LotNumber ? ',' + LotNumber : ''}`
              .split(',')
              .map(part => part.trim()) // 去除每个部分的前后空格
              .filter(part => part !== '') // 过滤掉空的部分
              .join(', '); // 使用逗号和空格重新连接;

            data = {
              installationAddress,
              Floor,
              FlatUnitRoom,
              LotNumber,
              status: 'new',
              Region,
              District,
              Street,
              Estate,
              Building,
              FLATVALUE: flatSelectValue + ' ' + (FlatUnitRoom || ''),
            };
          }
        });

        return flag ? data : '';
      },
    },
  };
</script>

<style lang="less" scoped>
  .moadl-button-Ok {
    padding: 0;
  }
</style>
