import that from '@/main.js';

export default {
  bodyStyle: {
    height: '360px',
    padding: '10px',
    overflow: 'auto',
  },
  columns: [
    {
      title: that.$t('customerVerify.Sel'),
      dataIndex: 'Sel',
      key: 'Sel',
      width: 60,
      ellipsis: true,
      scopedSlots: { customRender: 'Sel' },
    },
    {
      title: that.$t('customerVerify.SB No'),
      dataIndex: 'SB_NO',
      key: 'SB_NO',
      width: 100,
      ellipsis: true,
    },
    {
      title: that.$t('customerVerify.Address'),
      scopedSlots: { customRender: 'SB_ADDR' },
      ellipsis: true,
    },
    {
      title: that.$t('customerVerify.Resource'),
      dataIndex: 'RESOURCE',
      key: 'RESOURCE',
      width: 100,
      ellipsis: true,
    },
    {
      title: that.$t('customerVerify.Spare'),
      dataIndex: 'SPARE',
      key: 'SPARE',
      width: 80,
      ellipsis: true,
    },
    {
      title: that.$t('customerVerify.2N'),
      dataIndex: '2N',
      key: '2N',
      width: 80,
      ellipsis: true,
    },
    {
      title: that.$t('customerVerify.DP'),
      dataIndex: 'DP',
      key: 'DP',
      fixed: 'right',
      width: 80,
      ellipsis: true,
    },
  ],
  addressRules: {
    FLOOR: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: ['blur', 'change'] },
    ],
    FLAT: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: ['blur', 'change'] },
    ],
  },
  flatStaticList: [
    {
      label: 'FLAT',
      value: 'FLAT',
    },
    {
      label: 'ROOM',
      value: 'ROOM',
    },
    {
      label: 'SHOP',
      value: 'SHOP',
    },
    {
      label: 'SUITE',
      value: 'SUITE',
    },
    {
      label: 'UNIT',
      value: 'UNIT',
    },
  ],
};
