<!-- 选址弹窗 -->
<template>
  <div class="page">
    <!-- 选址部分 -->
    <div v-show="!isShowNewAddress">
      <a-form-model
        :model="form"
        :rules="addressRules"
        :colon="false"
        layout="vertical"
        ref="searchFormRules"
      >
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item :label="$t('customerVerify.Address')">
              <a-select
                show-search
                allowClear
                v-validateAddress
                v-model="form.GEOSEQ"
                :placeholder="$t('common.selectPlaceholder')"
                style="width: 100%"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="fetching ? undefined : null"
                @search="handleSearch"
                @change="addressSelectChange"
              >
                <a-spin v-if="fetching" slot="notFoundContent" size="small" />
                <a-select-option v-for="item in searchAddressList" :key="item.GEOSEQ">
                  {{ item?.ADDRTEXT?.EN || '' }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-model-item :label="$t('customerVerify.Floor')" prop="FLOOR" labelAlign="left">
              <a-select
                v-if="isFloorSelectType"
                v-containsSqlInjection
                allowClear
                v-model="form.FLOOR"
                :placeholder="$t('common.selectPlaceholder')"
                @change="floorSelectChange"
              >
                <a-select-option v-for="item in floorList" :key="item.FLOOR_VALUE">
                  <span :title="item.FLOOR_EN">
                    {{ item.FLOOR_EN }}
                  </span>
                </a-select-option>
              </a-select>
              <a-input
                v-else
                ref="floorInput"
                v-model.trim="form.FLOOR"
                @input="handleInput($event, 'FLOOR')"
                @blur="handleFloorInputBlur"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('customerVerify.Flat')" prop="FLAT" labelAlign="left">
              <a-input-group compact>
                <a-select
                  v-model="form.flatSelectValue"
                  :placeholder="$t('common.selectPlaceholder')"
                  style="width: 40%"
                >
                  <a-select-option
                    v-for="item in flatStaticList"
                    :value="item.value"
                    :key="item.value"
                  >
                    <span :title="item.label">
                      {{ item.label }}
                    </span>
                  </a-select-option>
                </a-select>
                <a-select
                  v-if="isFlatSelectType"
                  v-model.trim="form.FLAT"
                  style="width: calc(60% - 5px)"
                  :placeholder="$t('common.selectPlaceholder')"
                  @change="flatSelectChange"
                >
                  <a-select-option
                    v-for="item in flatList"
                    :value="item.FLAT_VALUE"
                    :key="item.FLAT_VALUE"
                  >
                    <span :title="item.FLAT_EN">
                      {{ item.FLAT_EN }}
                    </span>
                  </a-select-option>
                </a-select>
                <a-input
                  v-else
                  ref="flatInput"
                  v-model.trim="form.FLAT"
                  style="width: calc(60% - 5px)"
                  @input="handleInput($event, 'FLAT')"
                  :placeholder="$t('common.inputPlaceholder')"
                  @blur="handleFlatInputBlur"
                  :maxLength="15"
                />
              </a-input-group>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('customerVerify.SB_No')" prop="SB_No" labelAlign="left">
              <a-input
                v-validate-number
                v-model="form.SB_NO"
                :placeholder="$t('common.inputPlaceholder')"
                :disabled="inputDisabled"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item
              :label="$t('customerVerify.ServiceNo')"
              prop="ServiceNo"
              labelAlign="left"
            >
              <a-input
                v-validate-number
                v-model="form.SERVICE_NO"
                :placeholder="$t('common.inputPlaceholder')"
                :disabled="inputDisabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row :gutter="24" style="margin-top: 10px">
          <a-col :span="12">
            <!-- TODO国际化 -->
            <a-checkbox v-model="getAllSB" class="all-sb-checkbox">{{
              this.$t('customerVerify.showAllSBNOCheckBox')
            }}</a-checkbox>
          </a-col>
          <a-col :span="12">
            <div class="btns-container">
              <a-button class="cancel-botton" @click="resetAddress">
                {{ $t('common.buttonReset') }}
              </a-button>
              <a-button
                :loading="spinning"
                type="primary"
                @click="queryAddress"
                class="search-button"
              >
                {{ $t('common.buttonInquiry') }}
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-form-model>

      <div class="dashedLine"></div>
      <div class="masterAddress">
        <span>
          {{ masterAddressInfo.DETAILS_EN?.ADDRESS ?? '' }}
        </span>
      </div>
      <a-divider />
      <!-- 查询列表 -->
      <a-table
        v-if="needQuerySBList"
        :columns="columns"
        :data-source="dataList"
        :loading="tableLoading"
        :scroll="{ x: 600, y: 300 }"
        :pagination="false"
      >
        <span slot="Sel" slot-scope="text, record, index">
          <a-radio @change="handleRadioChange" :value="index" :checked="radioValue == index" />
        </span>
        <span slot="SB_ADDR" slot-scope="text, record, index">
          <a-popover>
            <template slot="content">
              <span>{{ record.SB_ADDR }}</span>
            </template>
            <span>{{ record.SB_ADDR }}</span>
          </a-popover>
        </span>
      </a-table>

      <CustomPagination
        v-show="dataList && dataList.length"
        :current-page="pageNum"
        :more-page="morePage"
        @pagination-change="handlePaginationChange"
      />
    </div>

    <!-- 地址新增部分 -->
    <div v-show="isShowNewAddress">
      <NewAddressMapping ref="newAddressMappingRef" />
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <a-button class="cancel-botton" @click="handleCancel">{{
        $t('common.buttonCancel')
      }}</a-button>
      <a-button class="cancel-botton" @click="handleShowNewAddress" v-show="!isShowNewAddress">
        {{ $t('common.newAddress') }}
      </a-button>
      <a-button class="cancel-botton" @click="handlePrevious" v-show="isShowNewAddress">
        {{ $t('common.previous') }}
      </a-button>
      <a-button type="primary" @click="handleOk">{{ $t('common.buttonConfirm') }}</a-button>
    </div>

    <!-- 提示弹窗 -->
    <TipsPopWindow v-if="tipsVisible" :visible="tipsVisible" @Ok="handleTipsOk" :text="tipsText" />
  </div>
</template>

<script>
  import { queryByCustId } from '@/api/accountSetting';
  import {
    queryAddressByNo,
    querySbList,
    searchAddress,
    searchFlat,
    searchFloor,
    detailAddress,
    queryAddressByServiceNo,
  } from '@/api/customerVerify';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';
  import { hasAnyValue } from '@/utils/utils';
  import CustomPagination from '@/views/components/customPagination';
  import { mapState } from 'vuex';
  import NewAddressMapping from './components/newAddressMapping.vue';
  import config from './config.js';

  export default {
    name: 'SelectAddress',
    props: {
      title: {
        type: String,
        default: '',
      },
      currentProductType: {
        type: String,
        default: '',
      },
      showAddress: {
        type: Boolean,
        default: false,
      },
      // 三户不需要展示和查询sbList
      needQuerySBList: {
        type: Boolean,
        default: true,
      },
      businessType: {
        type: String,
        default: '',
      },
    },
    components: {
      CustomPagination,
      NewAddressMapping,
      TipsPopWindow,
    },
    data() {
      return {
        getAllSB: false,
        columns: config.columns,
        radioValue: 0,
        tipsText: '',
        spinning: false,
        fetching: false,
        tipsVisible: false,
        isShowNewAddress: false,
        tableLoading: false,
        searchAddressList: [],
        floorList: [],
        flatList: [],
        dataList: [],
        form: {
          flatSelectValue: 'FLAT',
          FLOOR: undefined,
          FLAT: undefined,
        },
        pageNum: 1,
        pageSize: 10,
        morePage: false,
        selectedRow: {},
        addressRules: config.addressRules,
        flatStaticList: config.flatStaticList,
        radioOptions: [],
        currentMasterAddress: '',
        isFloorSelectType: true,
        isFlatSelectType: true,
        masterAddressInfo: {},
        inputDisabled: false,
        SB_NO_FormData: '',
        addressBID: '',
        faltInputValue: false, // 记录Flat当前的值是下拉值还是输入值
      };
    },
    computed: {
      ...mapState('quotation', {
        productType: state => state.productType,
        custId: state => state.custId,
      }),
      ...mapState('macd', {
        selectedOrderList: state => state.selectedOrderList,
      }),
    },
    watch: {
      getAllSB: {
        handler(val) {
          if (val) {
            this.queryAddress();
          }
        },
      },
    },
    mounted() {
      if (this.showAddress) this.getDetailInfo();
    },
    methods: {
      // 限制输入
      handleInput(event, params) {
        const value = event.target.value;
        // 允许字母、数字和空格，但空格不能连续
        const filteredValue = value.replace(/[^0-9a-zA-Z\s]|(\s{2,})/g, (match, p1) => {
          return p1 ? ' ' : '';
        });

        if (value !== filteredValue) {
          this.showTooltip = true;
        } else {
          this.showTooltip = false;
        }

        this.form[params] = filteredValue;
      },
      flatSelectChange(value) {
        this.faltInputValue = false;
        this.$refs.searchFormRules.validateField('FLAT');
        if (value == 'otherFlat') {
          this.isFlatSelectType = false;
          this.form.FLAT = '';
          this.$nextTick(() => {
            this.$refs.flatInput.focus();
          });
          this.faltInputValue = true;
        } else {
          // 如果flatList带单位，切换的时候单位也要换
          const flatObj = this.flatList.find(item => item.FLAT_VALUE === value);
          if (flatObj.FLAT_EN == 'ALL FLATS' || flatObj.FLAT_VALUE == '.') return;
          if (flatObj && flatObj.FLAT_EN) {
            const parts = flatObj.FLAT_EN.split(' ');
            const unitList = ['FLAT', 'ROOM', 'SHOP', 'SUITE', 'UNIT'];
            // 如果不在这五个单位里面则return
            if (!unitList.includes(parts[0])) return;
            const flatValue = parts.length > 1 ? parts[0] : '';
            if (flatValue) {
              this.form.flatSelectValue = flatValue;
            }
          }
        }
      },
      handleFlatInputBlur() {
        this.isFlatSelectType = true;
        this.$refs.searchFormRules.validateField('FLAT');
      },
      floorSelectChange(value) {
        if (value == 'otherFloor') {
          this.isFloorSelectType = false;
          this.form.FLOOR = '';
          this.$nextTick(() => {
            this.$refs.floorInput.focus();
            this.isFlatSelectType = false;
          });
          this.flatList = [];
          this.form.FLAT = undefined;
          return;
        }
        this.floorChange(value);
        this.form.FLAT = undefined;
        this.isFlatSelectType = true;
      },
      handleFloorInputBlur() {
        this.isFloorSelectType = true;
      },
      resetAddress() {
        this.form = {
          flatSelectValue: 'FLAT',
          FLAT: undefined,
        };
        this.inputDisabled = false;
        this.searchAddressList = [];
        this.floorList = [];
        this.flatList = [];
        this.masterAddressInfo = {};
        this.dataList = [];
        this.getAllSB = false;
        this.faltInputValue = false;
      },
      handlePaginationChange(pageNum) {
        this.pageNum = pageNum;
        this.queryAddress();
      },
      // 输入地址搜索，500毫秒请求一次
      handleSearch(inputValue) {
        const regex =
          /(\b(truncate|alter|grant|select|update|and|or|delete|insert|trancate|where|char|chr|into|exists|union|all|substr|ascii|declare|exec|count|sum|master|drop|execute|waitfor)\b|(\*|;|@|\uFF01|\uFF1F|!|[?]|#|%|<|>|\\|\||\^|`|"|\{|\}|\[|\]|\(|\)|\$|&|=|\+))/gi;
        let value = inputValue;
        if (regex.test(inputValue)) {
          value = inputValue.replace(regex, '');
          const searchInput = document.querySelector('.ant-select-search__field');
          this.$nextTick(() => {
            searchInput.value = value;
          });
          return;
        }
        if (value.length < 2) {
          return;
        }
        if (this.timeout) {
          clearTimeout(this.timeout);
          this.timeout = null;
        }
        this.timeout = setTimeout(() => {
          this.inputToSelectAddress(value);
        }, 500);
      },

      // 地址接口查询
      inputToSelectAddress(value) {
        if (!value) return;
        this.searchAddressList = [];
        this.fetching = true;
        searchAddress({ ADDR: value }).then(res => {
          this.searchAddressList = res.DATA || [];
          this.fetching = false;
        });
      },

      // 输入后的地址查询 楼层下拉框数据查询
      addressSelectChange(value, isClear = true) {
        if (!value) {
          this.form.GEOSEQ = '';
          this.form.FLOOR = '';
          this.form.FLAT = '';
          this.floorList = [];
          this.flatList = [];
          return;
        }
        // 普通选择时清空
        if (isClear) {
          this.form.FLOOR = undefined;
          this.form.FLAT = undefined;
          this.floorList = [];
          this.flatList = [];
        }
        // 找出当前address的那一项
        const selectedAddress = this.searchAddressList.find(item => item.GEOSEQ === value);
        if (selectedAddress) {
          this.addressBID = selectedAddress.BID;
        }
        searchFloor({ GEOSEQ: this.form.GEOSEQ }).then(res => {
          this.floorList = res.DATA || [];
          this.floorList.push({
            FLOOR_ZH: 'Other Floor',
            FLOOR_EN: 'Other Floor',
            FLOOR_VALUE: 'otherFloor',
          });
        });
      },

      // floor 切换
      floorChange(value) {
        if (!this.form.GEOSEQ || !value) return;
        const { GEOSEQ, FLOOR } = this.form;

        // 室下拉框查询
        searchFlat({ GEOSEQ, FLOOR }).then(res => {
          this.flatList = res.DATA?.map(item => ({
            FLAT_ZH: item.FLAT_ZH,
            FLAT_EN: item.STREET_NO ? item.STREET_NO : item.FLAT_EN,
            FLAT_VALUE: item.FLAT_VALUE || 'specialValue', // 但选项ALLFLAT值为空的时候取 ‘specialValue’ ,然后再查询的时候根据这个值转一遍空字符串
          }));
          this.flatList.push({
            FLAT_ZH: 'Other Flat',
            FLAT_EN: 'Other Flat',
            FLAT_VALUE: 'otherFlat',
          });
        });
      },
      // 查询地址
      async queryAddress() {
        const { SERVICE_NO, GEOSEQ } = this.form;
        this.SB_NO_FormData = this.form.SB_NO;

        // 通过SB_NO查询
        if (this.SB_NO_FormData && !GEOSEQ && !this.inputDisabled) {
          await this.handleSbNoQuery();
          return;
        }

        // 通过SERVICE_NO查询
        if (SERVICE_NO && !GEOSEQ && !this.inputDisabled) {
          const serviceNoBool = await this.handleServiceNoQuery(SERVICE_NO);
          if (!serviceNoBool) return;
        }

        // 参数校验
        if (!hasAnyValue(this.form)) {
          this.tipsText = this.$t('customerVerify.tipsText1');
          this.tipsVisible = true;
          return;
        }

        // 普通查询
        await this.handleNormalQuery();
      },

      // 通过SB_NO查询
      async handleSbNoQuery() {
        const res = await queryAddressByNo({ SB_NO: this.SB_NO_FormData });
        const addressInfo = res.DATA?.[0];
        this.searchAddressList = res.DATA ?? [];

        if (addressInfo) {
          const { ADDRTEXT, GEOSEQ, BID } = addressInfo;

          setTimeout(() => {
            this.addressBID = BID;
            this.$set(this.form, 'GEOSEQ', GEOSEQ);
            this.addressSelectChange(ADDRTEXT.EN);
          }, 500);

          this.inputDisabled = true;
        }
      },
      // 通过SERVICE_NO查询
      async handleServiceNoQuery(SERVICE_NO) {
        this.faltInputValue = false;
        const res = await queryAddressByServiceNo({ SERVICE_NO });
        const serviceNoInfo = res.DATA?.[0];

        // 检查 serviceNoInfo 是否为 null 或 undefined
        if (!serviceNoInfo) {
          this.$message.error(this.$t('customerVerify.noExitCurrentServiceNO'));
          return false;
        }
        const { GEOSEQ, STANDARD_ADDRESS, SB_NO, FLOOR, FLAT, FLOOR_VALUE, FLAT_VALUE } =
          serviceNoInfo;
        this.SB_NO_FormData = SB_NO;

        this.floorList.push({
          FLOOR_ZH: FLOOR,
          FLOOR_EN: FLOOR,
          FLOOR_VALUE: FLOOR_VALUE,
        });
        this.flatList.push({
          FLAT_ZH: FLAT,
          FLAT_EN: FLAT,
          FLAT_VALUE: FLAT_VALUE,
        });
        this.inputToSelectAddress(STANDARD_ADDRESS);
        this.$nextTick(() => {
          this.$set(this.form, 'GEOSEQ', GEOSEQ);
          this.$set(this.form, 'FLOOR', FLOOR_VALUE);
          this.$set(this.form, 'FLAT', FLAT_VALUE);
          this.addressSelectChange(STANDARD_ADDRESS, false);
          this.floorChange(FLOOR_VALUE);
        });

        this.inputDisabled = true;
        return true;
      },
      // 处理普通查询
      async handleNormalQuery() {
        const valid = await this.$refs.searchFormRules.validate();

        if (!valid) return;

        this.spinning = true;
        this.tableLoading = true;

        try {
          // 获取主地址信息
          await this.fetchMasterAddress();

          // 查询SB列表
          if (this.needQuerySBList) {
            await this.fetchSbList();
          }
        } catch (error) {
          console.log(error, 'error');
        } finally {
          this.spinning = false;
          this.tableLoading = false;
        }
      },

      // 获取主地址信息
      async fetchMasterAddress() {
        try {
          // FLAT 值需要转换一下
          // 选择otherFlat取值 this.form.flatSelectValue + ' ' + this.form.FLAT
          const flatValue = this.form.FLAT == 'specialValue' ? '' : this.form.FLAT;
          const FLAT = this.faltInputValue
            ? this.form.flatSelectValue + ' ' + flatValue
            : flatValue;

          const masterParms = {
            GEOSEQ: this.form.GEOSEQ,
            FLOOR: this.form.FLOOR,
            FLAT,
          };

          const masterRes = await detailAddress(masterParms);
          this.masterAddressInfo = masterRes.DATA?.[0] || {};
        } catch (error) {
          console.error('获取主地址信息失败:', error);
        }
      },

      // 查询SB列表
      async fetchSbList() {
        const PRODUCT_TYPE =
          this.businessType == 'MACD'
            ? this.selectedOrderList[0].PRODUCT_TYPE_NAME
            : this.productType.label || this.currentProductType;
        const params = {
          ...this.form,
          FLAT: this.form.FLAT == 'specialValue' ? '' : this.form.FLAT,
          PAGE_NUM: this.pageNum,
          PAGE_SIZE: 10,
          GET_ALL_SB: this.getAllSB,
          PRODUCT_TYPE,
          BID: this.addressBID,
          SB_NO: this.SB_NO_FormData,
        };

        const res = await querySbList(params);
        this.dataList = res.DATA?.[0]?.LIST || [];
        if (this.dataList.length == 0 && !this.getAllSB) {
          this.$message.warning('customerVerify.checkShowAllSBNOCheckBox');
        } else if (this.dataList.length == 0 && this.getAllSB) {
          this.$message.warning('customerVerify.SBNOListHasNoData');
        }
        this.morePage = res.DATA?.[0]?.MORE_RES || false;

        // 设置选中行
        if (this.SB_NO_FormData && this.dataList.length > 0) {
          const index = this.dataList.findIndex(item => item.SB_NO === this.SB_NO_FormData);
          this.selectedRow = index !== -1 ? this.dataList[index] : this.dataList[0];
          this.radioValue = index !== -1 ? index : 0;
        } else {
          this.selectedRow = this.dataList[0];
          this.radioValue = 0;
        }
      },

      // 新装返回选址
      handlePrevious() {
        this.isShowNewAddress = false;
      },

      handleRadioChange(e) {
        this.radioValue = e.target.value;
        this.selectedRow = this.dataList[e.target.value];
        /* 通过当前字段id在表格数据中检索选中数据 */
      },

      onChange(page) {
        this.pagination.current = page;
        this.queryList();
      },
      onShowSizeChange(_, size) {
        this.pagination.pageSize = size;
        this.queryList();
      },

      // 关闭弹窗
      handleCancel() {
        this.$emit('cancel');
      },

      // 关闭提示弹窗
      handleTipsOk() {
        this.tipsVisible = false;
      },

      // 显示新增地址
      handleShowNewAddress() {
        this.isShowNewAddress = true;
      },

      async getDetailInfo() {
        const params = {
          'CUST_ID': this.custId,
        };
        const res = await queryByCustId(params);
        const data = res.DATA[0] || {};
        this.fetching = true;
        detailAddress({ 'GEOSEQ': data.SB_NUMBER }).then(res => {
          const { BID, GEOSEQ, DETAILS_EN, DETAILS_ZH } = res.DATA[0];
          this.searchAddressList = [
            {
              BID: BID,
              GEOSEQ: GEOSEQ,
              ADDRTEXT: {
                'EN': DETAILS_EN?.ADDRESS,
                'ZH': DETAILS_ZH?.ADDRESS,
              },
            },
          ];
          this.form.GEOSEQ = GEOSEQ;
          this.addressSelectChange(this.form.GEOSEQ);
          this.fetching = false;
        });
      },
      // 确定回调事件
      handleOk() {
        if (!this.isShowNewAddress) {
          if (this.needQuerySBList && !this.selectedRow?.SB_NO) {
            this.$message.warning(this.$t('common.selectPlaceholder'));
            return;
          }

          // 返回选址数据，为空对象，则没有选择地址，不提交emit
          const flag = Object.keys(this.selectedRow).length;
          let addressInfo = { ...this.selectedRow };
          const { GEOSEQ, FLOOR, FLAT } = this.form;
          // FLAT值需要转换一层
          const FLATVALUE = FLAT == 'specialValue' ? '' : FLAT;
          addressInfo = {
            status: 'normal',
            masterAddress: this.masterAddressInfo,
            ...addressInfo,
            EN_ADDR1: this.masterAddressInfo.DETAILS_EN.ADDR1,
            ZH_ADDR1: this.masterAddressInfo.DETAILS_ZH.ADDR1,
            EN_ADDR3: this.masterAddressInfo.DETAILS_EN.ADDR3,
            ZH_ADDR3: this.masterAddressInfo.DETAILS_ZH.ADDR3,
            GEOSEQ,
            FLOOR,
            FLAT: FLATVALUE,
            FLOORVALUE: FLOOR,
            FLATVALUE,
          };
          if (this.needQuerySBList && flag) {
            this.$emit('ok', addressInfo);
          } else if (!this.needQuerySBList) {
            this.$emit('ok', addressInfo);
          }
        } else {
          // 返回新装数据，为空串，新增地址表单校验不通过，不提交emit
          const newAddressData = this.$refs.newAddressMappingRef.handleOk();
          console.log('new-address', newAddressData);
          /**
           * ADDR1: Flat
           * ADDR3: Floor
           * ADDR4: Building
           * ADDR8: Estate
           * ADDR11: Street
           * ADDR12: District
           * ADDR13: Region
           */
          newAddressData &&
            this.$emit('ok', {
              status: 'new',
              EN_ADDR1: newAddressData.FlatUnitRoom ? newAddressData.FlatUnitRoom : '',
              ZH_ADDR1: newAddressData.FlatUnitRoom ? newAddressData.FlatUnitRoom : '',
              EN_ADDR3: newAddressData.Floor,
              ZH_ADDR3: newAddressData.Floor,
              SB_ADDR: newAddressData.installationAddress,
              EN_ADDR4: newAddressData.Building ? newAddressData.Building : '',
              ZH_ADDR4: newAddressData.Building ? newAddressData.Building : '',
              EN_ADDR8: newAddressData.Estate ? newAddressData.Estate : '',
              ZH_ADDR8: newAddressData.Estate ? newAddressData.Estate : '',
              EN_ADDR11: newAddressData.Street,
              ZH_ADDR11: newAddressData.Street,
              EN_ADDR12: newAddressData.District.label,
              ZH_ADDR12: newAddressData.District.label,
              EN_ADDR13: newAddressData.Region.label,
              ZH_ADDR13: newAddressData.Region.label,
              FLATVALUE: newAddressData.FLATVALUE, // 下单需要
            });
        }
      },
    },
  };
</script>

<style scoped lang="less">
  .page {
    padding-bottom: 15px;
    .btns-container {
      // margin-bottom: 15px;
      display: flex;
      justify-content: end;
      align-items: center;
    }
    .all-sb-checkbox {
      height: 32px;
      display: flex;
      align-items: center;
    }
    .footer {
      margin-top: 15px;
      display: flex;
      justify-content: end;
      align-items: center;
    }
    .cancel-botton {
      color: #01408e;
      border: 1px solid #01408e;
      margin-right: 10px;
    }
    .dashedLine {
      margin: 10px 0 !important;
      border-color: #d9d9d9 !important;
      border-top: 1.5px dashed #000;
    }
    .masterAddress {
      background-color: #e5f0ff;
      padding: 10px 20px;
      border-radius: 4px;
    }
  }
  .search-button {
    margin-right: 0px;
  }
</style>
