import that from '@/main.js';
const validateServiceNoNormal = that => (rule, value, callback) => {
  if (value) {
    if (value.length !== 8 || !/^\d+$/.test(value)) {
      callback(new Error(that.$t('assignNumber.eightNumberValidate')));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
export default {
  selectTypeList: [
    {
      value: 'Normal',
      label: 'Normal',
    },
    {
      value: 'Reserve',
      label: 'Reserve',
    },
    {
      value: 'Special Service Group',
      label: 'Special Service group',
    },
  ],
  numberSelectList: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
  ],
  CriteriaSelData: [
    { label: that.$t('customerVerify.NoCriteria'), value: '0' },
    { label: that.$t('customerVerify.PreferredFirst'), value: '1' },
    { label: that.$t('customerVerify.PreferredLast'), value: '2' },
  ],
  selectNumformRules: {
    serviceNoOfWorkingNumOther: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    customerNameOfWorkingNumOther: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    ServiceNoQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoReserve: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoSSG: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOC: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    Project: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceGruopOrService: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCodeDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOCDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    ProjectDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoOfWorkingNum: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    // serviceNoNormal: [{ required: false, message: '请输入一个完整的8位数号码', trigger: 'blur' }],
    serviceNoNormal: [
      { required: false, validator: validateServiceNoNormal(that), trigger: ['blur', 'change'] },
    ],
  },
  serviceNoTypeClient: [
    {
      label: 'New Installation',
      value: 'New Installation',
      key: '0',
      showFn: (productId, name) => {
        return name === 'AddNewNumber';
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number',
      value: 'Working Number',
      key: '1',
      showFn: (productId, name) => {
        return name === 'AddWorkingNumber';
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number(Other Customer)',
      value: 'Working Number(Other Customer)',
      key: '2',
      showFn: (productId, name) => {
        return name === 'AddWorkingNumberOther';
      },
    },
  ],
  serviceNoType: [
    {
      label: 'New Installation',
      value: 'New Installation',
      key: '0',
      showFn: () => {
        return true;
      },
    },
    // DEL 一期暂时不弄
    {
      label: 'PIPB Number',
      value: 'PIPB Number',
      // showFn: (productId) => { return ['300001'].includes(productId) },
      showFn: () => {
        return false;
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number',
      value: 'Working Number',
      key: '1',
      showFn: productId => {
        return ['300002', '300003', '300004'].includes(productId);
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number(Other Customer)',
      value: 'Working Number(Other Customer)',
      key: '2',
      showFn: (productId, name) => {
        return (
          ['300002', '300003', '300004'].includes(productId) &&
          (name === 'AddNewNumber' ||
            name === 'AddWorkingNumberOther' ||
            name === 'addDdiExtensionNumber' ||
            name === 'addAddonNumber')
        );
      },
    },
    //IDAP
    {
      label: 'DDI Extension Number',
      value: 'DDI Extension Number',
      key: '3',
      showFn: (productId, name) => {
        return ['300004'].includes(productId) && name === 'addDdiExtensionNumber';
      },
    },
  ],
};
