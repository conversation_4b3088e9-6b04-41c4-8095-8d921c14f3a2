<template>
  <div>
    <div class="header">
      <a-form-model
        :model="formData"
        v-bind="{}"
        :colon="false"
        ref="selectNumformRules"
        :rules="selectNumformRules"
        :key="formKey"
      >
        <a-row :gutter="24">
          <a-col :span="10">
            <a-form-model-item :label="$t('customerVerify.ServiceNoType')" prop="ServiceNoType">
              <a-select
                v-model="formData.ServiceNoType"
                :placeholder="$t('common.selectPlaceholder')"
                @change="serviceNoTypeChange"
              >
                <a-select-option
                  v-for="item in serviceNoTypeList"
                  :value="item.value"
                  :key="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <div v-if="formData.ServiceNoType == 'New Installation'">
          <a-row :gutter="24">
            <a-col :span="6">
              <a-form-model-item :label="$t('customerVerify.selectType')">
                <a-select
                  v-model="formData.selectType"
                  :placeholder="$t('common.selectPlaceholder')"
                  @change="handleSelectTypeChange"
                >
                  <a-select-option
                    v-for="item in selectTypeList"
                    :value="item.value"
                    :key="item.value"
                    ><a-tooltip placement="top">
                      <template slot="title">
                        <span>{{ item.label }}</span>
                      </template>
                      <span>{{ item.label }}</span>
                    </a-tooltip>
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- selectType: Normal -->
          <div v-if="formData.selectType == 'Normal'">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-model-item
                  :label="$t('customerVerify.Criteria')"
                  prop="Criteria"
                  labelAlign="left"
                >
                  <a-input-group compact>
                    <a-select
                      v-model="formData.Criteria"
                      :placeholder="$t('common.selectPlaceholder')"
                      style="width: 60%"
                    >
                      <a-select-option
                        v-for="item in CriteriaSelData"
                        :value="item.value"
                        :key="item.value"
                      >
                        <a-tooltip placement="top">
                          <template slot="title">
                            <span>{{ item.label }}</span>
                          </template>
                          <span>{{ item.label }}</span>
                        </a-tooltip>
                      </a-select-option>
                    </a-select>
                    <a-input
                      v-validate-number
                      v-model.trim="formData.CriteriaInputValue"
                      style="width: calc(40% - 5px); margin-left: 5px"
                      :disabled="formData.Criteria == '0'"
                      :maxLength="criteriaMaxLength"
                    />
                  </a-input-group>
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item :label="$t('customerVerify.ServiceNoQty')" prop="ServiceNoQty">
                  <a-input
                    v-validate-number
                    v-model="formData.ServiceNoQty"
                    :placeholder="$t('common.inputPlaceholder')"
                    :disabled="isDisabled"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item :label="$t('customerVerify.numberOfSelect')">
                  <a-select
                    allowClear
                    v-model="formData.numberOfSelect"
                    :placeholder="$t('common.selectPlaceholder')"
                    :disabled="isDisabled"
                  >
                    <a-select-option
                      v-for="item in numberSelectList"
                      :value="item.value"
                      :key="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item :label="$t('customerVerify.ServiceNo')" prop="serviceNoNormal">
                  <a-input
                    v-model.trim="formData.serviceNoNormal"
                    :placeholder="$t('customerVerify.completeNumber')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>

          <!-- selectType: Reserve -->
          <div v-if="formData.selectType == 'Reserve'">
            <a-row :gutter="24">
              <a-col :span="6">
                <a-form-model-item
                  :label="$t('customerVerify.ReservationCode')"
                  prop="reservationCode"
                >
                  <a-input
                    v-containsSqlInjection
                    v-model.trim="formData.reservationCode"
                    :placeholder="$t('common.inputPlaceholder')"
                    :maxLength="3"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item :label="$t('customerVerify.BOC')" prop="BOC">
                  <a-input
                    v-containsSqlInjection
                    v-model.trim="formData.BOC"
                    :placeholder="$t('common.inputPlaceholder')"
                    :maxLength="3"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="6">
                <a-form-model-item :label="$t('customerVerify.Project')" prop="Project">
                  <a-input
                    v-containsSqlInjection
                    v-model.trim="formData.Project"
                    :placeholder="$t('common.inputPlaceholder')"
                    :maxLength="20"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item :label="$t('customerVerify.ServiceNo')" prop="serviceNoReserve">
                  <a-input
                    v-containsSqlInjection
                    v-model.trim="formData.serviceNoReserve"
                    :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>

          <!-- selectType: Special Service Group -->
          <div v-if="formData.selectType == 'Special Service Group'">
            <a-row :gutter="24">
              <a-col :span="6">
                <a-form-model-item
                  :label="$t('customerVerify.serviceGroup_Service')"
                  prop="serviceGruopOrService"
                >
                  <a-select
                    v-model="formData.serviceGruopOrService"
                    :placeholder="$t('common.selectPlaceholder')"
                  >
                    <a-select-option
                      v-for="item in serviceGruopOrServiceList"
                      :value="item.value"
                      :key="item.value"
                    >
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item :label="$t('customerVerify.ServiceNo')" prop="serviceNoSSG">
                  <a-input
                    v-containsSqlInjection
                    v-model.trim="formData.serviceNoSSG"
                    :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div>
          <!-- todo--------   bug      --------校验清除失败 -->
          <!-- <div v-if="formData.selectType">
            <a-row :gutter="24">
              <a-col :span="24">
                <a-form-model-item label="Service No" prop="serviceNo">
                  <a-input
                    v-model="formData.serviceNo"
                    :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                  />
                </a-form-model-item>
              </a-col>
            </a-row>
          </div> -->
        </div>

        <div v-else-if="formData.ServiceNoType === 'Working Number'">
          <a-row :gutter="24">
            <a-col :span="24">
              <!-- TODO placeholder更新文案 -->
              <a-form-model-item
                :label="$t('customerVerify.ServiceNo')"
                prop="serviceNoOfWorkingNum"
              >
                <a-input
                  @blur="checkServiceNo"
                  v-containsSqlInjection
                  v-model.trim="formData.serviceNoOfWorkingNum"
                  :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                  :maxLength="89"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>

        <div v-else-if="formData.ServiceNoType === 'Working Number(Other Customer)'">
          <a-row :gutter="24">
            <a-col :span="12">
              <!-- TODO placeholder更新文案 -->
              <a-form-model-item
                :label="$t('customerVerify.CustomerName')"
                prop="customerNameOfWorkingNumOther"
              >
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.customerNameOfWorkingNumOther"
                  :placeholder="$t('customerVerify.enterTheCustomerName')"
                  :maxLength="89"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <!-- TODO placeholder更新文案 -->
              <a-form-model-item
                :label="$t('customerVerify.ServiceNo')"
                prop="serviceNoOfWorkingNumOther"
              >
                <a-input
                  @blur="checkServiceNo"
                  v-containsSqlInjection
                  v-model.trim="formData.serviceNoOfWorkingNumOther"
                  :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                  :maxLength="89"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <div v-else-if="formData.ServiceNoType === 'DDI Extension Number'">
          <a-row :gutter="24" justify="start" type="flex">
            <a-col :span="6">
              <a-form-model-item
                :label="$t('customerVerify.serviceGroup_Service')"
                prop="serviceGruopOrService"
              >
                <a-select
                  v-model="formData.serviceGruopOrService"
                  :placeholder="$t('common.selectPlaceholder')"
                >
                  <a-select-option
                    v-for="item in serviceGruopOrServiceList"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row :gutter="24" justify="start" type="flex">
            <a-col :span="5" flex="flex-start">
              <a-form-model-item
                :label="$t('customerVerify.ReservationCode')"
                prop="reservationCodeDN"
              >
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.reservationCodeDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="3"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="6" flex="flex-start">
              <a-form-model-item :label="$t('customerVerify.BOC')" prop="BOCDN">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.BOCDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="3"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="5" flex="flex-start">
              <a-form-model-item :label="$t('customerVerify.Project')" prop="ProjectDN">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.ProjectDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="20"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-model-item :label="$t('customerVerify.ServiceNo')" prop="serviceNoReserve">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.serviceNoReserve"
                  :placeholder="$t('customerVerify.numberLimitQuantityPrompt')"
                />
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
        <!-- selectType: Special Service Group -->
        <div v-if="formData.selectType == 'Special Service Group'">
          <a-row :gutter="24" justify="start" type="flex">
            <a-col flex="flex-start">
              <a-switch v-model="formData.ReserveDN" />
              <span style="margin-left: 10px">{{ $t('customerVerify.ReserveDN') }}</span>
            </a-col>
          </a-row>
          <a-row :gutter="24" justify="start" type="flex" v-if="formData.ReserveDN">
            <a-col :span="5" flex="flex-start">
              <a-form-model-item
                :label="$t('customerVerify.ReservationCode')"
                prop="reservationCodeDN"
              >
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.reservationCodeDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="3"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="5" flex="flex-start">
              <a-form-model-item :label="$t('customerVerify.BOC')" prop="BOCDN">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.BOCDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="3"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="5" flex="flex-start">
              <a-form-model-item :label="$t('customerVerify.Project')" prop="ProjectDN">
                <a-input
                  v-containsSqlInjection
                  v-model.trim="formData.ProjectDN"
                  :placeholder="$t('common.inputPlaceholder')"
                  :maxLength="20"
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="9" flex="flex-start" class="b-btns">
              <div class="btns-contaniner">
                <a-button ghost type="primary" @click="resetForm" class="reset-button">
                  {{ $t('common.buttonReset') }}
                </a-button>
                <a-button
                  :loading="btnLoading"
                  type="primary"
                  @click="handleQueryService"
                  class="search-button"
                >
                  {{ $t('common.buttonInquiry') }}
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>
        <a-row justify="end" type="flex" v-if="!formData.ReserveDN">
          <a-col>
            <a-button ghost type="primary" @click="resetForm" class="reset-button">
              {{ $t('common.buttonReset') }}
            </a-button>
            <a-button
              :loading="btnLoading"
              type="primary"
              @click="handleQueryService"
              class="search-button"
            >
              {{ $t('common.buttonInquiry') }}
            </a-button>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <div class="address-divider"><a-divider /></div>
    <div class="main">
      <GridList
        type="initAndStyle"
        v-if="serviceList.length"
        :showSeq="isSeq"
        :list="serviceList"
        :SelectAll="serviceSelectAll"
        :Allchecked="Allchecked"
        @handleRuleSelect="handleRuleSelect"
      >
        <div slot="footer" class="footer">
          <div class="checkAll">
            <a-checkbox @change="onSelectChange" :defaultChecked="true" v-model="Allchecked">{{
              $t('customerVerify.Allchecked')
            }}</a-checkbox>
          </div>
        </div>
      </GridList>
    </div>
    <!-- 底部按钮 -->
    <a-space class="flex-right">
      <a-button @click="selectNumberCancel">{{ $t('common.buttonCancel') }}</a-button>
      <a-button type="primary" @click="selectNumberConfirm">{{
        $t('common.buttonConfirm')
      }}</a-button>
    </a-space>
  </div>
</template>

<script>
  import {
    checkWorkingNumbers, // 下拉选择
    preAssignSsgOrReservedDn, // 特殊群组或预留号码
    querypreAssignSsgOrReservedDn,
    querySpareDnByType, // 基本号码
    querySpecialServiceGroup,
    queryWorkingNumber,
  } from '@/api/customerVerify';
  import GridList from '@/components/gridList';
  import { concatArray } from '@/utils/utils';
  import { mapState } from 'vuex';
  import config from './config';

  export default {
    name: 'SelectNumberPop',
    components: {
      GridList,
    },
    props: {
      SelectAll: {
        type: Array,
        default: () => [],
      },
      selectNumberType: {
        type: String,
        default: '',
      },
      businessType: {
        type: String,
        default: '',
      },
      isSeq: {
        // 编辑按钮是否弹出排序弹窗，macd改号
        type: Boolean,
        default: false,
      },
      relationTypeCode: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        serviceGroupSelData: [],
        btnLoading: false,
        formData: {
          ReserveDN: false,
          ServiceNoType: undefined,
          Criteria: '0',
          CriteriaInputValue: undefined,
          numberOfSelect: 1,
        },
        CriteriaSelData: config.CriteriaSelData,
        serviceList: [],
        serviceSelectAll: [],
        Allchecked: false,
        selectTypeList: config.selectTypeList,
        numberSelectList: config.numberSelectList,
        selectNumformRules: config.selectNumformRules,
        serviceGruopOrServiceList: [],
        formKey: 0, //用于强制重新渲染
        groupNumberOrderList: [],
      };
    },
    computed: {
      ...mapState('quotation', {
        custId: state => state.custId,
        productType: state => state.productType,
      }),
      // ...mapState('macd', {
      //   selectedOrderList: state => state.selectedOrderList,
      //   groupNumberOrderList: state => state.groupNumberOrderList,
      // }),
      serviceNoTypeList() {
        let value = this.productType.value;
        if (
          this.businessType == 'macdSelect' ||
          this.businessType == 'addDdiExtensionNumber' ||
          this.businessType == 'addAddonNumber' ||
          this.$route.query.mode == 'hunting' ||
          this.$route.query.mode == 'citienet'
        ) {
          value = this.selectedOrderList[0].PRODUCT_TYPE;
        }
        // 缓存路由名称，避免多次读取
        const routeName = this.$route.name;
        // 提前退出条件判断
        if (routeName.includes('idapChangeGroup')) {
          return config.serviceNoType.filter(item =>
            item.showFn(this.productType.value, this.businessType),
          );
        }
        if (routeName.includes('orderEdit') && this.relationTypeCode === '300008') {
          return config.serviceNoTypeHuntingForClient.filter(item =>
            item.showFn(this.relationTypeCode),
          );
        }
        // 缓存 productType 的 value
        const productTypeValue = value;
        const filterConfig =
          this.$route.query.mode === 'citinetChangeGroup' &&
          (routeName === 'AddNewNumber' ||
            routeName === 'AddWorkingNumber' ||
            routeName === 'AddWorkingNumberOther')
            ? config.serviceNoTypeClient
            : config.serviceNoType;
        return filterConfig.filter(item => item.showFn(productTypeValue, routeName));
      },
      // 标准的最大长度
      criteriaMaxLength() {
        const { Criteria } = this.formData;
        return Criteria === '1' ? 5 : 8;
      },
      isDisabled() {
        const { serviceNoNormal } = this.formData;
        // return serviceNoNormal ? true : false;
        return false;
      },
    },
    // watch: {
    //   'formData.serviceNoNormal'(newValue) {
    //     if (newValue) {
    //       this.formData.numberOfSelect = 1;
    //       this.formData.ServiceNoQty = '1';
    //     }
    //   },
    // },
    mounted() {
      // if (this.SelectAll.length) {
      //   this.serviceSelectAll = this.SelectAll;
      //   this.Allchecked = false;
      // }
      this.getSelectDatas();
    },
    methods: {
      checkServiceNo(e) {
        console.log(
          e,
          this.formData.serviceNoOfWorkingNum,
          'e------------',
          this.formData.serviceNoOfWorkingNum?.split(','),
        );
        if (!e.target.value) {
          return;
        }
        let data =
          this.formData.ServiceNoType === 'Working Number'
            ? this.formData.serviceNoOfWorkingNum
            : this.formData.serviceNoOfWorkingNumOther;

        // 定义正则表达式
        const regex = /^\d{8}(,\d{8})*$/;

        // 校验输入是否符合规则
        if (!regex.test(data)) {
          console.log('输入无效：每个逗号前后必须是八位数字');
          return;
        }
        const result = data
          ?.split(',') // 按逗号分割成数组
          .map(item => item.trim().replace(/[^0-9]/g, '')) // 去掉非数字字符
          .filter(item => item.length > 0); // 过滤掉空字符串 // 移除任何可能是由于多余逗号导致的空字符串 // 去掉每个元素的前后空格

        const reqData = {
          WORKING_NUMBER_LIST: result,
          IS_OTHER_CUSTOMER: this.formData.ServiceNoType === 'Working Number' ? 'N' : 'Y',
        };
        checkWorkingNumbers(reqData).then(res => {
          console.log(res.DATA[0], 'res', res.DATA[0].NO_WORKING_NUMBER_LIST);
          console.log(
            res.DATA[0].NO_WORKING_NUMBER_LIST.toString(),
            'res.DATA[0].NO_WORKING_NUMBER_LIST',
          );
          if (res?.DATA[0]?.NO_WORKING_NUMBER_LIST?.length > 0) {
            this.$message.error(
              `${res.DATA[0].NO_WORKING_NUMBER_LIST.toString()} ${this.$t(
                'assignNumber.numbercheckedTips',
              )}`,
            );
            return;
          }
          if (res?.DATA[0].WORKING_NUMBER_LIST?.length < 1) {
            return;
          }
        });
      },
      // ServiceGroup 下拉选择框 切换
      handleServiceGroupSelect(e) {
        if (!e) return;
      },
      // serviceNoType下拉框的change事件
      serviceNoTypeChange(value) {
        if (value == 'New Installation') {
          this.formData.selectType = 'Normal';
        } else {
          this.formData.selectType = '';
        }
      },
      // 取消按钮
      selectNumberCancel() {
        this.resetForm();
        this.formData = {
          ReserveDN: false,
          ServiceNoType: undefined,
          Criteria: '0',
          CriteriaInputValue: undefined,
          numberOfSelect: 1,
        };
        this.$emit('cancel');
      },
      // 确认按钮
      selectNumberConfirm() {
        if (!this.serviceSelectAll.length) {
          this.$message.error(this.$t('assignNumber.pleaseChooseOneNumber'));
          return;
        }

        const { ServiceNoType } = this.formData;
        // 判断当前所选的号码是否和ServiceNoQty的相等
        if (
          this.formData.ServiceNoQty < this.serviceSelectAll.length &&
          (this.businessType !== 'addDdiExtensionNumber' || this.businessType === 'addAddonNumber')
        ) {
          this.$message.error(
            this.$t('assignNumber.numberLimitPromptTips', {
              ServiceNoQty: this.$t('customerVerify.ServiceNoQty'),
            }),
          );
          return;
        }
        // 存在的号码列表
        console.log('groupNumberOrderList');
        const existingIds = this.groupNumberOrderList.map(item => item.value);
        // console.log(existingIds, 'existingIds');
        // 过滤掉 selectListData 中已存在的号码
        const newItems = this.serviceSelectAll.filter(item => !existingIds.includes(item.value));
        const numberItem = existingIds.filter(item => item !== undefined);

        // console.log(newItems, 'newItems', numberItem,newItems?.length ,this.serviceSelectAll?.length);
        if (newItems?.length < this.serviceSelectAll?.length) {
          this.$message.error(
            `${numberItem.map(item => item).join(', ')} ${this.$t(
              'assignNumber.numberExistedTips',
            )}`,
          );
          return;
        }
        if (this.businessType === 'addDdiExtensionNumber') {
          //   // 初始化一个数组用于存储不满足条件的项
          //   const numberNoHas = [];

          this.serviceSelectAll?.forEach(item => {
            if (!this.filterNoTolevel(item.SERVICE_NO)) {
              return;
            }
          });
        }
        this.$emit('confirm', ServiceNoType);
        this.resetForm();
        this.formData = {
          ReserveDN: false,
          ServiceNoType: undefined,
          Criteria: '0',
          CriteriaInputValue: undefined,
          numberOfSelect: 1,
        };
      },
      // handleServiceNoQtyChange(e) {
      //   this.formData = {
      //     ...this.formData,
      //     ServiceNo: undefined,
      //     ReservationCode: undefined,
      //     BOC: undefined,
      //     Project: undefined,
      //     ServiceGroup: undefined,
      //     Service: undefined,
      //   };
      // },

      // SelectType切换时重置输入
      handleSelectTypeChange() {
        // this.$refs.selectNumformRules.clearValidate();
        this.resetForm();
        this.formKey++; //改变 key 值
      },

      // 查询下拉框选项值
      getSelectDatas() {
        querySpecialServiceGroup().then(res => {
          this.serviceGruopOrServiceList = res.DATA.map(item => {
            return {
              label: item.SPECIAL_SERVICE_GROUP + '/' + item.SPECIAL_SERVICE,
              value: item.SPECIAL_SERVICE_GROUP + '/' + item.SPECIAL_SERVICE,
            };
          });
        });
      },

      // 校验必选框是否填写
      validate() {
        return new Promise((resolve, reject) => {
          this.$refs.selectNumformRules.validate(valid => {
            if (valid) {
              if (this.formData.ServiceNoType) {
                resolve();
              }
            } else {
              this.btnLoading = false;
            }
          });
        });
      },
      getKeyByValue(dataList, value) {
        const item = dataList.find(item => item.value === value);
        return item ? item.key : null;
      },
      // 根据type字段类型调用接口
      handleResponse(res, type, SSG_OR_RESERVED_Parmas) {
        console.log('handleResponse', res, type, SSG_OR_RESERVED_Parmas);
        let telNums;
        if (type == 'querySpareDnByType') {
          telNums = [];
          res.DATA[0].TEL_NUMS.map(item => {
            telNums.push({
              TEL_NUM: item,
              DN_REQUEST_ID: res.DATA[0].DN_REQUEST_ID,
              DN_TYPE: 'GENERAL_DN',
              //idap-新增
              SERVICE_NO: item,
              number: item,
              numberType: 'New Installation',
            });
          });
        } else if (type == 'preAssignSsgOrReservedDn') {
          // 先过滤掉 RESULT 为 false 的项
          // telNums = res.DATA.filter(item => item.RESULT).map(item => item.TEL_NUM);
          telNums = res.DATA.filter(item => item.RESULT);
          telNums.forEach(element => {
            element['DN_TYPE'] = 'SSG_OR_RESERVED';
          });
          // 处理 RESULT 为 false 的项，显示错误信息
          res.DATA.forEach(item => {
            if (!item.RESULT) {
              this.$message.error(item.TEL_NUM + ':' + item.ERROR_MSG);
            }
          });
        } else if (type === 'queryWorkingNumber') {
          telNums = res.DATA[0].LIST.map(item => ({
            TEL_NUM: item.SERVICE_NO,
            USER_ID: item.USER_ID,
            //idap-新增
            SERVICE_NO: item.SERVICE_NO,
            number: item.SERVICE_NO,
            numberType: 'New Installation',
            ...item,
          }));
        } else if (type === 'queryWorkingNumberOtherCustomer') {
          telNums = res.DATA[0].LIST.map(item => ({
            TEL_NUM: item.SERVICE_NO,
            SERVICE_NO: item.TEL_NUM,
            id: item.id,
            USER_ID: item.USER_ID ?? '',
            //idap-新增
            number: item.TEL_NUM,
            numberType: 'New Installation',
            ...item,
          }));
        }
        console.log('telNums', telNums);
        if (!telNums || telNums.length == 0) {
          this.$message.warning(this.$t('assignNumber.noResultsMatch'));
          return;
        }

        // 对号码进行升序排序
        telNums.sort((a, b) => a.TEL_NUM.localeCompare(b.TEL_NUM));
        console.log('telNums', telNums);

        // 获取 serviceList 的当前长度
        const currentLength = this.serviceList.length;
        const returnMaxSEQUENCE = this.returnMaxValueOfCALL_SEQUENCE(); // 获取数组中callSequence中的最大值

        let serviceNoTypeKey = this.getKeyByValue(
          config.serviceNoType,
          this.formData.ServiceNoType,
        );
        // 生成新的数组，并从 currentLength 开始递增 serialNumber
        const newItems = telNums.map((item, index) => ({
          USER_ID: item.USER_ID ?? '',
          sequence: returnMaxSEQUENCE + index + 1,
          value: item.TEL_NUM,
          key: item.TEL_NUM,
          DN_REQUEST_ID: item.DN_REQUEST_ID,
          DN_TYPE: item.DN_TYPE,
          ServiceNoType: this.formData.ServiceNoType,
          serviceNoTypeKey: serviceNoTypeKey,
          SERVICE_NO: item.TEL_NUM,
          BID: item.BID || '',
          CUST_ID: item.CUST_ID || '',
          // 特殊群组或预留号码 需要添加 preAssignSsgOrReservedDn 以下字段
          ...(type == 'preAssignSsgOrReservedDn' && {
            RESERVATION_ACCOUNT: SSG_OR_RESERVED_Parmas.RESERVATION_ACCOUNT ?? '',
            BOC: SSG_OR_RESERVED_Parmas.BOC ?? '',
            PROJECT_CODE: SSG_OR_RESERVED_Parmas.PROJECT_CODE ?? '',
            SPECIAL_SERVICE_GROUP: SSG_OR_RESERVED_Parmas.SPECIAL_SERVICE_GROUP ?? '',
            SPECIAL_SERVICE: SSG_OR_RESERVED_Parmas.SPECIAL_SERVICE ?? '',
          }),
          ...(this.selectNumberType == 'citinet' && {
            STANDARD_ADDRESS: item.STANDARD_ADDRESS,
            ACCOUNT_LIST: item.ACCOUNT_LIST,
            DEPARTMENTAL_BILL: item.DEPARTMENTAL_BILL,
            DEPARTMENTAL_BILL_NAME: item.DEPARTMENTAL_BILL_NAME,
            STANDARD_ADDRESS_ID: item.STANDARD_ADDRESS_ID,
            EN_ADDRESS: item.EN_ADDRESS,
          }),
          ...(this.selectNumberType == 'hunting' && {
            serialNumber: currentLength + index + 1,
          }),
          // todo                   -------------------- 纳入问题待完成  2025.1.21
          ...(type == 'queryWorkingNumber' && { numberType: 'naru', USER_ID: item.USER_ID }),
          ...(type == 'queryWorkingNumberOtherCustomer' && { USER_ID: item.USER_ID }),
          ...((type == 'queryWorkingNumber' || type == 'queryWorkingNumberOtherCustomer') &&
            (this.businessType == 'addDdiExtensionNumber' ||
              this.businessType === 'addAddonNumber') && {
              NUBMER_TYPE: item.numberType,
              // this.businessType == 'addDdiExtensionNumber'
              //   ? 'DDI Extension Number'
              //   : 'Add-on Number',
              USER_ID: item.USER_ID,
              SERVICE_NO: item.TEL_NUM,
              id: item.id,
              NUMBER: 'item.SERVICE_NO',
              status: 'new',
            }),
          // ...(type == 'queryDDIExtensionNumber' &&
          //   this.businessType == 'addDdiExtensionNumber' && {
          //     id: item.id,
          //     numberType: item.NUBMER_TYPE,
          //     number: item.NUBMER,
          //     SERVICE_NO: item.TEL_NUM,
          //     status: 'new',
          //   }),
          ...((this.businessType == 'addAddonNumber' ||
            this.businessType == 'addDdiExtensionNumber') && {
            status: 'new',
            NUBMER_TYPE: item.numberType,
            USER_ID: item.USER_ID,
            SERVICE_NO: item.TEL_NUM,
            id: item.id,
            NUMBER: item.TEL_NUM,
          }),
          // ...(( this.businessType === 'addOnNumber' )&& {
          //   id: item.id,
          //   numberType: this.businessType == 'addAddonNumber'?'Add-on Number' : '',
          //   number: item.TEL_NUM,
          //   SERVICE_NO: item.TEL_NUM??''
          // }),
        }));

        // 将新数组合并到 serviceList 中
        this.serviceList = [...this.serviceList, ...newItems];
        console.log('7788', this.serviceList);
        this.serviceList = Array.from(new Set(this.serviceList.map(JSON.stringify))).map(
          JSON.parse,
        );
        this.serviceList = concatArray(this.serviceList, 'value');
        // 使用 filter 方法过滤掉value空值
        this.serviceList = this.serviceList.filter(item => {
          return item.value;
        });
        console.log('99999', this.serviceList);
        this.Allchecked = true;
        this.serviceSelectAll = this.serviceList;
      },
      // 返回数据中CALL_SEQUENCE最大的值
      returnMaxValueOfCALL_SEQUENCE() {
        console.log('groupNumberOrderList');
        if (this.groupNumberOrderList.length === 0) {
          return 0; // 或者返回一个默认值，如 0
        }
        const callSequenceValues = this.groupNumberOrderList
          .filter(item => item.sequence)
          .map(item => parseInt(item.sequence, 10));
        console.log(callSequenceValues, ' callSequenceValues---------------------');
        if (callSequenceValues.length < 1) {
          return this.groupNumberOrderList.length;
        }
        const maxValue = Math.max(...callSequenceValues);
        return maxValue;
      },
      // 处理错误
      handleError(error) {
        console.error('An error occurred:', error);
      },
      // 获取号码列表
      async handleQueryService() {
        await this.validate();
        if (this.formData.ServiceNoType === 'New Installation') {
          this.getNewInstallation();
        } else if (this.formData.ServiceNoType === 'Working Number(Other Customer)') {
          this.getWorkingNumberOtherCustomer();
        } else if (this.formData.ServiceNoType === 'DDI Extension Number') {
          this.getDDIExtensionNumber();
        } else {
          this.getWorkingNumber();
        }
      },
      // 搜索其它用户下DDIExtensionNumber号码
      getDDIExtensionNumber() {
        const {
          ServiceNoType,
          selectType,
          Criteria,
          CriteriaInputValue,
          ServiceNoQty,
          numberOfSelect,
          serviceNoNormal,
          serviceNoReserve,
          serviceNoSSG,
          reservationCode,
          BOC,
          Project,
          serviceGruopOrService,
          reservationCodeDN,
          BOCDN,
          ProjectDN,
        } = this.formData;
        let dataToSend;
        const serviceNoArray = serviceNoReserve?.match(/[，,]/)
          ? serviceNoReserve.split(/[，,]/)
          : [serviceNoReserve];
        const GruopOrServiceArray = serviceGruopOrService.split('/');
        dataToSend = serviceNoArray.map(item => ({
          IS_NCR: true, //固定传值
          TEL_NUM: item,
          RESERVATION_ACCOUNT: reservationCodeDN,
          BOC: BOCDN,
          PROJECT_CODE: ProjectDN,
          SPECIAL_SERVICE_GROUP: GruopOrServiceArray[0],
          SPECIAL_SERVICE: GruopOrServiceArray[1],
        }));
        if (dataToSend) {
          const reserveParmas = {
            IS_VERIFY: true,
            TEL_TYPE: 'SSG_OR_RESERVED',
            DATA: dataToSend,
            PRODUCT_TYPE: this.productType?.value || null,
          };
          this.btnLoading = true;
          querypreAssignSsgOrReservedDn(reserveParmas)
            .then(res => {
              this.handleResponse(res, 'queryDDIExtensionNumber', dataToSend);
            })
            .finally(() => {
              this.btnLoading = false;
            });
        }
      },
      // 搜索其它用户下workingNumber号码
      getWorkingNumberOtherCustomer() {
        // this.formData.serviceNoOfWorkingNum
        const reqData = {
          SERVICE_NO: this.formData.serviceNoOfWorkingNumOther,
          CUSTOMER_NAME: this.formData.customerNameOfWorkingNumOther,
        };
        this.btnLoading = true;
        queryWorkingNumber(reqData)
          .then(res => {
            this.handleResponse(res, 'queryWorkingNumberOtherCustomer');
          })
          .finally(() => {
            this.btnLoading = false;
          });
      },
      // 搜索workingNumber号码
      getWorkingNumber() {
        // this.formData.serviceNoOfWorkingNum
        const dataArray = this.formData.serviceNoOfWorkingNum?.match(/[，,]/)
          ? this.formData.serviceNoOfWorkingNum.split(/[，,]/)
          : [this.formData.serviceNoOfWorkingNum];

        if (dataArray.length > 3) {
          this.$message.error(this.$t('assignNumber.numberQuantityLimit'));
          return;
        }
        const reqData = {
          CUSTOMER_NO: this.custId,
          SERVICE_NO: this.formData.serviceNoOfWorkingNum,
        };
        this.btnLoading = true;
        queryWorkingNumber(reqData)
          .then(res => {
            this.handleResponse(res, 'queryWorkingNumber');
          })
          .finally(() => {
            this.btnLoading = false;
          });
      },
      // 查询选号服务
      async getNewInstallation() {
        const {
          ServiceNoType,
          selectType,
          Criteria,
          CriteriaInputValue,
          ServiceNoQty,
          numberOfSelect,
          serviceNoNormal,
          serviceNoReserve,
          serviceNoSSG,
          reservationCode,
          BOC,
          Project,
          serviceGruopOrService,
          reservationCodeDN,
          BOCDN,
          ProjectDN,
        } = this.formData;

        if (!ServiceNoType || !selectType) {
          this.$message.error(this.$t('assignNumber.queryConditionInput'));
          this.btnLoading = false;
          return;
        }

        this.btnLoading = true;

        try {
          if (selectType === 'Normal' && Criteria === '0' && serviceNoNormal) {
            const serviceNoQty = Number(ServiceNoQty);
            // if (/^0+/.test(ServiceNoQty) || serviceNoQty != 1) {
            //   this.$message.error(
            //     this.$t('assignNumber.serviceNoQtyOfInterval', {
            //       ServiceNoQty: this.$t('customerVerify.ServiceNoQty'),
            //     }),
            //   );
            //   return;
            // }
            const numbers = numberOfSelect * serviceNoQty;
            if (numbers != 1) {
              this.$message.error(
                this.$t('assignNumber.numberOfSelectAndServiceNoQtyValue', {
                  numberOfSelect: this.$t('customerVerify.numberOfSelect'),
                  ServiceNoQty: this.$t('customerVerify.ServiceNoQty'),
                }),
              );
              return;
            }
            const normalParmas = {
              NUM_OF_RECORD: numbers,
              TEL_TYPE: 'GENERAL_DN',
              START_DIGIT: serviceNoNormal,
              LAST_DIGIT: '',
            };

            const res = await querySpareDnByType(normalParmas);
            this.handleResponse(res, 'querySpareDnByType');
          } else if (selectType === 'Normal') {
            // if (/^0+/.test(ServiceNoQty)) {
            //   this.$message.error('ServiceNoQty 必须是一个介于 1 到 20 之间的数字！');
            //   return;
            // }

            const serviceNoQty = Number(ServiceNoQty);
            if (
              /^0+/.test(ServiceNoQty) ||
              serviceNoQty < 1 ||
              serviceNoQty > 20 ||
              serviceNoQty == 0
            ) {
              this.$message.error(
                this.$t('assignNumber.serviceNoQtyOfInterval', {
                  ServiceNoQty: this.$t('customerVerify.ServiceNoQty'),
                }),
              );
              return;
            }

            const numbers = numberOfSelect * serviceNoQty;
            if (numbers > 200) {
              this.$message.error(
                this.$t('assignNumber.numberOfSelectAndServiceNoQtyOfProduct', {
                  numberOfSelect: this.$t('customerVerify.numberOfSelect'),
                  ServiceNoQty: this.$t('customerVerify.ServiceNoQty'),
                }),
              );
              return;
            }

            const normalParmas = {
              NUM_OF_RECORD: numbers,
              TEL_TYPE: 'GENERAL_DN',
              START_DIGIT: Criteria === '1' ? CriteriaInputValue : '',
              LAST_DIGIT: Criteria === '2' ? CriteriaInputValue : '',
            };

            const res = await querySpareDnByType(normalParmas);
            this.handleResponse(res, 'querySpareDnByType');
          } else {
            let dataToSend;
            let preAssignSsgOrReservedDnParmas;
            if (selectType === 'Reserve') {
              const dataArray = serviceNoReserve?.match(/[，,]/)
                ? serviceNoReserve.split(/[，,]/)
                : [serviceNoReserve];
              dataToSend = dataArray.map(item => ({
                TEL_NUM: item,
                RESERVATION_ACCOUNT: reservationCode,
                BOC: BOC,
                PROJECT_CODE: Project,
              }));
              preAssignSsgOrReservedDnParmas = {
                RESERVATION_ACCOUNT: reservationCode,
                BOC: BOC,
                PROJECT_CODE: Project,
              };
            } else if (selectType === 'Special Service Group') {
              const dataArray = serviceNoSSG?.match(/[，,]/)
                ? serviceNoSSG.split(/[，,]/)
                : [serviceNoSSG];
              const GruopOrServiceArray = serviceGruopOrService.split('/');
              dataToSend = dataArray.map(item => ({
                TEL_NUM: item,
                RESERVATION_ACCOUNT: reservationCodeDN,
                BOC: BOCDN,
                PROJECT_CODE: ProjectDN,
                SPECIAL_SERVICE_GROUP: GruopOrServiceArray[0],
                SPECIAL_SERVICE: GruopOrServiceArray[1],
              }));
              preAssignSsgOrReservedDnParmas = {
                RESERVATION_ACCOUNT: reservationCodeDN,
                BOC: BOCDN,
                PROJECT_CODE: ProjectDN,
                SPECIAL_SERVICE_GROUP: GruopOrServiceArray[0],
                SPECIAL_SERVICE: GruopOrServiceArray[1],
              };
            }

            if (dataToSend) {
              const reserveParmas = {
                IS_VERIFY: true,
                TEL_TYPE: 'SSG_OR_RESERVED',
                DATA: dataToSend,
                PRODUCT_TYPE: this.productType?.value || null,
              };

              const res = await preAssignSsgOrReservedDn(reserveParmas);
              // 特殊群组或预留号码需要把入参添加进去
              this.handleResponse(res, 'preAssignSsgOrReservedDn', preAssignSsgOrReservedDnParmas);
            }
          }
        } catch (error) {
          this.handleError(error);
        } finally {
          this.btnLoading = false;
        }
      },

      /* 重置 */
      resetForm() {
        this.formData = {
          selectType: this.formData.selectType, // 选择类型
          ServiceNoType: this.formData.ServiceNoType,
          Criteria: '0',
          numberOfSelect: 1,
        };
        // this.formData = {
        //   selectType: this.formData.selectType, // 选择类型
        //   ServiceNoType: this.formData.ServiceNoType,
        //   Criteria: '0',
        //   numberOfSelect: 1,
        //   serviceNo: '', // 确保 serviceNo 被重置
        //   reservationCode: '',
        //   BOC: '',
        //   Project: '',
        //   serviceGroup: '',
        //   reservationCodeDN: '',
        //   BOCDN: '',
        //   ProjectDN: '',
        // };

        // 重置表单字段的值和验证状态
        // this.$refs.selectNumformRules.resetFields();
        this.$refs.selectNumformRules.clearValidate();
        this.serviceSelectAll = [];
        this.serviceList = []; // 清空数据
        this.Allchecked = false;
      },
      // 点击号码
      handleRuleSelect(val) {
        const selectAll = JSON.parse(JSON.stringify(this.serviceSelectAll));
        const status = selectAll.some(item => item.key === val.key);
        if (status) {
          const data = selectAll.filter(item => item.key !== val.key);
          this.serviceSelectAll = data;
        } else {
          const data = [...selectAll, val];
          this.serviceSelectAll = data;
        }
        // hunting业务需要排序 加上一个serialNumber变量
        this.selectNumberType == 'hunting' &&
          this.serviceSelectAll.forEach((item, index) => {
            item.serialNumber = index + 1;
          });
        this.Allchecked = this.serviceSelectAll.length === this.serviceList.length;
      },
      // 全选checkBox
      onSelectChange(e) {
        this.Allchecked = e.target.checked;
        this.serviceSelectAll = e.target.checked ? [...this.serviceList] : [];
      },
    },
  };
</script>

<style lang="less" scoped>
  .moadl-button-Ok {
    padding: 0;
  }

  .header {
    button + button {
      margin-left: 10px;
    }
    .b-btns {
      padding-right: 0 !important;
      .btns-contaniner {
        height: 100%;
        padding-bottom: 18px;
        display: flex;
        align-items: end;
        justify-content: end;
      }
    }
  }

  .address-divider {
    // 分割线间距
    .ant-divider-horizontal {
      margin: 16px 0 !important;
    }
  }

  .main {
    .footer {
      display: flex;
      margin-top: 10px;
      .checkAll {
        width: 400px;
      }
      .pagination {
        flex: 1;
        display: flex;
        justify-content: end;
      }
    }
  }
  .flex-right {
    display: flex;
    justify-content: right;
    margin-top: 10px;
    padding-bottom: 10px;
  }
</style>
