<template>
  <div class="messageModal">
    <a-modal
      :visible="visible"
      :zIndex="1001"
      centered
      :footer="null"
      :width="550"
      :height="330"
      :maskClosable="false"
      @cancel="cancel"
    >
      <div class="content">
        <em class="iconfont icon-wancheng" />
        <div class="headermessage">{{ messageTitleTips }}</div>
        <div class="subTextcontent">{{ subText }}</div>
        <div class="btnRow">
          <a-button v-show="displayCancelBtn" ghost type="primary" @click="cancel">{{
            $t('common.buttonCancel')
          }}</a-button>
          <a-button :loading="loading" type="primary" @click="confirm">{{ confirmText }}</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
  export default {
    name: 'MessageModal',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      messageTitleTips: {
        type: String,
        default: '',
      },
      subText: {
        type: String,
        default: '',
      },
      confirmText: {
        type: String,
        default: '',
      },
      displayCancelBtn: {
        type: Bo<PERSON>an,
        default: true,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      cancel() {
        this.$emit('cancel');
      },
      confirm() {
        this.$emit('confirm');
      },
    },
  };
</script>

<style scoped lang="less">
  .content {
    width: 100%;
    height: 100%;
    text-align: center;
    padding: 20px 0 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    .iconfont {
      font-size: 60px;
      color: #2dcb31;
    }
    .headermessage {
      color: #000000;
      font-size: 24px;
      font-weight: 700;
    }

    .subTextcontent {
      font-size: 14px;
      color: #373d41;
      text-align: center;
      line-height: 22px;
      font-weight: 500;
    }
    .btnRow {
      margin-top: 32px;
      display: flex;
      justify-content: center;
    }
    .btnRow button:first-child {
      margin-right: 20px !important;
    }
  }
  /deep/ .ant-modal-body {
    padding: 0 10px !important;
    width: 550px;
    height: 330px;
  }
  /deep/ .ant-modal-header {
    padding: 0 10px !important;
    border-bottom: 0px;
  }
  /deep/ .ant-modal-content {
    border-radius: 8px !important;
  }
</style>
