<template>
  <div class="textarea-wrapper">
    <a-textarea
      class="textarea"
      :value="value"
      @input="onInput"
      :rows="rows"
      :maxLength="ifShowLimit ? upperLimit : null"
      :placeholder="$t('common.inputPlaceholder')"
      :disabled="disabled"
    />
    <div class="text-count" :class="[`text-count__${countPosWay}`]" v-if="ifShowLimit">
      <span class="text-length" :class="textLength >= upperLimit ? 'red-text' : ''">{{
        textLength
      }}</span
      ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TextareaWrapper',
    props: {
      value: {
        type: String,
        default: '',
      },
      rows: {
        type: Number,
        default: 3,
      },
      // 限制字数
      upperLimit: {
        type: Number,
        default: 500,
      },
      // 是否显示限制
      ifShowLimit: {
        type: Boolean,
        default: true,
      },
      // 数字显示位置 -- inner / outer
      countPosWay: {
        type: String,
        default: 'outer',
      },
      disabled: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {};
    },
    computed: {
      textLength() {
        if (typeof this.value === 'number') {
          return String(this.value).length;
        }
        return (this.value || '').length;
      },
    },
    methods: {
      onInput(e) {
        // a-textarea 可能直接返回字符串或 event
        const val = e && e.target ? e.target.value : e;
        this.$emit('input', val);
      },
    },
  };
</script>

<style lang="less" scoped>
  .textarea-wrapper {
    position: relative;
    display: block;
    .textarea {
      height: 190px;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      line-height: 20px;
      font-weight: 400;
    }
    .text-count {
      display: flex;
      justify-content: flex-end;
      &__inner {
        position: absolute;
        right: 10px;
        bottom: 10px;
      }
      &__outer {
        padding-top: 3px;
      }
      .text-length {
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .red-text {
        font-size: 14px;
        color: #e60017;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .upper-limit {
        font-size: 14px;
        color: #9e9e9e;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
    }
  }
</style>
