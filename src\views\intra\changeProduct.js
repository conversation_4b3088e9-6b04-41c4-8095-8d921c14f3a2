import {
  elementSort,
  generateFourDigitNumber,
} from '@/views/components/productTreeList/common/utils';
import { getElements } from '@/api/customerVerify';
import that from '@/main.js';

// 产品数据处理，调属性接口
export const dealProductElementAttrList = async function (list) {
  let attrLoadOfElementList = await getElementList(list);
  if (attrLoadOfElementList.length) {
    let obj = await getElementsApi(attrLoadOfElementList);
    let newList = await dealProductList(list, obj);
    return newList;
  }
  return list;
};

// 初始化调用elements接口，赋值interfaceElementList
export const getElementList = function (list) {
  return new Promise((resolve, reject) => {
    let attrLoadOfElementList = [];
    list.forEach(item => {
      (item.children || []).forEach(iitem => {
        (iitem.children || []).forEach(iiitem => {
          if (iiitem.checked && iiitem.HAS_ATTR == '1') {
            attrLoadOfElementList.push(iiitem);
          }
        });
      });
    });
    resolve(attrLoadOfElementList);
  });
};

// 获取元素属性接口
export const getElementsApi = function (attrLoadOfElementList) {
  return new Promise((resolve, reject) => {
    const params = {
      areaCode: '212',
      cityId: that.$store.state.app.userInfo.CITY_CODE,
      deptId: that.$store.state.app.userInfo.DEPART_ID,
      epachyId: that.$store.state.app.userInfo.EPARCHY_CODE,
      loginEpachyId: '0010',
      loginProvinceId: '11',
      pageData: true,
      provinceId: that.$store.state.app.userInfo.PROVINCE_CODE,
      staffId: that.$store.state.app.userInfo.STAFF_ID,
      tradeEpachyId: '0010',
    };
    // 入参
    let data = attrLoadOfElementList.map(x => {
      return x.DISCNT_CODE ? `DISCNT_${x.DISCNT_CODE}` : `SERVICE_${x.SERVICE_ID}`;
    });
    getElements(data, params).then(res => {
      const obj = res.DATA[0];
      resolve(obj);
    });
  });
};

// 处理产品数据
export const dealProductList = function (list, obj) {
  return new Promise((resolve, reject) => {
    let newList = JSON.parse(JSON.stringify(list)).map(x => {
      return {
        ...x,
        children: x.children.map(xx => {
          return {
            ...xx,
            children: xx.children.map(xxx => {
              // 增加属性数据
              if (xxx.checked && xxx.DISCNT_CODE) {
                let y = obj[`DISCNT_${xxx.DISCNT_CODE}`];
                if (y) {
                  // [x] 表示 当前的产品数据
                  let contractPeriod = getMrcElementItem(xx, xxx) || '';
                  // 删除元素会导致属性数据（interfaceElementList）被清空，但是存了个备份的interfaceElementListBackup
                  xxx.interfaceElementList = xxx.interfaceElementListBackup =
                    getInterfaceElementList(y.interfaceElement, y, contractPeriod, xxx);
                }
              }
              // 增加属性数据
              if (xxx.checked && xxx.SERVICE_ID) {
                let y = obj[`SERVICE_${xxx.SERVICE_ID}`];
                if (y) {
                  // 删除元素会导致属性数据（interfaceElementList）被清空，但是存了个备份的interfaceElementListBackup
                  xxx.interfaceElementList = xxx.interfaceElementListBackup =
                    getInterfaceElementList(y.interfaceElement, y, '', xxx);
                }
              }
              return xxx;
            }),
          };
        }),
      };
    });
    resolve(newList);
  });
};

// 属性数据处理
export const getInterfaceElementList = function (list, obj, contractPeriod, xxx) {
  return list.map(item => {
    // obj有值，说明是接口返回的，针对selectInitialData 对象是否含有elementCode值，将返回同级的selectInitialData下拉选择框数据，放到对应的item里面
    if (obj && Object.prototype.hasOwnProperty.call(obj.selectInitialData, item.elementCode)) {
      item.selectInitialDataList = obj.selectInitialData[item.elementCode];
    }

    // 是否有attributeList字段，将用户已订购的属性和全量的进行匹配，回显数据
    if (xxx?.attributeList?.length) {
      let orderedObj = xxx.attributeList.find(
        y => item.elementCode == y.CRM_ATTR_CODE || item.elementCode == y.ATTR_CODE,
      );
      if (orderedObj) {
        // 存在已订购的属性，则回显已订购的数据
        item.isOrdered = '1'; // 标识已订购
        item.orderedValue = orderedObj.CRM_ATTR_VALUE || orderedObj.ATTR_VALUE; // 存储已订购的值
        item[item.elementCode] = orderedObj.CRM_ATTR_VALUE || orderedObj.ATTR_VALUE || undefined;
      } else {
        // 不存在已订购的属性，则回显默认值
        item[item.elementCode] = item.intfElementInitValue || undefined;
      }
    } else {
      item[item.elementCode] = item.intfElementInitValue || undefined;
    }

    // 合约期保持和MRC的一致，且不可修改
    if (item.elementCode == 'contract_period' && contractPeriod) {
      // that.$set(item, 'contract_period', contractPeriod);
      that.$set(item, 'modifyRightCode', 'false');
    }

    // 与that.elementCode值一样，且该属性值为空的时候（注：不为空说明生成了或者填写过了）
    if (item.elementCode == 'PSWD' && !item[item.elementCode]) {
      // 自动生成0000-9999之间的四位数字
      that.$set(item, item.elementCode, generateFourDigitNumber());
    }
    return item;
  });
};

// 找MRC对应这项元素出来
export const getMrcElementItem = function (list, xxx) {
  // 找到Rebate对应的MRC（对应的关联关系）
  const obj = (xxx.DISCNT_ITEM || []).find(x => x.ATTR_CODE == 'rebate_mapping_mrc');
  if (obj) {
    // 找到Rebate对应关联关系的MRC的元素 注：ATTR_VALUE的值是MRC元素的DISCNT_CODE
    const element = list?.children.find(x => x.DISCNT_CODE == obj.ATTR_VALUE);
    if (element) {
      // 找到对应的MRC的值是多少
      const mrcObj = (element?.interfaceElementList || []).find(
        x => x.elementCode == 'contract_period',
      );
      if (mrcObj) {
        return mrcObj[mrcObj.elementCode];
      }
    }
  }
};

// 回显数据，即 添加 checked 字段，将已订购的勾上, isEdit是否可编辑，不可以编辑去掉未选中数据
export const addCheckedField = function (allData, orderedData, isEdit = true) {
  let isEditValue = isEdit;
  let detailAllData = [];
  // 遍历 all 数据
  allData.forEach(productItem => {
    if (
      !isEdit &&
      productItem.PRODUCT_MODE != '00' &&
      productItem.PRODUCT_TYPE_CODE != '300010' &&
      productItem.PRODUCT_TYPE_CODE != '300013'
    ) {
      // 附加产品可以编辑新增
      isEditValue = true;
    } else {
      isEditValue = false;
    }
    console.log('isEditValue', isEditValue);
    // 检查产品是否在 data 中
    const orderedProduct = orderedData?.find(x => x.PRODUCT_ID == productItem.PRODUCT_ID);
    productItem.checked = !!orderedProduct;
    // 下单需要入参的字段
    productItem.PROD_ITEM_ID = orderedProduct?.PROD_ITEM_ID || '';
    productItem.START_DATE = orderedProduct?.START_DATE || '';
    productItem.PRODUCT_TYPE_CODE = orderedProduct?.PRODUCT_TYPE_CODE || '';

    // 遍历包
    let PACKAGES = [];
    (productItem.PACKAGES || productItem.PACKAGE_LIST || []).forEach(packageItem => {
      const orderedPackage = orderedProduct?.PACKAGE_LIST.find(
        x => x.PACKAGE_ID == packageItem.PACKAGE_ID,
      );
      packageItem.checked = !!orderedPackage;

      // 遍历资费
      let TD_B_DISCNTS = [];
      (packageItem.TD_B_DISCNTS || packageItem.DISCNT_LIST || []).forEach(discount => {
        const orderedDiscount = orderedPackage?.DISCNT_LIST.find(
          x => x.DISCNT_CODE == discount.DISCNT_CODE,
        );
        discount.checked = !!orderedDiscount;
        if (!isEditValue && discount.checked) {
          TD_B_DISCNTS.push(discount);
        }
        // 属性
        discount.DISCNT_ITEM_LIST = orderedDiscount ? orderedDiscount.DISCNT_ITEM_LIST : [];
        // 下单需要入参的字段
        discount.DIS_ITEM_ID =
          orderedDiscount?.DIS_ITEM_ID || orderedDiscount?.ELEMENT_ITEM_ID || '';
        discount.START_DATE = orderedDiscount?.START_DATE || '';
      });

      // 遍历服务
      let TD_B_SERVICES = [];
      (packageItem.TD_B_SERVICES || packageItem.SERVICE_LIST || []).forEach(service => {
        const orderedService = orderedPackage?.SERVICE_LIST.find(
          x => x.SERVICE_ID == service.SERVICE_ID,
        );
        service.checked = !!orderedService;
        if (!isEditValue && service.checked) {
          TD_B_SERVICES.push(service);
        }
        // 属性
        service.SERVICE_ITEM_LIST = orderedService ? orderedService.SERVICE_ITEM_LIST : [];
        // 下单需要入参的字段
        service.SERVICE_ITEM_ID =
          orderedService?.SERVICE_ITEM_ID || orderedService?.ELEMENT_ITEM_ID || '';
        service.START_DATE = orderedService?.START_DATE || '';
      });
      if (!isEditValue && packageItem.checked) {
        packageItem.TD_B_DISCNTS = TD_B_DISCNTS;
        packageItem.TD_B_SERVICES = TD_B_SERVICES;
        PACKAGES.push(packageItem);
      }
    });
    if (!isEdit) {
      if (!isEditValue && productItem.checked) {
        //不可编辑选中数据
        detailAllData.push({ ...productItem, PACKAGES: PACKAGES });
      } else if (!isEditValue && !productItem.checked) {
        // 不可编辑且没有选中不展示
        detailAllData.push({ ...productItem, PACKAGES: [] });
      } else {
        // 附件产品可以编辑新增
        detailAllData.push({ ...productItem });
      }
    }
  });
  if (!isEdit) {
    return detailAllData;
  }
  return allData;
};

// 将数据转换成产品树组件的数据格式
// initStatusBool 初始化的状态
// checked 操作后的状态
export const convertToTreeData = function (showData, FORCE_TAG) {
  const treeData = showData.map(item => {
    return {
      key: String(item.PRODUCT_ID), // 展开的绑定的值
      type: 'Product', // 展示的type类型
      ID: item.PRODUCT_ID,
      checked: item.checked,
      FORCE_TAG: FORCE_TAG,
      NAME: item.PRODUCT_NAME,
      initStatusBool: item.checked,
      ...item,
      children: (item.PACKAGES || item.PACKAGE_LIST)?.map(iitem => {
        let discntsList = [];
        let servicesList = [];
        const productPackageKey = `${item.PRODUCT_ID}-${iitem.PACKAGE_ID}`;

        // 包下的资费
        if (
          (iitem.TD_B_DISCNTS || iitem.DISCNT_LIST) &&
          (iitem.TD_B_DISCNTS || iitem.DISCNT_LIST).length
        ) {
          discntsList = transferDiscnts(iitem.TD_B_DISCNTS || iitem.DISCNT_LIST, productPackageKey);
        }

        // 包下的服务
        if (
          (iitem.TD_B_SERVICES || iitem.SERVICE_LIST) &&
          (iitem.TD_B_SERVICES || iitem.SERVICE_LIST).length
        ) {
          servicesList = transferServices(
            iitem.TD_B_SERVICES || iitem.SERVICE_LIST,
            productPackageKey,
          );
        }

        let elementList = elementSort([...discntsList, ...servicesList]);

        elementList = elementList?.map(iiitem => {
          // 将资费和服务属性两个字段转换成一个字段
          let attributeList = iiitem.DISCNT_ITEM_LIST
            ? iiitem.DISCNT_ITEM_LIST
            : iiitem.SERVICE_ITEM_LIST;
          // 如果是rebate_fee，要转成正数，且为0不需要加 - 符号
          attributeList = dealRebateFee(attributeList);
          return {
            ...iiitem,
            // 存储这个字段用于判断给回显用户已订购数据，在产品树组件里的attributeEdit文件里做处理
            attributeList,
          };
        });

        // 包
        return {
          key: productPackageKey,
          type: 'Package',
          checked: iitem.FORCE_TAG == '1' ? true : iitem.checked,
          NAME: iitem.PACKAGE_NAME,
          children: elementList,
          initStatusBool: iitem.FORCE_TAG == '1' ? true : iitem.checked,
          ...iitem,
        };
      }),
    };
  });
  return treeData;
};

// 处理Rebate费用展示，以及提交入参的问题（展示正数，入参负数）
export const dealRebateFee = function (attributeList) {
  return attributeList?.map(x => {
    // MACD的接口返回的是CRM_ATTR_CODE、CRM_ATTR_VALUE
    if (x.CRM_ATTR_CODE == 'rebate_fee' && x.CRM_ATTR_VALUE && x.CRM_ATTR_VALUE !== '0') {
      x.CRM_ATTR_VALUE = String(Math.abs(x.CRM_ATTR_VALUE));
    }

    // 改单的接口返回的是ATTR_CODE、ATTR_VALUE
    if (x.ATTR_CODE == 'rebate_fee' && x.ATTR_VALUE && x.ATTR_VALUE !== '0') {
      x.ATTR_VALUE = String(Math.abs(x.ATTR_VALUE));
    }
    return x;
  });
};

// 包下的资费
export const transferDiscnts = function (list, productPackageKey) {
  return list.map(iiitem => {
    return {
      key: `${productPackageKey}-${iiitem.DISCNT_CODE}`,
      type: 'Pricing',
      checked: iiitem.FORCE_TAG == '1' ? true : iiitem.checked,
      NAME: iiitem.DISCNT_NAME,
      initStatusBool: iiitem.FORCE_TAG == '1' ? true : iiitem.checked,
      ...iiitem,
    };
  });
};

// 包下的服务
export const transferServices = function (list, productPackageKey) {
  return list.map(iiitem => {
    return {
      key: `${productPackageKey}-${iiitem.SERVICE_ID}`,
      type: 'Vas',
      checked: iiitem.FORCE_TAG == '1' ? true : iiitem.checked,
      NAME: iiitem.SERVICE_NAME,
      initStatusBool: iiitem.FORCE_TAG == '1' ? true : iiitem.checked,
      ...iiitem,
    };
  });
};

// 产品项
export const getProductItem = function (productItem, modifyTag = '0') {
  return {
    PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID,
    PRODUCT_NAME: productItem.NAME,
    PARENT_PRODUCT_ID: '',
    PRODUCT_MODE: productItem.PRODUCT_MODE,
    START_DATE: modifyTag == '1' ? productItem.START_DATE : '',
    END_DATE: '',
    PROD_ITEM_ID: productItem.PROD_ITEM_ID || '',
    MODIFY_TAG: modifyTag,
  };
};

// 元素项
export const getElementItem = function (productItem, packageItem, elementItem, modifyTag = '0') {
  return {
    ELEMENT_ID: elementItem.DISCNT_CODE || elementItem.SERVICE_ID,
    ELEMENT_NAME: elementItem.DISCNT_NAME || elementItem.SERVICE_NAME,
    ELEMENT_TYPE_CODE: elementItem.DISCNT_CODE ? 'D' : 'S',
    BELONG_PACKAGE_ID: packageItem.PACKAGE_ID,
    BELONG_PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID,
    START_DATE: modifyTag == '1' ? elementItem.START_DATE : '',
    END_DATE: '',
    ELEMENT_ITEM_ID:
      modifyTag == '0' ? '' : elementItem.DIS_ITEM_ID || elementItem.SERVICE_ITEM_ID || '',
    PROD_ITEM_ID: productItem.PROD_ITEM_ID
      ? modifyTag == '0'
        ? productItem.PROD_ITEM_ID
        : ''
      : '',
    MODIFY_TAG: modifyTag,
  };
};

// 元素属性项
export const getElementAttrItem = function (
  productItem,
  packageItem,
  elementItem,
  attrItem,
  modifyTag = '0',
) {
  return {
    ATTR_CODE: attrItem.elementCode, // 属性编码
    ATTR_TYPE: elementItem.DISCNT_CODE ? '0' : '1', // * 属性类型 资费0 服务1
    ATTR_VALUE: attrItem[attrItem.elementCode], // 属性值 将item3.elementCode作key值，例如：contract_period=12
    ATTR_VALUE_NAME: '',
    BELONG_ELEMENT_TYPE: elementItem.DISCNT_CODE ? 'D' : 'S', // 归属元素类型 D:资费，S服务
    BELONG_ELEMENT_ID: elementItem.DISCNT_CODE || elementItem.SERVICE_ID, // 归属元素id
    START_DATE: '',
    END_DATE: '',
    ELEMENT_ITEM_ID: elementItem.DIS_ITEM_ID || elementItem.SERVICE_ITEM_ID || '',
    MODIFY_TAG: modifyTag,
  };
};

// 筛选出change的三层产品数据
export const filterChangeList = function (obj, list) {
  obj = {
    productInfo: [],
    elementInfo: [],
    elementAttrInfo: [],
  };
  list.forEach(productItem => {
    // 产品
    obj.productInfo.push(getProductItem(productItem));
    productItem.children.forEach(packageItem => {
      packageItem.children.forEach(elementItem => {
        // 元素
        obj.elementInfo.push(getElementItem(productItem, packageItem, elementItem));
        elementItem.interfaceElementList &&
          elementItem.interfaceElementList.forEach(attrItem => {
            // 元素属性
            if (attrItem[attrItem.elementCode]) {
              obj.elementAttrInfo.push(
                getElementAttrItem(productItem, packageItem, elementItem, attrItem, '0'),
              );
              addStandardFeeAttr(productItem, packageItem, elementItem, attrItem, '0', obj);
            }
          });
      });
    });
  });
  return obj;
};

// 更新元素信息
export const elementInfoUpdate = function (obj, productItem, packageItem, componentsUseSceneType) {
  packageItem.children.forEach(elementItem => {
    if (elementItem.initStatusBool) {
      if (elementItem.checked) {
        // 没变
        // 但是属性可能会发生变化
        elementItem.interfaceElementList &&
          elementItem.interfaceElementList.forEach(attrItem => {
            // isOrdered 是已订购的
            if (attrItem.isOrdered == '1') {
              if (attrItem.orderedValue) {
                if (attrItem.orderedValue == attrItem[attrItem.elementCode]) {
                  // 没变
                } else {
                  // 修改
                  obj.elementAttrInfo.push(
                    getElementAttrItem(
                      productItem,
                      packageItem,
                      elementItem,
                      attrItem,
                      componentsUseSceneType == 'changeOrder' ? '0' : '2',
                    ),
                  );
                  addStandardFeeAttr(
                    productItem,
                    packageItem,
                    elementItem,
                    attrItem,
                    componentsUseSceneType == 'changeOrder' ? '0' : '2',
                    obj,
                  );
                }
              }
            } else {
              // 新增
              if (attrItem[attrItem.elementCode]) {
                obj.elementAttrInfo.push(
                  getElementAttrItem(productItem, packageItem, elementItem, attrItem, '0'),
                );
                addStandardFeeAttr(productItem, packageItem, elementItem, attrItem, '0', obj);
              }
            }
          });
      } else {
        // 删除
        console.log('删除');
        obj.elementInfo.push(getElementItem(productItem, packageItem, elementItem, '1'));
      }
    } else {
      if (elementItem.checked) {
        // 新增
        console.log('新增');
        obj.elementInfo.push(getElementItem(productItem, packageItem, elementItem, '0'));
        elementItem.interfaceElementList &&
          elementItem.interfaceElementList.forEach(attrItem => {
            // 元素属性
            if (attrItem[attrItem.elementCode]) {
              obj.elementAttrInfo.push(
                getElementAttrItem(productItem, packageItem, elementItem, attrItem, '0'),
              );
              addStandardFeeAttr(productItem, packageItem, elementItem, attrItem, '0', obj);
            }
          });
      } else {
        // 没变
        // 因为没有勾选，不需要考虑数据
      }
    }
  });
  return obj;
};

// 更新元素信息 考虑父级即包级的删除变更影响到子级
export const packageDeleteElementInfoUpdate = function (obj, productItem, packageItem) {
  packageItem.children.forEach(elementItem => {
    // true => true || true => false
    if (
      (elementItem.initStatusBool && elementItem.checked) ||
      (elementItem.initStatusBool && !elementItem.checked)
    ) {
      obj.elementInfo.push(getElementItem(productItem, packageItem, elementItem, '1'));
    }
  });
  return obj;
};

// 更新元素信息 考虑父级即包级的新增变更影响到子级
export const packageAddElementInfoUpdate = function (obj, productItem, packageItem) {
  packageItem.children.forEach(elementItem => {
    if (elementItem.FORCE_TAG == '1' || (!elementItem.initStatusBool && elementItem.checked)) {
      obj.elementInfo.push(getElementItem(productItem, packageItem, elementItem, '0'));
      elementItem.interfaceElementList &&
        elementItem.interfaceElementList.forEach(attrItem => {
          // 元素属性
          if (attrItem[attrItem.elementCode]) {
            obj.elementAttrInfo.push(
              getElementAttrItem(productItem, packageItem, elementItem, attrItem),
            );
            addStandardFeeAttr(productItem, packageItem, elementItem, attrItem, '0', obj);
          }
        });
    }
  });
  return obj;
};

// 元素属性项(增加standard_fee属性编码)
export const addStandardFeeAttr = function (
  productItem,
  packageItem,
  elementItem,
  attrItem,
  modifyTag = '0',
  obj,
) {
  const typeList = ['rent_fee', 'otc_fee', 'rebate_fee'];
  if (typeList.includes(attrItem.elementCode)) {
    obj.elementAttrInfo.push({
      ATTR_CODE: 'standard_fee', // 属性编码
      ATTR_TYPE: elementItem.DISCNT_CODE ? '0' : '1', // * 属性类型 资费0 服务1
      ATTR_VALUE: attrItem.intfElementInitValue, // 属性值 将item3.elementCode作key值，例如：contract_period=12
      ATTR_VALUE_NAME: '',
      BELONG_ELEMENT_TYPE: elementItem.DISCNT_CODE ? 'D' : 'S', // 归属元素类型 D:资费，S服务
      BELONG_ELEMENT_ID: elementItem.DISCNT_CODE || elementItem.SERVICE_ID, // 归属元素id
      START_DATE: '',
      END_DATE: '',
      ELEMENT_ITEM_ID: elementItem.DIS_ITEM_ID || elementItem.SERVICE_ITEM_ID || '',
      MODIFY_TAG: modifyTag,
    });
  }
};

// 判断一个对象中的三个特定字段是否为空数组
export const productFieldsEmptyArrays = function (
  obj,
  field1 = 'PRODUCT_INFO',
  field2 = 'ELEMENT_INFO',
  field3 = 'ELEMENT_ATTR_INFO',
) {
  // 确保 obj 是一个对象
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  // 辅助函数：判断某个字段是否是空数组
  const isEmptyArray = field => {
    const value = obj[field];
    return Array.isArray(value) && value.length === 0;
  };

  // 主判断逻辑
  return isEmptyArray(field1) && isEmptyArray(field2) && isEmptyArray(field3);
};

// import store from '@/store';
// import i18n from '@/utils/language/index';

/**
 * 下单页面五个方法(含部分报文封装，产品，元素节点的获取)
 * @param {*} getGeneralInfomationOfOrder
 * @param {*} getProductInfoList
 * @param {*} getProductAttrInfoList
 * @param {*} getElementInfoList
 * @param {*} getElementAttrInfoList
 * @returns 封装下单报文体
 */

// 下单报文部分封装
export const getGeneralInfomationOfOrder = (custId, currentTime) => ({
  // IN_MODE_CODE: '1000', // 订单接入渠道
  // EXT_ORDER_ID: getUniqueOrderID(), // 外部订单编号（针对in_mode_code对应系统的单号）
  // TRADE_PROVINCE_CODE: store.state.app.userInfo.PROVINCE_CODE, // 业务受理省分
  // TRADE_EPARCHY_CODE: store.state.app.userInfo.EPARCHY_CODE, // 业务受理地市
  // TRADE_CITY_CODE: store.state.app.userInfo.CITY_CODE, // 业务受理区县
  // TRADE_STAFF_ID: store.state.app.userInfo.STAFF_ID, // 下单员工
  // TRADE_STAFF_NAME: store.state.app.userInfo.STAFF_NAME,
  // DEPART_ID: store.state.app.userInfo.DEPART_ID, // 下单员工归属部门
  // CHANNEL_ID: '0', // 下单员工归属渠道
  // CHANNEL_TYPE: '0', // 渠道类型
  // ORDER_TIME: currentTime, // 下单时间
  // CUST_INFO: [
  //   {
  //     IS_EXTENDS_CUST: '1', //  * 是否继承已有客户0：新建客户，1：继承老客户
  //     CUST_ID: custId,
  //   },
  // ],
});
/**
 * MODIFY_TAG   修改标记 0：新增、1删除、2修改
 */

// 获取主产品节点信息
export const getProductInfoList = (item, getParentProductID, getChildProductID = '', type) => {
  const productList = getProductList(item);
  return productList.map(productItem => ({
    PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID,
    PRODUCT_NAME: productItem.NAME || productItem.PRODUCT_NAME,
    PARENT_PRODUCT_ID: handle_PARENT_PRODUCT_ID(
      productItem,
      getParentProductID,
      getChildProductID,
      type,
    ),
    PRODUCT_TYPE_CODE: productItem.PRODUCT_TYPE_CODE,
    PRODUCT_MODE: productItem.PRODUCT_MODE, // 产品模式
    MODIFY_TAG: '0',
  }));
};
// 获取主产品属性节点信息
export const getProductAttrInfoList = (
  item,
  productTypeValue,
  productTypeRoot,
  tabCountList,
  lineInstallNumber,
) => {
  const productList = getProductList(item);
  let tempList = [];
  productList.forEach(productItem => {
    tempList.push(
      {
        ATTR_CODE: productItem.PRODUCT_TYPE_CODE || productTypeValue, // 属性编码
        ATTR_TYPE: '0', // 属性类型
        ATTR_VALUE: productItem.PRODUCT_TYPE_CODE_ROOT || productTypeRoot, // 属性值
        ATTR_VALUE_NAME: '', // 属性值描述
        BELONG_PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID, // 归属产品
        MODIFY_TAG: '0', // 修改标志-----------   待做：如修改了原产品类型是否要传修改标识
      },
      {
        ATTR_CODE: 'price_element_count', // 属性编码
        ATTR_TYPE: '0', // 属性类型
        ATTR_VALUE: getPriceElementCount(
          productItem.PRODUCT_MODE,
          productItem,
          productTypeValue,
          tabCountList,
        ), // 属性值
        ATTR_VALUE_NAME: '', // 属性值描述
        BELONG_PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID, // 归属产品
        MODIFY_TAG: '0', // 修改标志-----------   待做：如修改了原产品类型是否要传修改标识
      },
      {
        ATTR_CODE: 'qty', // 属性编码
        ATTR_TYPE: '0', // 属性类型
        ATTR_VALUE: lineInstallNumber || 0, // 属性值
        ATTR_VALUE_NAME: '', // 属性值描述
        BELONG_PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID, // 归属产品
        MODIFY_TAG: '0', // 修改标志-----------   待做：如修改了原产品类型是否要传修改标识
      },
    );
  });
  return tempList;
};
/**
 * 获取订单属性节点信息
 * 注：订单行级别 - 可以不传productId
 */
export const getOrderLineAttrInfo = (item, dataSource, lineInstallNumber) => {
  const productList = getProductList(item);
  let tempList = [];
  productList.forEach(productItem => {
    tempList.push(...formatExtraToProductNode(dataSource), {
      ATTR_CODE: 'lineInstallNumber', // 属性编码
      ATTR_TYPE: '0', // 属性类型
      ATTR_VALUE: lineInstallNumber, // 属性值
      ATTR_VALUE_NAME: '', // 属性值描述
      BELONG_PRODUCT_ID: '', // 归属产品 - productItem.ID || productItem.PRODUCT_ID
      MODIFY_TAG: '0', // 修改标志-----------   待做：如修改了原产品类型是否要传修改标识
    });
  });
  return tempList;
};
// 辅助函数 - 格式化传入的数组转成主产品属性节点的格式
function formatExtraToProductNode(data, productId = '') {
  let dataObj = [];
  // 补充详情所需用到的计算结果
  if (Object.keys(data).length > 0) {
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const ele = data[key];
        dataObj.push({
          ATTR_CODE: key, // 属性编码
          ATTR_TYPE: '0', // 属性类型
          ATTR_VALUE: ele, // 属性值
          ATTR_VALUE_NAME: '', // 属性值描述
          BELONG_PRODUCT_ID: productId, // 归属产品
          MODIFY_TAG: '0', // 修改标志-----------   待做：如修改了原产品类型是否要传修改标识
        });
      }
    }
  }
  return dataObj;
}

const getPriceElementCount = (productMode, productItem, productTypeValue, tabCountList) => {
  if (productMode === '00') {
    // 主产品
    return tabCountList[0];
  }
  const productType = productItem.PRODUCT_TYPE_CODE || productTypeValue;
  if (productType == '300010') {
    // 设备
    return tabCountList[2];
  } else if (productType == '300013') {
    // 礼品
    return tabCountList[3];
  }
  return tabCountList[1]; //附件产品
};

// 获取元素节点信息
export const getElementInfoList = item => {
  const productList = getProductList(item);
  let ELEMENT_INFOLIST = [];
  productList.forEach(productItem => {
    productItem.children?.forEach(packageItem => {
      packageItem.children?.forEach(elementItem => {
        ELEMENT_INFOLIST.push({
          ELEMENT_ID: elementItem.DISCNT_CODE ?? elementItem.SERVICE_ID,
          ELEMENT_NAME: elementItem.DISCNT_NAME ?? elementItem.SERVICE_NAME,
          ELEMENT_TYPE_CODE: elementItem.DISCNT_CODE ? 'D' : 'S', // D资费 S服务
          BELONG_PACKAGE_ID: packageItem.PACKAGE_ID, // 元素归属包
          BELONG_PRODUCT_ID: productItem.ID || productItem.PRODUCT_ID, // 元素归属产品
          ELEMENT_ITEM_ID: '', // 传空
          PROD_ITEM_ID: '', // 对应的产品唯一标识
          MODIFY_TAG: '0', // 修改标识
        });
      });
    });
  });
  return ELEMENT_INFOLIST;
};
// 获取元素属性节点信息
export const getElementAttrInfoList = item => {
  const productList = getProductList(item);
  let ELEMENT_ATTR_INFOLIST = [];
  productList.forEach(productItem => {
    productItem.children?.forEach(packageItem => {
      packageItem.children?.forEach(elementItem => {
        elementItem?.interfaceElementList &&
          Array.isArray(elementItem?.interfaceElementList) &&
          elementItem?.interfaceElementList?.forEach(item4 => {
            if (item4[item4?.elementCode]) {
              addElementAttrInfoList(
                ELEMENT_ATTR_INFOLIST,
                elementItem,
                item4?.elementCode,
                handle_AttrValueOfElementAttrInfoList(item4),
              );

              // 保存资费原始值
              // 无论是否修改原值，都要把standard_fee加进去
              if (['rent_fee', 'rebate_fee', 'otc_fee', 'yrc_fee'].includes(item4?.elementCode)) {
                addElementAttrInfoList(
                  ELEMENT_ATTR_INFOLIST,
                  elementItem,
                  'standard_fee',
                  item4.intfElementInitValue,
                );
              }
            }
          });
      });
    });
  });
  return ELEMENT_ATTR_INFOLIST;
};

// 获取合并后的 productList
const getProductList = item => {
  if (item.MainProduct || item.AdditionalProduct) {
    return [...(item.MainProduct || []), ...(item.AdditionalProduct || [])];
  }
  return Array.isArray(item) ? item : [item]; // 如果 item 本身是一个对象，确保返回一个数组
};

// 处理继承的产品ID
const handle_PARENT_PRODUCT_ID = (productItem, getParentProductID, getChildProductID, type) => {
  if (type === 'CP') {
    // 获取第一页的产品id 群组附加产品就传群主主产品的ID,成员主产品就传群组主产品的ID，成员附加产品就传成员主产品的ID，
    return productItem.PRODUCT_MODE === '00' ? getParentProductID : getChildProductID;
  }
  return '0'; // 单产品
};

// 处理元素列表的 ATTR_VALUE
const handle_AttrValueOfElementAttrInfoList = item4 => {
  if (item4.elementCode === 'rebate_fee') {
    return item4[item4.elementCode] === '0'
      ? item4[item4.elementCode]
      : `-${Math.abs(item4[item4.elementCode])}`;
  }
  return item4[item4.elementCode];
};

// 添加标准费用属性
const addElementAttrInfoList = (ELEMENT_ATTR_INFOLIST, elementItem, attrCode, attrValue) => {
  ELEMENT_ATTR_INFOLIST.push({
    ATTR_CODE: attrCode,
    ATTR_TYPE: elementItem.DISCNT_CODE ? '0' : '1', // 资费0 服务1
    ATTR_VALUE: attrValue,
    ATTR_VALUE_NAME: '0',
    BELONG_ELEMENT_TYPE: elementItem.DISCNT_CODE ? 'D' : 'S', // D资费 S服务
    BELONG_ELEMENT_ID: elementItem.DISCNT_CODE || elementItem.SERVICE_ID,
    MODIFY_TAG: '0',
  });
};
