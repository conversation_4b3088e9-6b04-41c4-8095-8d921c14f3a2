<template>
  <div>
    <!-- 账户信息  Table -->
    <a-table
      class="queryTable"
      :columns="accountColumns"
      :data-source="datas"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
    >
      <template slot="chargeCategory" slot-scope="text, record">
        <a-select
          v-model="record.chargeCategory"
          :placeholder="$t('common.selectPlaceholder')"
          style="width: 100%"
          @change="chargeCategory(record)"
        >
          <a-select-option v-for="item in typeList" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <span slot="action" slot-scope="record, index">
        <i class="iconfont icon-shanchu" @click="handleDeleteEvent(record)" />
      </span>
    </a-table>
    <!-- Service No. List -->
    <div class="secondLevel-header-title">{{ $t('accountSetting.serviceNumList') }}</div>
    <a-form-model :model="form" v-bind="{}" :colon="false" layout="vertical" ref="searchForm">
      <a-row :gutter="24">
        <a-col :span="9">
          <a-form-model-item
            :label="$t('accountSetting.criteria')"
            labelAlign="left"
            :validateStatus="validateStatus"
            :help="helpText"
          >
            <a-input-group compact>
              <a-select
                v-model="form.criteria"
                :placeholder="$t('common.selectPlaceholder')"
                style="width: 40%"
                @change="handleCriteriaChange"
              >
                <a-select-option v-for="item in criteriaList" :value="item.value" :key="item.value">
                  <a-tooltip placement="top">
                    <template slot="title">
                      <span>{{ item.label }}</span>
                    </template>
                    <span>{{ item.label }}</span>
                  </a-tooltip>
                </a-select-option>
              </a-select>
              <a-input
                v-validate-number
                :disabled="form.criteria == '0'"
                style="width: calc(60% - 5px); margin-left: 5px"
                v-model.trim="form.criteriaInp"
                :maxLength="criteriaMaxLength"
              />
            </a-input-group>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.assignedDeptBill')">
            <a-select
              v-model="form.assignedDeptBill"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option
                v-for="item in assignedDeptBillList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('accountSetting.serviceNum')">
            <a-input
              v-validate-number
              v-model="form.serviceNo"
              :placeholder="$t('common.inputPlaceholder')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="3" class="b-btns">
          <div class="btns-container">
            <a-button
              ghost
              type="primary"
              @click="reset"
              class="reset-button btnDistance"
              style="margin-right: 10px"
            >
              {{ $t('common.buttonReset') }}
            </a-button>
            <a-button type="primary" class="search-button" @click="filterExitAccount">
              {{ $t('common.buttonFilter') }}
            </a-button>
          </div>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 号码列表 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :rowKey="(record, index) => `${index}`"
      :pagination="false"
    >
      <span slot="checkBox" slot-scope="record, index">
        <a-checkbox v-model="record.check" @change="onCheckbox"></a-checkbox>
      </span>
    </a-table>

    <!-- 底部按钮 -->
    <div class="bottomBtn">
      <a-checkbox @change="checkStatusChange" v-model="SelectAll">{{
        $t('accountSetting.selectOrUnselectAll')
      }}</a-checkbox>
      <div>
        <a-button ghost type="primary" @click="assignDepBill" :disabled="isChooseData()">{{
          $t('accountSetting.assignDepartmentalBill')
        }}</a-button>
        <a-button type="primary" @click="removeDepBill" :disabled="isChooseData()">{{
          $t('accountSetting.removeDepartmentalBill')
        }}</a-button>
      </div>
    </div>

    <!-- 查询部门账单和新增部门账单弹窗 -->
    <a-modal
      :title="currentModelTitle"
      width="50%"
      destroyOnClose
      :footer="null"
      :visible="queryAndNewDepartmentBillVisible"
      @cancel="handleDepCancel"
    >
      <QueryAndNewDepartmentBill
        @changeTilte="changeTilte"
        @confirm="handleDepConfirm"
        @cancel="handleDepCancel"
      />
    </a-modal>
  </div>
</template>

<script>
  import config from '../../config';
  import { mapState } from 'vuex';
  import QueryAndNewDepartmentBill from '@/views/components/queryAndNewDepartmentBill';

  export default {
    name: 'serviceNumList',
    components: {
      QueryAndNewDepartmentBill,
    },
    props: {
      datas: {
        // 账户信息
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          criteria: '0',
          criteriaInp: '',
        },
        accountColumns: config.accountColumns,
        columns: config.serviceNumListColumns,
        typeList: config.typeList,
        tableData: [],
        temDatas: [],
        criteriaList: config.criteriaList,
        assignedDeptBillList: config.assignedDeptBillList,
        validateStatus: 'success',
        helpText: '',
        queryAndNewDepartmentBillVisible: false,
        currentModelTitle: this.$t('accountSetting.queryDepartmentBill'),
        SelectAll: false,
      };
    },
    computed: {
      ...mapState('orderCapture', {
        numberSelectionPage: state => state.numberSelectionPage,
      }),
      // 标准的最大长度
      criteriaMaxLength() {
        const { criteria } = this.form;
        return criteria === '1' ? 5 : criteria === '2' ? 3 : 0;
      },
    },
    watch: {
      numberSelectionPage: {
        handler(newVal, oldVal) {
          if (newVal?.serviceNoList) {
            this.temDatas = JSON.parse(JSON.stringify(newVal.serviceNoList));
            this.filterExitAccount();
            this.SelectAll = this.temDatas.every(item => item.check === true);
          }
        },
        deep: true,
        immediate: true,
      },
      temDatas: {
        handler(newVal, oldVal) {
          if (newVal) {
            // this.$store.dispatch('customerVerify/setAccountSettingNumList', newVal);
          }
        },
        deep: true,
        immediate: true,
      },
      datas: {
        handler(newVal, oldVal) {
          this.updateNumberList();
        },
      },
    },
    mounted() {},
    methods: {
      handleCriteriaChange(value) {
        this.form.criteriaInp = '';
      },
      // 修改部门账单弹窗标题
      changeTilte(title) {
        this.currentModelTitle = title;
      },
      // 处理部门账单数据
      handleDepConfirm(billData) {
        this.fixNumDataFunc(billData, 'add');
        this.queryAndNewDepartmentBillVisible = false;
      },
      // 关闭弹窗
      handleDepCancel() {
        this.queryAndNewDepartmentBillVisible = false;
      },
      // 如果有勾选数据才让按钮解禁
      isChooseData() {
        return !this.temDatas.some(item => item.check);
      },
      // 存储号段数据渲染到表格上
      // addCheckAttribute() {
      //   let newArr = [];
      //   this.numberSelectionPage.serviceNoList?.map(item => {
      //     newArr.push({
      //       ...item,
      //       check: false,
      //       serviceNo: item.value,
      //       billingAccountNo_R: this?.datas[0]?.ACCOUNT_NO ?? '',
      //       billingAccountNo_I: this?.datas[1]?.ACCOUNT_NO ?? '',
      //       departmentalBillName: '',
      //       departmentalBill: '',
      //     });
      //   });
      //   this.temDatas = newArr;
      //   this.filterExitAccount();
      // },
      // 更新号码列表
      updateNumberList() {
        let billingAccountNo_R = '';
        let billingAccountNo_I = '';
        this.datas.map(item => {
          if (item.chargeCategory == 'R') {
            billingAccountNo_R = item.ACCOUNT_NO;
          } else if (item.chargeCategory == 'I') {
            billingAccountNo_I = item.ACCOUNT_NO;
          }
        });
        this.temDatas.forEach(item => {
          item.billingAccountNo_R = billingAccountNo_R;
          item.billingAccountNo_I = billingAccountNo_I;
        });
        this.filterExitAccount();
      },

      // 修改列表数据  ---  将DEPARTMENT_NAME字段 插入或移除 数据表格里面
      fixNumDataFunc(data, action) {
        let actionVal =
          action === 'add'
            ? this.$t('accountSetting.newAddTips')
            : this.$t('accountSetting.removeTips');
        let fixBool = false; // 判断是否新增、移除
        this.temDatas.forEach(item => {
          if (item.check) {
            if (item.departmentalBillName) {
              item.departmentalBillName = action === 'add' ? data.DEPARTMENT_NAME : '';
              item.departmentalBill = action === 'add' ? data.DEPARTMENT_INDEX : '';
              fixBool = true;
            } else if (action === 'add' && !item.departmentalBillName) {
              item.departmentalBillName = data.DEPARTMENT_NAME;
              item.departmentalBill = data.DEPARTMENT_INDEX;
              fixBool = true;
            }
          }
        });

        fixBool
          ? this.$message.success(actionVal + this.$t('accountSetting.successTips'))
          : this.$message.error(this.$t('accountSetting.notRepeatTips') + actionVal);
      },
      onCheckbox(record) {},
      // chargeCategory 下拉选择
      chargeCategory(record) {
        if (record.chargeCategory == 'R') {
          this.temDatas.map(item => {
            item.billingAccountNo_R = record.ACCOUNT_NO;
          });
        } else if (record.chargeCategory == 'I') {
          this.temDatas.map(item => {
            item.billingAccountNo_I = record.ACCOUNT_NO;
          });
        }
        this.updateNumberList();
        this.$emit('updateChargeCategory', record);
      },
      handleDeleteEvent(record) {
        this.$emit('deleteServiceNumListDatas', record);
      },
      // 全选 / 取消全选状态
      checkStatusChange(e) {
        this.SelectAll = e.target.checked;
        if (e.target.checked) {
          this.temDatas.forEach(item => {
            item.check = true;
          });
        } else {
          this.temDatas.forEach(item => {
            item.check = false;
          });
        }
      },
      // 过滤条件
      filterExitAccount() {
        // 检查是否有必要进行过滤
        const { criteria, criteriaInp, assignedDeptBill, serviceNo } = this.form;

        let filteredData = this.temDatas;
        if (serviceNo) {
          filteredData = this.temDatas.filter(item => item.serviceNo === serviceNo);
        } else if (criteria && criteriaInp) {
          if (criteria == '1') {
            filteredData = this.temDatas.filter(item =>
              item.serviceNo.slice(0, 5).includes(criteriaInp),
            );
          } else if (criteria == '2') {
            filteredData = this.temDatas.filter(item =>
              item.serviceNo.slice(5, 8).includes(criteriaInp),
            );
          }
        } else if (assignedDeptBill) {
          if (assignedDeptBill == '1') {
            filteredData = this.temDatas.filter(item => item.departmentalBillName);
          } else {
            filteredData = this.temDatas.filter(item => !item.departmentalBillName);
          }
        }
        // 更新 departmentBillDatas 为过滤后的数据
        this.tableData = filteredData;
      },
      // 插入部门账单
      assignDepBill() {
        this.queryAndNewDepartmentBillVisible = true;
      },
      // 移除部门账单
      removeDepBill() {
        this.fixNumDataFunc({}, 'remove');
      },
      // 重置
      reset() {
        this.form = {
          criteria: '0',
          criteriaInp: '',
          assignedDeptBill: '',
          serviceNo: '',
        };
      },
    },
  };
</script>

<style lang="less" scoped>
  .queryTable {
    margin: 10px 0px;
  }
  .b-btns {
    .btns-container {
      height: 74px;
      padding-bottom: 12px;
      display: flex;
      align-items: end;
      justify-content: end;
    }
  }
  .main-search-divider {
    .ant-divider-horizontal {
      margin: 0 !important;
    }
  }
  .bottomBtn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    .ant-btn {
      margin-right: 10px; /* 为每个按钮设置右边距 */
    }
    .ant-btn:last-child {
      margin-right: 0; /* 最后一个按钮没有右边距 */
    }
  }
  .assignDepBill {
    min-width: 160px;
    height: 22px;
    border-radius: 2px;
    letter-spacing: 0 !important;
    text-align: center !important;
    font-weight: 400 !important;
    padding: 0px 8px;
  }
  .removeDepBill {
    min-width: 160px;
    height: 22px;
    border-radius: 2px !important;
    padding: 0px 8px;
  }
</style>
