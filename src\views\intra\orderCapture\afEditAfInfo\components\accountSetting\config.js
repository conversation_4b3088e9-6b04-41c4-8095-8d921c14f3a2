import that from '@/main.js';

let obj = {
  accountColumns: [
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_NO',
      key: 'ACCOUNT_NO',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA_NAME',
      key: 'BILL_MEDIA_NAME',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD_NAME',
      key: 'PAYMENT_METHOD_NAME',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCT_FAMILY_NAME',
      key: 'PRODUCT_FAMILY_NAME',
    },
    {
      title: that.$t('accountSetting.chargeCategory'),
      scopedSlots: { customRender: 'chargeCategory' },
    },
    {
      title: that.$t('common.action'),
      key: '10',
      scopedSlots: { customRender: 'action' },
    },
  ],
  serviceNumListColumns: [
    {
      title: '',
      scopedSlots: { customRender: 'checkBox' },
    },
    {
      title: that.$t('accountSetting.serviceNo'),
      dataIndex: 'serviceNo',
      key: 'serviceNo',
    },
    {
      title: that.$t('accountSetting.billingAccountNo_R'),
      dataIndex: 'billingAccountNo_R',
      key: 'billingAccountNo_R',
    },
    {
      title: that.$t('accountSetting.billingAccountNo_I'),
      dataIndex: 'billingAccountNo_I',
      key: 'billingAccountNo_I',
    },
    {
      title: that.$t('accountSetting.departmentalBill'),
      dataIndex: 'departmentalBill',
      key: 'departmentalBill',
    },
  ],
  typeList: [
    {
      value: 'R',
      label: 'R',
    },
    {
      value: 'I',
      label: 'I',
    },
    // {
    //   value:'A',
    //   label:'A',
    // },
    // {
    //   value:'P',
    //   label:'P',
    // }
  ],
  criteriaList: [
    {
      value: '0',
      label: that.$t('accountSetting.noCriteria'),
    },
    {
      value: '1',
      label: that.$t('accountSetting.preferredFirst'),
    },
    {
      value: '2',
      label: that.$t('accountSetting.preferredLast'),
    },
  ],
  assignedDeptBillList: [
    {
      value: '1',
      label: that.$t('accountSetting.assigned'),
    },
    {
      value: '2',
      label: that.$t('accountSetting.unAssigned'),
    },
  ],
  formRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(that.$t('accountSetting.emailCannotBeEmpty')));
          } else {
            const emails = value
              .split(',') // 按逗号分割字符串
              .map(email => email.trim()) // 去掉每个元素的前后空格
              .filter(email => email); // 过滤掉空字符串;
            if (emails.length > 3) {
              callback(new Error(that.$t('accountSetting.numberOfEmail')));
            } else {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
  },
  checkFormRules: {
    BILL_MEDIA: [
      {
        type: 'array',
        required: true,
        message: that.$t('common.cannotBeEmpty'),
        trigger: 'change',
      },
    ],
    WAIVED_PAPER_BILL_FEE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    INCLUDE_ODD_CENTS_IN_BILL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    DISPLAY_ZERO_BILL_ITEMS: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    BILL_ADDRESS_TYPE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
  },
  addressFormRules: {
    ADDRESS_EN: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    ADDRESS_LINE4_EN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
  },
  updateRules: {
    ACCOUNT_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    LOB: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    PRODUCT_FAMILY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BILL_EMAIL: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
      // 邮箱个数最多三个，英文逗号隔开，包含@符号
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback(new Error(that.$t('accountSetting.emailCannotBeEmpty')));
          } else {
            const emails = value
              .split(',') // 按逗号分割字符串
              .map(email => email.trim()) // 去掉每个元素的前后空格
              .filter(email => email); // 过滤掉空字符串;
            if (emails.length > 3) {
              callback(new Error(that.$t('accountSetting.numberOfEmail')));
            } else {
              for (let i = 0; i < emails.length; i++) {
                if (!emails[i].includes('@')) {
                  callback(new Error(that.$t('accountSetting.emailSymbol')));
                  return;
                }
              }
            }
            callback();
          }
        },
        trigger: 'blur',
      },
    ],
    BILL_DAY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  billFrequenceOption: [
    { label: that.$t('accountSetting.month'), value: 'Month' },
    { label: that.$t('accountSetting.quarter'), value: 'Quarter' },
    { label: that.$t('accountSetting.year'), value: 'Year' },
  ],
  billAddressOption: [
    { label: that.$t('accountSetting.standard'), value: 'Standard' },
    { label: that.$t('accountSetting.nonStandard'), value: 'Non-standard' },
  ],
  payMentMethodTabList: [
    {
      value: 'Cash',
      label: that.$t('accountSetting.cash'),
    },
    {
      value: 'CreditCard',
      label: that.$t('accountSetting.creditCardAutopay'),
    },
    {
      value: 'DirectDebit',
      label: that.$t('accountSetting.directDebit'),
    },
  ],
  cardFormRules: {
    CARD_COUNTRY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    CARD_TYPE: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    EXPIRATION_DATE: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' },
    ],
    CREDIT_CARD_TOKEN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    CARD_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
  debitFormRules: {
    CARD_COUNTRY: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'change' }],
    CREDIT_CARD_TOKEN: [
      { required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' },
    ],
    CARD_NAME: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
    BANK_NO: [{ required: true, message: that.$t('common.cannotBeEmpty'), trigger: 'blur' }],
  },
};

export default obj;
