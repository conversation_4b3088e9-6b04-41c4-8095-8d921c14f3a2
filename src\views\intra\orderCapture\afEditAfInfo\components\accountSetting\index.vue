<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('accountSetting.accountSetting') }}</div>
    <a-button
      class="marginBtn primary-button"
      type="primary"
      @click="existBillingAccount"
      :disabled="serviceNumListDatas.length >= 2"
    >
      {{ $t('accountSetting.ExistBillingAccount') }}
    </a-button>
    <a-button class="common-button" @click="newBillingAccount">{{
      $t('accountSetting.newBillingAccount')
    }}</a-button>
    <!-- 查询账户 -->
    <QueryAccount
      v-if="queryAccountVisible"
      :isNewInstallFlag="true"
      accountType="newInstall"
      :visible="queryAccountVisible"
      @cancel="queryAccouontmodalCancel"
      @Ok="queryAccouontmodalConfirm"
    />
    <!-- 页面内容 -->
    <serviceNumList
      v-show="serviceNumListDatas.length > 0"
      ref="serviceNumList"
      :datas="serviceNumListDatas"
      @updateChargeCategory="updateChargeCategory"
      @queryDepartmentBillConfirm="queryDepartmentBillConfirm"
      @deleteServiceNumListDatas="deleteServiceNumListDatas"
    />

    <!-- 新增账户 -->
    <a-drawer
      :title="$t('accountSetting.createNewAccount')"
      placement="right"
      :closable="false"
      :visible="addAccountVisible"
      @close="addAccountCancel"
      :width="width"
    >
      <AddAccount v-if="addAccountVisible" @cancel="addAccountCancel" />
    </a-drawer>
  </div>
</template>

<script>
  import serviceNumList from './components/serviceNumList';
  import QueryAccount from '@/views/components/queryAccount/index.vue';
  import AddAccount from '@/views/components/addAccount/index.vue';
  import { getCurrentTime, getDeadlineTime, generateUUID } from '@/utils/utils';
  export default {
    name: 'accountSetting',
    components: {
      serviceNumList,
      QueryAccount,
      AddAccount,
    },
    data() {
      return {
        width: '100%',
        queryAccountVisible: false,
        queryDepartmentBillVisible: false,
        addAccountVisible: false,
        isServiceNumList: false,
        serviceNumListDatas: [],
      };
    },
    methods: {
      // 更新当前行chargeCategory的下拉值
      updateChargeCategory(updatedRecord) {
        // 检查是否违反了规则
        const R_ElementLen = this.serviceNumListDatas.filter(
          item => item.chargeCategory === 'R',
        ).length;
        const I_ElementLen = this.serviceNumListDatas.filter(
          item => item.chargeCategory === 'I',
        ).length;
        if (R_ElementLen == 2 || I_ElementLen == 2) {
          // this.$message.error('账户列表最多只能包含两条账户记录，且一条为 R，另一条为 I');
          this.$message.error(this.$t('accountSetting.accountListTips'));
          // 将另一条记录的 chargeCategory 置为未选
          this.serviceNumListDatas.forEach(item => {
            if (item.index != updatedRecord.index) {
              item.chargeCategory = undefined;
            }
          });
        }
      },
      // 选择账户确认
      queryAccouontmodalConfirm(arr) {
        this.queryAccountVisible = false;
        // 检查 serviceNumListDatas 的长度
        if (this.serviceNumListDatas.length < 2) {
          // 添加新记录，并设置 chargeCategory
          this.serviceNumListDatas.push({
            ...arr,
            index: generateUUID(),
            chargeCategory:
              this.serviceNumListDatas.length === 0
                ? 'R'
                : this.serviceNumListDatas.length === 1 &&
                  this.serviceNumListDatas[0].chargeCategory === 'R'
                ? 'I'
                : 'R',
          });
        }
      },
      deleteServiceNumListDatas(row) {
        // 删除与 row.ACCOUNT_NO 和 row.chargeCategory 同时匹配的记录
        this.serviceNumListDatas = this.serviceNumListDatas.filter(item => item.index != row.index);
      },
      // 校验
      async validate() {
        const startDateTime = await getCurrentTime();
        return new Promise((resolve, reject) => {
          const accountListError = this.$t('customerVerify.accountInfoOfNewInstall');
          // 检查是否选择账户信息
          if (this.serviceNumListDatas.length === 0) {
            this.$message.error(accountListError);
            reject(); // 只有当条件不满足时才调用 reject
          } else {
            // 某行是否存在 R 类型
            let hasR_Data = this.serviceNumListDatas.some(item => item.chargeCategory === 'R');
            if (!hasR_Data) {
              this.$message.error(accountListError);
              reject(); // 只有当条件不满足时才调用 reject
            } else {
              // 整合数据
              const PAYRELATION_INFO = this.serviceNumListDatas.map(item => ({
                ACCOUNT_ID: item.ACCOUNT_NO, // 已有账户id
                CHARGE_CATEGORY: item.chargeCategory, // 付费类别（R/I/A/P）
                ACCOUNT_NAME: item.ACCOUNT_NAME, // 账号名称
                START_DATE: startDateTime,
                END_DATE: getDeadlineTime(),
                MODIFY_TAG: '0', // 修改标识
              }));

              const numbersWithAccountInfo = this.$refs.serviceNumList.temDatas;

              const orderSummaryAccountInfo = this.$refs.serviceNumList.datas;

              // 存储数据
              this.$store.dispatch('orderCapture/setAccountSettingPage', {
                PAYRELATION_INFO,
                numbersWithAccountInfo,
                orderSummaryAccountInfo,
              });
              resolve(); // 所有条件都满足时调用 resolve
            }
          }
        });
      },
      addAccountCancel() {
        this.addAccountVisible = false;
      },
      queryDepartmentBillConfirm() {
        this.queryDepartmentBillVisible = true;
      },
      queryAccouontmodalCancel() {
        this.queryAccountVisible = false;
      },
      existBillingAccount() {
        this.queryAccountVisible = true;
      },
      newBillingAccount() {
        this.addAccountVisible = true;
      },
    },
  };
</script>

<style lang="less" scoped>
  .marginBtn {
    margin-right: 10px;
  }
</style>
