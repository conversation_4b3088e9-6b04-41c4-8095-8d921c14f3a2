<template>
  <div>
    <!-- 选址 -->
    <AddressMappingForm :headerTitle="$t('comp.addressMapping')" ref="AddressMappingFormRef" />

    <!-- 分割线 -->
    <a-divider />

    <div class="common-custom-header">
      <!-- 标题 -->
      <div class="secondLevel-header-title">{{ $t('customerVerify.oscaNewProductTitle') }}</div>
      <a-button type="primary" @click="editProduct" style="margin-right: 0px !important">{{
        $t('orderCapture.EditAFInfo')
      }}</a-button>
    </div>

    <!-- 产品树 -->
    <CollapseProductTree
      ref="collapseProductTree"
      :treeList="productSelection.currentShowTreeMap"
    ></CollapseProductTree>

    <!-- 汇总 -->
    <ProductChargeTotal :productCount="productSelection.productCount"></ProductChargeTotal>

    <!-- 价格计算 -->
    <footer-tool-bar class="footer-tool-bar">
      <a-button ghost type="primary" @click="cancel" class="reset-button product-footer-btn">
        {{ $t('customerVerify.Cancel') }}
      </a-button>
      <a-button type="primary" @click="confirm" class="search-button product-footer-btn">
        {{ $t('customerVerify.Confirm') }}
      </a-button>
    </footer-tool-bar>
  </div>
</template>
<script>
  import AddressMappingForm from '@/views/components/addressMappingForm';
  import CollapseProductTree from '@/views/components/collapseProductTree/index.vue';
  import ProductChargeTotal from '@/views/components/productChargeTotal/index.vue';
  import FooterToolBar from '@/components/footerToolbar';
  import { gotoAddProductTree } from '@/views/intra/quotation/addEditQuotation/components/addProductTree';
  import { mapState } from 'vuex';
  export default {
    name: 'addressProduct',
    components: {
      AddressMappingForm,
      CollapseProductTree,
      ProductChargeTotal,
      FooterToolBar,
    },
    props: {
      orderId: {
        // 账户信息
        type: String,
        default: '',
      },
    },
    computed: {
      ...mapState('quotation', {
        productSelection: state => state.productSelection,
      }),
    },
    data() {
      return {};
    },
    mounted() {
      // 数据回显
      this.$nextTick(() => {
        const { SB_ADDRESS, masterAddress, SB_NO } = this.productSelection.addressInfo;
        this.$refs.AddressMappingFormRef.form = {
          ...this.productSelection.addressInfo,
          SB_ADDRESS,
          masterAddress: masterAddress || SB_ADDRESS,
          SB_NO,
        };
        this.$refs.collapseProductTree.setCurrentProduct(
          this.productSelection.currentShowTreeMap.itemId,
        );
      });
    },
    methods: {
      // 编辑产品
      editProduct() {
        const item = this.productSelection.currentShowTreeMap;
        let currentItem = item.mainProduct[0];
        this.$store.dispatch('quotation/setProductTypeData', {
          value: currentItem.PRODUCT_TYPE_CODE,
        });
        this.$store.dispatch('quotation/setProductStatus', 'edit');
        const routePath = gotoAddProductTree(currentItem.PRODUCT_TYPE_CODE);
        this.$router.push({
          path: routePath,
          query: {
            status: 'edit',
            index: 0,
            orderId: this.orderId,
            orderLineId: currentItem.ORDER_LINE_ID,
            fromPage: 'orderCapture',
          },
        }); //编辑产品
      },
      // 取消产品选择
      cancel() {
        this.$router.customBack();
      },
      // 确认产品选择
      async confirm() {
        // await this.validate();
        this.$router.customBack();
      },
      // 校验
      validate() {
        return true;
      },
    },
  };
</script>

<style lang="less" scoped>
  .common-custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: 30px;
  }
</style>
