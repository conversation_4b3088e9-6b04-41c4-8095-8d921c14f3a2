import i18n from '@/utils/language/index';
import that from '@/main';
export default {
  changeServiceNoListColumns: [
    {
      title: i18n.t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: i18n.t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: 'Ex-directory',
      dataIndex: 'directory',
      key: 'directory',
    },
    {
      title: 'Directory Name',
      dataIndex: 'directoryName',
      key: 'directoryName',
    },
    {
      title: i18n.t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],

  serviceNumberColumns: [
    {
      title: i18n.t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
      width: 200,
    },
    {
      title: i18n.t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
      width: 150,
    },
    {
      title: i18n.t('orderList.product'),
      dataIndex: 'product',
      key: 'product',
      width: 350,
    },
    {
      title: i18n.t('orderList.oldInstallationAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: i18n.t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: i18n.t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 80,
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
      width: 150,
    },
  ],
  assignedNumberColumns: [
    {
      title: i18n.t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: i18n.t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: i18n.t('orderList.oldInstallationAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
    },
    {
      title: i18n.t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
    },
    {
      title: i18n.t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  relocationColumns: [
    {
      title: i18n.t('orderList.serviceNo'),
      dataIndex: 'SERIAL_NUMBER',
      key: 'SERIAL_NUMBER',
      width: 120,
    },
    {
      title: i18n.t('orderList.oldAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
    },
    {
      title: i18n.t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  changeServiceNumberColumns: [
    {
      title: i18n.t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: i18n.t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: i18n.t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  oneTimeChargeColumns: [
    {
      title: i18n.t('suspendResume.chargeName'),
      dataIndex: 'chargeName',
      key: 'chargeName',
    },
    {
      title: i18n.t('macd.chargePerDn'),
      dataIndex: 'chargePerDn',
      key: 'chargePerDn',
    },
    {
      title: i18n.t('macd.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: i18n.t('macd.standardCharge'),
      dataIndex: 'standardCharge',
      key: 'standardCharge',
    },
    {
      title: i18n.t('macd.waivedAmountPerLine'),
      scopedSlots: { customRender: 'waivedAmountPerLine' },
    },
    {
      title: i18n.t('macd.totalCharge'),
      scopedSlots: { customRender: 'totalCharge' },
    },
  ],
  hktContactColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
      width: 80,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_staffID', customRender: 'staffID' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
      width: 150,
    },
    {
      title: i18n.t('fulfillmentInfo.salesCode'),
      scopedSlots: { customRender: 'salesCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_agentCode', customRender: 'agentCode' },
      width: 150,
    },
    {
      scopedSlots: { title: 'customTitle_agentName', customRender: 'agentName' },
      width: 150,
    },
    {
      scopedSlots: { title: 'customTitle_dummyCode', customRender: 'dummyCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_orderSaleType', customRender: 'orderSaleType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
      width: 150,
    },
    {
      title: i18n.t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
      width: 150,
    },
    {
      title: i18n.t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
      width: 200,
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 80,
    },
  ],
  customerContactColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
      width: 80,
    },
    {
      title: i18n.t('fulfillmentInfo.title'),
      scopedSlots: { customRender: 'customerTitle' },
      width: 120,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
    },
    {
      title: i18n.t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
    },
    {
      title: i18n.t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
  // selectNumberPop
  selectTypeList: [
    {
      value: 'Normal',
      label: 'Normal',
    },
    {
      value: 'Reserve',
      label: 'Reserve',
    },
    {
      value: 'Special Service Group',
      label: 'Special Service group',
    },
  ],
  numberSelectList: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
  ],
  CriteriaSelData: [
    { label: i18n.t('customerVerify.NoCriteria'), value: '0' },
    { label: i18n.t('customerVerify.PreferredFirst'), value: '1' },
    { label: i18n.t('customerVerify.PreferredLast'), value: '2' },
  ],
  selectNumformRules: {
    ServiceNoQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoReserve: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoSSG: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOC: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    Project: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceGruopOrService: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCodeDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOCDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    ProjectDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
  },
  serviceNoType: [
    {
      label: 'New Installation',
      value: 'New Installation',
      showFn: () => {
        return true;
      },
    },
    // DEL 一期暂时不弄
    {
      label: 'PIPB Number',
      value: 'PIPB Number',
      // showFn: (productId) => { return ['300001'].includes(productId) },
      showFn: () => {
        return false;
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number',
      value: 'Working Number',
      showFn: () => {
        return false;
      },
    },
  ],
  changeGroupChangeActionList: [
    {
      label: 'Change Order',
      value: 'citinetChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'citinet',
      productType: '300002',
    },
    {
      label: 'Change Order',
      value: 'huntingChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'hunting',
      productType: '300003',
    },
    {
      label: 'Change Order',
      value: 'idapChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'idap',
      productType: '300004',
    },
  ],
  // 订单概要 - 号码列表表格列
  summaryNumberColumns: [
    {
      title: i18n.t('fulfillmentInfo.seq'),
      dataIndex: 'Seq',
      key: 'Seq',
    },
    {
      title: i18n.t('orderList.serviceNumber'),
      dataIndex: 'SERVICE_NUMBER',
      key: 'SERVICE_NUMBER',
    },
    {
      title: i18n.t('customerVerify.InstallationAddress'),
      dataIndex: 'INSTALLATION_ADDRESS',
      key: 'INSTALLATION_ADDRESS',
    },
    {
      title: i18n.t('orderList.product'),
      dataIndex: 'PRODUCT',
      key: 'PRODUCT',
    },
    {
      title: i18n.t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 80,
    },
  ],
  changeOtherInfoColumns: [
    {
      title: i18n.t('macd.changeType'),
      dataIndex: 'changeType',
      key: 'changeType',
      width: 200,
      scopedSlots: { customRender: 'changeType' },
    },
    {
      title: i18n.t('macd.before'),
      dataIndex: 'BEFORE',
      key: 'BEFORE',
      width: 400,
      ellipsis: true,
      scopedSlots: { customRender: 'BEFORE' },
    },
    {
      title: i18n.t('macd.after'),
      dataIndex: 'AFTER',
      key: 'AFTER',
      width: 400,
      ellipsis: true,
      scopedSlots: { customRender: 'AFTER' },
    },
  ],
  // MACD改单 chargeList表格列
  macdChargeListColumns: [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      customRender: (_, record) => {
        if (record.NAME) return record.NAME;
        else if (record.DISCNT_NAME) return record.DISCNT_NAME;
        else if (record.PACKAGE_NAME) return record.PACKAGE_NAME;
        else if (record.PRODUCT_NAME) return record.PRODUCT_NAME;
      },
      width: 600,
    },
    {
      title: i18n.t('chargeList.type'),
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: i18n.t('chargeList.charge'),
      dataIndex: 'charge',
      key: 'charge',
      scopedSlots: { customRender: 'charge' },
    },
    {
      title: i18n.t('chargeList.period'),
      dataIndex: 'period',
      key: 'period',
      scopedSlots: { customRender: 'period' },
    },
  ],
  // chargeList 合计表格列
  chargeTotalColumns: [
    {
      title: i18n.t('chargeList.serviceNoQty'), //
      dataIndex: 'serviceNoQty',
      key: 'serviceNoQty',
    },
    {
      title: i18n.t('chargeList.MRCTotal'), //
      dataIndex: 'MRCTotal',
      key: 'MRCTotal',
      scopedSlots: { customRender: 'MRCTotal' },
    },
    {
      title: i18n.t('chargeList.MRCRebateTotal'), //
      dataIndex: 'MRCRebateTotal',
      key: 'MRCRebateTotal',
      scopedSlots: { customRender: 'MRCRebateTotal' },
    },
    {
      title: i18n.t('chargeList.OTCTotal'), //
      dataIndex: 'OTCTotal',
      key: 'OTCTotal',
      scopedSlots: { customRender: 'OTCTotal' },
    },
  ],
  accountColumns: [
    {
      title: 'Sel',
      scopedSlots: { customRender: 'Sel' },
    },
    {
      title: i18n.t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: i18n.t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_NO',
      key: 'ACCOUNT_NO',
    },
    {
      title: i18n.t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: i18n.t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA_NAME',
      key: 'BILL_MEDIA_NAME',
    },
    {
      title: i18n.t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD_NAME',
      key: 'PAYMENT_METHOD_NAME',
    },
    {
      title: i18n.t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCT_FAMILY_NAME',
      key: 'PRODUCT_FAMILY_NAME',
    },
    {
      title: i18n.t('accountSetting.chargeCategory'),
      scopedSlots: { customRender: 'chargeCategory' },
    },
    {
      title: i18n.t('common.action'),
      key: '10',
      scopedSlots: { customRender: 'action' },
    },
  ],
  assignBillingAccountColumns: [
    {
      title: i18n.t('accountSetting.numberType'),
      key: 'NUMBER_TYPE',
      dataIndex: 'NUMBER_TYPE',
    },
    {
      title: i18n.t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: i18n.t('accountSetting.chargeCategory'),
      key: 'CHARGE_CATEGORY',
      dataIndex: 'CHARGE_CATEGORY',
    },
    {
      title: i18n.t('accountSetting.billingAccountNo'),
      key: 'ACCOUNT_ID',
      dataIndex: 'ACCOUNT_ID',
    },
  ],
  assignDepartmentalBillColumns: [
    {
      title: i18n.t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: i18n.t('accountSetting.departmentalBill'),
      key: 'DEPARTMENT_BILL_NO',
      dataIndex: 'DEPARTMENT_BILL_NO',
    },
  ],
  iddServiceNumberColumns: [
    {
      title: i18n.t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
      width: 200,
    },
    {
      title: i18n.t('orderList.product'),
      dataIndex: 'product',
      key: 'product',
      // width: 350,
    },
    {
      title: i18n.t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 80,
    },
    {
      title: i18n.t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
      width: 150,
    },
  ],
  accountInfoColumns: [
    {
      title: i18n.t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: i18n.t('accountSetting.accountName'),
      key: 'ACCOUNT_NAME',
      dataIndex: 'ACCOUNT_NAME',
    },
    {
      title: i18n.t('accountSetting.billingAccountNo'),
      key: 'ACCOUNT_ID',
      dataIndex: 'ACCOUNT_ID',
    },
    {
      title: i18n.t('accountSetting.billDay'),
      key: 'BILL_DAY',
      dataIndex: 'BILL_DAY',
    },
    {
      title: i18n.t('accountSetting.billMedia'),
      key: 'BILL_MEDIA',
      dataIndex: 'BILL_MEDIA',
    },
    {
      title: i18n.t('accountSetting.paymentMethod'),
      key: 'PAYMENT_METHOD',
      dataIndex: 'PAYMENT_METHOD',
    },
    {
      title: i18n.t('accountSetting.prodFamily'),
      key: 'PRODUCE_FAMILY',
      dataIndex: 'PRODUCE_FAMILY',
    },
    {
      title: i18n.t('accountSetting.chargeCategory'),
      key: 'CHARGE_CATEGORY',
      dataIndex: 'CHARGE_CATEGORY',
    },
  ],

  // macd改单拆机业务表格列
  macdTerminationColumns: [
    {
      title: i18n.t('orderList.orderType'),
      scopedSlots: { customRender: 'SCENE_TYPE' },
    },
    {
      title: i18n.t('common.productName'),
      key: 'MAIN_PRODUCT_NAME',
      dataIndex: 'MAIN_PRODUCT_NAME',
    },
    {
      title: i18n.t('orderList.disconnectReason'),
      key: 'CANCEL_REASON_AFTER',
      dataIndex: 'CANCEL_REASON_AFTER',
    },
    {
      title: i18n.t('orderList.remark'),
      KEY: 'REMARK_AFTER',
      dataIndex: 'REMARK_AFTER',
    },
    {
      title: i18n.t('orderList.srd'),
      KEY: 'SRD_AFTER',
      dataIndex: 'SRD_AFTER',
    },
    {
      title: i18n.t('common.buttonAmend'),
      scopedSlots: { customRender: 'AMEND' },
    },
    {
      title: i18n.t('common.draftAction'),
      scopedSlots: { customRender: 'DRAFT_ACTION' },
    },
  ],
  // macd改单非拆机业务表格列
  macdServiceListColumns: [
    {
      title: i18n.t('orderList.orderType'),
      scopedSlots: { customRender: 'SCENE_TYPE' },
    },
    {
      title: i18n.t('common.before'),
      scopedSlots: { customRender: 'BEFORE' },
    },
    {
      title: i18n.t('common.after'),
      scopedSlots: { customRender: 'AFTER' },
    },
    {
      title: i18n.t('common.buttonAmend'),
      scopedSlots: { customRender: 'AMEND' },
    },
    {
      title: i18n.t('common.draftAction'),
      scopedSlots: { customRender: 'DRAFT_ACTION' },
    },
  ],
};

export const formGroupColumns = [
  {
    title: that.$t('assignNumber.serviceGroupNo'),
    dataIndex: 'GROUP_SERVICE_NO',
    key: 'GROUP_SERVICE_NO',
  },
  {
    title: that.$t('assignNumber.billingAccountNo'),
    dataIndex: 'BILLING_ACCOUNT_NO',
    key: 'BILLING_ACCOUNT_NO',
  },
];

export const inTransitOrdersList = [
  // {
  //   value: '1',
  //   label: that.$t('assignNumber.pending'),
  // },
  {
    value: '0',
    label: that.$t('assignNumber.completed'),
  },
];
