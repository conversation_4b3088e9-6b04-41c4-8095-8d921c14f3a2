<template>
  <div class="fulfillment-info">
    <div class="secondLevel-header-title">{{ $t('fulfillmentInfo.fulfillmentInfo') }}</div>

    <!-- HKT Contact -->
    <AddHKTContact ref="addHKTContactRef" />

    <!-- Customer Contact List -->
    <AddCustomerContactList ref="addCustomerContactListRef" />

    <!-- SRD -->
    <SRD ref="SRDRef" :changeTable="changeTable" :SRDTableData="SRDTableData" :showSRD="showSRD" />

    <!-- OTHER_INFORMATION -->
    <OtherInformation
      :getCustContactListDatas="getCustContactListDatas"
      ref="otherInformationRef"
      :showSRD="showSRD"
    />
  </div>
</template>

<script>
  import { getCurrentTime, getDeadlineTime, getLoginInfoHKTContact } from '@/utils/utils';
  import AddHKTContact from '@/views/components/addHKTContact/index.vue';
  import AddCustomerContactList from '@/views/components/addCustomerContact/index.vue';
  import OtherInformation from '@/views/components/otherInformation/index.vue';
  import SRD from '@/views/components/SRD/index.vue';
  import { mapState } from 'vuex';
  export default {
    name: 'fulfillmentInfo',
    components: {
      AddHKTContact,
      AddCustomerContactList,
      SRD,
      OtherInformation,
    },
    props: {
      changeTable: {
        type: Boolean,
        default: false,
      },
      SRDTableData: {
        type: Array,
        default: () => [],
      },
      showSRD: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        form: {},
      };
    },
    computed: {
      ...mapState('quotation', {
        productType: state => state.productType,
        productSelection: state => state.productSelection,
      }),
      ...mapState('orderCapture', {
        accountSettingPage: state => state.accountSettingPage,
      }),
    },
    methods: {
      // 获取customerContactListRef过滤后的值传入子组件
      getCustContactListDatas() {
        const filterDataList = JSON.parse(
          JSON.stringify(this.$refs.addCustomerContactListRef.filterDataList),
        );
        return filterDataList;
      },

      // 获取HKT联系人和客户联系人信息
      submitHKTAndCustomerData() {
        const getHKTContactInfo = this.$refs.addHKTContactRef.filterDataList;
        const getCustContactInfo = this.$refs.addCustomerContactListRef.filterDataList;
        let submitHKTContactInfo = [];
        let submitCustContactInfoInfo = [];
        // 组装数据
        getHKTContactInfo.map(item => {
          submitHKTContactInfo.push({
            PARTICIPANT_TYPE: item.PARTICIPANTS_TYPE,
            PARTICIPANT_ID: item.STAFF_ID,
            SALES_CODE: item.SALES_CODE,
            AGENT_CODE: item.AGENT_CODE,
            ORDER_SALE_TYPE: item.ORDER_SALES_TYPE,
            PARTICIPANT_NAME: item.NAME || item.AGENT_NAME,
            PARTICIPANT_LANDLINE_PHONE: item.CONTACT_PHONE,
            PARTICIPANT_MOBILE: item.MOBILE,
            PARTICIPANT_EMAIL: item.EMAIL,
            CONTACT_TYPE: '1', // 0：Customer   1:HKT    2:myHKT
          });
        });
        // 把工号信息当成一个HKT联系人传入
        submitHKTContactInfo.push(getLoginInfoHKTContact(this.$store.state.app.userInfo));

        getCustContactInfo.map(item => {
          submitCustContactInfoInfo.push({
            TITLE: item.TITLE,
            PARTICIPANT_TYPE: item.PARTICIPANTS_TYPE,
            PARTICIPANT_ID: item.STAFF_ID,
            SALES_CODE: item.SALES_CODE,
            AGENT_CODE: item.AGENT_CODE,
            ORDER_SALE_TYPE: item.ORDER_SALES_TYPE,
            PARTICIPANT_NAME: item.NAME,
            PARTICIPANT_LANDLINE_PHONE: item.CONTACT_PHONE,
            PARTICIPANT_MOBILE: item.MOBILE,
            PARTICIPANT_EMAIL: item.EMAIL,
            CONTACT_TYPE: '0',
          });
        });

        return {
          contactList: [...submitHKTContactInfo, ...submitCustContactInfoInfo],
          HKT_ContactList: getHKTContactInfo,
          Customer_ContactList: getCustContactInfo,
        };
      },

      // 获取 SRD 数据
      submitSRDInfoData() {
        const SRDRef = this.$refs.SRDRef;
        const SRDFormRef = SRDRef ? SRDRef.$refs.SRDFormRef : null;

        // 根据 this.changeTable 的值动态选择数据来源(this.changeTable== true,则为table,否则为form)
        const SRDTableInfo = SRDRef.$refs.SRDTableRef?.localSRDTableData ?? [];
        const appointmentResults = SRDFormRef?.appointmentResults ?? {};
        const SRDInfo = SRDFormRef?.form ?? {};

        if (this.changeTable) {
          return { SRDTableInfo };
        }
        return { SRDInfo, appointmentResults };
      },

      // 获取 other 数据
      submitOtherInfoData() {
        return this.$refs.otherInformationRef.ruleForm ?? {};
      },
      // 获取装机属性列表函数
      getInstallInfoAttrList(code, value, SRD) {
        return {
          ATTR_TYPE: '1',
          ATTR_CODE: code,
          ATTR_VALUE: value || '',
          ATTR_VALUE_NAME: '1',
          MODIFY_TAG: '0',
          START_DATE: SRD,
          END_DATE: getDeadlineTime(),
        };
      },
      // 创建属性信息的通用函数
      createInstallInfoAttrList(dataSource, SRD) {
        const commonAttributes = [
          this.getInstallInfoAttrList('EN_ADDR1', dataSource.EN_ADDR1, SRD),
          this.getInstallInfoAttrList('ZH_ADDR1', dataSource.ZH_ADDR1, SRD),
          this.getInstallInfoAttrList('EN_ADDR3', dataSource.EN_ADDR3, SRD),
          this.getInstallInfoAttrList('ZH_ADDR3', dataSource.ZH_ADDR3, SRD),
          this.getInstallInfoAttrList('FLATVALUE', dataSource.FLATVALUE, SRD),
        ];

        return dataSource.status === 'normal'
          ? [
              ...commonAttributes,
              this.getInstallInfoAttrList('BID', dataSource.BID, SRD),
              this.getInstallInfoAttrList('GEOSEQ', dataSource.GEOSEQOfMasterAddress, SRD),
              this.getInstallInfoAttrList('FLOORVALUE', dataSource.FLOORVALUE, SRD),
              this.getInstallInfoAttrList('ADDRESS_2N_TAG', dataSource.ADDRESS_2N_TAG, SRD),
              this.getInstallInfoAttrList('ADDRESS_2N_CODE', dataSource.ADDRESS_2N_CODE, SRD),
            ]
          : [
              ...commonAttributes,
              this.getInstallInfoAttrList('EN_ADDR4', dataSource.EN_ADDR4, SRD),
              this.getInstallInfoAttrList('ZH_ADDR4', dataSource.ZH_ADDR4, SRD),
              this.getInstallInfoAttrList('EN_ADDR8', dataSource.EN_ADDR8, SRD),
              this.getInstallInfoAttrList('ZH_ADDR8', dataSource.ZH_ADDR8, SRD),
              this.getInstallInfoAttrList('EN_ADDR11', dataSource.EN_ADDR11, SRD),
              this.getInstallInfoAttrList('ZH_ADDR11', dataSource.ZH_ADDR11, SRD),
              this.getInstallInfoAttrList('EN_ADDR12', dataSource.EN_ADDR12, SRD),
              this.getInstallInfoAttrList('ZH_ADDR12', dataSource.ZH_ADDR12, SRD),
              this.getInstallInfoAttrList('EN_ADDR13', dataSource.EN_ADDR13, SRD),
              this.getInstallInfoAttrList('ZH_ADDR13', dataSource.ZH_ADDR13, SRD),
            ];
      },
      // 组装装机列表数据
      assemble_INSTALLATTRINFO(data, SRD) {
        return this.createInstallInfoAttrList(data, SRD);
      },
      // 组装装机列表数据（使用 item）
      assemble_INSTALLATTRINFO_Table(item, SRD) {
        return this.createInstallInfoAttrList(item, SRD);
      },
      // 获取 INSTALL_INFO 信息
      submitInstallInfo() {
        // IDD用不到装机地址信息
        if (!this.productSelection.addressInfo) return null;
        const { masterAddress, InstallationAddress, SB_NO, EN_ADDR3, EN_ADDR1, status } =
          this.productSelection.addressInfo;
        const { SRD } = this.submitSRDInfoData().SRDInfo || {};
        const INSTALL_ATTR_INFO = this.assemble_INSTALLATTRINFO(
          this.productSelection.addressInfo,
          SRD,
        );
        const {
          APPOINTMENT_DATE,
          APPOINTMENT_FROM_TIME,
          APPOINTMENT_ID,
          APPOINTMENT_TO_TIME,
          PREWIRING_DATE,
          PREWIRING_FROM_TIME,
          PREWIRING_TO_TIME,
          APPOINTMENT_REMARK,
        } = this.submitSRDInfoData().appointmentResults || {};
        return {
          STANDARD_ADDRESS_ID: SB_NO, // 标准地址编码,存放SB_NO
          STANDARD_ADDRESS: InstallationAddress, // Installation Address标准地址
          DETAIL_ADDRESS: masterAddress, // 详细地址(masterAddress)
          APPOINTMENT_ID: APPOINTMENT_ID, // 预约时间id   施工时间
          APPOINTMENT_START_DATE: this.formatDateTime(APPOINTMENT_DATE, APPOINTMENT_FROM_TIME), // 预约开始时间
          APPOINTMENT_END_DATE: this.formatDateTime(APPOINTMENT_DATE, APPOINTMENT_TO_TIME), // 预约结束时间

          //todo 剩余PRE_APPOINTMENT_ID没给 --------2025/1/21
          PRE_APPOINTMENT_ID: '0', // 预布线预约id   ---  没有给
          PRE_WIRING_START_DATE: this.formatDateTime(PREWIRING_DATE, PREWIRING_FROM_TIME), // 预布线预约开始时间
          PRE_WIRING_END_DATE: this.formatDateTime(PREWIRING_DATE, PREWIRING_TO_TIME), // 预布线预约结束时间
          INSTALL_REMARK: APPOINTMENT_REMARK, // 装机备注
          START_DATE: SRD,
          END_DATE: getDeadlineTime(),
          MODIFY_TAG: '0', //  修改标识
          INSTALL_ATTR_INFO,
        };
      },

      // 获取 table INSTALL_INFO 信息
      submitTableInstallInfo() {
        const { SRDTableInfo } = this.submitSRDInfoData();
        // 获取添加部门账单信息后的选号数据
        let numbersWithAccountInfo = [];

        this.accountSettingPage.numbersWithAccountInfo.forEach(item => {
          const { InstallationAddress, keepCurrentAddress } = item.addressInfo;
          SRDTableInfo.forEach(item2 => {
            if (InstallationAddress !== item2.ADDRESS) return;
            const { appointmentResults, masterAddress, SRD } = item2;
            const INSTALL_ATTR_INFO = this.assemble_INSTALLATTRINFO_Table(item2, item2.SRD);
            const INSTALL_INFO = {
              STANDARD_ADDRESS_ID: keepCurrentAddress ? '-1' : item2.SB_NO,
              STANDARD_ADDRESS: item2.ADDRESS,
              DETAIL_ADDRESS: masterAddress,
              APPOINTMENT_ID: appointmentResults?.APPOINTMENT_ID,
              APPOINTMENT_START_DATE: this.formatDateTime(
                appointmentResults?.APPOINTMENT_DATE,
                appointmentResults?.APPOINTMENT_FROM_TIME,
              ),
              APPOINTMENT_END_DATE: this.formatDateTime(
                appointmentResults?.APPOINTMENT_DATE,
                appointmentResults?.APPOINTMENT_TO_TIME,
              ),
              PRE_APPOINTMENT_ID: '0',
              PRE_WIRING_START_DATE: this.formatDateTime(
                appointmentResults?.PREWIRING_DATE,
                appointmentResults?.PREWIRING_FROM_TIME,
              ),
              PRE_WIRING_END_DATE: this.formatDateTime(
                appointmentResults?.PREWIRING_DATE,
                appointmentResults?.PREWIRING_TO_TIME,
              ),
              INSTALL_REMARK: appointmentResults?.APPOINTMENT_REMARK,
              START_DATE: SRD,
              END_DATE: getDeadlineTime(),
              MODIFY_TAG: '0',
              // 如果没有勾选keepCurrentAddress，则传INSTALL_ATTR_INFO节点
              ...(!keepCurrentAddress && { INSTALL_ATTR_INFO }),
            };
            numbersWithAccountInfo.push({
              ...item,
              SRDTable: item2,
              INSTALL_INFO,
            });
          });
        });

        // 获取群组订单行的装机信息
        const installAddressOfHomePage = this.productSelection.addressInfo;
        const getInstallAddressOfGroup = SRDTableInfo.find(
          item => item.ADDRESS === installAddressOfHomePage.InstallationAddress,
        );
        // 装机属性
        const INSTALL_ATTR_INFO = this.assemble_INSTALLATTRINFO_Table(
          getInstallAddressOfGroup,
          getInstallAddressOfGroup.SRD,
        );
        const {
          APPOINTMENT_ID,
          APPOINTMENT_DATE,
          APPOINTMENT_FROM_TIME,
          APPOINTMENT_TO_TIME,
          PREWIRING_DATE,
          PREWIRING_FROM_TIME,
          PREWIRING_TO_TIME,
          APPOINTMENT_REMARK,
        } = getInstallAddressOfGroup?.appointmentResults || {};
        const BBGIDNumberInstallAddress = {
          STANDARD_ADDRESS_ID: getInstallAddressOfGroup?.SB_NO, // 标准地址编码,存放SB_NO
          STANDARD_ADDRESS: getInstallAddressOfGroup?.ADDRESS, // Installation Address标准地址
          DETAIL_ADDRESS: getInstallAddressOfGroup?.masterAddress, // 详细地址         ---选址获取(和标准地址一样)
          APPOINTMENT_ID: APPOINTMENT_ID, // 预约时间id   施工时间
          APPOINTMENT_START_DATE: this.formatDateTime(APPOINTMENT_DATE, APPOINTMENT_FROM_TIME), // 预约开始时间
          APPOINTMENT_END_DATE: this.formatDateTime(APPOINTMENT_DATE, APPOINTMENT_TO_TIME), // 预约结束时间
          PRE_APPOINTMENT_ID: '0', // 预布线预约id   ---  没有给
          PRE_WIRING_START_DATE: this.formatDateTime(PREWIRING_DATE, PREWIRING_FROM_TIME), // 预布线预约开始时间
          PRE_WIRING_END_DATE: this.formatDateTime(PREWIRING_DATE, PREWIRING_TO_TIME), // 预布线预约结束时间
          INSTALL_REMARK: APPOINTMENT_REMARK, // 装机备注
          START_DATE: getInstallAddressOfGroup?.SRD,
          END_DATE: getDeadlineTime(),
          MODIFY_TAG: '0', //  修改标识
          INSTALL_ATTR_INFO,
        };
        // 存储数据
        this.$store.dispatch('orderCapture/setAccountSettingPage', {
          ...this.accountSettingPage,
          numbersWithAccountInfo,
          BBGIDNumberInstallAddress,
        });
      },
      // 定义一个函数来生成属性对象
      createAttrInfo(attrCode, attrValue, condition = true, currentTime) {
        if (!condition) return null; // 如果条件不满足，返回 null
        return {
          ATTR_TYPE: '1', // 属性类型
          ATTR_CODE: attrCode, // 属性编码
          ATTR_VALUE: attrValue, // 属性值
          ATTR_VALUE_NAME: '1', // 属性值名称
          MODIFY_TAG: '0', // 修改标识
          START_DATE: currentTime,
          END_DATE: getDeadlineTime(),
        };
      },
      // 获取订单属性信息
      getOrderAttrInfo(currentTime) {
        const otherInfo = this.submitOtherInfoData();
        const { isWaitCoInstallOrder, orderIDOfHuntingForCitinet } =
          this.$store.state.customerVerify;
        const { welcomeLetter, welcomeLetterCustomerHotlineNumber, isNewOpenHuntingForCitinet } =
          otherInfo;
        const welcomeLetterAttr = this.createAttrInfo(
          'Welcome Letter',
          welcomeLetter,
          welcomeLetter,
          currentTime,
        );
        const welcomeLetterCustomerHotlineNumberAttr = this.createAttrInfo(
          'Welcome Letter Customer Hotline Number',
          welcomeLetterCustomerHotlineNumber,
          welcomeLetterCustomerHotlineNumber,
          currentTime,
        );
        const isWaitCoInstallOrderAttr = this.createAttrInfo(
          'is_wait_co_install_order',
          '1',
          isNewOpenHuntingForCitinet,
          currentTime,
        );
        // citinet跳转到huntingForCitinet业务需要用到以下
        const sharedOrderIdAttr = this.createAttrInfo(
          'shared_order_id',
          orderIDOfHuntingForCitinet,
          orderIDOfHuntingForCitinet && isWaitCoInstallOrder == '0' ? true : false,
          currentTime,
        );
        const isAddOnHuntingAttr = this.createAttrInfo(
          'is_add_on_hunting',
          '1',
          orderIDOfHuntingForCitinet ? true : false,
          currentTime,
        );
        // huntingForCitinet在途单传以下prior_order_id
        // const priorOrderIdAttr = this.createAttrInfo(
        //   'prior_order_id',
        //   this.$store.state.customerVerify.orderIDOfHuntingForCitinet,
        //   this.$store.state.customerVerify.orderIDOfHuntingForCitinet ? true : false,
        //   currentTime,
        // );
        // 加装Hunting改单逻辑需要 --- 增加以下属性
        const rootOrderIdAttr = this.createAttrInfo(
          'root_order_id',
          orderIDOfHuntingForCitinet,
          orderIDOfHuntingForCitinet && (isWaitCoInstallOrder != '0' || !isWaitCoInstallOrder)
            ? true
            : false,
          currentTime,
        );
        const ORDER_ATTR_INFO = [
          welcomeLetterAttr,
          welcomeLetterCustomerHotlineNumberAttr,
          isWaitCoInstallOrderAttr,
          sharedOrderIdAttr,
          isAddOnHuntingAttr,
          // priorOrderIdAttr,
          rootOrderIdAttr,
        ].filter(item => item !== null); // 过滤掉 null 值
        return ORDER_ATTR_INFO;
      },

      // 格式化时间函数
      formatDateTime(date, time) {
        return date ? `${date} ${time}` : '';
      },
      // 校验
      async validate() {
        return new Promise((resolve, reject) => {
          // 将所有的验证和提交逻辑封装在一个异步函数中
          async function performValidationAndSubmission() {
            try {
              const currentTime = await getCurrentTime();
              // 首先验证 HKT 联系人信息
              await this.$refs.addHKTContactRef.validate();
              // 如果 HKT 联系人信息验证通过，则验证客户联系人信息
              await this.$refs.addCustomerContactListRef.validate();
              // 如果客户联系人信息验证通过，则验证 SRD 字段
              await this.$refs.SRDRef.validate();
              // 如果 SRD 字段验证通过，则验证 OASIS Serial No 字段
              await this.$refs.otherInformationRef.validate();

              // 如果所有的验证都通过，则存储数据
              const { contactList, HKT_ContactList, Customer_ContactList } =
                this.submitHKTAndCustomerData();

              // 初始化 SRDInfo 和 appointmentResults
              const { SRDInfo, appointmentResults, SRDTableInfo } = this.submitSRDInfoData();

              const otherInfo = this.submitOtherInfoData();
              const { isNewOpenHuntingForCitinet } = otherInfo;

              const ORDER_ATTR_INFO = this.getOrderAttrInfo(currentTime);

              let INSTALL_INFO = {}; // 初始化为空对象，避免未定义问题
              // 装机信息
              if (this.changeTable) {
                this.submitTableInstallInfo(); // 当srd为表格Table时执行该函数
              } else {
                INSTALL_INFO = this.submitInstallInfo(); // 只有在 this.changeTable 为 true 时才调用方法并赋值
              }

              this.$store.commit('customerVerify/Set_FulfillmentInfoPage', {
                HKT_ContactList,
                Customer_ContactList,
                contactList,
                otherInfo,
                ORDER_ATTR_INFO,
                ...(!this.changeTable
                  ? { SRDInfo, INSTALL_INFO, appointmentResults }
                  : { SRDTableInfo }), // 只有在this.changeTable为false时才传递INSTALL_INFO
                isNewOpenHuntingForCitinet, // 跳转到huntingForCitinet时需要以此做判断(布尔值)
              });

              // 验证通过，调用 resolve
              resolve();
            } catch (error) {
              // 验证失败，调用 reject 并传递错误信息
              reject(error);
            }
          }

          // 执行异步函数
          performValidationAndSubmission.call(this);
        });
      },
    },
  };
</script>
<style lang="less" scoped>
  .fulfillment-info {
    /deep/.ant-form-vertical .ant-form-explain {
      background-color: #fff !important;
      z-index: 999 !important;
      margin-top: 2px;
    }
  }
  /deep/.ant-form-vertical .ant-form-explain {
    background-color: #fff !important;
    z-index: 999 !important;
    margin-top: 2px;
  }
  /deep/.has-error .ant-form-explain {
    background-color: #fff !important;
    z-index: 999 !important;
    margin-top: 2px;
  }
</style>
