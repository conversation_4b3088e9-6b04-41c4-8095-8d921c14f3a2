import that from '@/main.js';

export default {
  selectTypeList: [
    {
      label: 'Normal',
      value: 'Normal',
    },
    {
      label: 'Reserve',
      value: 'Reserve',
    },
    {
      label: 'Special Service Group',
      value: 'Special Service Group',
    },
  ],
  CriteriaSelData: [
    { label: that.$t('customerVerify.NoCriteria'), value: '0' },
    { label: that.$t('customerVerify.PreferredFirst'), value: '1' },
    { label: that.$t('customerVerify.PreferredLast'), value: '2' },
  ],
  formRules: {
    DDILineQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    DDILevelQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOC: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    Project: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    ReservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceGruopOrService: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
  },
  selectNumformRules: {
    ServiceNoQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoOfTwenty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOC: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    Project: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceGruopOrService: [{ required: true, message: 'Please Enter', trigger: 'change' }],
  },
  serviceGruopOrServiceList: [
    {
      label: 'Please Select',
      value: '',
    },
    {
      label: 'PRODUCT / PRODUCT',
      value: '0',
    },
    {
      label: '0060E / 0060E',
      value: '1',
    },
  ],
  selectNoTypeList: [
    {
      label: 'New Installation',
      value: '0',
    },
    {
      label: 'PIPB Number',
      value: '1',
    },
  ],
  numberOfSelectList: [
    {
      label: '1',
      value: 1,
    },
    {
      label: '2',
      value: 2,
    },
    {
      label: '3',
      value: 3,
    },
  ],
};

// 合并账户页面号码数据
export const mergedServiceNoList = (list, accountSettingNumList) => {
  // 创建一个映射表，用于快速查找 accountSettingNumList 中的数据
  const accountSettingMap = new Map(accountSettingNumList.map(item => [item.value, item]));

  // 遍历 list 并合并数据
  const mergedList = list.map(item => {
    // 如果 accountSettingMap 中存在相同的 value，则覆盖指定字段
    if (accountSettingMap.has(item.value)) {
      const accountItem = accountSettingMap.get(item.value);
      return {
        ...item, // 保留 list 中的原始数据
        check: accountItem.check, // 使用 accountSettingNumList 中的数据
        serviceNo: accountItem.serviceNo,
        billingAccountNo_R: accountItem.billingAccountNo_R,
        billingAccountNo_I: accountItem.billingAccountNo_I,
        departmentalBillName: accountItem.departmentalBillName,
        departmentalBill: accountItem.departmentalBill,
      };
    }
    return item; // 如果没有匹配的 value，保留原始数据
  });
  return mergedList;
};
