<template>
  <div class="two-outer-container">
    <header>
      <div class="secondLevel-header-title">
        {{ $t('customerVerify.numberSelection') }}
      </div>
      <div>
        <a-button type="primary" @click="addServiceNumsOpen">{{
          $t('customerVerify.AddServiceNo')
        }}</a-button>
      </div>
    </header>
    <main>
      <div v-if="selectListData.length">
        <div class="headSearch">
          <a-form-model :model="formData" :colon="false" layout="vertical" ref="Form">
            <a-row :gutter="24">
              <a-col :span="10">
                <a-form-model-item
                  :label="$t('customerVerify.Criteria')"
                  prop="Criteria"
                  labelAlign="left"
                >
                  <a-input-group compact>
                    <a-select
                      v-model="formData.Criteria"
                      :placeholder="$t('common.selectPlaceholder')"
                      style="width: 40%"
                      @change="handleCriteriaChange"
                    >
                      <a-select-option
                        v-for="item in CriteriaSelData"
                        :value="item.value"
                        :key="item.value"
                      >
                        <a-tooltip placement="top">
                          <template slot="title">
                            <span>{{ item.label }}</span>
                          </template>
                          <span>{{ item.label }}</span>
                        </a-tooltip>
                      </a-select-option>
                    </a-select>
                    <a-input
                      v-validate-number
                      v-model.trim="formData.CriteriaInputValue"
                      style="width: calc(60% - 5px); margin-left: 5px"
                      :disabled="formData.Criteria == '0'"
                      :maxLength="criteriaMaxLength"
                    />
                  </a-input-group>
                </a-form-model-item>
              </a-col>
              <a-col :span="5">
                <a-form-model-item
                  :label="$t('customerVerify.ServiceNo')"
                  prop="ServiceNo"
                  labelAlign="left"
                >
                  <a-input
                    v-validate-number
                    v-model="formData.ServiceNo"
                    :placeholder="$t('common.inputPlaceholder')"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="4">
                <div class="btns-container">
                  <a-button
                    :loading="spinning"
                    type="primary"
                    @click="FilterList"
                    class="search-button"
                    >{{ $t('common.buttonFilter') }}</a-button
                  >
                </div>
              </a-col>
            </a-row>
          </a-form-model>
        </div>

        <a-divider />

        <!-- 已选的号码数量 -->
        <div class="filter">
          {{ $t('customerVerify.SelectedServiceNoQty') }} : {{ SelectAll.length }}
        </div>

        <!-- 号码列表 -->
        <div class="gridList">
          <div v-if="!filterSelListData.length">
            {{ $t('customerVerify.NoData') }}
          </div>
          <!-- 如果type类型为真，那么选号表格为可拖动 -->
          <GridList
            v-show="filterSelListData.length"
            type="init"
            :list="filterSelListData"
            :SelectAll="SelectAll"
            :Allchecked="Allchecked"
            @handleRuleSelect="handleRuleSelect"
            @iconClick="iconClick"
            :showIcon="false"
          >
            <div slot="footer" class="footer">
              <div class="checkAll">
                <a-checkbox @change="onSelectChange" v-model="Allchecked">{{
                  $t('customerVerify.Allchecked')
                }}</a-checkbox>
              </div>
              <div class="button">
                <a-button
                  class="table-add-button removebutton"
                  type="primary"
                  @click="removeSelected"
                  :disabled="SelectAll.length == 0"
                  >{{ $t('customerVerify.RemoveSelected') }}</a-button
                >
              </div>
            </div>
          </GridList>
        </div>
      </div>
    </main>

    <a-divider />

    <!-- DN Selection Remark -->
    <div class="filter">
      {{ $t('customerVerify.SelectedServiceNoQty') }}
    </div>

    <div class="textarea-wrapper">
      <a-textarea
        class="textarea"
        v-model="dnSelectionRemark"
        :rows="2"
        :maxLength="upperLimit"
        :placeholder="$t('common.inputPlaceholder')"
      />
      <div class="text-count">
        <span
          class="text-length"
          :class="textLength(dnSelectionRemark) >= upperLimit ? 'red-text' : ''"
          >{{ textLength(dnSelectionRemark) }}</span
        ><span class="text-length">/</span><span class="upper-limit">{{ upperLimit }}</span>
      </div>
    </div>

    <!-- 添加号码 -->
    <a-modal
      :visible="selectNumberVisible"
      :title="$t('customerVerify.addTitle')"
      width="50%"
      :footer="false"
      @cancel="selectNumberCancel"
    >
      <SelectNumberPop
        ref="SelectNumberPop"
        :SelectAll="selectListData"
        @cancel="selectNumberCancel"
        @confirm="selectNumberConfirm"
      />
    </a-modal>
    <!-- 校验提示 -->
    <MessageModal
      :closable="closable"
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="messageModalCancel"
      @confirm="messageModalConfirm"
    />

    <!-- 编辑产品 弹窗-->
    <a-modal
      v-model="editProductVisible"
      :title="$t('customerVerify.editProductTitle')"
      width="80%"
      :maskClosable="false"
      :footer="null"
      @cancel="editProductCancel"
    >
      <!-- 选产品 -->
      <ProductTreeTabs
        ref="ProductTreeTabs"
        :mainProductList="mainProductList"
        :mainProductExpandedRowKeys="mainProductExpandedRowKeys"
        :mainProductCheckboxDisabled="mainProductCheckboxDisabled"
        :mainProductEditBtnChangeColorBool="true"
        :additionalProductList="additionalProductList"
        :additionalProductExpandedRowKeys="additionalProductExpandedRowKeys"
        :additionalProductCheckboxDisabled="additionalProductCheckboxDisabled"
        @getAdditionalProduct="getAdditionalProduct"
      />
      <a-form-model :model="directoryFormData" layout="inline" v-bind="{}" style="margin-top: 10px">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-model-item :label="$t('customerVerify.exDirectory')" prop="exDirectory">
              <a-switch
                v-model="directoryFormData.exDirectory"
                :checked-children="this.$t('common.yes')"
                :un-checked-children="this.$t('common.no')"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item :label="$t('customerVerify.directoryName')" prop="directoryName">
              <a-input
                v-model="directoryFormData.directoryName"
                :placeholder="$t('common.inputPlaceholder')"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-checkbox v-model="isCopyToAll">
        {{ $t('customerVerify.copyToAll') }}
      </a-checkbox>
      <!-- 底部按钮 -->
      <a-space class="flex-right">
        <a-button @click="editProductCancel">{{ $t('common.buttonCancel') }}</a-button>
        <a-button type="primary" @click="editProductOk">{{ $t('common.buttonConfirm') }}</a-button>
      </a-space>
    </a-modal>
  </div>
</template>

<script>
  import { directory } from '@/api/customerVerify';
  import GridList from '@/components/gridList';
  import MessageModal from '@/components/messageModal';
  import { getMainProductTreeSelectedList } from '@/views/components/productTreeList/common/utils';
  import ProductTreeTabs from '@/views/components/productTreeList/tabList';
  import { emailUnique } from '@/views/components/productTreeList/validate/utils';
  import SelectNumberPop from '@/views/components/selectNumberPop';
  import { mergedServiceNoList } from './config.js';
  import { mapState } from 'vuex';
  import config from './config.js';

  export default {
    name: 'numberSelection',
    components: { GridList, MessageModal, ProductTreeTabs, SelectNumberPop },
    data() {
      return {
        closable: false,
        tipsVisible: false,
        tipsMessage: '',
        spinning: false,
        addTitle: this.$t('customerVerify.addTitle'),
        formData: {
          Criteria: '0',
          CriteriaInputValue: '',
        },
        layout: {
          labelCol: { span: 6 },
          wrapperCol: { span: 12 },
        },
        selectListData: [],
        filterSelListData: [],
        SelectAll: [],
        filterStatus: false,
        Allchecked: false,
        editProductVisible: false,
        editProductTitle: this.$t('customerVerify.editProductTitle'),
        currentSelectData: {},
        CriteriaSelData: config.CriteriaSelData,
        isCopyToAll: false,
        mainProductList: [],
        mainProductExpandedRowKeys: [],
        mainProductCheckboxDisabled: false,
        additionalProductList: [],
        additionalProductExpandedRowKeys: [],
        additionalProductCheckboxDisabled: false,
        isRemoveSelected: false,
        selectNumberVisible: false,
        directoryFormData: {
          exDirectory: false,
          directoryName: '',
        },
        dnSelectionRemark: '', // 销售的号码备注信息
        upperLimit: 200, //字数限制
        accountSettingNumList: [],
      };
    },
    watch: {
      selectListData: {
        handler(val) {
          this.filterSelListData = this.getFilterList(val);
        },
        deep: true,
      },
      'productSelection.productInfo.mainProductTreeSelectedList': {
        handler(newList) {
          if (newList?.length > 0) {
            this.whetherNeedCheckEX_Directory(newList[0].ID);
            // 返回上一页更换主产品的时候 需要清空选号页面选择的成员产品
            this.selectListData = this.selectListData.map(item => {
              return {
                ...item,
                MainProduct: '',
                AdditionalProduct: '',
              };
            });
          } else {
            this.directoryFormData.exDirectory = false;
          }
        },
        deep: true, // 深度监听，确保能够监听到数组内部的变化
        immediate: true, // 立即执行一次，确保在组件加载时也能触发
      },
    },
    computed: {
      ...mapState('quotation', {
        productSelection: state => state.productSelection,
        // customerName: state => state.customerInfo.CUSTOMER_NAME,
        // accountSettingNumList: state => state.accountSettingNumList,
        productType: state => state.productType,
      }),
      // 标准的最大长度
      criteriaMaxLength() {
        const { Criteria } = this.formData;
        return Criteria === '1' ? 5 : Criteria === '2' ? 3 : 0;
      },
    },
    mounted() {},
    methods: {
      // 输入数据的字数
      textLength(value) {
        if (typeof value === 'number') {
          return String(value).length;
        }
        return (value || '').length;
      },
      // 判断当前产品是否需要勾选EX-Directory并存储到vuex中
      async whetherNeedCheckEX_Directory(PRODUCT_ID) {
        const res = await directory({ PRODUCT_ID });
        this.directoryFormData.exDirectory = res.DATA[0] == '0' ? false : true;
      },
      handleCriteriaChange(value) {
        this.formData.CriteriaInputValue = '';
      },
      // 获取过滤条件后的数据
      getFilterList(listData) {
        const { Criteria, CriteriaInputValue, ServiceNo } = this.formData;
        let data;
        if (ServiceNo) {
          data = listData.filter(item => item.value.includes(ServiceNo));
          return data;
        } else if (Criteria && CriteriaInputValue) {
          if (Criteria == '1') {
            data = listData.filter(item => item.value.slice(0, 5).includes(CriteriaInputValue));
          } else if (Criteria == '2') {
            data = listData.filter(item => item.value.slice(5, 8).includes(CriteriaInputValue));
          }
          return data;
        }

        data = listData;

        return data;
      },
      // 点击过滤按钮
      FilterList() {
        this.filterSelListData = this.getFilterList(this.selectListData);
      },
      // 添加号码弹窗 打开
      addServiceNumsOpen() {
        this.selectNumberVisible = true;
      },
      // 添加号码弹窗 关闭
      selectNumberCancel() {
        this.selectNumberVisible = false;
      },
      // 添加号码弹窗 确认回调
      selectNumberConfirm() {
        const MAX_NUMBERS = 200;
        if (this.selectListData.length >= MAX_NUMBERS) {
          this.$message.error(
            this.$t('customerVerify.limitBusinessNumbers', {
              Number: MAX_NUMBERS,
            }),
          );
        } else {
          // 拷贝并按照升序排序
          const selectNumList = JSON.parse(
            JSON.stringify(this.$refs.SelectNumberPop.serviceSelectAll),
          ).sort((a, b) => a.value.localeCompare(b.value));

          if (Array.isArray(selectNumList)) {
            // 存在的号码列表
            const existingIds = new Set(this.selectListData.map(item => item.value));

            // 过滤掉 selectListData 中已存在的号码
            const newItems = selectNumList.filter(item => !existingIds.has(item.value));

            // 检查是否超过200的限制
            const remainingSlots = MAX_NUMBERS - this.selectListData.length; // 剩余可添加的名额
            this.selectListData.push(...newItems.slice(0, remainingSlots));

            // 默认全部选中
            this.SelectAll = JSON.parse(JSON.stringify(this.selectListData));
            if (remainingSlots < newItems.length) {
              this.$message.warning(
                this.$t('customerVerify.omitUnnecessaryNumbers', { Number: MAX_NUMBERS }),
              );
            }
          }
        }

        // 判断当前号码是否全选
        this.Allchecked = this.SelectAll.length === this.selectListData.length;

        // 调用取消回调
        this.selectNumberCancel();
      },
      // 单个号码选中 val={key:1,value:1}
      handleRuleSelect(val) {
        // const selectAll = JSON.parse(JSON.stringify(this.SelectAll));
        // const status = selectAll.some(item => item.value === val.value);
        // if (status) {
        //   const data = selectAll.filter(item => item.value !== val.value);
        //   this.SelectAll = data;
        // } else {
        //   const data = [...selectAll, val];
        //   this.SelectAll = data;
        // }
        // // 判断当前号码是否全选
        // this.Allchecked = this.SelectAll.length === this.selectListData.length;
      },
      // 号码编辑产品按钮 打开弹窗
      iconClick(val) {
        // 当前选择的数据
        this.currentSelectData = val;

        // 如果编辑过产品数据，则使用上一次的数据，否则使用初始的选产品页面的数据
        const { mainProductTreeSelectedList, additionalProductTreeSelectedList } =
          this.productSelection?.productInfo || {};

        if (val.MainProduct) {
          this.mainProductList = val.MainProduct;
          this.additionalProductList = val.AdditionalProduct;
        } else {
          this.mainProductList = JSON.parse(JSON.stringify(mainProductTreeSelectedList));
          this.additionalProductList = JSON.parse(
            JSON.stringify(additionalProductTreeSelectedList),
          );
        }
        // 如果编辑过directory数据，则使用上一次的数据，否则使用初始化数据
        if (val.directoryFormData) {
          this.directoryFormData = val.directoryFormData;
        } else {
          this.directoryFormData = {
            exDirectory: this.directoryFormData.exDirectory,
            directoryName: this.customerName,
          };
        }

        let keysList = [];
        this.mainProductList.forEach(item => {
          // 将产品和包的key筛选出来，用作全部层级展开显示
          keysList.push(item.key);
          if (item.children && item.children.length > 0) {
            item.children.forEach(child => {
              keysList.push(child.key);
            });
          }
        });
        // 全部展开显示的key值
        this.mainProductExpandedRowKeys = keysList;

        // 多选框全部置灰
        this.mainProductCheckboxDisabled = true;
        this.additionalProductCheckboxDisabled = true;

        // 弹窗显示
        this.editProductVisible = true;
      },
      getAdditionalProduct() {},
      // 编辑产品 弹窗 关闭
      editProductCancel() {
        this.isCopyToAll = false;
        // 设置表单初始化值
        this.editProductVisible = false;
        this.currentSelectData = {};
      },
      // 编辑产品 弹窗 确认回调
      editProductOk() {
        const productInfo = this.$refs.ProductTreeTabs.getSubComponentProductTreeList();
        // 已选择的产品数据
        // const mainProductTreeSelectedList = getMainProductTreeSelectedList(selectedMainProduct);

        const AdditionalProductData =
          JSON.parse(JSON.stringify(productInfo.AdditionalProduct)).filter(item => item.checked) ||
          [];
        const additionalProductTreeSelectedList =
          getMainProductTreeSelectedList(AdditionalProductData);
        if (this.isCopyToAll) {
          // 复制给已选中的号码
          this.selectListData = this.selectListData.map(item => {
            const matchedItem = this.SelectAll.find(item2 => item.value === item2.value);
            if (matchedItem) {
              item.directoryFormData = JSON.parse(JSON.stringify(this.directoryFormData));
              item.MainProduct = JSON.parse(JSON.stringify(productInfo.MainProduct)) || [];
              item.AdditionalProduct = additionalProductTreeSelectedList;
            }
            return item;
          });
        } else {
          this.selectListData = this.selectListData.map(item => {
            if (item.value == this.currentSelectData.value) {
              item.directoryFormData = JSON.parse(JSON.stringify(this.directoryFormData));
              item.MainProduct = JSON.parse(JSON.stringify(productInfo.MainProduct)) || [];
              item.AdditionalProduct = additionalProductTreeSelectedList;
            }
            return item;
          });
        }
        this.editProductCancel();
      },
      // 全选/反选 全部号码
      onSelectChange(e) {
        this.Allchecked = e.target.checked;
        this.SelectAll = e.target.checked ? [...this.selectListData] : [];
      },
      // 移除号码
      removeSelected() {
        this.closable = true;
        this.tipsVisible = true;
        this.tipsMessage = this.$t('common.removeConfirm');
        this.isRemoveSelected = true;
      },
      // 提示弹窗 确认回调
      messageModalConfirm() {
        if (this.isRemoveSelected) {
          this.selectListData = this.selectListData.filter(item => {
            return !this.SelectAll.some(i => i.value === item.value);
          });
          this.formData.SelectedServiceNoQty = 0;
          this.Allchecked = false;
          this.SelectAll = [];
          this.isRemoveSelected = false;
        }
        this.tipsVisible = false;
        this.closable = false;
      },
      messageModalCancel() {
        this.tipsVisible = false;
        this.closable = false;
      },

      // 当前productType为standalone 同时产品名称为oneCall时，需要校验选号页面的号码是否为8开头的号码，否则需要阻止下一步
      needsOneCallNumberValidation() {
        const { mainProductTreeSelectedList } = this.productSelection.productInfo;
        return (
          this.productType.value == '300012' && mainProductTreeSelectedList[0]?.NAME === 'One Call'
        );
      },

      // 校验是否都以8开头的号码
      validateOneCallNumbers() {
        const invalidNumbers = this.SelectAll.filter(item => !item.value.startsWith('8'));
        if (invalidNumbers.length > 0) {
          return {
            valid: false,
            message: this.$t('assignNumber.verificationNumberStartsWithEight'),
          };
        }
        return { valid: true };
      },
      // 数据处理
      processNumberSelection() {
        const { mainProductTreeSelectedList, additionalProductTreeSelectedList } =
          this.productSelection.productInfo;
        // 如果没有编辑过产品数据，默认将选产品页面的数据填充到号码里
        let list = this.SelectAll.map(item => {
          this.selectListData.forEach(iitem => {
            if (item.value === iitem.value) {
              if (iitem.MainProduct) {
                item.MainProduct = iitem.MainProduct;
                item.AdditionalProduct = iitem.AdditionalProduct;
              } else {
                item.MainProduct = mainProductTreeSelectedList;
                item.AdditionalProduct = additionalProductTreeSelectedList;
              }
              if (iitem.directoryFormData) {
                item.directoryFormData = iitem.directoryFormData;
              } else {
                item.directoryFormData = {
                  exDirectory: this.directoryFormData.exDirectory,
                  directoryName: this.customerName,
                };
              }
            }
          });
          item.check = false;
          item.serviceNo = item.value;
          item.billingAccountNo_R = '';
          item.billingAccountNo_I = '';
          item.departmentalBillName = '';
          item.departmentalBill = '';
          return item;
        });

        // 组装好数据格式，入参方法进行校验
        let numberList = list.map(x => {
          return {
            ...x,
            productList: x.MainProduct || [],
          };
        });

        // 邮箱校验
        emailUnique(numberList);

        const serviceNoList = mergedServiceNoList(list, this.accountSettingNumList);
        this.$store.dispatch('orderCapture/setNumberSelection', {
          serviceNoList,
        });
      },
      // 校验 => 通过 => 存储当前页面数据到vuex
      validate() {
        if (this.SelectAll.length === 0) {
          this.tipsMessage = this.$t('comp.tipsText4');
          this.tipsVisible = true;
          return false;
        }
        if (this.needsOneCallNumberValidation()) {
          const validationResult = this.validateOneCallNumbers();
          if (!validationResult.valid) {
            this.tipsMessage = validationResult.message;
            this.tipsVisible = true;
            return false;
          }
        }

        // 校验通过 => 存储当前页面数据到vuex
        this.processNumberSelection();
        return true;
      },
    },
  };
</script>

<style lang="less" scoped>
  .two-outer-container {
    box-sizing: border-box;
    height: 100%;

    header {
      background-color: #fff;
      box-sizing: border-box;
      // padding: 10px 22px 0 22px;

      .addbutton {
        width: 110px;
      }
    }

    main {
      // padding: 10px 22px 0 22px;
      .headSearch {
        // margin-right: 20%;
        .btns-container {
          height: 92px;
          display: flex;
          align-items: center;
        }
      }
      .address-divider {
        .ant-divider-horizontal {
          margin: 0 0 16px 0 !important;
        }
      }
      .filter {
        .ant-col-6 {
          display: flex;
        }

        .b-btns {
          justify-content: flex-end;
        }

        /deep/ .ant-form-item-label {
          width: auto !important;
          padding: 0 !important;
          line-height: 32px !important;
        }

        .checkBtn {
          display: flex;
          align-items: center;
          padding-bottom: 10px;
        }
      }
      .gird-divider {
        .ant-divider-horizontal {
          margin: 0 0 10px 0 !important;
        }
      }
      .gridList {
        .removebutton {
          width: 120px;
        }
        .footer {
          display: flex;
          margin-top: 10px;
          .checkAll {
            width: 400px;
          }
          .button {
            flex: 1;
            display: flex;
            justify-content: end;
          }
        }
      }
    }
  }
  .flex-right {
    display: flex;
    justify-content: right;
    margin-top: 10px;
    padding-bottom: 10px;
  }
  .textarea-wrapper {
    margin-top: 6px;
    position: relative;
    display: block;
    .textarea {
      height: 100px;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      line-height: 20px;
      font-weight: 400;
    }
    .text-count {
      display: flex;
      justify-content: flex-end;
      .text-length {
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .red-text {
        font-size: 14px;
        color: #e60017;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
      .upper-limit {
        font-size: 14px;
        color: #9e9e9e;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
    }
  }
</style>
