import that from '@/main';
export default {
  tabList: [
    {
      value: '1',
      title: that.$t('orderCapture.AddressProduct'),
      component: 'AddressProduct',
      comp: 'AddressProduct',
      validate: true,
    },
    {
      value: '2',
      title: that.$t('orderCapture.DNSelection'),
      component: 'NumberSelection',
      comp: 'NumberSelection',
    },
    {
      value: '3',
      title: that.$t('orderCapture.Account'),
      component: 'AccountSetting',
      comp: 'AccountSetting',
    },
    {
      value: '4',
      title: that.$t('orderCapture.Fulfillment'),
      component: 'FulfillmentInfo',
      comp: 'FulfillmentInfo',
    },
  ],
  changeServiceNoListColumns: [
    {
      title: that.$t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: that.$t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: 'Ex-directory',
      dataIndex: 'directory',
      key: 'directory',
    },
    {
      title: 'Directory Name',
      dataIndex: 'directoryName',
      key: 'directoryName',
    },
    {
      title: that.$t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],

  serviceNumberColumns: [
    {
      title: that.$t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
      width: 200,
    },
    {
      title: that.$t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
      width: 150,
    },
    {
      title: that.$t('orderList.product'),
      dataIndex: 'product',
      key: 'product',
      width: 350,
    },
    {
      title: that.$t('orderList.oldInstallationAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: that.$t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: that.$t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 80,
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
      width: 150,
    },
  ],
  assignedNumberColumns: [
    {
      title: that.$t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: that.$t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: that.$t('orderList.oldInstallationAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
    },
    {
      title: that.$t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
    },
    {
      title: that.$t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  relocationColumns: [
    {
      title: that.$t('orderList.serviceNo'),
      dataIndex: 'SERIAL_NUMBER',
      key: 'SERIAL_NUMBER',
      width: 120,
    },
    {
      title: that.$t('orderList.oldAddress'),
      dataIndex: 'OLD_INSTALLATION_ADDRESS',
      key: 'OLD_INSTALLATION_ADDRESS',
    },
    {
      title: that.$t('orderList.newInstallationAddress'),
      dataIndex: 'NEW_INSTALLATION_ADDRESS',
      key: 'NEW_INSTALLATION_ADDRESS',
      width: 500,
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  changeServiceNumberColumns: [
    {
      title: that.$t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
    },
    {
      title: that.$t('orderList.newServiceNo'),
      dataIndex: 'NEW_SERVICE_NO',
      key: 'NEW_SERVICE_NO',
    },
    {
      title: that.$t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
    },
  ],
  oneTimeChargeColumns: [
    {
      title: that.$t('suspendResume.chargeName'),
      dataIndex: 'chargeName',
      key: 'chargeName',
    },
    {
      title: that.$t('macd.chargePerDn'),
      dataIndex: 'chargePerDn',
      key: 'chargePerDn',
    },
    {
      title: that.$t('macd.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: that.$t('macd.standardCharge'),
      dataIndex: 'standardCharge',
      key: 'standardCharge',
    },
    {
      title: that.$t('macd.waivedAmountPerLine'),
      scopedSlots: { customRender: 'waivedAmountPerLine' },
    },
    {
      title: that.$t('macd.totalCharge'),
      scopedSlots: { customRender: 'totalCharge' },
    },
  ],
  hktContactColumns: [
    {
      title: that.$t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
      width: 80,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_staffID', customRender: 'staffID' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.salesCode'),
      scopedSlots: { customRender: 'salesCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_agentCode', customRender: 'agentCode' },
      width: 150,
    },
    {
      scopedSlots: { title: 'customTitle_agentName', customRender: 'agentName' },
      width: 150,
    },
    {
      scopedSlots: { title: 'customTitle_dummyCode', customRender: 'dummyCode' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_orderSaleType', customRender: 'orderSaleType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
      width: 150,
    },
    {
      title: that.$t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
      width: 200,
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 80,
    },
  ],
  customerContactColumns: [
    {
      title: that.$t('fulfillmentInfo.seq'),
      scopedSlots: { customRender: 'seq' },
      width: 80,
    },
    {
      title: that.$t('fulfillmentInfo.title'),
      scopedSlots: { customRender: 'customerTitle' },
      width: 120,
    },
    {
      scopedSlots: { title: 'customTitle_participantsType', customRender: 'participantsType' },
      width: 200,
    },
    {
      scopedSlots: { title: 'customTitle_name', customRender: 'name' },
    },
    {
      scopedSlots: { title: 'customTitle_contactPhone', customRender: 'contactPhone' },
    },
    {
      title: that.$t('fulfillmentInfo.mobile'),
      scopedSlots: { customRender: 'mobile' },
    },
    {
      title: that.$t('fulfillmentInfo.email'),
      scopedSlots: { customRender: 'email' },
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
    },
  ],
  // selectNumberPop
  selectTypeList: [
    {
      value: 'Normal',
      label: 'Normal',
    },
    {
      value: 'Reserve',
      label: 'Reserve',
    },
    {
      value: 'Special Service Group',
      label: 'Special Service group',
    },
  ],
  numberSelectList: [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
  ],
  CriteriaSelData: [
    { label: that.$t('customerVerify.NoCriteria'), value: '0' },
    { label: that.$t('customerVerify.PreferredFirst'), value: '1' },
    { label: that.$t('customerVerify.PreferredLast'), value: '2' },
  ],
  selectNumformRules: {
    ServiceNoQty: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoReserve: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceNoSSG: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCode: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOC: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    Project: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    serviceGruopOrService: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    reservationCodeDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    BOCDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
    ProjectDN: [{ required: true, message: 'Please Enter', trigger: 'blur' }],
  },
  serviceNoType: [
    {
      label: 'New Installation',
      value: 'New Installation',
      showFn: () => {
        return true;
      },
    },
    // DEL 一期暂时不弄
    {
      label: 'PIPB Number',
      value: 'PIPB Number',
      // showFn: (productId) => { return ['300001'].includes(productId) },
      showFn: () => {
        return false;
      },
    },
    // Hunting / Citinet / IDAP
    {
      label: 'Working Number',
      value: 'Working Number',
      showFn: () => {
        return false;
      },
    },
  ],
  changeGroupChangeActionList: [
    {
      label: 'Change Order',
      value: 'citinetChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'citinet',
      productType: '300002',
    },
    {
      label: 'Change Order',
      value: 'huntingChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'hunting',
      productType: '300003',
    },
    {
      label: 'Change Order',
      value: 'idapChangeGroup',
      abbreviation: 'chg Order',
      // productType: 'idap',
      productType: '300004',
    },
  ],
  // 订单概要 - 号码列表表格列
  summaryNumberColumns: [
    {
      title: that.$t('fulfillmentInfo.seq'),
      dataIndex: 'Seq',
      key: 'Seq',
    },
    {
      title: that.$t('orderList.serviceNumber'),
      dataIndex: 'SERVICE_NUMBER',
      key: 'SERVICE_NUMBER',
    },
    {
      title: that.$t('customerVerify.InstallationAddress'),
      dataIndex: 'INSTALLATION_ADDRESS',
      key: 'INSTALLATION_ADDRESS',
    },
    {
      title: that.$t('orderList.product'),
      dataIndex: 'PRODUCT',
      key: 'PRODUCT',
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      fixed: 'right',
      width: 80,
    },
  ],
  changeOtherInfoColumns: [
    {
      title: that.$t('macd.changeType'),
      dataIndex: 'changeType',
      key: 'changeType',
      width: 200,
      scopedSlots: { customRender: 'changeType' },
    },
    {
      title: that.$t('macd.before'),
      dataIndex: 'BEFORE',
      key: 'BEFORE',
      width: 400,
      ellipsis: true,
      scopedSlots: { customRender: 'BEFORE' },
    },
    {
      title: that.$t('macd.after'),
      dataIndex: 'AFTER',
      key: 'AFTER',
      width: 400,
      ellipsis: true,
      scopedSlots: { customRender: 'AFTER' },
    },
  ],
  // MACD改单 chargeList表格列
  macdChargeListColumns: [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      customRender: (_, record) => {
        if (record.NAME) return record.NAME;
        else if (record.DISCNT_NAME) return record.DISCNT_NAME;
        else if (record.PACKAGE_NAME) return record.PACKAGE_NAME;
        else if (record.PRODUCT_NAME) return record.PRODUCT_NAME;
      },
      width: 600,
    },
    {
      title: that.$t('chargeList.type'),
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: that.$t('chargeList.charge'),
      dataIndex: 'charge',
      key: 'charge',
      scopedSlots: { customRender: 'charge' },
    },
    {
      title: that.$t('chargeList.period'),
      dataIndex: 'period',
      key: 'period',
      scopedSlots: { customRender: 'period' },
    },
  ],
  // chargeList 合计表格列
  chargeTotalColumns: [
    {
      title: that.$t('chargeList.serviceNoQty'), //
      dataIndex: 'serviceNoQty',
      key: 'serviceNoQty',
    },
    {
      title: that.$t('chargeList.MRCTotal'), //
      dataIndex: 'MRCTotal',
      key: 'MRCTotal',
      scopedSlots: { customRender: 'MRCTotal' },
    },
    {
      title: that.$t('chargeList.MRCRebateTotal'), //
      dataIndex: 'MRCRebateTotal',
      key: 'MRCRebateTotal',
      scopedSlots: { customRender: 'MRCRebateTotal' },
    },
    {
      title: that.$t('chargeList.OTCTotal'), //
      dataIndex: 'OTCTotal',
      key: 'OTCTotal',
      scopedSlots: { customRender: 'OTCTotal' },
    },
  ],
  accountColumns: [
    {
      title: 'Sel',
      scopedSlots: { customRender: 'Sel' },
    },
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_NO',
      key: 'ACCOUNT_NO',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA_NAME',
      key: 'BILL_MEDIA_NAME',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD_NAME',
      key: 'PAYMENT_METHOD_NAME',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCT_FAMILY_NAME',
      key: 'PRODUCT_FAMILY_NAME',
    },
    {
      title: that.$t('accountSetting.chargeCategory'),
      scopedSlots: { customRender: 'chargeCategory' },
    },
    {
      title: that.$t('common.action'),
      key: '10',
      scopedSlots: { customRender: 'action' },
    },
  ],
  assignBillingAccountColumns: [
    {
      title: that.$t('accountSetting.numberType'),
      key: 'NUMBER_TYPE',
      dataIndex: 'NUMBER_TYPE',
    },
    {
      title: that.$t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: that.$t('accountSetting.chargeCategory'),
      key: 'CHARGE_CATEGORY',
      dataIndex: 'CHARGE_CATEGORY',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      key: 'ACCOUNT_ID',
      dataIndex: 'ACCOUNT_ID',
    },
  ],
  assignDepartmentalBillColumns: [
    {
      title: that.$t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: that.$t('accountSetting.departmentalBill'),
      key: 'DEPARTMENT_BILL_NO',
      dataIndex: 'DEPARTMENT_BILL_NO',
    },
  ],
  iddServiceNumberColumns: [
    {
      title: that.$t('orderList.oldServiceNo'),
      dataIndex: 'OLD_SERVICE_NO',
      key: 'OLD_SERVICE_NO',
      width: 200,
    },
    {
      title: that.$t('orderList.product'),
      dataIndex: 'product',
      key: 'product',
      // width: 350,
    },
    {
      title: that.$t('common.action'),
      key: 'action',
      fixed: 'right',
      scopedSlots: { customRender: 'action' },
      width: 80,
    },
    {
      title: that.$t('common.deleteDraft'),
      key: 'deleteDraft',
      fixed: 'right',
      scopedSlots: { customRender: 'deleteDraft' },
      width: 150,
    },
  ],
  accountInfoColumns: [
    {
      title: that.$t('accountSetting.serviceNo'),
      key: 'SERIAL_NUMBER',
      dataIndex: 'SERIAL_NUMBER',
    },
    {
      title: that.$t('accountSetting.accountName'),
      key: 'ACCOUNT_NAME',
      dataIndex: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      key: 'ACCOUNT_ID',
      dataIndex: 'ACCOUNT_ID',
    },
    {
      title: that.$t('accountSetting.billDay'),
      key: 'BILL_DAY',
      dataIndex: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      key: 'BILL_MEDIA',
      dataIndex: 'BILL_MEDIA',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      key: 'PAYMENT_METHOD',
      dataIndex: 'PAYMENT_METHOD',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      key: 'PRODUCE_FAMILY',
      dataIndex: 'PRODUCE_FAMILY',
    },
    {
      title: that.$t('accountSetting.chargeCategory'),
      key: 'CHARGE_CATEGORY',
      dataIndex: 'CHARGE_CATEGORY',
    },
  ],

  // macd改单拆机业务表格列
  macdTerminationColumns: [
    {
      title: that.$t('orderList.orderType'),
      scopedSlots: { customRender: 'SCENE_TYPE' },
    },
    {
      title: that.$t('common.productName'),
      key: 'MAIN_PRODUCT_NAME',
      dataIndex: 'MAIN_PRODUCT_NAME',
    },
    {
      title: that.$t('orderList.disconnectReason'),
      key: 'CANCEL_REASON_AFTER',
      dataIndex: 'CANCEL_REASON_AFTER',
    },
    {
      title: that.$t('orderList.remark'),
      KEY: 'REMARK_AFTER',
      dataIndex: 'REMARK_AFTER',
    },
    {
      title: that.$t('orderList.srd'),
      KEY: 'SRD_AFTER',
      dataIndex: 'SRD_AFTER',
    },
    {
      title: that.$t('common.buttonAmend'),
      scopedSlots: { customRender: 'AMEND' },
    },
    {
      title: that.$t('common.draftAction'),
      scopedSlots: { customRender: 'DRAFT_ACTION' },
    },
  ],
  // macd改单非拆机业务表格列
  macdServiceListColumns: [
    {
      title: that.$t('orderList.orderType'),
      scopedSlots: { customRender: 'SCENE_TYPE' },
    },
    {
      title: that.$t('common.before'),
      scopedSlots: { customRender: 'BEFORE' },
    },
    {
      title: that.$t('common.after'),
      scopedSlots: { customRender: 'AFTER' },
    },
    {
      title: that.$t('common.buttonAmend'),
      scopedSlots: { customRender: 'AMEND' },
    },
    {
      title: that.$t('common.draftAction'),
      scopedSlots: { customRender: 'DRAFT_ACTION' },
    },
  ],
};

export const formGroupColumns = [
  {
    title: that.$t('assignNumber.serviceGroupNo'),
    dataIndex: 'GROUP_SERVICE_NO',
    key: 'GROUP_SERVICE_NO',
  },
  {
    title: that.$t('assignNumber.billingAccountNo'),
    dataIndex: 'BILLING_ACCOUNT_NO',
    key: 'BILLING_ACCOUNT_NO',
  },
];

export const inTransitOrdersList = [
  // {
  //   value: '1',
  //   label: that.$t('assignNumber.pending'),
  // },
  {
    value: '0',
    label: that.$t('assignNumber.completed'),
  },
];
