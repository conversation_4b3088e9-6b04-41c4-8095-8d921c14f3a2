<template>
  <div class="operation-quotation">
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>

    <!-- 报价单 - 明细 -->
    <div v-if="orderId" class="details-order-info">
      <a-form-model :model="orderDetailData" layout="inline">
        <a-row :gutter="24" justify="start" type="flex">
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.PreAFNo')">
              {{ orderId }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.CreateDate')">
              {{ orderDetailData.CREATE_DATE }}
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <!-- 已选中的tab列表 -->
    <div class="tabList">
      <div
        :class="['item', currentSelectedTabItem.value == item.value ? 'active' : '']"
        v-for="(item, index) of tabList"
        :key="item.value"
        @click="selectedTabChange(item, index)"
      >
        {{ item.title }}
      </div>
    </div>
    <!-- 展示内容 -->
    <keep-alive>
      <component
        :key="currentSelectedTabItem.value"
        :ref="currentSelectedTabItem.component"
        :is="currentSelectedTabItem.comp"
        :isEdit="isEdit"
        :orderId="orderId"
      />
    </keep-alive>
    <FooterToolBar class="footer-tool-bar">
      <a-button ghost type="primary" @click="handleCancel" class="fixed-bottom-btn">{{
        $t('customerVerify.Cancel')
      }}</a-button>
      <a-button ghost type="primary" @click="saveInfo" class="fixed-bottom-btn">{{
        $t('customerVerify.Save')
      }}</a-button>

      <!-- TODO -->
      <!-- <a-button type="primary" class="fixed-bottom-btn bg-blue" @click="submit">{{
        $t('customerVerify.Submit')
      }}</a-button> -->
    </FooterToolBar>
    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      @cancel="tipsVisible = false"
      @confirm="tipsVisible = false"
    />
  </div>
</template>

<script>
  import PageHeader from '@/views/components/pageHeader/index.vue';
  import AddressProduct from '@/views/intra/orderCapture/afEditAfInfo/components/addressProduct/index.vue';
  import NumberSelection from '@/views/intra/orderCapture/afEditAfInfo/components/numberSelection/index.vue';
  import AccountSetting from '@/views/intra/orderCapture/afEditAfInfo/components/accountSetting/index.vue';
  import FulfillmentInfo from '@/views/intra/orderCapture/afEditAfInfo/components/fulfillmentInfo/index.vue';
  import FooterToolBar from '@/components/footerToolbar';
  import MessageModal from '@/components/messageModal';
  import config from './config';
  export default {
    name: 'AfEditAfInfo',
    components: {
      PageHeader,
      AddressProduct,
      NumberSelection,
      AccountSetting,
      FulfillmentInfo,
      FooterToolBar,
      MessageModal,
    },
    computed: {
      // 上一页页面信息
      prePageInfo() {
        return this.$t('orderCapture.AfOperationTitle');
      },
      isEdit() {
        return this.$router.query?.status === 'edit';
      },
      orderId() {
        return this.$router.query?.orderId || '****************';
      },
      orderStatus() {
        return this.$router.query?.status || 'S0';
      },
    },
    data() {
      return {
        // 当前显示的tab
        currentSelectedTabItem: config.tabList[0],
        // 展示的tab内容
        tabList: config.tabList,
        orderDetailData: {},
        tipsVisible: false,
        tipsMessage: '',
      };
    },
    mounted() {},
    methods: {
      // tab切换
      selectedTabChange(item, index) {
        if (index === 2) {
          // 添加账户，校验是否已选号码
          const tempItem = this.tabList[1];
          if (tempItem.validate) {
            this.currentSelectedTabItem = item;
          } else {
            if (this.$refs[tempItem.component]) {
              tempItem.validate = this.$refs[tempItem.component].validate();
              if (tempItem.validate) {
                //校验通过
                this.currentSelectedTabItem = item;
              }
            } else {
              this.tipsVisible = true;
              this.tipsMessage = this.$t('comp.tipsText4');
            }
          }
        } else {
          this.currentSelectedTabItem = item;
        }
      },
      //取消  返回上一个页面
      handleCancel() {
        this.$router.customBack();
      },
      //保存按钮
      saveInfo() {
        this.commitInfo();
      },
      //提交按钮
      async submit() {
        // this.commitInfo();
      },
    },
  };
</script>

<style lang="less" scoped>
  @foot-height: 80px;
  .operation-quotation {
    padding-bottom: @foot-height;
  }
  .details-order-info {
    background: #e9e9e9;
    height: 40px;
    padding: 4px 20px;
    margin-bottom: 16px;
    .ant-col {
      padding: 0 !important;
    }
    .ant-form {
      padding-left: 18px;
    }
  }
  .order-status {
    background: #e3f0ff;
    border: 1px solid rgba(0, 114, 255, 1);
    border-radius: 11px;
    font-size: 14px;
    color: #0072ff;
    font-weight: 400;
    padding: 1px 15px;
  }
  .tabList {
    display: flex;
    margin-bottom: 20px;
    .item {
      position: relative;
      height: 40px;
      line-height: 40px;
      border-radius: 2px;
      padding: 0 10px;
      cursor: pointer;
    }
    .active {
      font-weight: bolder;
      &::before {
        content: '';
        position: absolute;
        bottom: 0;
        width: 80%;
        height: 2px;
        background: #01408e;
        text-align: center;
        margin: 0 auto;
        left: 50%;
        transform: translateX(-50%);
      }
      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        width: 0;
        height: 0;
        border: 6px solid transparent;
        border-top-color: #01408e;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .footer-tool-bar {
    height: @foot-height;
    box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>
