<template>
  <div class="af-generate">
    <div class="secondLevel-header-title">{{ $t('orderCapture.AFGeneration') }}</div>
    <div class="af-generate-content">
      <div class="af-generate-part">
        <GenerationTip>{{ $t('orderCapture.AFGenerationTip1') }}</GenerationTip>
        <div class="download-file-part">
          <em class="iconfont icon-wenjian"></em>
          <span> {{ $t('orderSummary.preview') }}{{ $t('common.buttonExport') }} AF </span>
          <em class="iconfont icon-xiazai"></em>
        </div>
        <span class="last-export-part">{{ $t('orderCapture.LastExportedRecord') }}:</span>
      </div>
      <div class="af-generate-part">
        <GenerationTip>{{ $t('orderCapture.AFGenerationTip2') }}</GenerationTip>
        <div class="supplement-part">
          <SupplementBtn :disabled="false">{{
            $t('orderCapture.SupplementartyFormCitinet')
          }}</SupplementBtn>
          <SupplementBtn :disabled="true">{{
            $t('orderCapture.SupplementartyFormIDAP')
          }}</SupplementBtn>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
  export default {
    name: 'AfGeneration',
    components: {
      // tips - 提示
      GenerationTip: {
        functional: true, // 可选，提升性能
        render(h, ctx) {
          return (
            <div class="generation-tip">
              <i class="iconfont icon-tishishuoming"></i>
              {ctx.slots().default}
            </div>
          );
        },
      },
      // supplement
      SupplementBtn: {
        functional: true, // 可选，提升性能
        render(h, ctx) {
          return (
            <a-button
              ghost
              type="primary"
              class="supplement-reset-btn"
              disabled={ctx.props.disabled}
            >
              <i class="iconfont icon-wenjian"></i>
              {ctx.slots().default}
              <i class="iconfont icon-xiazai icon-download"></i>
            </a-button>
          );
        },
      },
    },
    data() {
      return {};
    },
    methods: {},
  };
</script>

<style lang="less" scoped>
  @af-primary-color: #0072ff;
  .generation-tip {
    background: #d2e6ff;
    border-radius: 2px;
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    margin-top: 16px;
    & > .iconfont {
      color: @af-primary-color;
      margin-right: 8.5px;
    }
  }
  .af-generate {
    &-content {
      display: flex;
      justify-content: space-between;
    }
    &-part {
      flex: 0.495;
      background: #ffffff;
      border: 1px solid rgba(221, 221, 221, 1);
      border-radius: 2px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 0 11px;
      min-height: 130px;
    }
  }
  .download-file-part {
    color: @af-primary-color;
    font-size: 14px;
    letter-spacing: 0;
    text-align: center;
    margin-top: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
    & > .icon-wenjian {
      margin-right: 5px;
    }
    & > .icon-xiazai {
      font-size: 12px;
      margin-left: 20px;
    }
  }

  .last-export-part {
    margin-top: 10px;
    font-size: 14px;
    color: #000000;
    letter-spacing: 0;
    text-align: center;
  }

  .supplement-part {
    margin-top: 29px;
  }
  .supplement-reset-btn {
    border: 1px solid @af-primary-color;
    border-radius: 4px !important;
    font-size: 14px;
    color: #000000;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    margin: 0;
    // 遮罩层
    position: relative;
    overflow: hidden;
    z-index: 1;

    // 按钮内容层级
    .icon-download {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%) scale(0.5);
      opacity: 0;
      font-size: 18px;
      color: #fff;
      transition: all 0.3s;
      z-index: 2;
      pointer-events: none;
    }
    &[disabled='disabled'] {
      .icon-download {
        display: none;
      }
      &::after {
        display: none;
      }
    }

    // mask 层
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      opacity: 0;
      transition: opacity 0.3s;
      z-index: 1;
    }

    &:hover::after {
      opacity: 1;
    }
    &:hover .icon-download {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
      color: #fff;
    }

    & > .iconfont {
      color: @af-primary-color;
      margin-right: 9px;
    }
    & + & {
      margin-left: 20px;
    }
  }
</style>
