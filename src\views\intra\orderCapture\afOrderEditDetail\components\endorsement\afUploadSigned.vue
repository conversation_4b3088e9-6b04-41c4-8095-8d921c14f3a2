<template>
  <div>
    <div class="upload-btn" @click="onAdd" v-if="fileDataResource.length === 0">
      <i class="iconfont icon-shangchuan"></i>
      {{ $t('orderCapture.UploadSignedAF') }}
    </div>
    <div v-else>
      <div class="secondLevel-header-title">{{ $t('orderCapture.LastestSignedAF') }}</div>
      <a-table
        :columns="signColumns"
        :data-source="fileDataResource"
        :pagination="false"
        :row-key="row => row.FILE_URL"
      >
        <span slot="name" slot-scope="record, index">
          <a @click.stop="downAttachment(record)">{{ record.FILE_NAME }}</a>
        </span>
        <span slot="action" slot-scope="record, index">
          <Rpopover @onConfirm="onDelete(record)" :content="$t('common.deleteConfirm')">
            <template slot="button">
              <a class="iconfont icon-shanchu action-icon"></a>
            </template>
          </Rpopover>
        </span>
      </a-table>
    </div>
    <PopWindow
      :visible="visible"
      :title="$t('comp.AttachmentUploading')"
      @cancel="handleCancel"
      :bodyStyle="bodyStyle"
      :footer="true"
    >
      <template #Content>
        <div class="search-container">
          <a-upload
            :multiple="false"
            :accept="acceptFileType"
            :showUploadList="true"
            list-type="text"
            :file-list="fileList"
            :before-upload="file => beforeUpload(file)"
          >
            <div class="flexCenter" v-show="fileList.length == 0">
              <div class="listAdd upload">+ {{ $t('common.upload') }}</div>
            </div>
          </a-upload>
        </div>
      </template>
      <template #footer>
        <a-button
          type="primary"
          :disabled="uploadLoading"
          :loading="uploadLoading"
          @click="handleOk"
          class="modal-button-Ok"
          >{{ $t('common.buttonConfirm') }}</a-button
        >
      </template>
    </PopWindow>
  </div>
</template>
<script>
  import { saveAfOrderRemark } from '@/api/orderCapture';
  import { uploadFile } from '@/api/common';
  import { getCurrentTime } from '@/utils/utils';
  import { fileMixin } from '../../mixins/fileMixin';
  import Rpopover from '@/components/Rpopover';
  import PopWindow from '@/components/popWindow';
  export default {
    name: 'AfUploadSigned',
    components: {
      PopWindow,
      Rpopover,
    },
    props: {
      orderId: {
        type: String,
        default: '',
      },
    },
    mixins: [fileMixin],
    data() {
      return {
        visible: false,
        uploadLoading: false,
        bodyStyle: {
          height: '150px',
          padding: '10px',
        },
        fileList: [],
        fileDataResource: [],
        attachmentInfo: {}, // 上传的附件信息
      };
    },
    computed: {
      signColumns() {
        return [
          {
            title: this.$t('orderCapture.SignedFileName'),
            dataIndex: 'FILE_NAME',
            key: 'FILE_NAME',
            scopedSlots: { customRender: 'name' },
          },
          {
            title: this.$t('comp.UploadTime'),
            dataIndex: 'OPER_TIME',
            key: 'OPER_TIME',
          },
          {
            title: this.$t('comp.UploadUser'),
            dataIndex: 'OPER_STAFF_NAME',
            key: 'OPER_STAFF_NAME',
          },
          {
            title: this.$t('common.action'),
            scopedSlots: { customRender: 'action' },
            className: 'action-column',
          },
        ];
      },
    },
    methods: {
      init(arr = []) {
        this.fileDataResource = [].concat(arr);
      },
      // 上传接口
      async handleUpload(file) {
        let formData = new FormData();
        formData.append('file', file);
        this.uploadLoading = true;
        const currentTime = await getCurrentTime();
        const res = await uploadFile(formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        this.$message.success(this.$t('common.successMessage'));
        this.uploadLoading = false;
        const curFile = await this.formatFileItem(file, {
          OPER_TIME: currentTime,
          FILE_URL: res?.DATA[0],
        });
        this.attachmentInfo = { ...curFile };
        // upload组件 - 回显使用
        this.fileList.push(file);
      },
      onAdd() {
        this.visible = true;
      },
      // 取消操作
      handleCancel() {
        // 初始化
        this.fileList = [];
        this.attachmentInfo = {};
        this.visible = false;
      },
      // 上传弹窗确定按钮
      handleOk() {
        if (!this.attachmentInfo.FILE_URL) {
          this.$message.info(this.$t('comp.UploadAttchmentTips'));
          return;
        }
        this.saveAttach();
      },
      // 删除
      onDelete(record) {
        // 新增后删除的，直接删除数据
        let index = this.dataList.findIndex(item => item.id === record.id);
        if (index !== -1) {
          this.dataList.splice(index, 1);
        }
        this.addDataId(this.dataList);
      },
      // 保存 附件
      async saveAttach() {
        this.saveLoading = true;
        const params = {
          ORDER_ID: this.orderId,
          MULTIMEDIA_FILES: [{ ...this.attachmentInfo }],
        };
        console.log(params, 'changeEditStatus');
        saveAfOrderRemark(params)
          .then(res => {
            if (res?.DATA[0] && res.DATA[0].status === '0') {
              this.$message.success(this.$t('common.successMessage'));
            } else {
              this.$message.error(this.$t('common.errorMessage'));
            }

            // 表格 - 界面展示所用
            this.fileDataResource = [this.attachmentInfo];
            setTimeout(() => {
              this.handleCancel();
            }, 100);
          })
          .catch(err => {
            this.$message.error(err);
          });
      },
    },
  };
</script>
<style lang="less" scoped>
  @af-primary-color: #0072ff;
  .listAdd {
    margin-top: 10px;
    width: 100%;
    height: 40px;
    background-color: #ffffff;
    border: 1px dashed #0076ff;
    color: #0076ff;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }
  .flexCenter {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .upload {
    height: 32px;
    line-height: 32px;
    width: 210px;
  }
  /deep/ .ant-upload {
    width: 100%;
  }
  /deep/ .ant-modal {
    width: 520px !important;
  }
  /deep/.ant-divider-dashed {
    margin: 0px 0 !important;
  }

  .upload-btn {
    border: 1px dashed @af-primary-color;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: @af-primary-color;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
    height: 32px;
    & > .iconfont {
      margin-right: 9px;
    }
  }
</style>
