<template>
  <div>
    <div
      class="file-list"
      :style="{ justifyContent: fileList.length === 0 ? 'center' : 'flex-start' }"
    >
      <div
        class="file-list-item"
        v-for="(item, index) in fileList"
        :key="item.FILE_URL + '_' + index"
        :class="[notAllowEdit ? 'btn-not-allow' : '']"
      >
        <i class="iconfont icon-wenjian icon-left"></i>
        <EllipsisTooltip :text="item.FILE_NAME"></EllipsisTooltip>
        <a-icon type="close" class="icon-right" @click.stop="deleteFile(item)" />
      </div>
      <a-upload
        :multiple="false"
        :accept="acceptFileType"
        :showUploadList="false"
        :file-list="null"
        :before-upload="file => beforeUpload(file)"
        :disabled="notAllowEdit"
      >
        <div class="upload" :class="[notAllowEdit ? 'btn-not-allow' : '']">
          + {{ $t('common.upload') }}
        </div>
      </a-upload>
    </div>
    <p class="file-tips">{{ $t('orderCapture.SupportedExtension') }}: {{ acceptFileType }}</p>
  </div>
</template>

<script>
  import { uploadFile } from '@/api/common';
  import { getCurrentTime } from '@/utils/utils';
  import { fileMixin } from '../../mixins/fileMixin';
  import EllipsisTooltip from '@/views/components/ellipsisTooltip/index.vue';
  export default {
    name: 'AttachmentBtnList',
    components: {
      EllipsisTooltip,
    },
    mixins: [fileMixin],
    methods: {
      // 上传接口
      async handleUpload(file) {
        console.log(file, 'file');
        let formData = new FormData();
        formData.append('file', file);
        const currentTime = await getCurrentTime();
        const res = await uploadFile(formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        this.$message.success(this.$t('common.successTip'));
        this.fileList.push(
          await this.formatFileItem(file, {
            OPER_TIME: currentTime,
            FILE_URL: res?.DATA[0],
          }),
        );
      },
    },
  };
</script>

<style lang="less" scoped>
  .common-btn-style(@borderColor, @borderStyle: solid, @btnWidth: 210px, @btnHeight: 32px) {
    border: 1px @borderStyle @borderColor;
    height: @btnHeight;
    line-height: @btnHeight;
    width: @btnWidth;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    margin: 0 8px 5px 0;
  }

  .file-list {
    display: flex;
    align-items: center;
    &-item {
      .common-btn-style(#ddd);
      color: #333;
      position: relative;
      padding: 0 32px;
      & > .icon-left,
      & > .icon-right {
        position: absolute;
        color: #a9b0b4;
        top: 50%;
        transform: translateY(-50%);
      }
      & > .icon-left {
        left: 9px;
      }
      & > .icon-right {
        right: 9px;
      }
    }
  }
  .upload {
    .common-btn-style(#0076ff, dashed);
    justify-content: center;
    color: #0076ff;
    cursor: pointer;
  }
  .btn-not-allow {
    cursor: not-allowed;
    background: #f5f5f5;
    color: #bbb;
    border-color: #eee;
    pointer-events: none;
    opacity: 0.7;
  }
  .file-tips {
    margin: 0;
    text-align: center;
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
  }
</style>
