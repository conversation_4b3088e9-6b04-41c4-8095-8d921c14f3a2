<template>
  <div>
    <div class="secondLevel-header-title">
      {{ title }}
    </div>
    <TextareaWrapper v-model="textValue" countPosWay="inner" :disabled="disabled"></TextareaWrapper>
    <a-divider dashed />
    <!-- 最多允许上传5个附件 -->
    <AttachmentBtnList
      ref="attachmentBtnList"
      :list="attachmentList"
      :disabled="disabled"
      :fileType="fileType"
    ></AttachmentBtnList>
    <a-divider dashed />
    <div class="note-btn">
      <a-button
        :ghost="!disabled"
        type="primary"
        @click="changeEditStatus"
        :loading="saveLoading"
        >{{ disabled ? $t('common.edit') : $t('common.save') }}</a-button
      >
    </div>
  </div>
</template>

<script>
  import { saveAfOrderRemark } from '@/api/orderCapture';
  import TextareaWrapper from '@/views/components/textareaWrapper/index.vue';
  import AttachmentBtnList from './attachmentBtnList';
  export default {
    name: 'AttachmentNote',
    components: {
      TextareaWrapper,
      AttachmentBtnList,
    },
    props: {
      orderId: {
        type: String,
        default: '',
      },
      fileType: {
        type: String,
        default: '01',
      },
      title: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        textValue: '',
        saveLoading: false,
        disabled: true,
        attachmentList: [],
      };
    },
    methods: {
      init({ content, attachments }) {
        if (content !== undefined && content !== null) {
          this.textValue = content;
        }
        if (attachments !== undefined && attachments !== null) {
          this.attachmentList = attachments;
        }
      },
      async changeEditStatus() {
        // 触发保存
        if (!this.disabled) {
          this.saveLoading = true;
          const fileList = this.$refs.attachmentBtnList.fileList;
          const params = {
            ORDER_ID: this.orderId,
            MULTIMEDIA_FILES: [...fileList],
          };
          // 赋值 - 备注信息
          if (this.fileType === '14' || this.fileType === '15') {
            const remarkTypeMap = {
              '14': 'sales_note',
              '15': 'asm_note',
            };
            const remarkItem = {
              ATTR_CODE: remarkTypeMap[this.fileType],
              ATTR_VALUE: this.textValue,
            };
            params.ORDER_REMARK_ITEM = [remarkItem];
          }
          console.log(params, 'changeEditStatus');
          saveAfOrderRemark(params)
            .then(res => {
              if (res?.DATA[0] && res.DATA[0].status === '0') {
                this.$message.success(this.$t('common.successMessage'));
              } else {
                this.$message.error(this.$t('common.errorMessage'));
              }
            })
            .catch(err => {
              this.$message.error(err);
            })
            .finally(() => {
              this.saveLoading = false;
            });
        }
        // 触发 - 更改效果
        this.disabled = !this.disabled;
      },
    },
  };
</script>

<style lang="less" scoped>
  .note-btn {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    & > .ant-btn {
      width: 80px;
    }
  }
</style>
