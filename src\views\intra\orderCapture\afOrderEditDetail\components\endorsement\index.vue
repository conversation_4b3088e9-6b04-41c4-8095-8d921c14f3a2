<template>
  <div>
    <div class="endorsement-info">
      <div class="text-red">
        <!-- #TODO: 按目前的设计，计算没问题。但是在业务层面的话，ftg没算入盈利中，存在信息误差。后面需要跟需求沟通 -->
        <span class="info-title">{{ $t('orderCapture.TotalContractRevenue') }}: </span>${{
          basicData.totalContractRevenue || 0
        }}
      </div>
      <!-- #TODO: 审核通过后不显示审批人信息行 -->
      <div>
        <span class="info-title">{{ $t('orderCapture.AFEndorsementApprover') }}: </span>
        {{ aFEndorsementApprover }}
      </div>
    </div>
    <a-divider />
    <!-- 补充表单 原型：SupplementArty Form Download -->
    <AfGeneration ref="afGeneration"></AfGeneration>
    <a-divider />
    <!-- Af Upload Signed, #TODO：如果有 Last exported record 信息，才显示 -->
    <AfUploadSigned
      ref="afUploadSigned"
      :orderId="orderId"
      fileType="13"
      :maxFileNumber="1"
      acceptFileType=".pdf"
    ></AfUploadSigned>
    <a-divider />
    <!-- 附件 - 交互说明：原有带入的报价单不可删除 -->
    <Attachment
      :fileList="attachmentList"
      :ifShowUpload="!isEdit"
      :actionBtnShowType="!isEdit ? 'download,delete' : 'download'"
      ref="attachmentRef"
    />
    <a-divider />
    <!-- 审批备注 -->
    <OfferPreApprovalRemark ref="offerPreApprovalRemark"></OfferPreApprovalRemark>
    <a-divider />
    <!-- 销售笔记 -->
    <AttachmentNote
      ref="saleAttachmentNote"
      :title="$t('orderCapture.SalesNote')"
      fileType="14"
      :orderId="orderId"
    ></AttachmentNote>
    <a-divider />
    <!-- ASM笔记 -->
    <AttachmentNote
      ref="asmAttachmentNote"
      :title="$t('orderCapture.ASMSalesSupportNote')"
      fileType="15"
      :orderId="orderId"
    ></AttachmentNote>
    <a-divider />
    <!-- 审批原因 -->
    <ApprovalReason :selectReasons="selectReasons"></ApprovalReason>
  </div>
</template>

<script>
  import { queryAfEndorsement } from '@/api/orderCapture';
  import { splitArrayByValues } from '../../config';
  import AfGeneration from './afGeneration.vue';
  import AfUploadSigned from './afUploadSigned.vue';
  import Attachment from '@/views/components/attachment/index.vue';
  import OfferPreApprovalRemark from './offerPreApprovalRemark';
  import AttachmentNote from './attachmentNote';
  import ApprovalReason from './approvalReason.vue';
  export default {
    name: 'AfEndorsement',
    props: {
      orderId: {
        type: String,
        default: '',
      },
      isEdit: { type: Boolean, default: false },
      basicData: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      AfGeneration,
      AfUploadSigned,
      Attachment,
      OfferPreApprovalRemark,
      AttachmentNote,
      ApprovalReason,
    },
    data() {
      return {
        aFEndorsementApprover: '',
        attachmentList: [],
        selectReasons: [],
      };
    },
    watch: {
      basicData: {
        handler(val) {
          console.log(val, 'watch - val');
          this.initData();
        },
        immediate: true,
        deep: true,
      },
    },
    activated() {
      this.getData();
    },
    methods: {
      getData() {
        const params = {
          ORDER_ID: this.orderId,
        };
        queryAfEndorsement({
          ...params,
        })
          .then(res => {
            const current = res.DATA?.[0];
            console.log(current, 'endorsement - current');
            if (current) {
              this.aFEndorsementApprover = current.AF_ENDORSEMENT_APPROVER;
              // 组装 generation 信息
              if (current.LAST_EXPORT_INFO) {
                const { FILE_TYPE, FILE_NAME, FILE_URL, OPER_STAFF_ID, OPER_TIME } =
                  current.LAST_EXPORT_INFO;
                this.generationExportInfo = {
                  FILE_TYPE,
                  FILE_NAME,
                  FILE_URL,
                  OPER_STAFF_ID,
                  OPER_TIME,
                };
              }
              // 组装 uploadSign信息
              if (current.LAST_SIGNED_INFO && current.LAST_SIGNED_INFO.length != 0) {
                this.$refs.afUploadSigned.init(current.LAST_EXPORT_INFO);
              }
              // 处理 - 附件信息
              this.attachmentList = [].concat(current.ATTACHMENT_INFO);
              // 处理 - sale附件
              this.$refs.saleAttachmentNote.init({ attachments: current.SALES_UPLOAD_ATTACHMENT });
              // 处理 - asm附件
              this.$refs.asmAttachmentNote.init({ attachments: current.ASM_UPLOAD_ATTACHMENT });
            }
          })
          .catch(error => {
            console.log(error);
          });
      },
      initData() {
        const { remarkInfo } = this.basicData;
        // 分解出 sale & asm note 的备注信息
        if (remarkInfo) {
          const noteRemark = splitArrayByValues([...remarkInfo], 'ATTR_CODE', [
            'sales_note',
            'asm_note',
          ]);
          console.log(noteRemark, 'noteRemark');
          // 处理审批备注
          this.$nextTick(() => {
            this.$refs.offerPreApprovalRemark.init(remarkInfo);
            this.$refs.saleAttachmentNote.init({
              content: noteRemark?.sales_note?.[0].ATTR_VALUE || '',
            });
            this.$refs.asmAttachmentNote.init({
              content: noteRemark?.asm_note?.[0].ATTR_VALUE || '',
            });
          });
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .endorsement-info {
    margin-top: 3px;
    & > div {
      font-size: 14px;
      margin-top: 10px;
      color: #373d41;
      line-height: 20px;
      font-weight: 400;
      &.text-red {
        font-size: 18px;
        color: #e60017;
        line-height: 25px;
        font-weight: 500;
      }
    }
    .info-title {
      font-size: 14px;
      color: #373d41;
      line-height: 25px;
      font-weight: 500;
      margin-right: 3px;
    }
  }
</style>
