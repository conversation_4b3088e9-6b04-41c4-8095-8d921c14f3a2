<template>
  <div class="offer-pre-approval-remark">
    <div class="secondLevel-header-title">
      {{ $t('quotation.OfferPreApprovalRemark') }}
    </div>
    <div v-for="(value, key) in remarkInfo" :key="key" class="remark-details">
      <span class="remark-details-title">{{ value.title }}:</span>
      <p class="remark-details-content">{{ value.content }}</p>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'OfferPreApprovalRemark',
    data() {
      return {
        remarkInfo: {
          competitor: {
            title: this.$t('quotation.Competitors'),
            content: '',
          },
          competitor_offer: {
            title: this.$t('quotation.CompetitorsOffers'),
            content: '',
          },
          impact: {
            title: this.$t('quotation.ImpactIfLoseTheDeal'),
            content: '',
          },
          opportunity: {
            title: this.$t('quotation.UpcomingBusinessOpportunities'),
            content: '',
          },
        },
      };
    },
    methods: {
      init(arr = []) {
        arr.forEach(item => {
          if (this.remarkInfo[item.ATTR_CODE]) {
            this.remarkInfo[item.ATTR_CODE].content = item.ATTR_VALUE;
          }
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .offer-pre-approval-remark {
    padding: 0 1px;
  }
  .remark-details {
    padding: 3px 0 17px;
    &:last-child {
      padding-bottom: 0;
    }
    &-title {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      font-weight: 600;
    }
    &-content {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      font-weight: 400;
      margin: 0;
    }
  }
</style>
