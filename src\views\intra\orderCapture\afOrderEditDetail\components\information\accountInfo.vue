<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('orderSummary.accountInfo') }}</div>
    <a-table
      :columns="filteredColumns"
      :data-source="dataResource"
      :pagination="false"
      :locale="concatLocale"
    >
    </a-table>
  </div>
</template>
<script>
  import { accountTableColumns } from './tableConfig';
  import { tableMixins } from '../../mixins/tableMixin';
  export default {
    name: 'AccountInfo',
    props: {
      dataResource: {
        type: Array,
        default: () => [],
      },
      showServiceNo: {
        type: Boolean,
        default: false,
      },
    },
    mixins: [tableMixins],
    data() {
      return {
        columns: accountTableColumns.columns || [],
        accountColumns: accountTableColumns.accountColumns || [],
      };
    },
    computed: {
      filteredColumns() {
        return this.showServiceNo ? this.accountColumns : this.columns;
      },
    },
  };
</script>
