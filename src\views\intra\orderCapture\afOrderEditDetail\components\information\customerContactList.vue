<template>
  <div>
    <div class="secondLevel-header-title">{{ $t('orderSummary.customerContactList') }}</div>
    <a-table
      :columns="tableColumns"
      :data-source="dataResource"
      :pagination="false"
      :locale="concatLocale"
    >
      <span slot="PARTICIPANTS_TYPE" slot-scope="record, index">
        {{ changeTypeLabel(record) }}
      </span>
      <span slot="CUSTOMER_TITLE" slot-scope="record, index"> {{ changeTitleLabel(record) }} </span>
    </a-table>
  </div>
</template>
<script>
  import { customerContactTableColumns } from './tableConfig';
  import { queryCreateCustomerEnum } from '@/api/fulfillmentInfo';
  import { tableMixins } from '../../mixins/tableMixin';
  export default {
    name: 'CustomerContactList',
    props: {
      dataResource: {
        type: Array,
        default: () => [],
      },
      isDetail: {
        type: Boolean,
        default: false,
      },
    },
    mixins: [tableMixins],
    data() {
      return {
        columns: customerContactTableColumns,
        participantsType: [],
        titleList: [],
      };
    },
    mounted() {
      this.getTypeList();
    },
    computed: {
      tableColumns() {
        const { isDetail } = this;
        return customerContactTableColumns.filter(item => item.showFn(isDetail));
      },
    },
    methods: {
      // 查枚举数据
      async getTypeList() {
        const params = {
          CODE_TYPE: 'PARTICIPANTS_TYPE_CUSTOMER',
        };
        try {
          const res = await queryCreateCustomerEnum(params);
          this.participantsType = res.DATA;
          const res2 = await queryCreateCustomerEnum({ 'CODE_TYPE': 'TITLE_TYPE' });
          this.titleList = res2.DATA;
        } catch (error) {
          console.log(error);
        }
      },
      changeTypeLabel(record) {
        return this.participantsType?.find(item => item.CODE_VALUE === record.PARTICIPANTS_TYPE)
          ?.CODE_NAME;
      },
      changeTitleLabel(record) {
        return (
          this.titleList?.find(item => item.CODE_VALUE === record.TITLE)?.CODE_NAME ||
          this.titleList?.find(item => item.CODE_NAME === record.TITLE)?.CODE_NAME
        );
      },
    },
  };
</script>
