<template>
  <div>
    <!-- af单 - 明细 -->
    <div class="details-order-info">
      <a-form-model :model="orderInfo" layout="inline">
        <a-row :gutter="24" justify="start" type="flex">
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.PreAFNo')">
              {{ orderInfo.ORDER_ID }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.CreateDate')">
              {{ orderInfo.CREATE_DATA }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('quotation.Status')" labelAlign="left">
              <span class="order-status">{{ orderInfo.STATUS_NAME }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <!-- 客户信息 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.CustomerInformation') }}
    </div>
    <a-form-model :model="cusInfo" :colon="false" ref="customerForm" :rules="cusInfoRules">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.CustomerName')" prop="CUST_NAME">
            <a-input name="CUST_NAME" v-model="cusInfo.CUST_NAME" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.MarketSegment')" prop="MARKET_SEGMENT">
            <a-input name="MARKET_SEGMENT" v-model="cusInfo.MARKET_SEGMENT" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.LOB')" prop="LOB_NAME" labelAlign="left">
            <a-input name="LOB_NAME" v-model="cusInfo.LOB_NAME" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-divider />
    <!-- Sale&Asm - 明细 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.SalesASMInformation') }}
    </div>
    <a-form-model :model="salesInfo" :colon="false" ref="salesAsmForm">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start" v-for="(item, key) in salesInfo" :key="key">
          <a-form-model-item :label="item.label" :rules="item.rules">
            <a-input v-model="item.value" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- ASM Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMStaffNo')" prop="ASM_STAFF_NO">
            <a-input-search
              :loading="loadingASM"
              v-model.trim="asmInfo.ASM_STAFF_NO"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeASM('code')"
              @search="handleSearch('ASM')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMName')" prop="ASM_NAME">
            <a-input-search
              :loading="loadingASM"
              v-model.trim="asmInfo.ASM_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeASM('name')"
              @search="handleSearch('ASM')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMContactNo')" prop="ASM_CONTACT_NO">
            <a-input name="ASM_CONTACT_NO" v-model="asmInfo.ASM_CONTACT_NO" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.ASMEmail')" prop="ASM_EMAIL" labelAlign="left">
            <a-input name="ASM_EMAIL" v-model="asmInfo.ASM_EMAIL" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  export default {
    name: 'DetailsInformation',
    data() {
      return {
        loadingASM: false,
        orderInfo: {
          ORDER_ID: '',
          CREATE_DATA: '',
          STATUS_NAME: '',
        },
        cusInfo: {
          CUST_NAME: '',
          MARKET_SEGMENT: '',
          LOB_NAME: '',
        },
        salesInfo: {
          SALES_CODE: {
            value: '',
            rules: {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
            label: this.$t('quotation.SalesmanCode'),
          },
          SALES_NAME: {
            value: '',
            rules: {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
            label: this.$t('quotation.SalesName'),
          },
          SALES_SEGMENT: {
            value: '',
            rules: {},
            label: this.$t('quotation.SalesSegment'),
          },
          SALES_TEAM: {
            value: '',
            rules: {},
            label: this.$t('quotation.SalesTeam'),
          },
          SALES_CONTACT_NO: {
            value: '',
            rules: {},
            label: this.$t('quotation.SalesSM'),
          },
          SALES_EMAIL: {
            value: '',
            rules: {},
            label: this.$t('quotation.SalesContactNo'),
          },
          SALES_SM: {
            value: '',
            rules: {},
            label: this.$t('quotation.SalesEmail'),
          },
        },
        asmInfo: {
          ASM_STAFF_NO: '', //员工ID
          ASM_NAME: '', //姓名
          ASM_CONTACT_NO: '', //联系电话
          ASM_EMAIL: '', //邮箱
        },
      };
    },
    computed: {
      cusInfoRules() {
        return {
          CUST_NAME: [
            {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
          ],
          MARKET_SEGMENT: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
          LOB_NAME: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
        };
      },
    },
    methods: {
      init({ orderInfo = {}, cusInfo = {}, salesAsmInfo = [] }) {
        console.log(orderInfo, cusInfo, salesAsmInfo);
        this.orderInfo = Object.freeze({ ...orderInfo });
        this.cusInfo = Object.freeze({ ...cusInfo });

        // 处理
        const { saleObj, asmObj } = this.formatSalesAsm(salesAsmInfo);
        this.saleObj = Object.freeze({ ...saleObj });
        for (const key in this.salesInfo) {
          if (Object.prototype.hasOwnProperty.call(this.salesInfo, key)) {
            const ele = this.salesInfo[key];
            ele.value = saleObj[key];
          }
        }
        this.asmInfo = { ...asmObj };
      },
      // 格式化 - 销售 & ASM
      formatSalesAsm(arr = []) {
        let saleObj = {},
          asmObj = {};
        for (let i = 0; i < arr.length; i++) {
          const ele = arr[i];
          if (ele.SALES_TYPE === '01') {
            // 01 - 销售
            saleObj = {
              SALES_CODE: ele.SALES_CODE,
              SALES_NAME: ele.STAFF_NAME,
              SALES_SEGMENT: ele.SALES_SEGMENT,
              SALES_TEAM: ele.SALES_TEAM,
              SALES_CONTACT_NO: ele.CONTACT_NO,
              SALES_EMAIL: ele.EMAIL,
              SALES_SM: ele.SALES_SM,
            };
          } else if (ele.SALES_TYPE === '02') {
            // 02 - ASM
            asmObj = {
              ASM_STAFF_NO: ele.SALES_CODE,
              ASM_NAME: ele.STAFF_NAME,
              ASM_CONTACT_NO: ele.CONTACT_NO,
              ASM_EMAIL: ele.EMAIL,
            };
          }
        }
        return { saleObj, asmObj };
      },
      // 查询销售和ASM信息
      handleSearch(type) {
        if (type == 'sales') {
          this.loadingSales = true;
          setTimeout(() => {
            this.loadingSales = false;
          }, 100);
          console.log('销售');
        } else {
          console.log('ASM');
        }
      },
      // ASM 信息更改，清空ASM相关信息；
      changeASM(type) {
        if (type == 'code') {
          this.formData.ASM_NAME = '';
        } else {
          this.formData.ASM_STAFF_NO = '';
        }
        this.formData.ASM_CONTACT_NO = '';
        this.formData.ASM_EMAIL = '';
      },
    },
  };
</script>

<style lang="less" scoped>
  .details-order-info {
    background: #e9e9e9;
    height: 40px;
    padding: 4px 20px;
    margin-bottom: 16px;
    .ant-col {
      padding: 0 !important;
    }
    .ant-form {
      padding-left: 18px;
    }
  }
  .order-status {
    background: #e3f0ff;
    border: 1px solid #0072ff;
    border-radius: 11px;
    font-size: 14px;
    color: #0072ff;
    font-weight: 400;
    padding: 1px 15px;
  }
</style>
