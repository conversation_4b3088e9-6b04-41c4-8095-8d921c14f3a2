<template>
  <div class="fulfillment-info">
    <div class="secondLevel-header-title">{{ $t('orderSummary.fulfillmentInfo') }}</div>
    <div class="details-order-info">
      <a-form-model :model="dataResource" layout="inline">
        <a-row :gutter="24" justify="start" type="flex">
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('orderSummary.srd')">
              {{ dataResource.SRD }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('orderSummary.appointmentDate')">
              {{ dataResource.APPOINTMENT_START_DATE }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('orderSummary.preWiringDate')" labelAlign="left">
              {{ dataResource.PRE_WIRING_START_DATE }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('orderSummary.project')" labelAlign="left">
              {{ dataResource.PROJECT_CODE }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item
              :label="$t('orderSummary.billingCustomerReference')"
              labelAlign="left"
            >
              {{ dataResource.BILLING_CUSTOMER_REFERENCE }}
            </a-form-model-item>
          </a-col>
          <a-col :span="18">
            <a-form-model-item :label="$t('orderSummary.orderRemark')" labelAlign="left">
              {{ dataResource.ORDER_REMARK }}
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'FulfillmentInfo',
    props: {
      dataResource: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {};
    },
    methods: {},
  };
</script>

<style lang="less" scoped></style>
