<template>
  <div>
    <!-- 详情信息 -->
    <DetailsInformation ref="detailsInformation"></DetailsInformation>

    <a-divider />
    <!-- 产品树 -->
    <InformationProductTree
      ref="informationProductTree"
      :orderId="orderId"
      :productTabs="productTabs"
      :chargeListMap="chargeListMap"
      :addressListMap="addressListMap"
      @handleTabChange="handleTabChange"
    ></InformationProductTree>
    <a-divider dashed />
    <!-- 帐户信息 -->
    <AccountInfo :dataResource="accountInfoData"></AccountInfo>
    <a-divider dashed />
    <!-- HKT 联系人 -->
    <HktContactList ref="hktContactList" isDetail :dataResource="hktContactList"></HktContactList>
    <a-divider dashed />
    <!-- 客户联系人 -->
    <CustomerContactList isDetail :dataResource="customerContactList"></CustomerContactList>
    <a-divider dashed />
    <!-- 开通信息 -->
    <FulfillmentInfo :dataResource="fulfillmentInfo"></FulfillmentInfo>
  </div>
</template>

<script>
  import DetailsInformation from './detailsInformation';
  import InformationProductTree from './informationProductTree';
  import HktContactList from './hktContactList';
  import CustomerContactList from './customerContactList';
  import AccountInfo from './accountInfo';
  import FulfillmentInfo from './fulfillmentInfo';
  export default {
    name: 'AfInformation',
    components: {
      DetailsInformation,
      InformationProductTree,
      AccountInfo,
      HktContactList,
      CustomerContactList,
      FulfillmentInfo,
    },
    props: {
      orderId: {
        type: String,
        default: '',
      },
      basicData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        currentTabId: '',
        productTabs: [],
        chargeListMap: {},
        addressListMap: {},
        accountListMap: {},
        hktContactList: [], // hkt联系人列表
        customerContactList: [],
        fulfillmentMap: {},
      };
    },
    computed: {
      accountInfoData() {
        if (!this.accountListMap || !this.currentTabId || !this.accountListMap[this.currentTabId]) {
          return [];
        }
        return this.accountListMap[this.currentTabId];
      },
      fulfillmentInfo() {
        if (!this.fulfillmentMap || !this.currentTabId || !this.fulfillmentMap[this.currentTabId]) {
          return {};
        }
        return this.fulfillmentMap[this.currentTabId];
      },
    },
    watch: {
      basicData: {
        handler(val) {
          console.log(val, 'watch - val');
          this.initData();
        },
        immediate: true,
        deep: true,
      },
    },
    methods: {
      async initData() {
        if (!this.basicData || !this.basicData.orderInfo) {
          return false;
        }
        const {
          orderInfo,
          cusInfo,
          salesAsmInfo,
          productTabs,
          addressListMap,
          chargeListMap,
          accountListMap,
          hktConcatList,
          customerConcatList,
          fulfillmentMap,
        } = this.basicData;
        console.log(this.basicData, 'basicData - information');
        this.$nextTick(() => {
          // 赋值 - 基础信息
          this.$refs.detailsInformation.init({
            orderInfo,
            cusInfo,
            salesAsmInfo,
          });
          this.$store.dispatch('quotation/setCustId', cusInfo.CUST_ID);
          // 处理产品的Tabs
          this.productTabs = productTabs;
          this.currentTabId =
            // 处理产品树所需要的地址信息
            this.addressListMap = addressListMap;
          // 处理产品树所需要的费用列表
          this.chargeListMap = chargeListMap;
          // 缓存 - 产品账户信息
          this.accountListMap = accountListMap;
          // HKT & CUSTOMER
          this.hktContactList = hktConcatList;
          this.customerContactList = customerConcatList;
          // 缓存 - 履约信息
          this.fulfillmentMap = fulfillmentMap;

          this.$nextTick(() => {
            // 初始化 - acou
            this.handleTabChange({ currentTab: productTabs[0] });
          });
        });
      },
      handleTabChange({ currentTab = {} }) {
        if (!currentTab || !currentTab.id) {
          this.currentTabId = '';
        }
        this.currentTabId = currentTab.id.split('_').length > 1 ? currentTab.id.split('_')[0] : '';
      },
    },
  };
</script>

<style lang="less" scoped></style>
