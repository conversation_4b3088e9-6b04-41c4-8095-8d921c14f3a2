<template>
  <div class="details-product-tree">
    <!-- 标题 -->
    <div class="secondLevel-header-title">
      {{ $t('comp.ProductSelection') }}
    </div>

    <!-- 产品 - Tab栏 -->
    <ProductSelectTab
      v-if="productList.length != 0"
      ref="productSelectTab"
      :list="productTabs"
      @change="handleTabChange"
    />

    <!-- 产品 - 地址栏 -->
    <ProductTabAddress
      v-if="productList.length != 0"
      :addressInfo="addressInfo"
    ></ProductTabAddress>

    <!-- 编辑按钮 -->
    <a-button type="primary" @click="gotEditAfInfo" class="search-button btn-position">
      {{ $t('orderCapture.EditAFInfo') }}
    </a-button>

    <!-- 产品树 -->
    <CollapseProductTree
      v-if="productList.length != 0"
      ref="collapseProductTree"
      :treeList="currentShowTreeMap"
    ></CollapseProductTree>

    <!-- 汇总 -->
    <ProductChargeTotal
      v-if="productList.length != 0"
      :productCount="productCount"
    ></ProductChargeTotal>
  </div>
</template>

<script lang="jsx">
  import { mapState } from 'vuex';
  import { debounce } from '@/utils/utils';
  import { qryAmendProduct } from '@/api/quotation';
  import { getElements } from '@/api/customerVerify';

  import ProductTabAddress from './productTabAddress.vue';
  import ProductSelectTab from '@/views/components/productSelectTab/index.vue';
  import CollapseProductTree from '@/views/components/collapseProductTree/index.vue';
  import ProductChargeTotal from '@/views/components/productChargeTotal/index.vue';
  import { FIELD_SIGN } from '@/views/components/productChargeTotal/config.js';
  export default {
    name: 'InformationProductTree',
    props: {
      orderId: {
        type: String,
        default: '',
      },
      productTabs: {
        type: Array,
        default: () => [],
      },
      // 全部产品的费用
      chargeListMap: {
        type: Object,
        default: () => {},
      },
      // 全部产品的地址
      addressListMap: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      ProductSelectTab,
      CollapseProductTree,
      ProductChargeTotal,
      ProductTabAddress,
    },
    data() {
      return {
        cacheProductTreeData: {},
        currentShowTreeMap: {},
        productList: [],
        productCount: {},
        addressInfo: {
          SB_NO: '',
          SB_ADDRESS: '',
          MODIFY_TAG: '0',
        },
      };
    },

    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
    },
    mounted() {
      // 使用防抖，确保只在最后一次变更后 500ms 触发
      const debouncedHandler = debounce(newVal => {
        console.log(newVal, '是否为最后的渲染');
        this.$nextTick(() => {
          this.initTreeAndTabData();
        });
      }, 500);
      this.unHandlerWatch = this.$watch(
        () => this.productList, // 可以监听计算属性或函数返回值
        debouncedHandler,
        { deep: true },
      );

      this.init();
    },
    methods: {
      init(orderId = '') {
        const params = {
          ORDER_ID: orderId || this.orderId,
        };
        qryAmendProduct(params).then(res => {
          if (!res?.DATA?.length) {
            return false;
          }
          this.getProductList(res);
        });
      },
      // 格式化 - collapseTree显示的数据
      initTreeAndTabData() {
        const currentTree = this.productList;
        if (currentTree.length === 0) {
          return false;
        }
        const treeObj = {};
        // 得出 tab 的数据
        for (let i = 0; i < currentTree.length; i++) {
          const ele = currentTree[i];
          const ele_main_product = ele.mainProduct[0];
          const treeKey = `${ele.SERIAL_NUMBER}_${ele_main_product.key}`;
          // 内层 - 展示数据
          // #TODO - 后期优化，不做重复格式
          const treeKeyValue = {
            mainProduct: [],
            addition: [],
            equipment: [],
            premium: [],
          };
          // 赋值 - 四个collapse数据
          treeKeyValue.mainProduct = ele.mainProduct;
          treeKeyValue.addition = ele.additionalProduct;
          treeKeyValue.equipment = ele.equipmentProduct;
          treeKeyValue.premium = ele.premiumProduct;
          treeObj[treeKey] = treeKeyValue;
        }
        this.cacheProductTreeData = JSON.parse(JSON.stringify(treeObj));
        // 默认Tabs第一个
        this.$nextTick(() => {
          this.handleTabChange(this.productTabs[0]);
        });
      },

      /**
       * 获取产品列表
       * @param {Object} res - 查询修改产品接口返回的数据
       */
      getProductList(res) {
        let orderListTemp = [];
        res.DATA.forEach(orderItem => {
          console.log(orderItem);
          // 获取产品键列表并排序
          let productKeyList = Object.keys(orderItem).sort();
          // 遍历产品键
          productKeyList.forEach(productKey => {
            let mainProductListTemp = [];
            let additionalProductListTemp = [];
            let equipmentProductListTemp = [];
            let premiumProductListTemp = [];
            let mainProductKey = '';
            let orderItemTemp = orderItem[productKey];
            // 遍历产品项
            orderItemTemp.forEach(async productItem => {
              // 处理产品下的包数据
              let packageListTemp = this.getPackageList(
                productItem.PRODUCT_ID,
                productItem.PACKAGE_LIST,
              );
              let formattingData = {
                type: 'Product',
                key: String(productItem.PRODUCT_ID),
                PRODUCT_TYPE_CODE: productItem.PRODUCT_TYPE_CODE,
                PRODUCT_DESC: productItem?.PRODUCT_DESC || '',
                SERIAL_NUMBER: String(productKey),
                children: packageListTemp,
                NAME: productItem.PRODUCT_NAME,
                PROD_ITEM_ID: productItem.PROD_ITEM_ID,
                ORDER_LINE_ID: productItem.ORDER_LINE_ID,
              };
              // 判断产品类型 00主产品 01附加产品
              if (productItem.PRODUCT_MODE === '00') {
                mainProductListTemp.push(formattingData);
                mainProductKey = productKey;
              } else {
                // 判断【PRODUCT_TYPE_CODE】 - 附件产品 、设备（300010）、礼品（300013）
                if (productItem.PRODUCT_TYPE_CODE == '300010') {
                  equipmentProductListTemp.push(formattingData);
                } else if (productItem.PRODUCT_TYPE_CODE == '300013') {
                  premiumProductListTemp.push(formattingData);
                } else {
                  additionalProductListTemp.push(formattingData);
                }
              }
            });
            // 将处理后的产品数据添加到临时订单列表
            orderListTemp.push({
              mainProductKey,
              SERIAL_NUMBER: productKey,
              mainProduct: mainProductListTemp,
              additionalProduct: additionalProductListTemp,
              equipmentProduct: equipmentProductListTemp,
              premiumProduct: premiumProductListTemp,
            });
            console.log(orderListTemp, ' orderListTemp');
          });
        });
        // 更新订单列表
        this.productList = orderListTemp?.filter(item => {
          return item.mainProduct.length > 0;
        });
        // #TODO： 处理请求Elements数据导致不更新问题，但这种方式不稳定，后期优化
        // setTimeout(() => {
        //   this.productList = JSON.parse(JSON.stringify(this.productList));
        // }, 500);
      },

      /**
       * 处理包数据
       * @param {string} PRODUCT_ID - 产品 ID
       * @param {Array} list - 包数据列表
       * @returns {Array} 处理后的包数据列表
       */
      getPackageList(PRODUCT_ID, list) {
        if (!list?.length) {
          return [];
        }
        let packageListTemp = [];
        let allRecords = [];
        list.forEach((item, index) => {
          let children = [];
          let serviceRecords = item.SERVICE_LIST || [];
          let discntRecords = item.DISCNT_LIST || [];
          // 合并服务和资费记录
          allRecords = allRecords.concat(serviceRecords, discntRecords);
          // 处理服务记录
          children = this.getServiceOrDiscntList(
            PRODUCT_ID + '-' + item.PACKAGE_ID,
            index,
            children,
            serviceRecords,
            allRecords,
          );
          // 处理资费记录
          children = this.getServiceOrDiscntList(
            PRODUCT_ID + '-' + item.PACKAGE_ID,
            index,
            children,
            discntRecords,
            allRecords,
          );
          packageListTemp.push({
            type: 'Package',
            key: PRODUCT_ID + '-' + item.PACKAGE_ID,
            children,
            NAME: item.PACKAGE_NAME,
          });
        });
        // 统一处理所有资费
        this.processAllRecords(allRecords);
        return packageListTemp;
      },

      /**
       * 处理服务和资费数据
       * @param {string} upKey - 上级键
       * @param {number} upIndex - 上级索引
       * @param {Array} childrenSum - 子项汇总数组
       * @param {Array} list - 服务或资费数据列表
       * @param {Array} allRecords - 所有需要处理的记录数组
       * @returns {Array} 处理后的子项汇总数组
       */
      getServiceOrDiscntList(upKey, upIndex, childrenSum, list, allRecords) {
        if (!list?.length) {
          return childrenSum;
        }
        list.forEach((item, index) => {
          // 判断是服务还是资费
          let type = item?.SERVICE_NAME ? 'Vas' : 'Pricing';
          // 将处理后的服务或资费数据添加到子项汇总数组
          // #TODO：会出现重复的key值，目前加入ELE  MENT_TYPE_CODE做区分
          childrenSum.push({
            ...item,
            type,
            key:
              upKey +
              '-' +
              String(item.ELEMENT_ITEM_ID) +
              String(index) +
              String(item.ELEMENT_TYPE_CODE),
            interfaceElementList: [],
            NAME: item?.SERVICE_NAME ? item.SERVICE_NAME : item.DISCNT_NAME,
            HAS_ATTR: 0,
          });
        });
        return childrenSum;
      },

      /**
       * 统一处理所有服务和资费记录
       * @param {Array} records - 所有需要处理的记录数组
       */
      async processAllRecords(records) {
        this.spinning = true;
        const data = records.map(record => {
          // 根据记录类型生成键
          return record.ELEMENT_TYPE_CODE === 'D'
            ? `DISCNT_${record.DISCNT_CODE}`
            : `SERVICE_${record.SERVICE_ID}`;
        });
        const elementObjs = await this.getElementsApi(data);
        this.spinning = false;
        records.forEach((record, index) => {
          const key =
            record.ELEMENT_TYPE_CODE === 'D'
              ? `DISCNT_${record.DISCNT_CODE}`
              : `SERVICE_${record.SERVICE_ID}`;
          const elementObj = elementObjs[key];
          const dataList = record.DISCNT_ITEM_LIST || record.SERVICE_ITEM_LIST || [];
          // 处理接口元素列表
          const interfaceElementList = this.getInterfaceElementList(elementObj, dataList);
          this.productList.forEach(orderItem => {
            // 遍历主产品列表
            this.matchRecord(orderItem.mainProduct, record, interfaceElementList);
            // 遍历附加产品列表
            this.matchRecord(orderItem.additionalProduct, record, interfaceElementList);
            // 遍历设备列表
            this.matchRecord(orderItem.equipmentProduct, record, interfaceElementList);
            // 遍历礼品列表
            this.matchRecord(orderItem.premiumProduct, record, interfaceElementList);
            // orderItem.mainProduct.forEach(item => {
            //   // 遍历主产品下的包数据
            //   item.children.forEach(packageItem => {
            //     // 遍历包数据下的服务或资费
            //     packageItem.children.forEach(serviceOrDiscnt => {
            //       // 匹配记录
            //       if (serviceOrDiscnt.ELEMENT_ITEM_ID === record.ELEMENT_ITEM_ID) {
            //         console.log(serviceOrDiscnt, record, 'mainProduct - interfaceElementList');
            //         // 更新接口元素列表
            //         serviceOrDiscnt.interfaceElementList = interfaceElementList;
            //         // 更新是否有属性标识
            //         serviceOrDiscnt.HAS_ATTR = interfaceElementList.length ? 1 : 0;
            //       }
            //     });
            //   });
            // });
            // // 遍历附加产品列表
            // orderItem.additionalProduct.forEach(item => {
            //   // 遍历附加产品下的包数据
            //   item.children.forEach(packageItem => {
            //     // 遍历包数据下的服务或资费
            //     packageItem.children.forEach(serviceOrDiscnt => {
            //       // 匹配记录
            //       if (serviceOrDiscnt.ELEMENT_ITEM_ID === record.ELEMENT_ITEM_ID) {
            //         console.log(interfaceElementList, 'additionalProduct - interfaceElementList');
            //         // 更新接口元素列表
            //         serviceOrDiscnt.interfaceElementList = interfaceElementList;
            //         // 更新是否有属性标识
            //         serviceOrDiscnt.HAS_ATTR = interfaceElementList.length ? 1 : 0;
            //       }
            //     });
            //   });
            // });
            // // 遍历设备列表
            // orderItem.equipmentProduct.forEach(item => {
            //   // 遍历附加产品下的包数据
            //   item.children.forEach(packageItem => {
            //     // 遍历包数据下的服务或资费
            //     packageItem.children.forEach(serviceOrDiscnt => {
            //       // 匹配记录
            //       if (serviceOrDiscnt.ELEMENT_ITEM_ID === record.ELEMENT_ITEM_ID) {
            //         console.log(interfaceElementList, 'equipmentProduct - interfaceElementList');
            //         // 更新接口元素列表
            //         serviceOrDiscnt.interfaceElementList = interfaceElementList;
            //         // 更新是否有属性标识
            //         serviceOrDiscnt.HAS_ATTR = interfaceElementList.length ? 1 : 0;
            //       }
            //     });
            //   });
            // });
            // // 遍历礼品列表
            // orderItem.premiumProduct.forEach(item => {
            //   // 遍历附加产品下的包数据
            //   item.children.forEach(packageItem => {
            //     // 遍历包数据下的服务或资费
            //     packageItem.children.forEach(serviceOrDiscnt => {
            //       // 匹配记录
            //       if (serviceOrDiscnt.ELEMENT_ITEM_ID === record.ELEMENT_ITEM_ID) {
            //         console.log(interfaceElementList, 'premiumProduct - interfaceElementList');
            //         // 更新接口元素列表
            //         serviceOrDiscnt.interfaceElementList = interfaceElementList;
            //         // 更新是否有属性标识
            //         serviceOrDiscnt.HAS_ATTR = interfaceElementList.length ? 1 : 0;
            //       }
            //     });
            //   });
            // });
          });
        });
      },

      /**
       * 获取元素属性接口
       * @param {Array} record - 请求数据数组
       * @returns {Object} 元素属性对象
       */
      async getElementsApi(record) {
        const params = {
          areaCode: '212',
          cityId: this.userInfo.CITY_CODE,
          deptId: this.userInfo.DEPART_ID,
          epachyId: this.userInfo.EPARCHY_CODE,
          loginEpachyId: '0010',
          loginProvinceId: '11',
          pageData: true,
          provinceId: this.userInfo.PROVINCE_CODE,
          staffId: this.userInfo.STAFF_ID,
          tradeEpachyId: '0010',
        };
        const res = await getElements(record, params);
        return res.DATA[0];
      },

      /**
       * 处理资费服务在表格和详情弹窗中显示默认值
       * @param {Object} elementObj - 元素属性对象
       * @param {Array} dataList - 数据列表
       * @returns {Array} 处理后的接口元素列表
       */
      getInterfaceElementList(elementObj, dataList) {
        let { interfaceElement, selectInitialData } = elementObj;
        return interfaceElement.map(item => {
          dataList.forEach(x => {
            // 处理折扣费用
            if (x.ATTR_CODE === 'rebate_fee' && x.ATTR_VALUE && x.ATTR_VALUE !== '0') {
              this.$set(x, 'ATTR_VALUE', String(Math.abs(x.ATTR_VALUE)));
            }
            // 匹配属性代码
            if (item.elementCode === x.ATTR_CODE) {
              this.$set(item, item.elementCode, x.ATTR_VALUE);
            }
            // 处理合同周期
            if (x.ATTR_CODE === 'contract_period' && selectInitialData) {
              this.$set(item, 'selectInitialDataList', selectInitialData[item.elementCode]);
            }
          });
          return item;
        });
      },
      // 业务 - 匹配记录
      matchRecord(arr, record, interfaceElementList) {
        const matchKey = record.ELEMENT_TYPE_CODE === 'D' ? 'DISCNT_CODE' : 'SERVICE_ID';

        arr.forEach(item => {
          // 遍历主产品下的包数据
          item.children.forEach(packageItem => {
            // 遍历包数据下的服务或资费
            packageItem.children.forEach(serviceOrDiscnt => {
              // old - if (serviceOrDiscnt.ELEMENT_ITEM_ID === record.ELEMENT_ITEM_ID) {
              // 匹配记录
              if (serviceOrDiscnt[matchKey] === record[[matchKey]]) {
                // 更新接口元素列表
                serviceOrDiscnt.interfaceElementList = interfaceElementList;
                // 更新是否有属性标识
                serviceOrDiscnt.HAS_ATTR = interfaceElementList.length ? 1 : 0;
              }
            });
          });
        });
      },
      handleTabChange(item) {
        console.log(item, this.cacheProductTreeData);
        this.currentShowTreeMap = JSON.parse(JSON.stringify(this.cacheProductTreeData[item.id]));
        this.currentShowTreeMap.itemId = item.id;
        console.log('item.id', item.id);
        this.$nextTick(() => {
          this.$refs.collapseProductTree.setCurrentProduct(item.id);
        });
        // 更新 - 费用统计
        const ORDER_LINE_ID = item.id.split('_').length > 1 ? item.id.split('_')[0] : item.id;
        // 获取对应产品的费用列表
        const chargeList = this.chargeListMap[ORDER_LINE_ID];
        this.productCount = {
          [FIELD_SIGN.FIELD_POINTS]: chargeList.redeemablePoints,
          [FIELD_SIGN.FIELD_LINE_NUMBER]: chargeList.lineInstallNumber,
          [FIELD_SIGN.FIELD_MRC_TOTAL]: chargeList.totalMRCForAllLine,
          [FIELD_SIGN.FIELD_OTC_TOTAL]: chargeList.totalOTCForAllLine,
          [FIELD_SIGN.FIELD_MRC_PERIOD_TOTAL]: chargeList.totalMRCForAllLinePeriod,
        };
        // 获取对应产品的地址
        const addressInfo = this.addressListMap[ORDER_LINE_ID];
        this.addressInfo.SB_NO = addressInfo.SB_NO;
        this.addressInfo.SB_ADDRESS = addressInfo.SB_ADDRESS;
        this.addressInfo.MODIFY_TAG = addressInfo.MODIFY_TAG;

        // 回调给父组件
        this.$emit('handleTabChange', { currentTab: item });
      },
      // 跳转 - Edit Af Info 页面
      gotEditAfInfo() {
        const productSelection = {
          addressInfo: this.addressInfo,
          currentShowTreeMap: this.currentShowTreeMap,
          productCount: this.productCount,
          productInfo: {
            mainProductTreeSelectedList: this.currentShowTreeMap.mainProduct,
            additionalProductTreeSelectedList: [
              ...(this.currentShowTreeMap.addition || []),
              ...(this.currentShowTreeMap.equipment || []),
              ...(this.currentShowTreeMap.premium || []),
            ],
          },
        };
        this.$store.dispatch('quotation/setProductSelection', productSelection);
        this.$router.push({
          name: 'afEditAfInfo',
          query: {},
        });
      },
    },
    beforeDestroy() {
      if (this.unHandlerWatch) {
        this.unHandlerWatch(); // 手动停止监听
      }
    },
  };
</script>

<style lang="less" scoped>
  .details-product-tree {
    position: relative;
  }
  .btn-position {
    width: 100px;
    position: absolute;
    right: 0;
    top: 22px;
  }
  .product-count {
    background: #e5f0ff;
    height: 40px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow: auto;
    padding: 0 20px;
    margin-top: 11px;
    &-title {
      font-size: 14px;
      color: #373d41;
      line-height: 25px;
      font-weight: 500;
      padding-right: 3px;
    }
    &-content {
      font-size: 14px;
      color: #373d41;
      line-height: 20px;
      font-weight: 400;
      padding-right: 60px;
      position: relative;
      &.tag-red {
        color: #e60017;
      }
      &.tag-f18 {
        font-size: 18px;
      }
      &.has-line {
        &::after {
          content: '';
          width: 1px;
          height: 20px;
          background: #9e9e9e;
          position: absolute;
          right: 30px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
</style>
