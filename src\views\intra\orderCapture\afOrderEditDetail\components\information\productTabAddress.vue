<template>
  <div class="product-tab-address">
    <div class="part-item">
      <span class="part-item-title">{{ $t('comp.InstallationAddress') }} :</span>
      <span class="part-item-content">{{ addressInfo.SB_ADDRESS }}</span>
    </div>
    <div class="part-item">
      <span class="part-item-title">{{ $t('orderCapture.SBNo') }} :</span>
      <span class="part-item-content">{{ addressInfo.SB_NO }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ProductTabAddress',
    props: {
      addressInfo: {
        type: Object,
        default: () => {},
      },
    },
  };
</script>

<style lang="less" scoped>
  .product-tab-address {
    border-top: 1px dashed #d9d9d9;
    border-bottom: 1px dashed #d9d9d9;
    height: 32px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin: 10px 0;
    .part-item {
      font-size: 14px;
      color: #373d41;
      line-height: 20px;
      font-weight: 400;
      flex: 0.5;
      &-title {
        font-weight: 500;
      }
      &-content {
        padding-left: 2px;
      }
    }
  }
</style>
