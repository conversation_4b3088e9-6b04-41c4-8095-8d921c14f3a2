import that from '@/main.js';

/**
 * 表格列的配置
 * TABLE - COLUMN - CONFIG
 */
// account Info
export const accountTableColumns = {
  columns: [
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT ID',
      key: 'ACCOUNT ID',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA',
      key: 'BILL_MEDIA',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD',
      key: 'PAYMENT_METHOD',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PROD_FAMILY',
      key: 'PROD_FAMILY',
    },
    {
      title: that.$t('orderSummary.chargeCategory'),
      dataIndex: 'CHARGE_CATEGORY',
      key: 'CHARGE_CATEGORY',
    },
  ],
  accountColumns: [
    {
      title: that.$t('accountSetting.serviceNo'),
      dataIndex: 'SERIAL_NUMBER',
      key: 'SERIAL_NUMBER',
    },
    {
      title: that.$t('accountSetting.accountName'),
      dataIndex: 'ACCOUNT_NAME',
      key: 'ACCOUNT_NAME',
    },
    {
      title: that.$t('accountSetting.billingAccountNo'),
      dataIndex: 'ACCOUNT_ID',
      key: 'ACCOUNT_ID',
    },
    {
      title: that.$t('accountSetting.billDay'),
      dataIndex: 'BILL_DAY',
      key: 'BILL_DAY',
    },
    {
      title: that.$t('accountSetting.billMedia'),
      dataIndex: 'BILL_MEDIA',
      key: 'BILL_MEDIA',
    },
    {
      title: that.$t('accountSetting.paymentMethod'),
      dataIndex: 'PAYMENT_METHOD',
      key: 'PAYMENT_METHOD',
    },
    {
      title: that.$t('accountSetting.prodFamily'),
      dataIndex: 'PRODUCE_FAMILY',
      key: 'PRODUCE_FAMILY',
    },
    {
      title: that.$t('orderSummary.chargeCategory'),
      dataIndex: 'CHARGE_CATEGORY',
      key: 'CHARGE_CATEGORY',
    },
  ],
};

// HKT Contact
export const hktTableColumns = [
  {
    title: that.$t('orderSummary.seq'),
    dataIndex: 'id',
    key: 'id',
    showFn: () => {
      return true;
    },
    width: 120,
  },
  {
    title: that.$t('orderSummary.type'),
    scopedSlots: { customRender: 'PARTICIPANTS_TYPE' },
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('type') : true;
    },
    width: 120,
  },
  {
    title: that.$t('fulfillmentInfo.name'),
    dataIndex: 'PARTICIPANT_NAME',
    key: 'PARTICIPANT_NAME',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('name') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.salesCode'),
    dataIndex: 'SALES_CODE',
    key: 'SALES_CODE',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('salesCode') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.agentCode'),
    dataIndex: 'AGENT_CODE',
    key: 'AGENT_CODE',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('agentCode') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.orderSalesType'),
    scopedSlots: { customRender: 'ORDER_SALES_TYPE' },
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('orderSalesType') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.contactPhone'),
    dataIndex: 'PARTICIPANT_LANDLINE_PHONE',
    key: 'PARTICIPANT_LANDLINE_PHONE',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('contactPhone') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.mobile'),
    dataIndex: 'PARTICIPANT_MOBILE',
    key: 'PARTICIPANT_MOBILE',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('mobile') : true;
    },
    width: 150,
  },
  {
    title: that.$t('fulfillmentInfo.email'),
    dataIndex: 'PARTICIPANT_EMAIL',
    key: 'PARTICIPANT_EMAIL',
    showFn: (isDetail, excludeElementsList) => {
      return excludeElementsList ? !excludeElementsList.includes('email') : true;
    },
    width: 150,
  },
];

// Customer Contact
export const customerContactTableColumns = [
  {
    title: that.$t('orderSummary.seq'),
    dataIndex: 'id',
    key: 'id',
    showFn: isDetail => {
      return !isDetail;
    },
  },
  {
    title: that.$t('orderSummary.type'),
    scopedSlots: { customRender: 'PARTICIPANTS_TYPE' },
    showFn: () => {
      return true;
    },
  },
  {
    title: that.$t('orderSummary.name'),
    dataIndex: 'PARTICIPANT_NAME',
    key: 'PARTICIPANT_NAME',
    showFn: () => {
      return true;
    },
  },
  {
    title: that.$t('orderSummary.contactPhone'),
    dataIndex: 'PARTICIPANT_LANDLINE_PHONE',
    key: 'PARTICIPANT_LANDLINE_PHONE',
    showFn: () => {
      return true;
    },
  },
  {
    title: that.$t('orderSummary.mobile'),
    dataIndex: 'PARTICIPANT_MOBILE',
    key: 'PARTICIPANT_MOBILE',
    showFn: () => {
      return true;
    },
  },
  {
    title: that.$t('orderSummary.email'),
    dataIndex: 'PARTICIPANT_EMAIL',
    key: 'PARTICIPANT_EMAIL',
    showFn: () => {
      return true;
    },
  },
];
