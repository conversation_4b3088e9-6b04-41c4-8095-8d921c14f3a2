import that from '@/main.js';
import moment from 'moment';
export default {
  columns: [
    {
      title: that.$t('orderCapture.afNo'),
      dataIndex: 'AF_ORDER_ID',
      key: 'AF_ORDER_ID',
      width: 110,
    },
    {
      title: that.$t('orderCapture.OrderNo'),
      dataIndex: 'EXT_ORDER_ID',
      key: 'EXT_ORDER_ID',
      width: 110,
    },
    {
      title: that.$t('common.productName'),
      dataIndex: 'MAIN_PRODUCT_NAME',
      key: 'MAIN_PRODUCT_NAME',
      width: 120,
    },
    {
      title: that.$t('comp.InstallationAddress'),
      dataIndex: 'INSTALL_ADDRESS',
      key: 'INSTALL_ADDRESS',
      width: 200,
    },
    {
      title: that.$t('orderCapture.SubmitOrderUser'),
      dataIndex: 'SUBMIT_ORDER_USER',
      key: 'SUBMIT_ORDER_USER',
      width: 134,
    },
    {
      title: that.$t('orderCapture.SubmitOrderTime'),
      dataIndex: 'SUBMIT_ORDER_TIME',
      key: 'SUBMIT_ORDER_TIME',
      width: 140,
      customRender: (text, record) => {
        return moment(text).format('MM/DD/YYYY HH:mm:ss');
      },
    },
    {
      title: that.$t('orderCapture.OrderStatus'),
      dataIndex: 'ORDER_STATUS',
      key: 'ORDER_STATUS',
      width: 110,
    },
  ],
};
