<template>
  <div>
    <!-- 主列表 -->
    <a-table
      :columns="columns"
      :data-source="dataList"
      :loading="loading"
      :rowKey="(record, index) => `${index}`"
      :scroll="{ x: 1800 }"
      :pagination="false"
    >
    </a-table>
  </div>
</template>

<script>
  import config from './config';
  import { queryAfOrderInformation } from '@/api/orderCapture';
  export default {
    name: 'OrderList',
    props: {
      orderId: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        columns: config.columns,
        loading: false,
        dataList: [],
      };
    },
    activated() {
      this.getOrderList();
    },
    methods: {
      // 获取订单列表
      getOrderList() {
        this.loading = true;
        const params = {
          ORDER_ID: this.orderId,
        };
        queryAfOrderInformation({
          ...params,
        })
          .then(res => {
            this.dataList = res.DATA || [];
          })
          .catch(error => {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
          });
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .ant-table-tbody > tr > td:last-child.action-column {
    text-align: left !important;
  }
</style>
