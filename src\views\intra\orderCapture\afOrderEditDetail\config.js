import that from '@/main';
import { queryOrderDetail } from '@/api/quotation';
import moment from 'moment';

export const TAB_TAGS = {
  INFORMATION: 'information',
  ENDORSEMENT: 'endorsement',
  ORDER_INFORMATION: 'orderInformation',
};

export default {
  afTabList: [
    {
      value: '1',
      title: that.$t('orderCapture.AfInformation'),
      component: 'AfInformation',
      comp: TAB_TAGS.INFORMATION,
    },
    {
      value: '2',
      title: that.$t('orderCapture.AfEndorsement'),
      component: 'AfEndorsement',
      comp: TAB_TAGS.ENDORSEMENT,
    },
    {
      value: '3',
      title: that.$t('orderCapture.AfOrderInformation'),
      component: 'AfOrderInformation',
      comp: TAB_TAGS.ORDER_INFORMATION,
    },
  ],
};

// 获取订单详情
export const getOrderDetail = async orderId => {
  const res = await queryOrderDetail({ ORDER_ID: orderId });
  // 存储详情数据用于页面回显
  const orderDetail = res.DATA && res.DATA[0];
  // 处理回显的页面数据
  return processOrderDetailData(orderDetail);
};

// 处理订单详情数据
function processOrderDetailData(orderDetail) {
  const { CUST_INFO, ORDER_SALESMAN, ORDER_REMARK_ITEM, PARTICIPANT_INFO, ORDER_REMARKS } =
    orderDetail;
  // 订单信息
  const orderInfo = {
    ORDER_ID: orderDetail.ORDER_ID,
    CREATE_DATA: moment(orderDetail.CREATE_DATE).format('DD/MM/YYYY'),
    STATUS: orderDetail.ORDER_STATE,
    STATUS_NAME: orderDetail.ORDER_STATE_NAME,
  };
  // 客户信息
  const cusInfo = destructureArray(CUST_INFO, ['CUST_ID', 'CUST_NAME', 'LOB', 'MARKET_SEGMENT']);
  // sale & asm 信息
  const salesAsmInfo = destructureArray(
    ORDER_SALESMAN,
    [
      'SALES_TYPE',
      'SALES_CODE',
      'SALES_SEGMENT',
      'SALES_TEAM',
      'SALES_SM',
      'STAFF_NAME',
      'CONTACT_NO',
      'EMAIL',
    ],
    { forceArray: true },
  );
  // hkt contact & customer Concat
  // 0:Customer，1:HKT
  const CONTACT_TYPE_KEYS = {
    'customer': '0',
    'hkt': '1',
  };
  const contactListMap = splitArrayByValues(
    PARTICIPANT_INFO,
    'CONTACT_TYPE',
    Object.values(CONTACT_TYPE_KEYS),
  );
  const hktConcatList = contactListMap[CONTACT_TYPE_KEYS['hkt']];
  const customerConcatList = contactListMap[CONTACT_TYPE_KEYS['customer']];

  // 审批备注
  const remarkInfo = destructureArray(ORDER_REMARK_ITEM, ['ATTR_CODE', 'ATTR_VALUE'], {
    forceArray: true,
  });

  const amendOrderLineInfo = orderDetail.AMEND_ORDER_LINE_INFO;
  // 处理产品的Tabs
  const productTabs = formatProductTabs(amendOrderLineInfo);
  // 处理产品树所需要的地址信息
  const addressListMap = formatOrderLineMap(
    amendOrderLineInfo,
    'ORDER_INSTALL_ADDRESS',
    addressItem => ({
      SB_ADDRESS: addressItem.SB_ADDRESS,
      SB_NO: addressItem.SB_NO,
    }),
  );
  // 处理产品树所需要的费用列表
  const chargeListMap = formatOrderChargeList(amendOrderLineInfo);
  // 处理账户信息
  const accountListMap = formatOrderLineMap(
    amendOrderLineInfo,
    'PAYRELATION_INFO',
    payItem =>
      destructureArray(
        [payItem],
        [
          'ACCOUNT_ID',
          'ACCOUNT_NAME',
          'CHARGE_CATEGORY',
          'SERIAL_NUMBER',
          'START_DATE',
          'END_DATE',
          'Bill_DAY',
          'BILL_MEDIA',
          'PAYMENT_METHOD',
          'PROD_FAMILY',
        ],
      ),
    { forceArray: true },
  );
  // 履行信息
  const fulfillmentMap = formatFulfillmentInfo(amendOrderLineInfo, ORDER_REMARKS);

  // 计算出endorsement - totalContractRevenue
  let totalContractRevenue = 0;
  if (Object.keys(chargeListMap).length != 0) {
    for (const chargeKey in chargeListMap) {
      if (Object.prototype.hasOwnProperty.call(chargeListMap, chargeKey)) {
        const ele = chargeListMap[chargeKey];
        if (ele.totalMRCForAllLinePeriod && !isNaN(Number(ele.totalMRCForAllLinePeriod))) {
          totalContractRevenue += Number(ele.totalMRCForAllLinePeriod);
        }
      }
    }
  }
  return {
    [TAB_TAGS.INFORMATION]: {
      orderInfo,
      cusInfo,
      salesAsmInfo,
      hktConcatList,
      customerConcatList,
      productTabs,
      addressListMap,
      chargeListMap,
      accountListMap,
      fulfillmentMap,
    },
    [TAB_TAGS.ENDORSEMENT]: {
      remarkInfo,
      totalContractRevenue,
    },
  };
}

/**
 * 从数组中解构出指定的多个属性
 * @param {Array} array - 源数组
 * @param {Array} props - 需要解构的属性名数组
 * @param {Object} [options] - 配置选项
 * @param {boolean} [options.forceArray=false] - 强制返回数组形式
 * @returns {Array|Object} - 返回解构后的数据，单条数据时返回对象
 */
export const destructureArray = (array, props, options = {}) => {
  if (!Array.isArray(array)) {
    throw new TypeError('第一个参数必须是数组');
  }

  if (!Array.isArray(props)) {
    throw new TypeError('第二个参数必须是属性名数组');
  }

  const { forceArray = false } = options;

  // 处理空数组情况
  if (array.length === 0) {
    return forceArray ? [] : undefined;
  }

  // 解构数据
  const result = array.map(item => {
    const obj = {};
    props.forEach(prop => {
      obj[prop] = item[prop];
    });
    return obj;
  });

  // 根据条件和数据长度决定返回形式
  if (!forceArray && result.length === 1) {
    return result[0];
  }

  return result;
};

/**
 * 处理 - 产品树 - Tabs
 */
export const formatProductTabs = orderLineTemp => {
  let result = [];
  orderLineTemp.forEach((orderLineItem, i) => {
    const mainProductInfo = orderLineItem.PRODUCT_INFO?.find(x => x.PRODUCT_MODE === '00');
    if (mainProductInfo) {
      result.push({
        id: `${mainProductInfo.ORDER_LINE_ID}_${mainProductInfo.PRODUCT_ID}`,
        name: mainProductInfo.PRODUCT_NAME,
      });
    }
  });
  return result;
};

// 处理 - 产品下的SRD/APPOINTMENT_INFO/ORDER_LINE_ATTR_INFO
export const formatFulfillmentInfo = (orderLineTemp, orderRemarks = []) => {
  let result = {};
  const remarkInfos = orderRemarks?.[0]?.REMARK;

  orderLineTemp.forEach(orderLineItem => {
    const appointInfo = orderLineItem.APPOINTMENT_INFO?.[0];
    const orderLineInfo = getProductItemByKey(orderLineItem.ORDER_LINE_ATTR_INFO, [
      'Billing Customer Reference',
      'Project Code',
    ]);
    result[orderLineItem.ORDER_LINE_ID] = {
      SRD: orderLineItem.SRD,
      // APPOINTMENT_INFO - 只有一条
      APPOINTMENT_START_DATE: appointInfo?.APPOINTMENT_START_DATE,
      PRE_WIRING_START_DATE: appointInfo?.PRE_WIRING_START_DATE,
      PROJECT_CODE: orderLineInfo['Project Code'],
      BILLING_CUSTOMER_REFERENCEx: orderLineInfo['Billing Customer Reference'],
      ORDER_REMARK: remarkInfos,
    };
  });
  return result;
};
/**
 * 辅助函数 - 通用订单行数据处理器
 * @param {Array} orderLineTemp - 订单行数组
 * @param {String} subField - 需要处理的子字段名（如 'ORDER_INSTALL_ADDRESS'）
 * @param {Function} handler - 处理每个子项的回调 (item, orderLineItem) => any
 * @param {Object} [options] - 额外配置
 * @param {Boolean} [options.forceArray=false] - 是否强制每个 key 下为数组
 * @returns {Object}
 */
function formatOrderLineMap(orderLineTemp, subField, handler, options = {}) {
  const { forceArray = false } = options;
  const result = {};
  orderLineTemp.forEach(orderLineItem => {
    const subArr = orderLineItem[subField] || [];
    if (forceArray) {
      result[orderLineItem.ORDER_LINE_ID] = subArr.map(item => handler(item, orderLineItem));
    } else {
      // 兼容单对象或多对象
      subArr.forEach(item => {
        result[item.ORDER_LINE_ID || orderLineItem.ORDER_LINE_ID] = handler(item, orderLineItem);
      });
    }
  });
  return result;
}
/**
 * 处理 & 统计 - 数量和费用
 * @param {Object} orderDetailTemp - 包含订单详情的临时对象。
 * @returns {Object} - 各产品下的收费数据（MRC/OTC/REBATE）。
 */
export const formatOrderChargeList = orderLineTemp => {
  let orderLineAttTemp = {};
  // 从订单详情对象中解构出订单行信息
  orderLineTemp.forEach(orderLineItem => {
    orderLineAttTemp[orderLineItem.ORDER_LINE_ID] = {};
    // 遍历订单行中的每个产品项目
    // 初始化一个空数组，用于存储临时元素信息
    const details = getProductItemByKey(orderLineItem.ORDER_LINE_ATTR_INFO, [
      'lineInstallNumber',
      'totalMRCForAllLine',
      'totalOTCForAllLine',
      'totalMRCForAllLinePeriod',
      'redeemablePoints',
    ]);
    orderLineAttTemp[orderLineItem.ORDER_LINE_ID] = { ...details };
  });
  return orderLineAttTemp;
};
// 辅助函数 - 根据key返回订单详情中的PRODUCT_ITEM_INFO的对象
function getProductItemByKey(arr = [], keys) {
  if (!Array.isArray(arr)) {
    console.dir('警告：第一个参数必须是数组');
    return {};
  }
  let result = {};
  const filterArr = arr.filter(x => keys.includes(x.ATTR_CODE));
  for (let i = 0; i < filterArr.length; i++) {
    const ele = filterArr[i];
    result[ele.ATTR_CODE] = ele.ATTR_VALUE;
  }
  return result;
}

/**
 * 按属性值分组，只保留匹配的分组
 * @param {Array} arr - 源数组
 * @param {String} key - 属性名
 * @param {Array} values - 需要分组的属性值数组
 * @returns {Object} - { value1: [...], value2: [...] }
 */
export const splitArrayByValues = (arr, key, values) => {
  const result = {};
  const valueSet = new Set(values);
  for (const item of arr) {
    const v = item[key];
    if (valueSet.has(v)) {
      if (!result[v]) result[v] = [];
      result[v].push(item);
    }
  }
  return result;
};
