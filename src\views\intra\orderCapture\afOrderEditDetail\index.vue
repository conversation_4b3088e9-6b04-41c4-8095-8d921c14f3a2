<template>
  <div class="af-order-operate">
    <!-- RETURN栏 -->
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>
    <!-- 流程节点 -->
    <FlowSteps v-if="orderStatus && orderStatus != 'S8'" :orderStatus="orderStatus"></FlowSteps>
    <!-- 切换Tab -->
    <div class="tabList">
      <div
        :class="['item', currentSelectedTabItem.value == item.value ? 'active' : '']"
        v-for="item of tabList"
        :key="item.value"
        @click="selectedTabChange(item)"
      >
        {{ item.title }}
      </div>
    </div>
    <!-- 展示内容 -->
    <keep-alive>
      <component
        :key="currentSelectedTabItem.value"
        :is="currentSelectedTabItem.comp"
        :isEdit="isEdit"
        :orderId="orderId"
        :basicData="currentData"
      />
    </keep-alive>

    <!-- 时间线 -->
    <ActionTimeLine ref="actionTimeLine" :orderId="orderId"></ActionTimeLine>

    <!-- 底部按钮组 -->
    <FooterToolBar class="footer-tool-bar">
      <!-- #TODO: 交互说明：Sales权限按钮，当下权限有不可修改内容，待确定, reject 换 Cancel AF, submit 换 Re-assign -->
      <!-- 交互说明：ASM权限按钮 -->
      <a-button ghost type="primary" class="fixed-bottom-btn" @click="back">{{
        $t('common.buttonBack')
      }}</a-button>

      <a-button ghost type="danger" class="fixed-bottom-btn danger-type" @click="handleReject">{{
        $t('common.reject')
      }}</a-button>
      <a-button type="primary" class="fixed-bottom-btn bg-blue" @click="handleSubmit">{{
        $t('common.submit')
      }}</a-button>
    </FooterToolBar>
  </div>
</template>

<script>
  import config, { TAB_TAGS, getOrderDetail } from './config';

  import Endorsement from './components/endorsement/index';
  import Information from './components/information/index';
  import OrderInformation from './components/orderInformation/index';

  import PageHeader from '@/views/components/pageHeader/index.vue';
  import FlowSteps from '@/views/components/flowSteps/index.vue';
  import ActionTimeLine from '@/views/components/actionTimeLine';
  import FooterToolBar from '@/components/footerToolbar';
  export default {
    name: 'AfOrderEditDetail',
    components: {
      Endorsement,
      Information,
      OrderInformation,
      PageHeader,
      FlowSteps,
      ActionTimeLine,
      FooterToolBar,
    },
    props: {
      // 从哪个tab开始展示 四个tab
      // 1 第一个开始展示
      // 2 第二个tab开始展示，只显示三个tab
      // 4 第四个tab开始展示，即只显示第四个tab
      startFromWhichTabShow: {
        type: Number,
        default: 1,
      },
    },
    data() {
      return {
        currentSelectedTabItem: config.afTabList[this.startFromWhichTabShow - 1],
        tabList: [],
        currentData: {}, // 当前所需要的数据
        cacheDetails: {},
      };
    },
    computed: {
      // 上一页页面信息
      prePageInfo() {
        return this.$t('orderCapture.AfOperationTitle');
      },
      isEdit() {
        return this.$router.query?.status === 'edit';
      },
      orderId() {
        return this.$router.query?.orderId || '9925071200024270';
      },
      orderStatus() {
        return this.$router.query?.status || 'S0';
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        // 初始化TabList
        this.tabList = this.filterTab();

        // 加载初始数据
        this.cacheDetails = await getOrderDetail(this.orderId);
        // 显示对应的缓存数据
        this.$nextTick(() => {
          this.setCurrentData();
        });
      },
      // 过滤 - 后面不同产品 显示不同的Tab
      filterTab(type) {
        let result = [].concat(config.afTabList);
        if (type === 'IDD') {
          result = result.filter(x => x.value != '3');
        }
        return result;
      },
      // tab切换
      selectedTabChange(item) {
        // 切换 tab
        this.currentSelectedTabItem = item;
        // 显示对应的缓存数据
        this.$nextTick(() => {
          this.setCurrentData();
        });
      },
      // 根据切换的TAB - 赋值对应的所需的基础数据
      setCurrentData() {
        console.log(this.cacheDetails, 'cacheDetails');
        const curTab = this.currentSelectedTabItem;
        switch (curTab.comp) {
          case TAB_TAGS.INFORMATION:
            // 基础信息、sales&Asm、产品Tabs、产品分类Map【产品地址、费用、账户、履约】
            this.currentData = this.cacheDetails[TAB_TAGS.INFORMATION];
            break;
          case TAB_TAGS.ENDORSEMENT:
            // 审批备注
            this.currentData = this.cacheDetails[TAB_TAGS.ENDORSEMENT];
            break;
          default:
            this.currentData = {};
            break;
        }
      },
      back() {
        this.$router.customBack();
      },
      handleReject() {},
      handleSubmit() {},
    },
  };
</script>

<style lang="less" scoped>
  @foot-height: 80px;
  .footer-tool-bar {
    height: @foot-height;
    box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .af-order-operate {
    padding-bottom: @foot-height;
    .tabList {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
      .item {
        font-size: 14px;
        color: #333333;
        text-align: center;
        font-weight: 400;
        background: #e9e9e9;
        width: 140px;
        height: 36px;
        line-height: 36px;
        margin-right: 1px;
        cursor: pointer;
        &.active {
          border-top: 2px solid #01408e;
          background: #ffffff;
          color: #01408e;
        }
      }
    }
  }
</style>
