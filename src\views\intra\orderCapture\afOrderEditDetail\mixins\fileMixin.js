/**
 * 公用 file 混入
 */
import { getCurrentTime } from '@/utils/utils';
import { downloadFile } from '@/api/common';
import { mapState } from 'vuex';
export const fileMixin = {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    maxFileNumber: {
      type: Number,
      default: 5,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    acceptFileType: {
      type: String,
      default: '.msg,.eml,.txt,.doc,.docx,.docx,.xls,.xlsx,.csv,.ppt,.pptx,.pdf',
    },
    // 文件类型：01-欢迎信，11-OSCA报价单附件,12，AF单的PDF文件,13-AF单上传的已签约的文件,14-Sales上传的附件,15-ASM上传的附件
    fileType: {
      type: String,
      default: '11',
    },
    fileSizeLimit: {
      type: String,
      default: '10MB',
    },
  },
  data() {
    return {
      uploadLoading: false,
      fileList: [],
    };
  },
  computed: {
    ...mapState('app', {
      userInfo: state => state.userInfo,
    }),
    notAllowEdit() {
      return this.disabled || (this.maxFileNumber && this.fileList.length >= this.maxFileNumber);
    },
  },
  watch: {
    list() {
      this.initFileList();
    },
  },
  methods: {
    initFileList() {
      this.fileList = [].concat(this.list, this.fileList);
    },
    // 上传前的校验 - 注：可通过传false或者0实现不校验
    beforeUpload(file) {
      if (file) {
        if (this.maxFileNumber && this.fileList.length >= this.maxFileNumber) {
          this.$message.warning(
            this.$t('common.FileNumberVerification', { number: this.maxFileNumber }),
          );
          return false;
        }
        //文件限制在10MB内
        if (
          this.fileSizeLimit &&
          this.fileSizeLimit != '' &&
          file.size > this.convertToBytes(this.fileSizeLimit)
        ) {
          this.$message.error(this.$t('quotation.FileSizeLimit'));
          return false;
        }
        // 判断是否在可传入类型
        if (this.acceptFileType && this.acceptFileType != '') {
          const fileType = this.getFileType(file.name);
          const fileAcceptArr = this.acceptFileType.split(',');
          if (!fileAcceptArr.includes('.' + fileType)) {
            this.$message.error(
              this.$t('common.FileFormatVerification', { fileType: this.acceptFileType }),
            );
            return false;
          }
        }
        this.handleUpload(file);
      }
      return false;
    },
    // 匹配文件名最后的扩展名（不区分大小写），支持多点、空格、中文等复杂文件名
    getFileType(filename) {
      const match = filename.match(/\.([a-zA-Z0-9]+)(?=[?#]?)(?=\s*$)/);
      return match ? match[1].toLowerCase() : '';
    },
    // 组装 - 存入fileList的值
    async formatFileItem(file, customizeFile) {
      let OPER_TIME = customizeFile.OPER_TIME;
      if (!OPER_TIME || OPER_TIME === '') {
        OPER_TIME = await getCurrentTime();
      }
      let result = {
        SERVER_TYPE: customizeFile.SERVER_TYPE || '',
        FILE_TYPE: this.fileType,
        FILE_NAME: file.name,
        FILE_DESC: customizeFile.FILE_DESC || file.name,
        FILE_URL: customizeFile.FILE_URL,
        OPER_STAFF_ID: 'xasi0137', // this.userInfo.STAFF_ID;
        OPER_STAFF_NAME: '王洪焕', // this.userInfo.STAFF_NAME;
        OPER_TIME: OPER_TIME,
        MODIFY_TAG: '0',
      };
      return result;
    },

    // 下载附件
    downAttachment(record) {
      window.location.href = downloadFile(
        '?fileId=' + record.FILE_URL + '&fileName=' + record.FILE_NAME,
      );
    },
    // 删除文件
    deleteFile(record) {
      if (this.notAllowEdit) {
        return false;
      }
      // 新增后删除的，直接删除数据
      let index = this.fileList.findIndex(item => item.FILE_URL === record.FILE_URL);
      if (index !== -1) {
        this.fileList.splice(index, 1);
      }
    },
    /**
     * 将带有单位的存储大小字符串转换为字节数
     * @param {string} sizeStr - 包含数字和单位的字符串，如 "1.5GB", "500MB", "1024KB"
     * @returns {number} 转换后的字节数
     * @throws {Error} 如果输入格式无效或单位不被支持
     */
    convertToBytes(sizeStr) {
      // 定义单位到字节的转换因子
      const units = {
        'B': 1,
        'KB': 1024,
        'MB': 1024 ** 2,
        'GB': 1024 ** 3,
        'TB': 1024 ** 4,
        'PB': 1024 ** 5,
        'EB': 1024 ** 6,
        'ZB': 1024 ** 7,
        'YB': 1024 ** 8,
        'KIB': 1024,
        'MIB': 1024 ** 2,
        'GIB': 1024 ** 3,
        'TIB': 1024 ** 4,
        'PIB': 1024 ** 5,
        'EIB': 1024 ** 6,
        'ZIB': 1024 ** 7,
        'YIB': 1024 ** 8,
      };

      // 使用正则表达式解析输入
      const pattern = /^\s*([0-9]+(?:\.[0-9]+)?)\s*([A-Za-z]+)\s*$/;
      const match = sizeStr.trim().match(pattern);

      if (!match) {
        throw new Error(`Invalid input format: '${sizeStr}'. Valid examples: '1.5GB', '500MB'`);
      }

      const number = parseFloat(match[1]);
      let unit = match[2].toUpperCase();

      // 处理单独的'B'和二进制单位(iB)
      if (unit === 'B') {
        unit = 'B';
      } else if (unit.endsWith('IB')) {
        // 确保二进制单位格式正确
        if (!['K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'].includes(unit.slice(0, -2))) {
          throw new Error(`Unsupported binary unit: '${unit}'`);
        }
        unit = unit.slice(0, -1) + 'B'; // 转换为标准格式，如 KiB -> KIB
      } else if (unit.endsWith('B')) {
        // 处理KB, MB, GB等
        if (!['K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'].includes(unit.slice(0, -1))) {
          throw new Error(`Unsupported unit: '${unit}'`);
        }
      } else {
        // 处理不带B的单位，如1.5K -> 1.5KB
        if (['K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'].includes(unit)) {
          unit += 'B';
        } else {
          throw new Error(`Unsupported unit: '${unit}'`);
        }
      }

      if (!(unit in units)) {
        throw new Error(`Unsupported unit: '${unit}'`);
      }

      return Math.round(number * units[unit]);
    },
  },
};
