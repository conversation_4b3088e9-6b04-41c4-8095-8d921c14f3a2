<template>
  <div>
    <a-table
      :columns="tableColumns"
      :data-source="tableData"
      :loading="loading"
      row-key="afNo"
      :defaultExpandAllRows="true"
      :expandedRowKeys="expandedRowKeys"
      @expand="handleExpand"
      :pagination="{
        current: pagination.PAGE_NO,
        pageSize: pagination.PAGE_SIZE,
        total: pagination.total,
        class: 'common-custom-pagination',
        showQuickJumper: true,
        showSizeChanger: true,
        hideOnSinglePage: false, //只有一页时是否隐藏分页器
        showTotal: total => $t('common.tableTotalCount', { total }),
        onChange: page => {
          this.paginationChange(page);
        },
        onShowSizeChange: (_, size) => {
          this.paginationChange(1, size);
        },
      }"
    >
      <span slot="expandIcon" slot-scope="props" class="expand-icon">
        <img
          src="@/assets/images/up.svg"
          alt=""
          v-show="!props.expanded && props.record?.children"
          class="arrow"
          @click="props.onExpand(props.record, $event)"
        />
        <img
          src="@/assets/images/down.svg"
          alt=""
          v-show="props.expanded && props.record?.children"
          class="arrow"
          @click="props.onExpand(props.record, $event)"
        />
      </span>
      <template slot="status" slot-scope="value, record">
        <Flicker v-if="record.children?.length" :color="getStatusColor(record.status)" />
        <span>{{ record.status }}</span>
      </template>
      <template slot="action" slot-scope="value, record">
        <div v-if="record.children?.length" class="action-cell">
          <a
            class="iconfont icon-xiangqingmingxi action-icon"
            :title="$t('common.buttonView')"
            @click.stop="pageLink('detail', record, $event)"
          ></a>
          <a
            v-buttonPermission="'AF_MANAGEMENT_LIST_EDIT'"
            class="iconfont icon-xiugai action-icon"
            :title="$t('common.modaltitleEdit')"
            @click.stop="pageLink('edit', record, $event)"
          ></a>
          <a
            v-buttonPermission="'AF_MANAGEMENT_CANCEL'"
            class="iconfont icon-baocuo action-icon"
            :title="$t('common.buttonCancel')"
          ></a>
          <!-- v-if="record.STATUS === 'S0' || record.STATUS === 'S3'" -->
        </div>
      </template>
    </a-table>
  </div>
</template>

<script>
  import { tableColumns } from '../config';
  import icon_up from '@/assets/images/up.svg';
  import icon_down from '@/assets/images/down.svg';
  import { getAFOrderInfo } from '@/api/orderCapture';
  import Flicker from '@/components/flicker/index.vue';
  export default {
    name: 'orderCaptureAFList',
    components: {
      Flicker,
    },
    props: {
      customerInfo: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        loading: false,
        tableData: [],
        tableColumns,
        pagination: {
          PAGE_NO: 1,
          PAGE_SIZE: 10,
          total: 0,
        },
        expandedRowKeys: [],
      };
    },

    methods: {
      /**
       * 处理表格数据, 将数据转换为树形结构
       * @param data 原始数据
       * @returns 处理后的数据
       */
      handleTableData(data) {
        if (!data || !data.DATA || !data.DATA[0]?.QUERY_RESPONSE) {
          return [];
        }

        const groupedData = {};
        data.DATA[0].QUERY_RESPONSE.forEach(item => {
          if (!groupedData[item.ORDER_ID]) {
            groupedData[item.ORDER_ID] = {
              afNo: item.ORDER_ID,
              productName: '',
              installationAddress: '',
              nextApprove: item.APPROVER_NAME || '-',
              status: item.STATUS_NAME || '-',
              children: [],
            };
          }

          groupedData[item.ORDER_ID].children.push({
            afNo: item.ORDER_LINE_ID,
            productName: item.PRODUCT_NAME || '-',
            installationAddress: item.INSTALL_ADDRESS || '-',
            nextApprove: '',
            status: '',
          });
        });

        return Object.values(groupedData);
      },

      /**
       * 查询AF订单信息
       * @param data 查询参数
       */
      queryAFOrderInfo(data) {
        let params = {
          PAGE_NO: this.pagination.PAGE_NO,
          PAGE_SIZE: this.pagination.PAGE_SIZE,
          CUST_NO: this.customerInfo.CUSTOMER_NO,
        };
        if (data) {
          params = { ...params, ...data };
        }
        this.loading = true;
        getAFOrderInfo(params)
          .then(res => {
            this.tableData = this.handleTableData(res);
            console.log('this.tableData', this.tableData);
            if (res?.DATA?.[0]?.TOTAL_COUNT) {
              this.pagination.total = res.DATA[0].TOTAL_COUNT;
            }
            this.expandedRowKeys = this.tableData.map(item => item.afNo);
          })
          .finally(() => {
            this.loading = false;
          });
      },
      /**
       * 初始化分页查询
       * @param data 查询条件
       */
      handleQuery(data) {
        this.pagination.PAGE_NO = 1;
        this.pagination.PAGE_SIZE = 10;
        this.queryAFOrderInfo(data);
      },
      /**
       * 分页变化
       * @param page 页码
       * @param pageSize 每页条数
       */
      paginationChange(page, pageSize) {
        this.pagination.PAGE_NO = page || 1;
        this.pagination.PAGE_SIZE = pageSize || this.pagination.PAGE_SIZE;
        this.queryAFOrderInfo();
      },
      /**
       * 页面跳转
       * @param type 跳转类型 detail: 详情 edit: 编辑
       * @param record 跳转数据
       * @param event 事件
       */
      pageLink(type, record, event) {
        const page = type === 'detail' ? 'afOrderEditDetail' : 'afEditAfInfo';
        this.$router.push({
          path: `/orderCapture/orderCapture/${page}`,
          query: { afNo: record.afNo },
        });
        console.log(type, record, event);
      },
      /**
       * 自定义展开图标
       * @param props 展开图标的属性
       * @returns 展开图标
       */
      customExpandIcon(props) {
        const { expanded, onExpand, record } = props;
        if (!record.children || !record.children.length) {
          return null;
        }
        return (
          <img
            src={expanded ? icon_down : icon_up}
            class="expand-icon"
            onClick={e => onExpand(record, e)}
          />
        );
      },
      /**
       * 获取状态颜色
       * @param status 状态
       * @returns 状态颜色
       */
      getStatusColor(status) {
        const colorMap = {
          'AF pending approval': '#FFB100',
          'AF Approved': '#0072FF',
          'AF Rejected': '#E60017',
          'AF Completed': '#2DCB31',
          Cancelled: '#2DCB31',
        };
        return colorMap[status] || '#999999';
      },
      /**
       * 处理展开
       * @param expanded 是否展开
       * @param record 展开的行数据
       */
      handleExpand(expanded, record) {
        if (expanded) {
          this.expandedRowKeys.push(record.afNo);
        } else {
          this.expandedRowKeys = this.expandedRowKeys.filter(key => key !== record.afNo);
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .expand-icon {
    width: 12px;
    height: 11px;
    cursor: pointer;
    position: relative;
    display: inline-block;
    margin-right: 7px;
    .arrow {
      width: 12px;
      height: 12px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .action-cell {
    display: flex;
    gap: 8px;
    justify-content: center;
    .action-icon {
      font-size: 12px;
      color: #1890ff;
      &:hover {
        opacity: 0.8;
      }
      &:first-child {
        margin-right: 8px;
      }
      &:nth-child(2) {
        margin-right: 8px;
      }
    }
  }
</style>
