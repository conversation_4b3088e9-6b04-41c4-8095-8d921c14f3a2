<template>
  <div>
    <div class="common-custom-title">{{ $t('orderCapture.customAFList') }}</div>
    <a-radio-group v-model="radioValue">
      <a-radio
        v-for="item in radioList"
        :key="item.value"
        :value="item.value"
        style="margin-right: 39px"
      >
        {{ item.label }}
      </a-radio>
    </a-radio-group>

    <a-divider :dashed="true" />

    <!-- AF信息表单 -->
    <a-form-model
      v-if="radioValue === 'afInfo'"
      ref="formRef"
      :model="form"
      :colon="false"
      :rules="rules"
    >
      <a-row :gutter="24">
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.afInfo')">
            <a-input
              v-model.trim="form.ORDER_ID"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.afStatus')">
            <a-select
              v-model="form.STATUS"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in afStatusList" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.afCreateDate')" prop="createDate">
            <a-range-picker
              v-model="form.createDate"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              style="width: 100%"
              :placeholder="['', '']"
            >
              <template #suffixIcon>
                <span class="iconfont icon-rili" style="font-size: 14px"></span>
              </template>
            </a-range-picker>
          </a-form-model-item>
        </a-col>
        <a-col :span="6" style="text-align: right">
          <a-form-model-item label=" ">
            <a-button type="primary" @click="onSearch">{{ $t('common.buttonFilter') }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 产品表单 -->
    <a-form-model v-if="radioValue === 'product'" :colon="false">
      <a-row :gutter="24">
        <a-col :span="5">
          <a-form-model-item :label="$t('comp.LOB')">
            <a-select
              v-model="form.LOB"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              @change="handleLOBChange"
            >
              <a-select-option v-for="item in lobList" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('comp.ProductFamily')">
            <a-select
              v-model="form.productFamily"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              :disabled="isProductFamilyDisabled"
              @change="handleProductFamilyChange"
            >
              <a-select-option
                v-for="item in productFamilyList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('comp.ProductType')">
            <a-select
              v-model="form.productType"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              :disabled="isProductTypeDisabled"
              @change="handleProductTypeChange"
            >
              <a-select-option
                v-for="item in productTypeList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('comp.ProductName')">
            <a-select
              v-model="form.productName"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              :disabled="isProductNameDisabled"
            >
              <a-select-option
                v-for="item in productNameList"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="4" style="text-align: right">
          <a-form-model-item label=" ">
            <a-button type="primary" @click="onSearch">{{ $t('common.buttonFilter') }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 销售和ASM表单 -->
    <a-form-model v-if="radioValue === 'salesAndAsm'" :colon="false">
      <a-row :gutter="24">
        <a-col :span="5">
          <a-form-model-item :label="$t('orderCapture.salesSegment')">
            <a-input
              v-model.trim="form.salesSegment"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('orderCapture.salesName')">
            <a-input
              v-model.trim="form.salesName"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('orderCapture.salesManCode')">
            <a-input
              v-model.trim="form.SALES_CODE"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="5">
          <a-form-model-item :label="$t('orderCapture.asmName')">
            <a-input
              v-model.trim="form.asmName"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="4" style="text-align: right">
          <a-form-model-item label=" ">
            <a-button type="primary" @click="onSearch">{{ $t('common.buttonFilter') }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 地址表单 -->
    <a-form-model v-if="radioValue === 'address'" :colon="false">
      <a-row :gutter="24">
        <a-col :span="17">
          <a-form-model-item :label="$t('comp.InstallationAddress')">
            <a-input-search
              v-model="form.installationAddress"
              :placeholder="$t('common.selectPlaceholder')"
              @search="selectAddressVisible = true"
              @click="selectAddressVisible = true"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="7" style="text-align: right">
          <a-form-model-item label=" ">
            <a-button type="primary" @click="onSearch">{{ $t('common.buttonFilter') }}</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 选择地址弹窗 -->
    <a-modal
      :title="$t('customerVerify.addressMapping')"
      :width="880"
      destroyOnClose
      :footer="null"
      :visible="selectAddressVisible"
      @cancel="selectAddressCancel"
    >
      <SelectAddress @cancel="selectAddressCancel" @ok="handleConfirmAddress" />
    </a-modal>
  </div>
</template>

<script>
  import SelectAddress from '@/views/components/selectAddress';
  import { radioList, afStatusList } from '../config';
  import { queryParamList } from '@/api/common';
  import { getProductAll, productFamilyQuery, productTypeOperateQuery } from '@/api/customerVerify';
  import i18n from '@/i18n/index';

  export default {
    name: 'customerAFListForm',
    components: {
      SelectAddress,
    },
    props: {
      lobList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        selectAddressVisible: false,
        radioValue: 'afInfo',
        radioList, // 表单类型列表
        afStatusList, // AF状态列表
        isProductFamilyDisabled: true, // 产品系列是否禁用
        isProductTypeDisabled: true, // 产品类型是否禁用
        isProductNameDisabled: true, // 产品是否禁用
        form: {
          ORDER_ID: '',
          STATUS: undefined,
          createDate: [],
          LOB: undefined,
          productFamily: undefined,
          productType: undefined,
          productName: undefined,
          salesSegment: undefined,
          salesName: '',
          SALES_CODE: '',
          asmName: '',
          installationAddress: '',
          ADDRESS_STANDAR_ID: '',
        },
        productFamilyList: [], // 产品系列列表
        productTypeList: [], // 产品类型列表
        productNameList: [], // 产品列表
      };
    },
    computed: {
      rules() {
        return {
          createDate: [
            {
              message: i18n.t('quotation.CreateDateTimeLimit'),
              trigger: 'change',
              validator: (rule, value, callback) => {
                // 限定时间范围在三个月以内
                const [startDate, endDate] = value;

                const start = new Date(startDate);
                const end = new Date(endDate);

                // 计算月份差
                const months = (end.getFullYear() - start.getFullYear()) * 12;
                const monthDiff = months + end.getMonth() - start.getMonth();

                if (monthDiff > 3) {
                  callback(new Error(i18n.t('quotation.CreateDateTimeLimit')));
                } else {
                  callback();
                }
              },
            },
          ],
        };
      },
    },
    methods: {
      /**
       * 查询AF状态
       */
      queryAfStatusList() {
        queryParamList({
          PARAM_TYPE: 'ORDER_AFSTATUS_TYPE',
        }).then(res => {
          if (res?.DATA?.length > 0) {
            this.afStatusList = res.DATA.map(item => {
              return {
                value: item.DATA_ID,
                label: item.DATA_NAME,
              };
            });
            // console.log('this.afStatusList', this.afStatusList);
          }
        });
      },
      /**
       * 查询
       */
      onSearch() {
        // console.log('form', this.form);
        this.$refs.formRef.validate(valid => {
          if (valid) {
            let params = {};
            switch (this.radioValue) {
              case 'afInfo':
                params = {
                  ORDER_ID: this.form.ORDER_ID,
                  STATUS: this.form.STATUS,
                  START_DATE: this.form.createDate ? this.form.createDate[0] : '',
                  END_DATE: this.form.createDate ? this.form.createDate[1] : '',
                };
                break;
              case 'product':
                if (!this.form.productName)
                  return this.$message.warning(this.$t('orderCapture.pleaseSelectProductName'));
                params = {
                  PRODUCT_ID: this.form.productName,
                };
                break;
              case 'salesAndAsm':
                params = {
                  SALES_CODE: this.form.SALES_CODE,
                };
                break;
              case 'address':
                params = {
                  ADDRESS_STANDAR_ID: this.form.ADDRESS_STANDAR_ID,
                };
                break;
            }
            // console.log('params', params);
            this.$emit('AFFormSearch', params);
          }
        });
      },
      selectAddressCancel() {
        this.selectAddressVisible = false;
      },
      /**
       * 选择地址回填
       * @param address 地址
       */
      handleConfirmAddress(address) {
        // console.log('address', address);
        this.selectAddressVisible = false;
        this.form.ADDRESS_STANDAR_ID = address.masterAddress.SB_NO;
        this.form.installationAddress =
          address.status == 'normal' ? address.masterAddress.DETAILS_EN.ADDRESS : address.SB_ADDR;
      },
      // LOB变化处理
      handleLOBChange(value) {
        this.resetProductForm();
        if (value) {
          this.getProductFamilyList(value);
          this.isProductFamilyDisabled = false;
        } else {
          this.isProductFamilyDisabled = true;
        }
      },
      // 产品系列变化处理
      handleProductFamilyChange(value) {
        this.form.productType = '';
        this.form.productName = '';
        this.productTypeList = [];
        this.productNameList = [];
        this.isProductTypeDisabled = true;
        this.isProductNameDisabled = true;

        if (value) {
          this.getProductTypeList(value);
        }
      },
      // 重置产品相关表单
      resetProductForm() {
        this.form.productFamily = '';
        this.form.productType = '';
        this.form.productName = '';
        this.productFamilyList = [];
        this.productTypeList = [];
        this.productNameList = [];
        this.isProductFamilyDisabled = true;
        this.isProductTypeDisabled = true;
        this.isProductNameDisabled = true;
      },
      // 获取产品系列列表
      async getProductFamilyList(lob) {
        try {
          const res = await productFamilyQuery({ LOB: lob });
          if (res.DATA) {
            this.productFamilyList = res.DATA.map(item => ({
              value: item.PRODUCT_FAMILY,
              label: item.PRODUCT_FAMILY_NAME,
            }));
          }
        } catch (error) {
          console.error('获取产品系列列表失败', error);
        }
      },
      // 获取产品类型列表
      async getProductTypeList(productFamily) {
        try {
          const res = await productTypeOperateQuery({
            PRODUCT_FAMILY: productFamily,
          });
          if (res.DATA) {
            this.productTypeList = res.DATA.map(item => ({
              value: item.PRODUCT_TYPE_CODE,
              label: item.PRODUCT_TYPE_NAME,
            }));
            this.isProductTypeDisabled = false;
          }
        } catch (error) {
          console.error('获取产品类型列表失败', error);
        }
      },
      // 获取产品列表
      async getProductList(productType) {
        try {
          const data = {
            STAFF_ID: this.$store.state.app.userInfo.STAFF_ID,
            EPARCHY_CODE: this.$store.state.app.userInfo.EPARCHY_CODE,
            PROVINCE_CODE: this.$store.state.app.userInfo.PROVINCE_CODE,
            PRODUCT_TYPE_CONDITIONS: [
              {
                PRODUCT_TYPE_CODE: productType,
                PRODUCT_TYPE_CODE_ROOT: '1000',
              },
            ],
          };
          const res = await getProductAll(data);
          if (res.DATA && res.DATA[0]?.QUERY_RESPONSE) {
            this.productNameList = res.DATA[0].QUERY_RESPONSE.map(item => ({
              value: item.ID,
              label: item.NAME,
            }));
            this.isProductNameDisabled = false;
          }
        } catch (error) {
          console.error('获取产品列表失败', error);
        }
      },
      // 产品类型变化处理
      handleProductTypeChange(value) {
        this.form.productName = '';
        this.productNameList = [];
        this.isProductNameDisabled = true;

        if (value) {
          this.getProductList(value);
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .search-form {
    margin-top: 24px;
    .ant-form-item {
      margin-bottom: 24px;
    }
    .ant-btn {
      margin-left: 8px;
    }
  }
</style>
