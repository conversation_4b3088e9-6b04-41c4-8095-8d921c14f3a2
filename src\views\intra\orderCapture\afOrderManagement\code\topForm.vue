<template>
  <div>
    <a-form-model :model="form" ref="form" :colon="false">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-form-model-item :label="$t('comp.CustomerName')" prop="CUSTOMER_NAME">
            <a-input
              v-model.trim="form.CUSTOMER_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
              v-containsSqlInjection
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('comp.CustomerNo')" prop="CUSTOMER_NO">
            <a-input-number
              v-model.trim="form.CUSTOMER_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
              style="width: 100%"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.dragonCustNo')" prop="DRAGON_CUSTOMER_NO">
            <a-input
              v-model.trim="form.DRAGON_CUSTOMER_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.documentType')" prop="DOCUMENT_TYPE">
            <a-select
              v-model="form.DOCUMENT_TYPE"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
              @change="changeDocumentType"
            >
              <a-select-option
                v-for="item in documentTypeList"
                :key="item.value"
                :value="item.value"
                >{{ item.label }}</a-select-option
              >
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.documentNo')" prop="DOCUMENT_NO">
            <a-input
              v-model.trim="form.DOCUMENT_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
              :disabled="disabledDocumentType"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.serviceNo')" prop="SERVICE_NO">
            <a-input
              v-model.trim="form.SERVICE_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.billingAccountNo')" prop="ACCOUNT_NO">
            <a-input
              v-model.trim="form.ACCOUNT_NO"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('orderCapture.lob')" prop="LOB">
            <a-select v-model="form.LOB" :placeholder="$t('common.selectPlaceholder')" allowClear>
              <a-select-option v-for="item in lobList" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row justify="end" type="flex" class="padding form-item-btn">
        <a-col>
          <a-form-model-item class="textAlignRight">
            <a-button ghost type="primary" class="reset-button" @click="resetForm">
              {{ $t('common.buttonReset') }}
            </a-button>
          </a-form-model-item>
        </a-col>
        <a-col>
          <a-form-model-item class="textAlignRight">
            <a-button type="primary" @click="queryOpen" class="search-button">
              {{ $t('common.buttonInquiry') }}
            </a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>

    <!-- 客户选择弹窗 -->
    <QueryCustomerList
      v-if="queryListVisible"
      :visible="queryListVisible"
      :title="queryListTitle"
      :queryData="form"
      :isCustomerCertificate="false"
      @cancel="queryListCancel"
      @Ok="queryListOk"
    />

    <!-- 信息提示框 -->
    <TipsPopWindow
      v-if="tipsVisible"
      :text="tipsText"
      :visible="tipsVisible"
      @cancel="tipsVisible = false"
      @Ok="tipsVisible = false"
    />
  </div>
</template>

<script>
  import QueryCustomerList from '@/views/components/queryCustomerList';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';
  import { queryDocumentType, getCustListQryList } from '@/api/customerVerify';
  import { hasAnyValue, qryBadPaymentIndicatorInfo } from '@/utils/utils';

  export default {
    name: 'orderCaptureTopForm',
    components: {
      QueryCustomerList,
      TipsPopWindow,
    },
    props: {
      lobList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        form: {
          CUSTOMER_NAME: '',
          CUSTOMER_NO: '',
          DRAGON_CUSTOMER_NO: '',
          DOCUMENT_TYPE: undefined,
          DOCUMENT_NO: '',
          SERVICE_NO: '',
          ACCOUNT_NO: '',
          LOB: undefined,
        },
        queryListVisible: false,
        queryListTitle: this.$t('comp.CustomerSelection'),
        tipsVisible: false, //信息提示框
        tipsText: '',
        documentTypeList: [],
        disabledDocumentType: true,
      };
    },
    created() {
      this.getDocumentTypeList();
    },
    methods: {
      /**
       * 查询文档类型
       */
      async getDocumentTypeList() {
        const res = await queryDocumentType();
        if (res?.DATA) {
          this.documentTypeList = res.DATA.map(item => ({
            label: item.PSPT_TYPE,
            value: item.PSPT_TYPE_CODE,
          }));
        }
      },
      /**
       * 查询客户列表取消
       */
      queryListCancel() {
        this.queryListVisible = false;
      },
      /**
       * 选择客户后回填客户信息
       * @param data 客户信息
       */
      queryListOk(data) {
        this.queryListVisible = false;
        this.$emit('sendCustomerInfo', data);
      },
      /**
       * 重置表单
       */
      resetForm() {
        this.$refs.form.resetFields();
      },
      // 表单查询
      queryOpen() {
        // 必须输入任一条件
        const status = hasAnyValue(this.form);
        if (!status) {
          this.tipsText = this.$t('customerVerify.atLeastOneCondition');
          this.tipsVisible = true;
          return;
        }

        // 查客户id不用弹窗，直接调接口查完显示,其他条件弹窗查询
        const uniqueSearch = {
          CUSTOMER_NO: this.form.CUSTOMER_NO,
        };
        if (hasAnyValue(uniqueSearch)) {
          this.queryList(uniqueSearch);
        } else {
          this.queryListVisible = true;
        }
      },
      /**
       * 查询客户列表
       * @param params 查询参数
       */
      queryList(params) {
        getCustListQryList({ ...params, PAGE_SIZE: 5, PAGE_NUM: 1 }).then(res => {
          if (res?.DATA?.length > 0) {
            this.queryListOk(res.DATA[0]);
          }
        });
      },
      changeDocumentType(value) {
        value ? (this.disabledDocumentType = false) : (this.disabledDocumentType = true);
      },
    },
  };
</script>

<style lang="less" scoped></style>
