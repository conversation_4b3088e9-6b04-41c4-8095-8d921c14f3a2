import i18n from '@/i18n/index';

export const radioList = [
  {
    label: i18n.t('orderCapture.afInfo'),
    value: 'afInfo',
  },
  {
    label: i18n.t('comp.Product'),
    value: 'product',
  },
  {
    label: i18n.t('orderCapture.salesAndAsm'),
    value: 'salesAndAsm',
  },
  {
    label: i18n.t('comp.Address'),
    value: 'address',
  },
];

export const afStatusList = [
  {
    label: i18n.t('orderCapture.afPendingApproval'),
    value: 'S4',
  },
  {
    label: i18n.t('orderCapture.afApproved'),
    value: 'S5',
  },
  {
    label: i18n.t('orderCapture.afRejected'),
    value: 'S6',
  },
  {
    label: i18n.t('orderCapture.afCompleted'),
    value: 'S7',
  },
  {
    label: i18n.t('orderCapture.afCancelled'),
    value: 'S8',
  },
];

export const tableColumns = [
  {
    title: i18n.t('orderCapture.afNo'),
    dataIndex: 'afNo',
    key: 'afNo',
    width: 200,
  },
  {
    title: i18n.t('comp.ProductName'),
    dataIndex: 'productName',
    key: 'productName',
    width: 120,
  },
  {
    title: i18n.t('comp.InstallationAddress'),
    dataIndex: 'installationAddress',
    key: 'installationAddress',
    width: 450,
    ellipsis: true,
  },
  {
    title: i18n.t('orderCapture.nextApprove'),
    dataIndex: 'nextApprove',
    key: 'nextApprove',
    width: 120,
  },
  {
    title: i18n.t('orderCapture.status'),
    key: 'status',
    dataIndex: 'status',
    width: 200,
    scopedSlots: { customRender: 'status' },
  },
  {
    title: i18n.t('common.action'),
    key: 'action',
    fixed: 'right',
    align: 'center',
    width: 120,
    scopedSlots: { customRender: 'action' },
  },
];

export const formProperties = [
  'CUSTOMER_NAME',
  'CUSTOMER_NO',
  'DRAGON_CUSTOMER_NO',
  'DOCUMENT_TYPE',
  'DOCUMENT_NO',
  'SERVICE_NO',
  'BILLING_ACCOUNT_NO',
  'LOB',
];
