<template>
  <div>
    <!-- 顶部查询表单 -->
    <order-capture-top-form :lobList="lobList" @sendCustomerInfo="sendCustomerInfo" />

    <a-divider />

    <!-- 预审单表单 -->
    <div class="common-custom-title">
      {{ $t('orderCapture.orderCaptureListFormTitleList') }}
    </div>
    <a-descriptions
      :column="4"
      :class="{
        'description-btm-0': Object.keys(form).length > 4,
      }"
    >
      <a-descriptions-item
        v-for="(value, key) in form"
        :key="key"
        :label="labelMap[key]"
        class="description-item"
      >
        {{ key === 'LOB' ? lobList.find(item => item.value === value)?.label : value || '-' }}
      </a-descriptions-item>
    </a-descriptions>

    <a-divider />

    <!-- 客户AF单表单 -->
    <customerAFListForm :lobList="lobList" @AFFormSearch="AFFormSearch" />

    <!-- AF单列表 -->
    <afList ref="afListRef" :customerInfo="form" style="margin-top: 8px" />

    <!-- 信息提示框 -->
    <TipsPopWindow
      v-if="tipsVisible"
      :text="tipsText"
      :visible="tipsVisible"
      @cancel="tipsVisible = false"
      @Ok="tipsVisible = false"
    />
  </div>
</template>

<script>
  import orderCaptureTopForm from './code/topForm.vue';
  import customerAFListForm from './code/customerAFListForm.vue';
  import afList from './code/afList.vue';
  import { formProperties } from './config';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';
  import { lobQuery } from '@/api/common';

  export default {
    name: 'orderCaptureList',
    components: {
      orderCaptureTopForm,
      customerAFListForm,
      afList,
      TipsPopWindow,
    },
    data() {
      return {
        tipsVisible: false,
        tipsText: '',
        form: {
          CUSTOMER_NAME: '',
          CUSTOMER_NO: '',
          DRAGON_CUSTOMER_NO: '',
          DOCUMENT_TYPE: '',
          DOCUMENT_NO: '',
          SERVICE_NO: '',
          BILLING_ACCOUNT_NO: '',
          LOB: '',
        },
        labelMap: {
          CUSTOMER_NAME: this.$t('comp.CustomerName'),
          CUSTOMER_NO: this.$t('comp.CustomerNo'),
          DRAGON_CUSTOMER_NO: this.$t('orderCapture.dragonCustNo'),
          DOCUMENT_TYPE: this.$t('orderCapture.documentType'),
          DOCUMENT_NO: this.$t('orderCapture.documentNo'),
          SERVICE_NO: this.$t('orderCapture.serviceNo'),
          BILLING_ACCOUNT_NO: this.$t('orderCapture.billingAccountNo'),
          LOB: this.$t('comp.LOB'),
        },
        lobList: [],
      };
    },
    created() {
      this.getLobList();
    },
    methods: {
      /**
       * 查询AF单列表
       * @param data 查询条件
       */
      AFFormSearch(data) {
        // 校验是否输入了客户信息
        if (!this.form.CUSTOMER_NO) {
          this.tipsVisible = true;
          this.tipsText = this.$t('orderCapture.pleaseEnterCustomerInfo');
          return;
        }
        // console.log(this.$refs.afListRef);
        this.$refs.afListRef.handleQuery(data);
      },
      /**
       * 选择客户信息后回填客户信息
       * @param data 客户信息
       */
      sendCustomerInfo(data) {
        formProperties.forEach(key => {
          this.form[key] = data[key] || '';
        });
      },
      /**
       * 获取LOB列表
       */
      async getLobList() {
        const res = await lobQuery();
        if (res?.DATA) {
          this.lobList = res.DATA.map(item => ({
            value: item.LOB,
            label: item.LOB_NAME,
          }));
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  /deep/ .ant-descriptions-item-label {
    color: #333 !important;
  }

  /deep/ .ant-descriptions-item-content {
    color: #666 !important;
  }

  .description-item {
    padding: 8px 16px;
  }
</style>
