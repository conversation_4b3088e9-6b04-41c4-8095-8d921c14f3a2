# AF订单管理模块说明文档

## 文件结构说明

```
afOrderManagement/
├── code/                          # 子组件目录
│   ├── afList.vue                # AF订单列表组件：展示AF订单的详细信息和操作
│   ├── customerAFListForm.vue    # 客户AF单查询表单：用于AF订单的筛选和查询
│   └── topForm.vue              # 顶部查询表单：用于客户信息的查询和选择
├── config.js                     # 配置文件：包含表格列配置、状态列表、表单属性等配置信息
├── index.vue                     # 主页面组件：整合所有子组件，实现AF订单管理的完整功能
└── readme.md                     # 本文档：项目说明文档
```

## 主要文件功能描述

### 1. index.vue
- 作为AF订单管理的主页面组件
- 整合了顶部查询、客户信息展示、AF单查询和列表展示等功能
- 负责组件间的数据传递和状态管理
- 实现了客户信息的展示和LOB列表的获取

### 2. config.js
- 定义了模块所需的配置信息
- 包含radio选项列表配置
- 定义AF订单状态列表
- 配置表格列的显示规则
- 定义表单属性列表

### 3. code/afList.vue
- 实现AF订单列表的展示
- 包含订单详情的查看功能
- 支持各种订单状态的展示
- 提供订单相关的操作功能

### 4. code/customerAFListForm.vue
- 提供AF订单的高级搜索功能
- 实现多条件组合查询
- 支持表单验证和提交
- 与主页面进行数据交互

### 5. code/topForm.vue
- 实现顶部客户信息查询功能
- 支持客户号、证件号等多种查询方式
- 提供客户信息的选择和回填功能
- 与主页面进行数据同步

## 使用说明
1. 首先通过顶部查询表单查询并选择客户信息
2. 客户信息确认后，可以使用AF单查询表单进行订单筛选
3. 在AF订单列表中可以查看订单详情和进行相关操作
4. 支持多种状态的订单管理和处理
