<template>
  <div>
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>
    <div class="productSelection">
      <!-- 选址 -->
      <AddressMappingForm v-if="fromPage == 'quotation'" ref="AddressMappingFormRef" />

      <!-- 分割线 -->
      <a-divider v-if="fromPage == 'quotation'" />

      <!-- 选产品 -->
      <ProductTreeTabs
        ref="ProductTreeTabs"
        :tabCountList="tabCountList"
        :lineInstallNum="lineInstallNumber"
        :mainProductList="mainProductList"
        :mainProductExpandedRowKeys="mainProductExpandedRowKeys"
        :mainProductSpinning="mainProductSpinning"
        :mainProductCheckboxDisabled="fromPage == 'quotation' ? false : true"
        :mainProductEditBtnShow="fromPage == 'quotation' ? true : false"
        :additionalProductList="additionalProductList"
        :additionalProductExpandedRowKeys="additionalProductExpandedRowKeys"
        :additionalProductSpinning="additionalProductSpinning"
        @getAdditionalProduct="getAdditionalProduct"
        @getProductMrcOtcData="handleProductMrcOtcData"
      />
      <!-- 价格计算 -->
      <div class="footer-tool-bar">
        <footer-tool-bar-price :productMrcOtcData="productMrcOtcData">
          <a-button ghost type="primary" @click="cancel" class="reset-button product-footer-btn">
            {{ $t('customerVerify.Cancel') }}
          </a-button>
          <a-button type="primary" @click="confirm" class="search-button product-footer-btn">
            {{ $t('customerVerify.Confirm') }}
          </a-button>
        </footer-tool-bar-price>
      </div>

      <!-- 校验提示 -->
      <MessageModal
        :visible="tipsVisible"
        :message="tipsMessage"
        @cancel="tipsVisible = false"
        @confirm="tipsVisible = false"
      />
    </div>
  </div>
</template>

<script>
  import { qryAmendProduct, queryAdditionPremium } from '@/api/quotation';
  import { searchMainProduct, searchAddition, defprodandmutex } from '@/api/customerVerify';
  import { getMainProductTreeSelectedList } from '@/views/components/productTreeList/common/utils';
  import {
    addCheckedField,
    convertToTreeData,
    dealProductElementAttrList,
  } from '@/views/intra/changeProduct.js';
  import { validateProductTreeRules } from '@/views/components/productTreeList/nextStepValidate/nextStepValidate';
  import PageHeader from '@/views/components/pageHeader/index.vue';
  import MessageModal from '@/components/messageModal';
  import AddressMappingForm from '@/views/components/addressMappingForm';
  import FooterToolBarPrice from '@/views/components/footerToolbarPrice';
  import ProductTreeTabs from '@/views/components/productTreeList/tabList';
  import { mapState } from 'vuex';
  import { mixins } from '../mixins/index';
  export default {
    name: 'ProductSelection',
    components: {
      PageHeader,
      AddressMappingForm,
      ProductTreeTabs,
      MessageModal,
      FooterToolBarPrice,
    },
    props: {
      pageTitle: {
        type: String,
        default: '',
      },
    },

    data() {
      return {
        mainProductList: [], //主产品
        mainProductExpandedRowKeys: [], //主产品展开的key
        additionalProductList: [], //附加产品
        additionalProductExpandedRowKeys: [], //附加产品展开的key
        tabCountList: [], //vas和price数量
        lineInstallNumber: '', //线路安装数量
        tipsVisible: false,
        tipsMessage: '',
        mainProductSpinning: false,
        additionalProductSpinning: false,
        productType: {},
      };
    },
    mixins: [mixins],
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
      ...mapState('quotation', {
        productSelection: state => state.productSelection,
        isCORP: state => state.isCORP,
      }),
      // 上一页页面信息
      prePageInfo() {
        return this.$t('comp.ProductSelection');
      },
      // 当前状态是否已经后台保存过
      isSaveDatabase() {
        return this.$route.query?.orderLineId;
      },
      // 订单编号
      orderId() {
        const { orderId } = this.$route.query;
        return orderId;
      },
      // 订单行编号
      orderLineId() {
        const { orderLineId } = this.$route.query;
        return orderLineId;
      },
      // 修改产品阶段
      fromPage() {
        const { fromPage } = this.$route.query;
        return fromPage;
      },
    },
    async mounted() {
      if (this.isEdit) {
        if (this.fromPage == 'quotation') {
          // 报价单阶段 地址更改
          this.editAdressInit();
        }
        if (this.isSaveDatabase) {
          // 后台保存过的产品数据回显
          const orderedData = await this.getQryAmendProduct();
          this.dealEditMainData(orderedData);
          this.getTabCountList(orderedData);
        } else {
          this.getLineInstallNumber();
          this.getMainProduct();
          this.getTabCountList();
        }
        // 获取附件产品数据
        this.getAdditionalProduct(
          this.productSelection?.productInfo?.MainProduct[0]?.PRODUCT_ID,
          true,
        );
      } else {
        // 获取主产品数据
        this.getMainProduct();
        this.getTabCountList();
      }
    },
    methods: {
      // 编辑状态下 - 回显
      editAdressInit() {
        console.log(this.productSelection);
        // 回显 - 地址信息
        if (this.productSelection?.addressInfo) {
          const { SB_ADDRESS, masterAddress, SB_NO } = this.productSelection.addressInfo;
          this.$nextTick(() => {
            this.$refs.AddressMappingFormRef.form = {
              ...this.productSelection.addressInfo,
              SB_ADDRESS,
              masterAddress: masterAddress || SB_ADDRESS,
              SB_NO,
            };
          });
        }
      },
      // 获取安装数量
      getLineInstallNumber() {
        this.lineInstallNumber = this.productSelection?.lineInstallNumber;
      },
      // 获取vas和price数量
      getTabCountList(orderedData) {
        if (orderedData) {
          // 获取选中vas和price的总量
          let countList = [0, 0, 0, 0];
          orderedData.forEach(item => {
            const tempItem = item.PRODUCT_ITEM_INFO.find(
              item => item.ATTR_CODE === 'price_element_count',
            );
            if (item.PRODUCT_MODE == '00') {
              const tempLineItem = item.PRODUCT_ITEM_INFO.find(item => item.ATTR_CODE === 'qty');
              this.lineInstallNumber = tempLineItem?.ATTR_VALUE;
              countList.splice(0, 1, tempItem?.ATTR_VALUE);
            } else {
              const productType = item.PRODUCT_TYPE_CODE;
              if (productType == '300010') {
                // 设备
                countList.splice(2, 1, tempItem?.ATTR_VALUE);
              } else if (productType == '300013') {
                // 礼品
                countList.splice(3, 1, tempItem?.ATTR_VALUE);
              } else {
                countList.splice(1, 1, tempItem?.ATTR_VALUE);
              }
            }
          });
          this.tabCountList = countList;
        } else {
          if (this.isEdit) {
            this.tabCountList = this.productSelection?.tabCountList || [0, 0, 0, 0];
          } else {
            this.tabCountList = [0, 0, 0, 0];
          }
        }
      },

      // 获取产品的数据
      async getQryAmendProduct() {
        let orderedData = [];
        this.mainProductSpinning = true;
        const orderedRes = await qryAmendProduct({
          ORDER_ID: this.orderId,
          ORDER_LINE_ID: this.orderLineId,
        });
        orderedData = orderedRes?.DATA[0][this.orderLineId] || [];

        // 更新当前项 存储已订购的数据
        const productInfo = {
          MainProduct: orderedData.filter(item => item.PRODUCT_MODE == '00'), // 已订购的主产品
          AdditionalProduct: convertToTreeData(
            orderedData.filter(item => item.PRODUCT_MODE != '00'),
            '1',
          ), // 已订购的附加产品
          lineInstallNumber: this.lineInstallNumber,
        };
        this.$store.dispatch('quotation/setProductSelection', {
          ...this.productSelection,
          index: this.$route.query.index || -1,
          productInfo,
        });
        return orderedData;
      },

      // 详情回显主产品数据处理
      async dealEditMainData(orderedData) {
        // 1、查询已订购的产品
        console.log('dealEditMainData');
        let mainProductObj = orderedData.find(item => item.PRODUCT_MODE == '00');

        // 2、查询已订购的产品的全量产品
        const params = {
          EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
          STAFF_ID: this.userInfo.STAFF_ID,
          PRODUCT_ID: mainProductObj.PRODUCT_ID,
          TRADE_TYPE_CODE: '10',
          PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
          DEFAULT_TAG: '0',
        };

        const allRes = await defprodandmutex(params);
        let allData = allRes?.DATA || [];

        // 3、将已订购的产品和全量产品进行对比，回显数据
        // 报价单阶段需要展示全部产品，AF单阶段展示选中的产品
        const showData =
          this.fromPage == 'orderCapture'
            ? addCheckedField(allData, orderedData, false)
            : addCheckedField(allData, orderedData); // 全量的产品数据
        const mainProductList = showData.filter(item => item.PRODUCT_MODE == '00'); // 筛选出主产品

        // 4、将数据转换成产品树组件的数据格式
        const mainProductTreeData = convertToTreeData(mainProductList, '1');

        let m_list = await dealProductElementAttrList(mainProductTreeData);
        this.mainProductSpinning = false;
        this.mainProductList = m_list;

        //AF单阶段，展开选中的内容
        if (this.fromPage == 'orderCapture') {
          // 全部展开显示的key值
          let keysList = [];
          this.mainProductList.forEach(item => {
            // 将产品和包的key筛选出来，用作全部层级展开显示
            keysList.push(item.key);
            if (item.children && item.children.length > 0) {
              item.children.forEach(child => {
                keysList.push(child.key);
              });
            }
          });
          // 全部展开显示的key值
          this.mainProductExpandedRowKeys = keysList;
        }
      },

      // 根据产品ID获取对应的产品数据
      async getIdForProductData(PRODUCT_ID) {
        // 2、查询已订购的产品的全量产品
        const params = {
          EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
          STAFF_ID: this.userInfo.STAFF_ID,
          PRODUCT_ID: PRODUCT_ID,
          TRADE_TYPE_CODE: '10',
          PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
          DEFAULT_TAG: '0',
        };
        const allRes = await defprodandmutex(params);
        const allData = allRes?.DATA || [];
        return allData;
      },

      // 详情回显附加产品数据处理
      async dealEditAdditionalProduct(arr) {
        try {
          // 先将每个已订购的附加产品的全量数据查询出来
          let allData = [];
          this.additionalProductSpinning = true;
          const AdditionalProduct = this.productSelection.productInfo?.AdditionalProduct || [];
          for (let i = 0; i < AdditionalProduct.length; i++) {
            const list = await this.getIdForProductData(AdditionalProduct[i].PRODUCT_ID);
            allData.push({
              ...list[0],
              PRODUCT_TYPE_CODE: AdditionalProduct[i].PRODUCT_TYPE_CODE,
            });
          }

          // 将已订购的产品和全量产品进行对比，回显数据
          const showData =
            this.fromPage == 'orderCapture'
              ? addCheckedField(allData, AdditionalProduct, false)
              : addCheckedField(allData, AdditionalProduct);

          // 将数据转换成产品树组件的数据格式
          const treeData = convertToTreeData(showData);

          let ids = treeData.map(item => item.PRODUCT_ID);
          let newList = arr.filter(item => !ids.includes(item.PRODUCT_ID || item.ID));
          newList = newList.map(item => {
            return {
              key: item.ID, // 展开的绑定的值
              type: 'Product', // 展示的type类型
              checked: false, // 默认不选中
              children: [], // 主产品下的数据
              ...item,
            };
          });

          // AF单数据处理，选中的数据不可更改，并且展开选中的数据
          if (this.fromPage == 'orderCapture') {
            let keysList = [];
            treeData.forEach(item => {
              // 将产品和包的key筛选出来，用作全部层级展开显示
              if (item.checked) {
                item.FORCE_TAG = '1';
                item.DIS_EDIT_TAG = '1';
              }
              keysList.push(item.key);
              if (item.children && item.children.length > 0) {
                item.children.forEach(child => {
                  keysList.push(child.key);
                  if (child.checked) {
                    child.FORCE_TAG = '1';
                    child.DIS_EDIT_TAG = '1';
                  }
                  if (child.children && child.children.length > 0) {
                    child.children.forEach(it => {
                      if (it.checked) {
                        it.FORCE_TAG = '1';
                        it.DIS_EDIT_TAG = '1';
                      }
                    });
                  }
                });
              }
            });
            // 全部展开显示的key值
            this.additionalProductExpandedRowKeys = keysList;
            let AdditionalProduct = treeData.filter(
              item => !['300010', '300013'].includes(item.PRODUCT_TYPE_CODE),
            );
            let EquipmentProduct = treeData.filter(item => item.PRODUCT_TYPE_CODE === '300010');
            let PremiumProduct = treeData.filter(item => item.PRODUCT_TYPE_CODE === '300013');
            newList = newList.filter(
              item => !['300010', '300013'].includes(item.PRODUCT_TYPE_CODE),
            );
            let additionalProductList = [
              ...AdditionalProduct,
              ...EquipmentProduct,
              ...PremiumProduct,
              ...newList,
            ];
            let a_list = await dealProductElementAttrList(additionalProductList);
            this.additionalProductSpinning = false;
            // 赋值展示
            this.additionalProductList = a_list;
          } else {
            let additionalProductList = [...treeData, ...newList];
            let a_list = await dealProductElementAttrList(additionalProductList);
            this.additionalProductSpinning = false;
            // 赋值展示
            this.additionalProductList = a_list;
          }
        } catch (err) {
          console.log('err', err);
        }
      },

      // 获取主产品数据
      getMainProduct() {
        if (this.isEdit) {
          // 报价单产品信息
          if (
            this.fromPage === 'quotation' &&
            this.productSelection.productInfo.MainProduct.length > 0
          ) {
            const list = this.productSelection.productInfo.MainProduct.map(item => {
              return {
                key: item.ID, // 展开的绑定的值
                type: 'Product', // 展示的type类型
                checked: item.checked || false, // 默认不选中
                children: item.children || [], // 主产品下的数据
                ...item,
              };
            });
            this.mainProductList = list;
          }
          // AF单产品信息
          if (
            this.fromPage === 'orderCapture' &&
            this.productSelection.currentShowTreeMap.mainProduct.length > 0
          ) {
            const list = this.productSelection.currentShowTreeMap.mainProduct.map(item => {
              return {
                key: item.ID, // 展开的绑定的值
                type: 'Product', // 展示的type类型
                checked: item.checked || false, // 默认不选中
                children: item.children || [], // 主产品下的数据
                ...item,
              };
            });
            this.mainProductList = list;
          }
        } else {
          const data = {
            STAFF_ID: this.userInfo.STAFF_ID,
            EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
            PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
            PRODUCT_IDS: [this.$route.query?.productId],
            PRODUCT_TYPE_CONDITIONS: [
              {
                PRODUCT_TYPE_CODE: this.$route.query?.productType,
                PRODUCT_TYPE_CODE_ROOT: this.$route.query?.productTypeRoot,
              },
            ],
            FORM: 1,
            SIZE: 9999,
          };
          this.mainProductSpinning = true;
          searchMainProduct(data)
            .then(res => {
              const list = res.DATA[0]?.QUERY_RESPONSE.map(item => {
                return {
                  key: item.ID, // 展开的绑定的值
                  type: 'Product', // 展示的type类型
                  checked: false, // 默认不选中
                  children: [], // 主产品下的数据
                  ...item,
                };
              });
              this.mainProductList = list;
            })
            .finally(() => {
              this.mainProductSpinning = false;
            });
        }
      },
      // 请求附加产品数据 manualTriggering代码触发还是点击触发
      getAdditionalProduct(mainProductId, codeTriggering) {
        const orderedAdditionalProduct = this.productSelection.productInfo?.AdditionalProduct || [];
        if (this.isEdit && orderedAdditionalProduct.length > 0) {
          if (this.isSaveDatabase) {
            if (codeTriggering) {
              this.getAdditionProductData(mainProductId);
            }
          } else {
            const list = orderedAdditionalProduct.map(item => {
              return {
                key: item.ID, // 展开的绑定的值
                type: 'Product', // 展示的type类型
                checked: item.checked || false, // 默认不选中
                children: item.children || [], // 主产品下的数据
                ...item,
              };
            });
            this.additionalProductList = list;
          }
        } else {
          this.getAdditionProductData(mainProductId);
        }
      },
      // 请求附加产品数据
      getAdditionProductData(mainProductId) {
        const data = {
          'PRODUCT_ID_B': mainProductId,
          'PRODUCT_STATUS': '4',
          'EPARCHY_CODE': this.userInfo.EPARCHY_CODE,
          'PROVINCE_CODE': this.userInfo.PROVINCE_CODE,
          'SEARCH_TEXT': '',
          'SIZE': 10,
          'STAFF_ID': this.userInfo.STAFF_ID,
        };
        const premiumData = {
          'PRODUCT_ID_B': mainProductId,
          'PRODUCT_TYPE_CONDITIONS': [
            {
              'PRODUCT_TYPE_CODE': '300013', //必传产品类型编码（礼品300013）
            },
          ],
          'SALE_TEAM': 'WLTESTT', // TODO团队信息
        };
        // 附件产品的礼品数据单独请求，需要传参销售团队, 大客户没有礼品, AF单不做礼品请求，直接用详情数据
        let requests = [searchAddition(data), queryAdditionPremium(premiumData)];
        if (this.isCORP || this.fromPage == 'orderCapture') {
          requests = [searchAddition(data)];
        }
        Promise.all(requests)
          .then(([addRes, premiumRes]) => {
            const addProduct = addRes?.DATA[0]?.QUERY_RESPONSE || [];
            const finalData = [
              ...addProduct.filter(item => item.PRODUCT_TYPE_CODE != '300013'),
              ...(premiumRes?.DATA[0]?.QUERY_RESPONSE || []),
            ];

            if (this.isEdit && this.isSaveDatabase) {
              this.dealEditAdditionalProduct(finalData);
            } else {
              const list = finalData?.map(item => {
                return {
                  key: item.ID, // 展开的绑定的值
                  type: 'Product', // 展示的type类型
                  checked: false, // 默认不选中
                  children: [], // 主产品下的数据
                  ...item,
                };
              });
              this.additionalProductList = list;
            }
          })
          .catch(error => {
            // 捕获请求错误并输出到控制台
            console.error('获取订单详情出错:', error);
          })
          .finally(() => {});
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },

      // 递归遍历主产品 查找产品 -> 包 -> 服务 -> SERV_ITEM-ATTR_CODE === 'siteWorkAppointment'的值
      getSiteWorkAppointmentValue(items) {
        for (const item of items) {
          if (item.type === 'Product' && item.checked) {
            if (item.children && item.children.length > 0) {
              const result = this.getSiteWorkAppointmentValue(item.children);
              if (result) return result;
            }
          }

          if (item.type === 'Package' && item.checked) {
            if (item.children && item.children.length > 0) {
              const result = this.getSiteWorkAppointmentValue(item.children);
              if (result) return result;
            }
          }

          if (item.type === 'Vas' && item.checked) {
            if (item.SERV_ITEM) {
              for (const servItem of item.SERV_ITEM) {
                if (servItem.ATTR_CODE === 'siteWorkAppointment') {
                  return servItem.ATTR_VALUE;
                }
              }
            }
          }
        }
        return null;
      },

      // 校验 => 通过 => 存储当前页面数据到vuex
      validate() {
        return new Promise((resolve, reject) => {
          // OSCA新地址格式保存 - 用的是form.newFormatData
          // #TODO: 后续再根据实际保存格式，是否调整成只用newFormatData

          const addressInfo =
            this.fromPage === 'quotation'
              ? this.$refs.AddressMappingFormRef.form
              : this.productSelection.addressInfo;

          // 获取组件里的产品数据
          let productInfo = this.$refs.ProductTreeTabs.getSubComponentProductTreeList();
          // 勾选的产品数据
          const selectedMainProduct = (productInfo?.MainProduct || []).filter(item => item.checked);
          const selectedAdditionalProduct = (productInfo?.AdditionalProduct || []).filter(
            item => item.checked,
          );
          // 已选择的产品数据
          const mainProductTreeSelectedList = getMainProductTreeSelectedList(selectedMainProduct);
          const additionalProductTreeSelectedList =
            getMainProductTreeSelectedList(selectedAdditionalProduct);

          const validateError = validateProductTreeRules(productInfo);
          // 线路安装数量
          this.lineInstallNumber = String(this.$refs.ProductTreeTabs.lineInstallNumber);
          // 7/9调整：地址选址不校验且移除必填
          // if (!addressInfo.SB_ADDRESS) {
          //   // 是否选址
          //   this.showTipModal(this.$t('customerVerify.tipsText2'));
          // } else
          if (!this.lineInstallNumber || this.$refs.ProductTreeTabs.lineInstallNumber <= 0) {
            // 是否填写已安装数量
            this.showTipModal(this.$t('customerVerify.tipsText7'));
          } else if (validateError) {
            this.showTipModal(validateError);
          } else {
            // vas和价格数量保存
            const tabCountList = this.$refs.ProductTreeTabs.tabList.map(item => item.count);
            // 获取该值，为后续施工预约下单入参使用
            const siteWorkAppointment = this.getSiteWorkAppointmentValue(
              mainProductTreeSelectedList,
            );
            productInfo = {
              ...productInfo,
              // 主产品，产品树状结构
              mainProductTreeSelectedList,
              // 附加产品，产品树状结构
              additionalProductTreeSelectedList,
            };

            const productSelection = {
              index: this.$route.query.index || -1,
              addressInfo,
              productInfo,
              tabCountList,
              siteWorkAppointment,
              lineInstallNumber: this.lineInstallNumber,
              // 额外补充 - 用于详情显示计算结果，减少详情的开发
              totalMRCForAllLine: this.productMrcOtcData.MRCtotal,
              totalOTCForAllLine: this.productMrcOtcData.OTCtotal,
              totalMRCForAllLinePeriod: this.productMrcOtcData.TotalMRCAllPeriod,
              redeemablePoints: this.$refs.ProductTreeTabs.selectedRedeemablePoints,
            };
            this.$store.dispatch('quotation/setProductSelection', productSelection);
            // 保存产品信息
            this.$store.dispatch('quotation/setProductStatus', this.$route.query?.status || '');
            resolve();
          }
        });
      },
      // 确认产品选择
      async confirm() {
        await this.validate();
        this.$router.customBack();
      },
    },
  };
</script>
<style lang="less" scoped>
  .productSelection {
    // 兼顾屏幕小的时候，防止底部栏挡住元素
    padding-bottom: 80px;
    .pageTitle {
      color: #333333;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }

  .product-footer-btn {
    width: 120px;
    height: 40px;
    font-size: 18px;
  }

  // .footer-tool-bar {
  //   button + button {
  //     margin-left: 10px;
  //   }
  // }
</style>
