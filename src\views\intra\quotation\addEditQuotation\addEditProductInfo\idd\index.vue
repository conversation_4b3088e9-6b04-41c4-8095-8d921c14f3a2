<template>
  <div>
    <!-- <div class="topReturnBtn" @click="goBack">
      <a-icon type="left" class="icon" />
      <span>{{ $t('common.return') }}</span>
      <span class="select-product-text">{{ $t('customerVerify.oscaNewProductTitle') }}</span>
    </div> -->
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>
    <div class="productSelection">
      <!-- <div class="pageTitle">{{ pageTitle }}</div> -->

      <!-- 标题 -->
      <!-- <div class="common-custom-title">{{ $t('customerVerify.ProductSelection') }}</div> -->

      <!-- 选产品 -->
      <ProductTreeTabs
        ref="ProductTreeTabs"
        :tabCountList="tabCountList"
        :mainProductList="mainProductList"
        :mainProductSpinning="mainProductSpinning"
        :additionalProductList="additionalProductList"
        :additionalProductSpinning="additionalProductSpinning"
        @getAdditionalProduct="getAdditionalProduct"
        @getProductMrcOtcData="handleProductMrcOtcData"
      />
      <!-- 价格计算 -->
      <div class="footer-tool-bar">
        <footer-tool-bar-price :productMrcOtcData="productMrcOtcData">
          <a-button ghost type="primary" @click="cancel" class="reset-button product-footer-btn">
            {{ $t('customerVerify.Cancel') }}
          </a-button>
          <a-button type="primary" @click="confirm" class="search-button product-footer-btn">
            {{ $t('customerVerify.Confirm') }}
          </a-button>
        </footer-tool-bar-price>
      </div>

      <!-- 校验提示 -->
      <MessageModal
        :visible="tipsVisible"
        :message="tipsMessage"
        @cancel="tipsVisible = false"
        @confirm="tipsVisible = false"
      />
    </div>
  </div>
</template>

<script>
  import { qryAmendProduct, queryAdditionPremium } from '@/api/quotation';
  import { searchMainProduct, searchAddition, defprodandmutex } from '@/api/customerVerify';
  import { getMainProductTreeSelectedList } from '@/views/components/productTreeList/common/utils';
  import {
    addCheckedField,
    convertToTreeData,
    dealProductElementAttrList,
    getProductInfoList,
    getProductAttrInfoList,
    getElementInfoList,
    getElementAttrInfoList,
  } from '@/views/intra/changeProduct.js';
  import { validateProductTreeRules } from '@/views/components/productTreeList/nextStepValidate/nextStepValidate';
  import PageHeader from '@/views/components/pageHeader/index.vue';
  import MessageModal from '@/components/messageModal';
  import FooterToolBarPrice from '@/views/components/footerToolbarPrice';
  import ProductTreeTabs from '@/views/components/productTreeList/tabList';
  import { mapState } from 'vuex';
  import { mixins } from '../mixins/index';
  export default {
    name: 'ProductSelection',
    components: {
      PageHeader,
      ProductTreeTabs,
      MessageModal,
      FooterToolBarPrice,
    },
    props: {
      pageTitle: {
        type: String,
        default: '',
      },
    },

    data() {
      return {
        mainProductList: [], //主产品
        additionalProductList: [], //附加产品
        tabCountList: [], //vas和price数量
        tipsVisible: false,
        tipsMessage: '',
        mainProductSpinning: false,
        additionalProductSpinning: false,
        productType: {},
      };
    },
    mixins: [mixins],
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
      ...mapState('quotation', {
        productSelection: state => state.productSelection,
      }),

      // 上一页页面信息
      prePageInfo() {
        return this.$t('comp.ProductSelection');
      },
      // 当前状态是否已经后台保存过
      isSaveDatabase() {
        return this.$route.query?.orderLineId;
      },
    },
    mounted() {
      this.getTabCountList();
      if (this.isEdit) {
        if (this.isSaveDatabase) {
          // 后台保存过的产品数据回显
          this.dealEditMainData();
        } else {
          this.getMainProduct();
        }
      } else {
        // 获取主产品数据
        this.getMainProduct();
      }
    },
    methods: {
      // 获取vas和price数量
      getTabCountList() {
        if (this.isEdit) {
          this.tabCountList = this.productSelection?.tabCountList || [0, 0, 0, 0];
        } else {
          this.tabCountList = [0, 0, 0, 0];
        }
      },

      // 详情回显产品数据处理
      async dealEditMainData() {
        // 1、查询已订购的产品
        console.log('dealEditMainData');
        let orderedData = [];
        this.mainProductSpinning = true;
        const orderedRes = await qryAmendProduct({
          ORDER_ID: this.orderId,
          ORDER_LINE_ID: this.orderLineId,
        });
        orderedData = orderedRes?.DATA[0][this.orderLineId] || [];
        let mainProductObj = orderedData.find(item => item.PRODUCT_MODE == '00');

        // 获取选中vas和price的总量
        let countList = [0, 0, 0, 0];
        orderedData.forEach(item => {
          const tempItem = item.PRODUCT_ITEM_INFO.find(
            item => item.ATTR_CODE === 'price_element_count',
          );
          if (item.PRODUCT_MODE == '00') {
            countList.splice(0, 1, tempItem?.ATTR_VALUE);
          } else {
            const productType = item.PRODUCT_TYPE_CODE;
            if (productType == '300010') {
              // 设备
              countList.splice(2, 1, tempItem?.ATTR_VALUE);
            } else if (productType == '300013') {
              // 礼品
              countList.splice(3, 1, tempItem?.ATTR_VALUE);
            } else {
              countList.splice(1, 1, tempItem?.ATTR_VALUE);
            }
          }
        });
        this.tabCountList = countList;

        // 更新当前项 存储已订购的数据
        const productInfo = {
          MainProduct: orderedData.filter(item => item.PRODUCT_MODE == '00'), // 已订购的主产品
          AdditionalProduct: convertToTreeData(
            orderedData.filter(item => item.PRODUCT_MODE != '00'),
            '1',
          ), // 已订购的附加产品
        };

        this.$store.dispatch('quotation/setProductSelection', {
          ...this.productSelection,
          index: this.$route.query.index || -1,
          productInfo,
        });

        // 2、查询已订购的产品的全量产品
        const params = {
          EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
          STAFF_ID: this.userInfo.STAFF_ID,
          PRODUCT_ID: mainProductObj.PRODUCT_ID,
          TRADE_TYPE_CODE: '10',
          PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
          DEFAULT_TAG: '0',
        };

        const allRes = await defprodandmutex(params);
        const allData = allRes?.DATA || [];

        // 3、将已订购的产品和全量产品进行对比，回显数据
        const showData = addCheckedField(allData, orderedData); // 全量的产品数据
        const mainProductList = showData.filter(item => item.PRODUCT_MODE == '00'); // 筛选出主产品

        // 4、将数据转换成产品树组件的数据格式
        const mainProductTreeData = convertToTreeData(mainProductList, '1');

        let m_list = await dealProductElementAttrList(mainProductTreeData);
        this.mainProductSpinning = false;
        this.mainProductList = m_list;
      },

      // 根据产品ID获取对应的产品数据
      async getIdForProductData(PRODUCT_ID) {
        // 2、查询已订购的产品的全量产品
        const params = {
          EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
          STAFF_ID: this.userInfo.STAFF_ID,
          PRODUCT_ID: PRODUCT_ID,
          TRADE_TYPE_CODE: '10',
          PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
          DEFAULT_TAG: '0',
        };
        const allRes = await defprodandmutex(params);
        const allData = allRes?.DATA || [];
        return allData;
      },

      // 处理附加产品
      async dealEditAdditionalProduct(arr) {
        try {
          // 先将每个已订购的附加产品的全量数据查询出来
          let allData = [];
          this.additionalProductSpinning = true;
          const AdditionalProduct = this.productSelection.productInfo?.AdditionalProduct || [];
          for (let i = 0; i < AdditionalProduct.length; i++) {
            const list = await this.getIdForProductData(AdditionalProduct[i].PRODUCT_ID);
            allData.push({
              ...list[0],
              PRODUCT_TYPE_CODE: AdditionalProduct[i].PRODUCT_TYPE_CODE,
            });
          }

          // 将已订购的产品和全量产品进行对比，回显数据
          const showData = addCheckedField(allData, AdditionalProduct);

          // 将数据转换成产品树组件的数据格式
          const treeData = convertToTreeData(showData);

          let ids = treeData.map(item => item.PRODUCT_ID);
          let newList = arr.filter(item => !ids.includes(item.PRODUCT_ID || item.ID));
          newList = newList.map(item => {
            return {
              key: item.ID, // 展开的绑定的值
              type: 'Product', // 展示的type类型
              checked: false, // 默认不选中
              children: [], // 主产品下的数据
              ...item,
            };
          });

          let additionalProductList = [...treeData, ...newList];
          console.log('合并后的数据', additionalProductList);
          let a_list = await dealProductElementAttrList(additionalProductList);
          this.additionalProductSpinning = false;
          // 赋值展示
          this.additionalProductList = a_list;
          console.log('additionalProductList', additionalProductList);
        } catch (err) {
          console.log('err', err);
        }
      },

      // 获取主产品数据
      getMainProduct() {
        if (this.isEdit && this.productSelection.productInfo.MainProduct.length > 0) {
          const list = this.productSelection.productInfo.MainProduct.map(item => {
            return {
              key: item.ID, // 展开的绑定的值
              type: 'Product', // 展示的type类型
              checked: item.checked || false, // 默认不选中
              children: item.children || [], // 主产品下的数据
              ...item,
            };
          });
          this.mainProductList = list;
        } else {
          const data = {
            STAFF_ID: this.userInfo.STAFF_ID,
            EPARCHY_CODE: this.userInfo.EPARCHY_CODE,
            PROVINCE_CODE: this.userInfo.PROVINCE_CODE,
            PRODUCT_IDS: [this.$route.query?.productId],
            PRODUCT_TYPE_CONDITIONS: [
              {
                PRODUCT_TYPE_CODE: this.$route.query?.productType,
                PRODUCT_TYPE_CODE_ROOT: this.$route.query?.productTypeRoot,
              },
            ],
            FORM: 1,
            SIZE: 9999,
          };
          this.mainProductSpinning = true;
          searchMainProduct(data)
            .then(res => {
              const list = res.DATA[0]?.QUERY_RESPONSE.map(item => {
                return {
                  key: item.ID, // 展开的绑定的值
                  type: 'Product', // 展示的type类型
                  checked: false, // 默认不选中
                  children: [], // 主产品下的数据
                  ...item,
                };
              });
              this.mainProductList = list;
            })
            .finally(() => {
              this.mainProductSpinning = false;
            });
        }
      },
      // 请求附加产品数据
      getAdditionalProduct(mainProductId) {
        const orderedAdditionalProduct = this.productSelection.productInfo?.AdditionalProduct || [];
        if (this.isEdit && orderedAdditionalProduct.length > 0) {
          if (this.isSaveDatabase) {
            this.getAdditionProductData(mainProductId);
          } else {
            const list = orderedAdditionalProduct.map(item => {
              return {
                key: item.ID, // 展开的绑定的值
                type: 'Product', // 展示的type类型
                checked: item.checked || false, // 默认不选中
                children: item.children || [], // 主产品下的数据
                ...item,
              };
            });
            this.additionalProductList = list;
          }
        } else {
          this.getAdditionProductData(mainProductId);
        }
      },
      // 请求附加产品数据
      getAdditionProductData(mainProductId) {
        const data = {
          'PRODUCT_ID_B': mainProductId,
          'PRODUCT_STATUS': '4',
          'EPARCHY_CODE': this.userInfo.EPARCHY_CODE,
          'PROVINCE_CODE': this.userInfo.PROVINCE_CODE,
          'SEARCH_TEXT': '',
          'SIZE': 10,
          'STAFF_ID': this.userInfo.STAFF_ID,
        };
        const premiumData = {
          'PRODUCT_ID_B': mainProductId,
          'PRODUCT_TYPE_CONDITIONS': [
            {
              'PRODUCT_TYPE_CODE': '300013', //必传产品类型编码（礼品300013）
            },
          ],
          'SALE_TEAM': 'WLTESTT', // TODO团队信息
        };
        // 附件产品的礼品数据单独请求，需要传参销售团队
        Promise.all([searchAddition(data), queryAdditionPremium(premiumData)])
          .then(([addRes, premiumRes]) => {
            const addProduct = addRes?.DATA[0]?.QUERY_RESPONSE || [];
            const finalData = [
              ...addProduct.filter(item => item.PRODUCT_TYPE_CODE != '300013'),
              ...(premiumRes.DATA[0]?.QUERY_RESPONSE || []),
            ];

            if (this.isEdit && this.isSaveDatabase) {
              this.dealEditAdditionalProduct(finalData);
            } else {
              const list = finalData?.map(item => {
                return {
                  key: item.ID, // 展开的绑定的值
                  type: 'Product', // 展示的type类型
                  checked: false, // 默认不选中
                  children: [], // 主产品下的数据
                  ...item,
                };
              });
              this.additionalProductList = list;
            }
          })
          .catch(error => {
            // 捕获请求错误并输出到控制台
            console.error('获取订单详情出错:', error);
          })
          .finally(() => {});
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },

      // 递归遍历主产品 查找产品 -> 包 -> 服务 -> SERV_ITEM-ATTR_CODE === 'siteWorkAppointment'的值
      getSiteWorkAppointmentValue(items) {
        for (const item of items) {
          if (item.type === 'Product' && item.checked) {
            if (item.children && item.children.length > 0) {
              const result = this.getSiteWorkAppointmentValue(item.children);
              if (result) return result;
            }
          }

          if (item.type === 'Package' && item.checked) {
            if (item.children && item.children.length > 0) {
              const result = this.getSiteWorkAppointmentValue(item.children);
              if (result) return result;
            }
          }

          if (item.type === 'Vas' && item.checked) {
            if (item.SERV_ITEM) {
              for (const servItem of item.SERV_ITEM) {
                if (servItem.ATTR_CODE === 'siteWorkAppointment') {
                  return servItem.ATTR_VALUE;
                }
              }
            }
          }
        }
        return null;
      },
      //订单报文信息
      getOrderInfo(productInfo, productSelection) {
        // SERIAL_NUMBER 服务号码-1； SCENE_TYPE 场景类型，10000-新开；TRADE_TYPE_CODE 订单业务类型：10-商品订购；NET_TYPE_CODE 电信类型，本期-1
        const PRODUCT_INFOLIST = getProductInfoList(productInfo);
        const PRODUCT_ATTR_INFOLIST = getProductAttrInfoList(
          productInfo,
          null,
          '1000',
          productSelection.tabCountList,
        );
        const ELEMENT_INFOLIST = getElementInfoList(productInfo);
        const ELEMENT_ATTR_INFOLIST = getElementAttrInfoList(productInfo);

        const orderLineInfo = {
          PRODUCT_INFO: PRODUCT_INFOLIST,
          PRODUCT_ATTR_INFO: PRODUCT_ATTR_INFOLIST,
          ELEMENT_INFO: ELEMENT_INFOLIST,
          ELEMENT_ATTR_INFO: ELEMENT_ATTR_INFOLIST,
        };
        return orderLineInfo;
      },

      // 校验 => 通过 => 存储当前页面数据到vuex
      validate() {
        return new Promise((resolve, reject) => {
          // OSCA新地址格式保存 - 用的是form.newFormatData
          // #TODO: 后续再根据实际保存格式，是否调整成只用newFormatData

          // 获取组件里的产品数据
          let productInfo = this.$refs.ProductTreeTabs.getSubComponentProductTreeList();
          // 勾选的产品数据
          const selectedMainProduct = (productInfo?.MainProduct || []).filter(item => item.checked);
          const selectedAdditionalProduct = (productInfo?.AdditionalProduct || []).filter(
            item => item.checked,
          );
          // 已选择的产品数据
          const mainProductTreeSelectedList = getMainProductTreeSelectedList(selectedMainProduct);
          const additionalProductTreeSelectedList =
            getMainProductTreeSelectedList(selectedAdditionalProduct);

          mainProductTreeSelectedList.forEach(item => {
            if (item.key === '91600078') {
              // product
              item.children?.forEach(iitem => {
                // package
                if (iitem.key === '91600078-51600179') {
                  iitem.children?.forEach(iiitem => {
                    // element
                    if (iiitem.key === '91600078-51600179-31600523') {
                      if (
                        !iiitem.interfaceElementList ||
                        !Array.isArray(iiitem.interfaceElementList) ||
                        iiitem.interfaceElementList.length < 1
                      ) {
                        // this.showTipModal('CCP税率不能为空！');
                        this.showTipModal(
                          this.$t('customerVerify.ccpTaxRate', {
                            productName: item.NAME,
                            packageName: iitem.NAME,
                            elementName: iiitem.NAME,
                          }),
                        );
                        reject();
                      }
                    }
                  });
                }
              });
            }
          });

          const validateError = validateProductTreeRules(productInfo);
          if (validateError) {
            // 是否勾选主产品
            this.showTipModal(validateError);
            reject();
          } else {
            // vas和价格数量保存
            const tabCountList = this.$refs.ProductTreeTabs.tabList.map(item => item.count);
            // 获取该值，为后续施工预约下单入参使用
            const siteWorkAppointment = this.getSiteWorkAppointmentValue(
              mainProductTreeSelectedList,
            );
            productInfo = {
              ...productInfo,
              // 主产品，产品树状结构
              mainProductTreeSelectedList,
              // 附加产品，产品树状结构
              additionalProductTreeSelectedList,
            };

            const productSelection = {
              index: this.$route.query.index || -1,
              productInfo,
              tabCountList,
              siteWorkAppointment,
            };

            // 产品信息的提交报文信息更改
            let mainCommitProductInfo = {};
            if (mainProductTreeSelectedList.length > 0) {
              mainCommitProductInfo = this.getOrderInfo(
                mainProductTreeSelectedList,
                productSelection,
              );
            } else {
              //没有更改，获取详情页保存的产品信息的提交报文
              if (this.isSaveDatabase) {
                mainCommitProductInfo =
                  this.productSelection.commitProductInfo.mainCommitProductInfo;
              }
            }
            let addCommitProductInfo = {};
            if (additionalProductTreeSelectedList.length > 0) {
              addCommitProductInfo = this.getOrderInfo(
                additionalProductTreeSelectedList,
                productSelection,
              );
            } else {
              if (this.isSaveDatabase) {
                addCommitProductInfo = this.productSelection.commitProductInfo.addCommitProductInfo;
              }
            }

            // 当前产品的产品信息
            this.$store.dispatch('quotation/setProductSelection', {
              index: this.$route.query.index || -1,
              productInfo,
              tabCountList,
              siteWorkAppointment,
              commitProductInfo: {
                NET_TYPE_CODE: '-1',
                TRADE_TYPE_CODE: '10',
                SCENE_TYPE: '10000',
                SERIAL_NUMBER: '-1',
                mainCommitProductInfo: mainCommitProductInfo,
                addCommitProductInfo: addCommitProductInfo,
              },
            });
            // 保存产品信息
            this.$store.dispatch('quotation/setProductStatus', this.$route.query?.status || '');
            resolve();
          }
        });
      },
      goBack() {
        if (this.$route.query?.status == 'add') {
          this.$store.dispatch('quotation/setProductSelection', {});
        }
        this.$router.customBack();
      },
      // 确认产品选择
      async confirm() {
        await this.validate();
        // this.tipsVisible = true;
        // this.tipsMessage = this.$t('customerVerify.tipsText3');
        this.$router.customBack();
      },
    },
  };
</script>
<style lang="less" scoped>
  .productSelection {
    // 兼顾屏幕小的时候，防止底部栏挡住元素
    padding-bottom: 80px;
    .pageTitle {
      color: #333333;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 10px;
    }
  }

  .product-footer-btn {
    width: 120px;
    height: 40px;
    font-size: 18px;
  }

  // .footer-tool-bar {
  //   button + button {
  //     margin-left: 10px;
  //   }
  // }
</style>
