import { TAG_VALUE_FTG } from '@/views/components/productTreeList/common/sign';
export const mixins = {
  data() {
    return {
      //产品价格
      productMrcOtcData: {
        MRCtitle: this.$t('quotation.TotalMRCForAllLine'),
        MRCtotal: 0,
        OTCtitle: this.$t('quotation.TotalOTCForAllLine'),
        OTCtotal: 0,
        TotalMRCAllPeriodTitle: this.$t('quotation.TotalMRCForAllLinePeriod'),
        TotalMRCAllPeriod: 0,
      },
    };
  },
  computed: {
    // 当前状态是否为编辑
    isEdit() {
      return this.$route.query?.status === 'edit';
    },

    // 订单编号
    orderId() {
      const { orderId } = this.$route.query;
      return orderId;
    },

    // 订单行编号
    orderLineId() {
      const { orderLineId } = this.$route.query;
      return orderLineId;
    },
  },
  methods: {
    // 产品价格计算
    handleProductMrcOtcData({ MRCtotal = 0, OTCtotal = 0, TotalMRCAllPeriod = 0 }) {
      this.productMrcOtcData.MRCtotal = parseFloat(MRCtotal).toFixed(2) || 0;
      this.productMrcOtcData.OTCtotal = parseFloat(OTCtotal).toFixed(2) || 0;
      this.productMrcOtcData.TotalMRCAllPeriod =
        TotalMRCAllPeriod != TAG_VALUE_FTG
          ? parseFloat(TotalMRCAllPeriod).toFixed(2) || 0
          : TAG_VALUE_FTG;
    },

    // 取消产品选择
    cancel() {
      if (this.$route.query?.status == 'add') {
        this.$store.dispatch('quotation/setProductSelection', {});
      }
      this.$router.customBack();
    },
  },
};
