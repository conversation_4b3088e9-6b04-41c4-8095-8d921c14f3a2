<template>
  <div>
    <div class="header">
      <div class="secondLevel-header-title">
        {{ $t('customerVerify.oscaNewProductTitle') }}
      </div>
    </div>
    <div
      v-if="productListData.length === 0"
      :class="['addProductBtn', isSelectCustomer ? 'active' : '']"
      @click="addOpen"
    >
      <span class="iconfont icon-tianjia btnIcon"></span>
      <p class="title">{{ $t('customerVerify.AddProductInstall') }}</p>
    </div>

    <div class="productList">
      <div
        class="productContent"
        :class="[isSelectCustomer ? 'active' : 'disabled']"
        v-for="(item, index) in productListData"
        :key="item.id"
      >
        <div class="productItem">
          <div class="text">
            <span
              >{{ index + 1 }} -
              {{
                item.productInfo?.MainProduct[0]?.NAME ||
                item.productInfo?.MainProduct[0]?.PRODUCT_NAME ||
                ''
              }}</span
            >
          </div>
          <div class="btn">
            <span :class="['iconfont icon-xiugai']" @click="editProduct(item, index)"></span>
            <span :class="['iconfont icon-shanchu']" @click="deleteProduct(item, index)"></span>
          </div>
        </div>
      </div>
      <div
        v-if="productListData.length !== 0"
        class="productContent"
        :class="[isSelectCustomer ? 'active' : 'disabled']"
        @click="addOpen"
      >
        <div class="noProductItem">
          <div class="btn">
            <span :class="['iconfont icon-tianjia btn']"></span>
          </div>
          <div class="text">
            {{ $t('customerVerify.AddProductInstall') }}
          </div>
        </div>
      </div>
    </div>
    <!-- 新增产品弹窗 -->
    <SelectProductType
      v-if="addPopWindowVisible"
      :visible="addPopWindowVisible"
      :title="addPopWindowTitle"
      :customerLob="customerLobValue"
      @cancel="addPopWindowVisible = false"
      @Ok="addPopWindowOk"
    />
  </div>
</template>

<script>
  import SelectProductType from './selectProductType.vue';
  import i18n from '@/i18n/index';
  import { mapState } from 'vuex';
  import { gotoAddProductTree } from './addProductTree';

  export default {
    name: 'selProductBox',
    components: { SelectProductType },
    props: {
      customerLobValue: {
        type: Object,
        default: () => {},
      },
      productInfos: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
      // currentProductType() {
      //   return this.productList[this.currentServiceNumberIndex]?.PRODUCT_INFO?.[0]
      //     ?.PRODUCT_NAME;
      // },
      // 订单编号
      orderId() {
        const { orderId } = this.$route.query;
        return orderId;
      },
    },
    watch: {
      productInfos: {
        deep: true,
        immediate: true,
        handler(val) {
          console.log('watch productInfos');
          this.productListData = val;
        },
      },
    },
    data() {
      return {
        spinning: false,
        // 是否已经选择客户
        isSelectCustomer: false,
        // 新增客户单
        addPopWindowVisible: false,
        addPopWindowTitle: i18n.t('customerVerify.SelectProduct'),

        productListData: [],
        orderedProductApiLoadCompleted: false,
      };
    },
    mounted() {
      this.productListData = this.productInfos;
    },
    activated() {
      console.log('addProductBox activated');
      this.productListData = this.productInfos;
    },
    methods: {
      addOpen() {
        if (!this.isSelectCustomer) return;
        this.$store.dispatch('quotation/setCleanState');
        this.addPopWindowVisible = true;
      },
      // 新增产品 产品类型选择后确认
      addPopWindowOk(obj) {
        this.$router.push({
          path: obj.routePath,
          query: {
            status: 'add',
            productFamily: obj.productFamily,
            productTypeRoot: obj.productTypeRoot,
            productType: obj.productType,
            productId: obj.productId,
            fromPage: 'quotation',
          },
        }); //新增产品
        this.addPopWindowVisible = false;
      },
      // 编辑选中的产品
      editProduct(item, index) {
        if (!this.isSelectCustomer) return;
        let currentItem = item?.productInfo.MainProduct[0];
        this.$store.dispatch('quotation/setProductSelection', item);
        this.$store.dispatch('quotation/setProductTypeData', {
          value: currentItem.PRODUCT_TYPE_CODE,
        });
        this.$store.dispatch('quotation/setProductStatus', 'edit');
        const routePath = gotoAddProductTree(currentItem.PRODUCT_TYPE_CODE);
        this.$router.push({
          path: routePath,
          query: {
            status: 'edit',
            index: index,
            orderId: this.orderId,
            orderLineId: currentItem.ORDER_LINE_ID,
            fromPage: 'quotation',
          },
        }); //编辑产品
      },
      // 删除选中的产品
      deleteProduct(item, index) {
        if (!this.isSelectCustomer) return;
        this.productListData.splice(index, 1);
        this.$emit('updateProductInfos', this.productListData);
      },
    },
  };
</script>

<style scoped lang="less">
  .header {
    background-color: #fff;
    box-sizing: border-box;
  }

  .addProductBtn {
    border: 1px dashed #9e9e9e;
    color: #9e9e9e;
    background-color: #e9e9e9;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 80px;
    cursor: pointer;
  }
  .addProductBtn .btnIcon {
    font-size: 30px;
    line-height: 30px;
    color: inherit;
  }
  .addProductBtn .title {
    font-size: 14px;
    color: inherit;
    margin: 6.7px 0 0;
  }

  .addProductBtn.active {
    color: #0076ff;
    background-color: #fff;
    border-color: #0076ff;
  }

  .productContent.active {
    color: #0072ff;
    border: 1px solid rgba(233, 233, 233, 1);
  }

  .productList {
    margin-top: 10px;
    display: flex;
    gap: 4%;
    flex-wrap: wrap;
    .productContent.disabled {
      border: 1px dashed #f8f8f8;
      color: #9e9e9e;
      background-color: #f8f8f8;
      .productItem {
        .text {
          background: #e9e9e9;
        }
      }
    }
    .productContent {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 22%;
      margin-top: 10px;
      border: 1px solid rgba(233, 233, 233, 1);
      height: 160px;
      .productItem {
        margin-top: 30px;
        width: 80%;
        .text {
          text-align: center;
          background: #d2e6ff;
          border-radius: 16px;
          padding: 4px 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          height: 35px;
          span {
            font-size: 18px;
            color: inherit;
            letter-spacing: 0;
            text-align: center;
            font-weight: 400;
          }
        }
        .btn {
          display: flex;
          justify-content: center;
          margin-top: 10px;
          span {
            font-size: 16px;
            padding: 12px;
            line-height: 16px;
            display: inline-block;
            color: inherit;
            cursor: pointer;

            &:hover {
              background: #ebf4ff;
            }
            &:last-child {
              margin-left: 10px;
            }
          }
        }
      }
      .noProductItem {
        .btn {
          display: flex;
          justify-content: center;
          color: inherit;
          span {
            display: inline-block;
            font-size: 30px;
            line-height: 30px;
          }
        }
        .text {
          margin-top: 10px;
          font-size: 14px;
          color: inherit;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
    }
  }
</style>
