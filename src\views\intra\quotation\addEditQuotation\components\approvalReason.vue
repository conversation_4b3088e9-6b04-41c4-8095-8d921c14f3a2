<template>
  <div>
    <!-- 标题 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.ApprovalReason') }}
    </div>
    <span class="approval-reference-title">
      {{ $t('quotation.ApprovalReference') }}
    </span>
    <div class="reason-list">
      <div
        v-for="(item, index) in reasonList"
        :key="index"
        :class="['list-item', item.checked ? 'isSelected' : '']"
        @click.stop="handleClick(item, index)"
      >
        {{ item.DATA_NAME }}
      </div>
    </div>
  </div>
</template>

<script>
  import { queryParamList } from '@/api/common';
  export default {
    name: 'ApprovalReason',
    props: {
      selectReasons: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      selectReasons: {
        handler(newValue, oldValue) {
          if (newValue) {
            this.formatList('DATA_NAME', newValue);
          }
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        reasonList: [],
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      async init() {
        const res = await queryParamList({
          TYPE_ID: 'QUOTATION_APPROVAL_REASON',
        });
        this.reasonList = res.DATA;
      },
      handleClick(item, index) {
        item.checked = !item.checked;
        this.$set(this.reasonList, index, item);
      },
      // 已选回显
      formatList(key, arr) {
        for (let i = 0; i < this.reasonList.length; i++) {
          const ele = this.reasonList[i];
          if (arr.includes(ele[key])) {
            ele.checked = true;
            this.$set(this.reasonList, i, ele);
          }
        }
      },
      // 获取已选的元素
      getSelectedRows() {
        return this.reasonList.filter(i => i.checked);
      },
    },
  };
</script>

<style lang="less" scoped>
  @import '@/styles/mixin.less';
  .approval-reference-title {
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    text-align: left;
    font-weight: 500;
  }
  .reason-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 11px;
    .list-item {
      height: 26px;
      line-height: 26px;
      text-align: center;
      min-width: 280px;
      margin-bottom: 22px;
      margin-right: 30px;
      cursor: pointer;
      position: relative;
      background: #ffffff;
      border: 1px solid rgba(220, 220, 220, 1);
      border-radius: 2px;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 400;
      transition: all 0.3s ease;
      &.isSelected {
        .is-selected__tick();
      }
    }
  }
</style>
