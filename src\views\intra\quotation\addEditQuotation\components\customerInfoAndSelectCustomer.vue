<template>
  <div class="header">
    <!-- 客户信息 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.CustomerInformation') }}
    </div>
    <a-form-model :model="formData" :colon="false" ref="customerForm" :rules="formRules">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.CustomerName')" prop="CUSTOMER_NAME">
            <a-input-search
              v-model.trim="formData.CUSTOMER_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeCustomer"
              @search="handleSearch"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.MarketSegment')" prop="MARKET_SEGMENT">
            <a-input name="MARKET_SEGMENT" v-model="formData.MARKET_SEGMENT" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.LOB')" prop="LOB_NAME" labelAlign="left">
            <a-input name="LOB_NAME" v-model="formData.LOB_NAME" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <!-- 客户选择弹窗 -->
    <QueryCustomerList
      v-if="queryListVisible"
      :visible="queryListVisible"
      :title="queryListTitle"
      :queryData="queryFormData"
      :isCustomerCertificate="false"
      @cancel="queryListCancel"
      @Ok="queryListOk"
    />
    <!-- 信息提示框 -->
    <TipsPopWindow
      v-if="tipsVisible"
      :text="$t('quotation.PleaseEnterCustomerName')"
      :visible="tipsVisible"
      @cancel="tipsVisible = false"
      @Ok="tipsVisible = false"
    />

    <!-- 校验提示 -->
    <MessageModal
      :visible="confirmVisible"
      :message="confirmMessage"
      :displayCancelBtn="true"
      @cancel="cancelChangeCustomer"
      @confirm="confirmChangeCustomer"
    />
  </div>
</template>

<script>
  import i18n from '@/i18n/index';
  import QueryCustomerList from '@/views/components/queryCustomerList';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';
  import MessageModal from '@/components/messageModal';

  import { hasAnyValue } from '@/utils/utils';
  export default {
    name: 'customerInfoAndSelectCustomer',
    components: { QueryCustomerList, TipsPopWindow, MessageModal },
    props: {
      // 客户信息
      customerInfo: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      customerInfo: {
        handler(val) {
          if (val) {
            this.formData = JSON.parse(JSON.stringify(val));
            this.queryFormData = {
              CUSTOMER_NAME: val.CUSTOMER_NAME,
            };
            this.oldCustomerData = JSON.parse(JSON.stringify(val));
          }
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        spinning: false,
        oldCustomerData: {}, //变更前客户信息
        newCustomerData: {}, //弹窗确认中的客户信息
        queryFormData: { CUSTOMER_NAME: '' }, //客户选择弹窗搜索条件
        formData: { CUSTOMER_NAME: '', MARKET_SEGMENT: '', LOB_NAME: '' }, //当前组件搜索条件
        formRules: {
          CUSTOMER_NAME: [
            {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
          ],
          MARKET_SEGMENT: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
          LOB_NAME: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
        },
        tipsVisible: false, //信息提示框
        queryListVisible: false, //客户查询弹窗
        queryListTitle: i18n.t('customerVerify.CustomerSelection'),
        confirmVisible: false, //消息确认框
        confirmMessage: this.$t('common.waiveAmountError'),
      };
    },
    computed: {},
    methods: {
      // 客户选择搜索
      handleSearch() {
        // 判断客户信息是否输入
        const uniqueSearch = {
          CUSTOMER_NAME: this.formData.CUSTOMER_NAME,
        };
        if (hasAnyValue(uniqueSearch)) {
          this.queryListVisible = true;
        } else {
          this.tipsVisible = true;
          return;
        }
      },
      // 客户改变
      changeCustomer() {
        //客户姓名更改，客户相关信息清空 重置表单
        this.formData.MARKET_SEGMENT = '';
        this.formData.LOB_NAME = '';
        this.formData.CUST_ID = '';
        this.formData.LOB = '';
        this.queryFormData = {
          CUSTOMER_NAME: this.formData.CUSTOMER_NAME,
        };
        this.$emit('update', this.formData);
      },
      //选择客户 取消按钮
      queryListCancel() {
        this.queryListVisible = false;
      },
      // 选择客户 确认按钮
      queryListOk(customerData) {
        console.log('customerData', customerData);
        if (!customerData) {
          this.tipsVisible = true;
          this.tipText = this.$t('customerVerify.oneCustomerError');
          return;
        }
        // 已选客户和现有客户LOB信息进行比较
        if (this.oldCustomerData.LOB && this.oldCustomerData.LOB.length > 0) {
          if (customerData.LOB != this.oldCustomerData.LOB) {
            //客户业务线变更，需要提示清空产品
            this.newCustomerData = customerData;
            this.confirmVisible = true;
            return;
          }
          this.updateCustomer(customerData);
        } else {
          this.updateCustomer(customerData);
        }
      },
      // 取消更改客户信息
      cancelChangeCustomer() {
        this.confirmVisible = false;
        // this.updateCustomer(this.oldCustomerData);
      },
      // 确认更改客户信息并且清空产品
      confirmChangeCustomer() {
        // 清空存储数据
        this.$store.dispatch('quotation/setCleanState');
        this.confirmVisible = false;
        this.oldCustomerData = JSON.parse(JSON.stringify(this.newCustomerData));
        this.updateCustomer(this.newCustomerData);
      },

      updateCustomer(customerData) {
        this.queryListCancel();
        this.formData.CUSTOMER_NAME = customerData.CUSTOMER_NAME;
        this.formData.MARKET_SEGMENT = customerData.MARKET_SEGMENT;
        this.formData.LOB_NAME = customerData.LOB_NAME;
        this.formData.LOB = customerData.LOB;
        this.formData.CUST_ID = customerData.CUSTOMER_NO;
        this.queryFormData = {
          CUSTOMER_NAME: this.formData.CUSTOMER_NAME,
        };
        this.$emit('update', this.formData);
      },
      //校验
      validate() {
        let validateFlag = true;
        this.$refs.customerForm.validate(valid => {
          validateFlag = valid;
        });
        return validateFlag;
      },
    },
  };
</script>
<style lang="less" scoped>
  header {
    background-color: #fff;
    box-sizing: border-box;
    // padding: 10px 22px 0 22px;

    .ant-col-6 {
      display: flex;
    }

    /deep/ .ant-form-item-label {
      width: auto !important;
      line-height: 32px !important;
    }

    .b-btns {
      justify-content: flex-end;
    }

    // button + button {
    //   margin-left: 10px;
    // }
  }
  .btnStyle {
    display: flex !important;
    align-items: end !important;
    justify-content: end !important;
    padding-bottom: 12px !important;
  }
  .btnDistance {
    // margin-right: 10px;
  }
  .button-group {
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
</style>
