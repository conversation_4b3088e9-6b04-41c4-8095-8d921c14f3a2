<template>
  <div class="header">
    <!-- Sales & ASM Infomation -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.SalesASMInformation') }}
    </div>
    <a-form-model
      :model="formData"
      :colon="false"
      ref="dataForm"
      :rules="isCORP ? formCORPRules : formSMERules"
    >
      <!-- Sales Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesmanCode')" prop="SALES_CODE">
            <a-input-search
              :loading="loadingSales"
              v-model.trim="formData.SALES_CODE"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeSales('code')"
              @search="handleSearch('sales')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesName')" prop="SALES_NAME">
            <a-input-search
              :loading="loadingSales"
              v-model.trim="formData.SALES_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeSales('name')"
              @search="handleSearch('sales')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesSegment')" prop="SALES_SEGMENT">
            <a-input name="SALES_SEGMENT" v-model="formData.SALES_SEGMENT" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.SalesTeam')" prop="SALES_TEAM" labelAlign="left">
            <a-input name="SALES_TEAM" v-model="formData.SALES_TEAM" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesContactNo')" prop="SALES_CONTACT_NO">
            <a-input name="SALES_CONTACT_NO" v-model="formData.SALES_CONTACT_NO" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesEmail')" prop="SALES_EMAIL">
            <a-input name="SALES_EMAIL" v-model="formData.SALES_EMAIL" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.SalesSM')" prop="SALES_SM" labelAlign="left">
            <a-input name="SALES_SM" v-model="formData.SALES_SM" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- ASM Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMStaffNo')" prop="ASM_STAFF_NO">
            <a-input-search
              :loading="loadingASM"
              v-model.trim="formData.ASM_STAFF_NO"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeASM('code')"
              @search="handleSearch('ASM')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMName')" prop="ASM_NAME">
            <a-input-search
              :loading="loadingASM"
              v-model.trim="formData.ASM_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              @change="changeASM('name')"
              @search="handleSearch('ASM')"
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMContactNo')" prop="ASM_CONTACT_NO">
            <a-input name="ASM_CONTACT_NO" v-model="formData.ASM_CONTACT_NO" disabled />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.ASMEmail')" prop="ASM_EMAIL" labelAlign="left">
            <a-input name="ASM_EMAIL" v-model="formData.ASM_EMAIL" disabled />
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: 'salesASMInfomation',
    props: {
      // 是否是CORP大客户，大客户ASM必填；SME小客户，ASM非必填
      isCORP: {
        type: Boolean,
        default: false,
      },
      // 销售和ASM信息
      orderSalesAsmInfo: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      orderSalesAsmInfo: {
        handler(val) {
          if (val) {
            this.formData = val;
          }
        },
        deep: true,
      },
    },
    computed: {
      ...mapState('app', {
        userInfo: state => state.userInfo,
      }),
      // 大客户校验
      formCORPRules() {
        return {
          ...this.formSMERules,
          ASM_NAME: [
            {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
          ],
          ASM_STAFF_NO: [
            {
              required: true,
              message: 'Please Enter',
              trigger: 'blur',
            },
          ],
          ASM_EMAIL: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
          ASM_CONTACT_NO: [
            {
              required: true,
              message: 'Please Select',
              trigger: 'blur',
            },
          ],
        };
      },
    },
    data() {
      return {
        loadingSales: false,
        loadingASM: false,
        formData: {
          SALES_CODE: 'sales1001', //销售编码
          SALES_SEGMENT: 'SME', //销售所属细分市场
          SALES_TEAM: 'SALES_TEAM', //销售团队
          SALES_SM: 'sales188X', //销售经理
          SALES_ID: 'xasi0085', //员工ID
          SALES_NAME: '秦玉慧', //姓名
          SALES_CONTACT_NO: '12301961108', //联系电话
          SALES_EMAIL: '<EMAIL>', //邮箱

          ASM_STAFF_NO: 'xasi0146', //员工ID
          ASM_NAME: '黄丹秋', //姓名
          ASM_CONTACT_NO: '12301961108', //联系电话
          ASM_EMAIL: '<EMAIL>', //邮箱
        },
        // 小客户校验
        formSMERules: {
          SALES_CODE: [
            {
              required: true,
              message: 'Please Enter SalesmanCode',
              trigger: 'blur',
            },
          ],
          SALES_NAME: [
            {
              required: true,
              message: 'Please Enter SalesName',
              trigger: 'blur',
            },
          ],
          SALES_SEGMENT: [
            {
              required: true,
              message: 'Please inquiry Salesman',
              trigger: 'blur',
            },
          ],
          SALES_TEAM: [
            {
              required: true,
              message: 'Please inquiry Salesman',
              trigger: 'blur',
            },
          ],
          SALES_EMAIL: [
            {
              required: true,
              message: 'Please inquiry Salesman',
              trigger: 'blur',
            },
          ],
          SALES_CONTACT_NO: [
            {
              required: true,
              message: 'Please inquiry Salesman',
              trigger: 'blur',
            },
          ],
        },
      };
    },
    methods: {
      // 查询销售和ASM信息
      handleSearch(type) {
        console.log('this.userInfo', this.userInfo);
        if (type == 'sales') {
          this.loadingSales = true;
          setTimeout(() => {
            this.loadingSales = false;
          }, 100);
          console.log('销售');
        } else {
          console.log('ASM');
        }
      },
      // 销售信息更改，清空销售相关信息； code清空名称，name清空编码
      changeSales(type) {
        if (type == 'code') {
          this.formData.SALES_NAME = '';
        } else {
          this.formData.SALES_CODE = '';
        }
        this.formData.SALES_CONTACT_NO = '';
        this.formData.SALES_EMAIL = '';
        this.formData.SALES_SEGMENT = '';
        this.formData.SALES_SM = '';
        this.formData.SALES_TEAM = '';
        this.formData.SALES_ID = '';
      },
      // ASM 信息更改，清空ASM相关信息；
      changeASM(type) {
        if (type == 'code') {
          this.formData.ASM_NAME = '';
        } else {
          this.formData.ASM_STAFF_NO = '';
        }
        this.formData.ASM_CONTACT_NO = '';
        this.formData.ASM_EMAIL = '';
      },
      //校验
      validate() {
        let validateFlag = true;
        this.$refs.dataForm.validate(valid => {
          validateFlag = valid;
        });
        return validateFlag;
      },
      //订单报文信息
      getOrderInfo() {
        return {
          ORDER_SALESMAN: [
            //SALES_TYP 01-销售，02-ASM
            {
              SALES_TYPE: '01',
              SALES_CODE: this.formData.SALES_CODE,
              SALES_SEGMENT: this.formData.SALES_SEGMENT || '',
              SALES_TEAM: this.formData.SALES_TEAM || '',
              SALES_SM: this.formData.SALES_SM || '',
              STAFF_ID: this.formData.SALES_ID || '',
              STAFF_NAME: this.formData.SALES_NAME || '',
              CONTACT_NO: this.formData.SALES_CONTACT_NO || '',
              EMAIL: this.formData.SALES_EMAIL || '',
            },
            // TODO
            {
              SALES_TYPE: '02',
              SALES_CODE: this.formData.SALES_CODE,
              SALES_SEGMENT: this.formData.SALES_SEGMENT || '',
              SALES_TEAM: this.formData.SALES_TEAM || '',
              SALES_SM: this.formData.SALES_SM || '',
              STAFF_ID: this.formData.ASM_STAFF_NO || '',
              STAFF_NAME: this.formData.ASM_NAME || '',
              CONTACT_NO: this.formData.ASM_CONTACT_NO || '',
              EMAIL: this.formData.ASM_EMAIL || '',
            },
          ],
        };
      },
    },
  };
</script>
<style lang="less" scoped>
  header {
    background-color: #fff;
    box-sizing: border-box;
    // padding: 10px 22px 0 22px;

    .ant-col-6 {
      display: flex;
    }

    /deep/ .ant-form-item-label {
      width: auto !important;
      line-height: 32px !important;
    }

    .b-btns {
      justify-content: flex-end;
    }

    // button + button {
    //   margin-left: 10px;
    // }
  }
  .btnStyle {
    display: flex !important;
    align-items: end !important;
    justify-content: end !important;
    padding-bottom: 12px !important;
  }
  .btnDistance {
    // margin-right: 10px;
  }
  .button-group {
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
  }
</style>
