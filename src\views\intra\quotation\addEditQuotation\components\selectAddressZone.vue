<template>
  <div>
    <a-form-model :model="formData" :colon="false" ref="addressZoneForm" :rules="addressZoneRules">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item
            :label="$t('quotation.AddressZone')"
            prop="ADDRESS_ZONE"
            labelAlign="left"
          >
            <a-select
              v-model="formData.ADDRESS_ZONE"
              :placeholder="$t('common.selectPlaceholder')"
              @change="changeZone"
              allowClear
            >
              <a-select-option v-for="item in zonesDatas" :value="item.DATA_ID" :key="item.DATA_ID">
                {{ item.DATA_NAME }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>
<script>
  import { queryParamList } from '@/api/common';
  export default {
    name: 'selectAddressZone',
    props: {
      // 是否是CORP大客户，大客户 地址区域不必填；SME小客户，地址区域必填
      isCORP: {
        type: Boolean,
        default: false,
      },
      // 地址区域信息
      addressZone: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      addressZone: {
        handler(val) {
          if (val) {
            this.formData = val;
          }
        },
        immediate: true,
        deep: true,
      },
    },
    computed: {
      // 地址区域的校验规则
      addressZoneRules() {
        if (!this.isCORP) {
          return {
            ADDRESS_ZONE: [
              {
                required: true,
                message: 'Please Select Address Zone',
                trigger: 'blur',
              },
            ],
          };
        }
        return {};
      },
    },
    data() {
      return {
        formData: { ADDRESS_ZONE: '', ADDRESS_ZONE_NAME: '' },
        zonesDatas: [],
      };
    },
    async created() {
      this.queryZonesData();
    },
    methods: {
      // 更改地址区域
      changeZone(value) {
        const zoneObj = this.zonesDatas.find(item => item.DATA_ID === value);
        this.formData.ADDRESS_ZONE_NAME = zoneObj.DATA_NAME;
      },
      // 获取Zone枚举数据
      queryZonesData() {
        queryParamList({ TYPE_ID: 'ADDRESS_ZONE_TYPE' })
          .then(res => {
            this.zonesDatas = res.DATA || [];
          })
          .catch(() => {});
      },
      //校验
      validate() {
        let validateFlag = true;
        this.$refs.addressZoneForm.validate(valid => {
          validateFlag = valid;
        });
        return validateFlag;
      },
    },
  };
</script>
