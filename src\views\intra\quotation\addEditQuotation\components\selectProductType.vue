<template>
  <PopWindow
    :visible="visible"
    :title="title"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    :bodyStyle="bodyStyle"
    :footer="true"
    modalWidth="880px"
  >
    <template #Content>
      <div class="selectProduct">
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="CustomerLobForm"
          :colon="false"
        >
          <a-form-model-item :label="$t('customerVerify.CustomerLob')" prop="LOB" labelAlign="left">
            <a-radio-group v-model="CustomerLobForm.LOB">
              <div class="radio-grid">
                <div class="radioBorder checked">
                  <a-tooltip>
                    <template slot="title"> {{ CustomerLobForm.LOB_NAME }}</template>
                    <a-radio :value="CustomerLobForm.LOB">
                      {{ CustomerLobForm.LOB_NAME }}
                    </a-radio>
                  </a-tooltip>
                </div>
              </div>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="ProductFamilyForm"
          :colon="false"
        >
          <a-form-model-item
            :label="$t('customerVerify.CustomerFamily')"
            prop="radioValue"
            labelAlign="left"
          >
            <a-radio-group v-model="ProductFamilyForm.ProductFamilyValue">
              <div class="radio-grid">
                <div v-for="(item, index) in ProductFamilyDatas" :key="index">
                  <div
                    :class="{
                      radioBorder: true,
                      checked: ProductFamilyForm.ProductFamilyValue == item.value,
                    }"
                  >
                    <a-tooltip>
                      <template slot="title"> {{ item.label }}</template>
                      <a-radio :value="item.value" :disabled="item.disabled">
                        {{ item.label }}
                      </a-radio>
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="ProductTypeForm"
          :colon="false"
        >
          <a-form-model-item
            :label="$t('customerVerify.ProductType')"
            labelAlign="right"
            prop="radioValue"
          >
            <a-radio-group v-model="ProductTypeForm.ProductTypeValue">
              <div class="radio-grid">
                <div v-for="(item, index) in ProductTypeDatas" :key="index">
                  <div
                    :class="{
                      radioBorder: true,
                      checked: ProductTypeForm.ProductTypeValue == item.value,
                    }"
                  >
                    <a-tooltip>
                      <template slot="title"> {{ item.label }}</template>
                      <a-radio :value="item.value" :disabled="item.disabled">
                        {{ item.label }}
                      </a-radio>
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
        <a-form-model
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :model="ProductNameForm"
          :colon="false"
        >
          <a-form-model-item
            :label="$t('customerVerify.ProductName')"
            prop="radioValue"
            labelAlign="left"
          >
            <a-radio-group v-model="ProductNameForm.productId">
              <div class="radio-grid">
                <div v-for="(item, index) in ProductNameDatas" :key="index">
                  <div
                    :class="{
                      radioBorder: true,
                      checked: ProductNameForm.productId == item.value,
                    }"
                  >
                    <a-tooltip>
                      <template slot="title"> {{ item.label }}</template>
                      <a-radio :value="item.value"> {{ item.label }} </a-radio>
                    </a-tooltip>
                  </div>
                </div>
              </div>
            </a-radio-group>
          </a-form-model-item>
        </a-form-model>
      </div>
    </template>
    <template #footer>
      <a-button class="modal-button-cancel" @click="handleCancel">{{
        $t('common.buttonCancel')
      }}</a-button>
      <a-button type="primary" @click="handleOk" class="moadl-button-Ok">{{
        $t('common.buttonConfirm')
      }}</a-button>
    </template>
  </PopWindow>
</template>

<script>
  import { productFamilyQuery, productTypeOperateQuery } from '@/api/customerVerify';
  import { queryAllProductName } from '@/api/quotation';
  import { gotoAddProductTree } from './addProductTree';

  import PopWindow from '@/components/popWindow';
  import { mapActions } from 'vuex';
  export default {
    name: 'functionAuthorityMaintenanceMQ',
    components: { PopWindow },
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      customerLob: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        confirmLoading: false,
        spinning: false,
        bodyStyle: {
          maxHeight: '500px',
          padding: '10px',
          overflow: 'auto',
        },
        layout: {},
        CustomerLobForm: {
          // ProductFamilyValue: "UCS",
        },
        ProductFamilyForm: {
          // ProductFamilyValue: "UCS",
        },
        ProductTypeForm: {
          // ProductTypeValue: "DEL",
        },
        ProductNameForm: {
          // ProductTypeValue: "DEL",
        },
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        ProductFamilyDatas: [],
        ProductTypeDatas: [],
        ProductNameDatas: [],
      };
    },
    watch: {
      'ProductFamilyForm.ProductFamilyValue': {
        handler(newVal) {
          this.getProductTypeData({ PRODUCT_FAMILY: newVal });
          console.log(newVal, 'newVal');
        },
        deep: true,
      },
      'ProductTypeForm.ProductTypeValue': {
        handler(newVal) {
          this.getProductNameData(newVal);
          console.log(newVal, 'newVal');
        },
        deep: true,
      },
    },
    created() {
      this.CustomerLobForm = JSON.parse(JSON.stringify(this.customerLob));
      console.log(this.customerLob, 'customerLob');
      // TOOD
      this.getProductFamilyData({});
    },
    methods: {
      ...mapActions('quotation', ['setProductSelection', 'setProductTypeData']),
      getProductFamilyData(val) {
        productFamilyQuery(val).then(res => {
          this.ProductFamilyDatas = res.DATA.map(item => {
            if (item.PRODUCT_FAMILY == 'UCS') {
              return {
                label: item.PRODUCT_FAMILY_NAME,
                value: item.PRODUCT_FAMILY,
                disabled: false,
              };
            }
            return {
              label: item.PRODUCT_FAMILY_NAME,
              value: item.PRODUCT_FAMILY,
              disabled: true,
            };
          });
          this.$set(this.ProductFamilyForm, 'ProductFamilyValue', 'UCS');
        });
      },
      // 产品类型数据获取
      getProductTypeData(val) {
        productTypeOperateQuery({ PRODUCT_MODE: '1|2', ...val }).then(res => {
          this.ProductTypeDatas = res.DATA.map(item => {
            if (item.PRODUCT_TYPE_CODE == '300001') {
              return {
                label: item.PRODUCT_TYPE_NAME,
                value: item.PRODUCT_TYPE_CODE,
                root: item.ROOT,
                disabled: false,
              };
            }
            return {
              label: item.PRODUCT_TYPE_NAME,
              value: item.PRODUCT_TYPE_CODE,
              root: item.ROOT,
              disabled: true,
            };
          });
          this.$set(this.ProductTypeForm, 'ProductTypeValue', this.ProductTypeDatas[0]?.value);
        });
      },
      // 产品名称数据获取
      getProductNameData(val) {
        const data = {
          FORM: 1,
          SIZE: 9999,
          STAFF_ID: this.userInfo?.STAFF_ID,
          EPARCHY_CODE: this.userInfo?.EPARCHY_CODE,
          PROVINCE_CODE: this.userInfo?.PROVINCE_CODE,
          PRODUCT_TYPE_CONDITIONS: [
            {
              PRODUCT_TYPE_CODE: val,
              PRODUCT_TYPE_CODE_ROOT: '1000',
            },
          ],
          LOB: this.CustomerLobForm.LOB,
          PRODUCT_FAMILY: this.ProductFamilyForm.ProductFamilyValue,
        };
        queryAllProductName(data)
          .then(res => {
            this.ProductNameDatas = res.DATA[0]?.QUERY_RESPONSE.map(item => {
              return {
                label: item.PRODUCT_NAME,
                value: item.PRODUCT_ID,
                ...item,
              };
            });
            this.$set(this.ProductNameForm, 'productId', this.ProductNameDatas[0]?.value);
          })
          .catch(error => {
            console.log('error', error);
          });
      },
      handleCancel() {
        this.$emit('cancel');
      },

      handleOk() {
        const productType = this.ProductTypeDatas.find(
          item => item.value == this.ProductTypeForm.ProductTypeValue,
        );
        this.setProductTypeData({ ...productType });
        this.$store.dispatch('quotation/setProductStatus', 'add');
        console.log('{ ...productType }', { ...productType });
        const routePath = gotoAddProductTree(productType.value);
        this.$emit('Ok', {
          routePath: routePath,
          productType: this.ProductTypeForm.ProductTypeValue,
          productTypeRoot: '1000',
          productFamily: this.ProductFamilyForm.ProductFamilyValue,
          productId: this.ProductNameForm.productId,
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .moadl-button-Ok {
    padding: 0;
  }

  .selectProduct {
    /deep/ .ant-form-item-label {
      line-height: 32px !important;
    }
    /deep/ .ant-form-item-children {
      width: 100%;
      display: inline-block;
      .ant-radio-group {
        width: 100%;
      }
    }
    /deep/ .ant-form-horizontal {
      margin-bottom: 20px;
    }
    /deep/ .ant-form-item-label > label {
      float: left !important;
    }
    .radio-grid {
      display: grid;
      grid-template-columns: repeat(4, 160px);
      row-gap: 10px;
      column-gap: 8px;
      .radioBorder {
        display: flex;
        align-items: center;
        height: 32px;
        border: solid 1px #eeeeee;
        padding: 2px 5px;
        border-radius: 2px;
        label {
          width: 100%;
          white-space: nowrap; /* 强制文本在同一行内显示 */
          overflow: hidden; /* 隐藏溢出内容 */
          text-overflow: ellipsis;
        }
      }
      .checked {
        border: solid 1px #01408e !important;
      }
    }
  }
</style>
