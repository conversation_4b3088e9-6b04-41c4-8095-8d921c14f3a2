<template>
  <div class="operation-quotation">
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>
    <!-- osca流程步骤条 -->
    <FlowSteps
      v-if="orderStatus && orderStatus != 'S8' && ifEdit"
      :orderStatus="orderStatus"
    ></FlowSteps>

    <!-- 报价单 - 明细 -->
    <div v-if="orderId" class="details-order-info">
      <a-form-model :model="orderDetailData" layout="inline">
        <a-row :gutter="24" justify="start" type="flex">
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.PreAFNo')">
              {{ orderId }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.CreateDate')">
              {{ orderDetailData.CREATE_DATE }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('quotation.Status')" labelAlign="left">
              <span class="order-status">{{ orderDetailData.ORDER_STATE_NAME }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <!-- 客户信息 -->
    <CustomerInfoAndSelectCustomer
      ref="customerInfoAndSelectCustomerRef"
      :customerInfo="orderDetailData.customerInfo"
      @update="handleCustomerUpdate"
    />

    <a-divider />

    <!-- 销售和跟单人员信息 -->
    <SalesASMInfomation
      :isCORP="isCORP"
      :orderSalesAsmInfo="orderDetailData.orderSalesAsmInfo"
      ref="salesASMInfomationRef"
    />

    <a-divider />

    <!-- 地址区域信息 -->
    <SelectAddressZone
      :isCORP="isCORP"
      :addressZone="orderDetailData.addressZone"
      ref="selectAddressZoneRef"
    />

    <a-divider />

    <!-- 产品选择 -->
    <SelProductBox
      :customerLobValue="customerLobValue"
      :productInfos="productInfos"
      :updateProductInfos="updateProductInfos"
      ref="selProductBoxRef"
    ></SelProductBox>

    <!-- 附件上传 -->
    <Attachment :fileList="orderDetailData.multimediaFiles" ref="attachmentRef" />

    <a-divider />

    <!-- 审批备注信息 -->
    <ApproveRemark :approveRemark="orderDetailData.orderRemark" ref="approveRemarkRef" />

    <a-divider />

    <!-- 审批原因 -->
    <ApprovalReason
      :selectReasons="orderDetailData.expansion"
      ref="approvalReason"
    ></ApprovalReason>

    <ActionTimeLine ref="actionTimeLine" :orderId="orderId" v-if="orderId"></ActionTimeLine>

    <FooterToolBar class="footer-tool-bar">
      <a-button ghost type="primary" @click="handleComplete" class="fixed-bottom-btn">{{
        $t('customerVerify.Cancel')
      }}</a-button>
      <a-button ghost type="primary" @click="saveInfo" class="fixed-bottom-btn">{{
        $t('customerVerify.Save')
      }}</a-button>

      <a-button v-if="orderId" type="primary" class="fixed-bottom-btn bg-blue" @click="submit">{{
        $t('customerVerify.Submit')
      }}</a-button>
    </FooterToolBar>

    <!-- 审批提醒 -->
    <ApproveMessageModal
      v-if="approveVisible"
      :loading="approveLoading"
      :visible="approveVisible"
      :approveFlows="['Main Product', 'Premium']"
      :approvers="[`Sales's AVP`, ' Marketing']"
      :displayCancelBtn="true"
      @cancel="approveVisible = false"
      @confirm="handleConfirmSubmit"
    />

    <!-- 信息提示 -->
    <MessageModal :visible="tipsVisible" :message="tipsMessage" @confirm="confirmOK" />
  </div>
</template>

<script>
  import PageHeader from '@/views/components/pageHeader/index.vue';
  import FlowSteps from '@/views/components/flowSteps/index.vue';
  import CustomerInfoAndSelectCustomer from '@/views/intra/quotation/addEditQuotation/components/customerInfoAndSelectCustomer.vue';
  import SalesASMInfomation from '@/views/intra/quotation/addEditQuotation/components/sales&ASMInfomation.vue';
  import Attachment from '@/views/components/attachment/index.vue';
  import ApproveRemark from '@/views/intra/quotation/addEditQuotation/components/approveRemark.vue';
  import ApproveMessageModal from '../../../components/approveMessageModal/index.vue';
  import SelectAddressZone from '@/views/intra/quotation/addEditQuotation/components/selectAddressZone.vue';
  import ApprovalReason from '@/views/intra/quotation/addEditQuotation/components/approvalReason.vue';
  import FooterToolBar from '@/components/footerToolbar';
  import SelProductBox from '@/views/intra/quotation/addEditQuotation/components/addProductBox.vue';
  import ActionTimeLine from '@/views/components/actionTimeLine/index.vue';
  import MessageModal from '@/components/messageModal';

  import { queryOrderDetail, oscaOrderReceive, submitApproval } from '@/api/quotation';
  import { getUniqueOrderID } from '@/utils/utils';
  import {
    getProductInfoList,
    getProductAttrInfoList,
    getElementInfoList,
    getElementAttrInfoList,
    getOrderLineAttrInfo,
  } from '@/views/intra/changeProduct.js';
  import { mapState } from 'vuex';
  export default {
    name: 'AddEditQuotation',
    components: {
      PageHeader,
      FlowSteps,
      CustomerInfoAndSelectCustomer,
      SalesASMInfomation,
      Attachment,
      ApproveRemark,
      ApproveMessageModal,
      SelectAddressZone,
      SelProductBox,
      ApprovalReason,
      ActionTimeLine,
      FooterToolBar,
      MessageModal,
    },
    computed: {
      ...mapState('quotation', {
        productSelection: state => state.productSelection,
        productStatus: state => state.productStatus,
      }),
      // 上一页页面信息
      prePageInfo() {
        return this.ifEdit
          ? `${this.$route.query.orderId}(${this.$t('common.edit')})`
          : this.$t('quotation.NewPreAF');
      },
      // 是否列表进入的编辑页面
      ifEdit() {
        return this.$route.query.orderId && this.$route.query.orderId !== '';
      },
      orderStatus() {
        return this.$route.query.orderStatus;
      },
    },
    data() {
      return {
        fromPageName: '',
        isCORP: false, // 是否大客户
        customerLobValue: {}, //客户LOB信息
        orderId: '', // 订单编码
        pageLoading: false, //页面加载
        approveLoading: false,
        approveVisible: false, // 弹窗-显隐
        tipsVisible: false,
        tipsMessage: '',
        submitApprovalSuccess: false, // 提交审批成功
        productInfos: [],
        // 订单详情信息
        orderDetailData: {
          orderId: '', // 订单编码
          createTime: '', // 订单时间
          orderStatus: '', // 订单状态
          customerInfo: {}, //客户信息
          orderSalesAsmInfo: {}, //销售和ASM信息
          addressZone: {}, //地址区域信息
          addressInfoList: [], //地址信息
          multimediaFiles: [], //附件信息
          orderRemark: {}, //备注信息
          expansion: [], //操作人选择的审批原因
        },
      };
    },
    beforeRouteEnter(to, from, next) {
      // 在进入页面时获取路由参数
      next(vm => {
        // 通过 `vm` 访问组件实例
        vm.fromPageName = from.name || 'quotationManagement';
      });
      next();
    },
    activated() {
      console.log('this.fromPageName', this.fromPageName);
      if (this.fromPageName == 'quotationManagement') {
        this.initData();
        if (!this.ifEdit) {
          this.isCORP = false;
          this.customerLobValue = {};
          this.$refs.selProductBoxRef.isSelectCustomer = false;
          this.productInfos = [];
          this.orderDetailData = {
            orderId: '', // 订单编码
            createTime: '', // 订单时间
            orderStatus: '', // 订单状态
            customerInfo: {}, //客户信息
            // TODO
            // orderSalesAsmInfo: {}, //销售和ASM信息
            addressZone: {}, //地址区域信息
            addressInfoList: [], //地址信息
            multimediaFiles: [], //附件信息
            orderRemark: {}, //备注信息
            expansion: [], //操作人选择的审批原因
          };
        }
      } else {
        // 保存产品信息
        if (this.productStatus === 'add' && JSON.stringify(this.productSelection) != '{}') {
          // 新增产品
          this.productInfos = [...this.productInfos, this.productSelection];
        } else if (this.productStatus === 'edit' && JSON.stringify(this.productSelection) != '{}') {
          this.productInfos.splice(this.productSelection.index, 1, this.productSelection);
        }
        console.log('this.productInfos', this.productInfos);
      }
    },
    mounted() {
      console.log('mounted');
    },
    methods: {
      // 数据初始化
      initData() {
        // TODO
        this.submitApprovalSuccess = false;
        this.orderId = this.$route.query.orderId;
        // 编辑页面回显信息
        if (this.ifEdit) {
          this.getDetailData();
          // 更新时间轨迹
          this.$nextTick(() => {
            this.$refs.actionTimeLine.updateInfo();
          });
        }
      },
      //
      async getDetailData() {
        this.pageLoading = true;
        try {
          const res = await queryOrderDetail({ ORDER_ID: this.orderId });
          this.pageLoading = false;
          this.orderDetailData = this.formatDetailData(res.DATA);
          console.log('this.orderDetailData', this.orderDetailData);
        } catch (error) {
          console.log(error, '出错了！');
        } finally {
          this.pageLoading = false;
        }
      },
      // 将下单报文转换成可视化表格数据
      formatDetailData(res) {
        const resData = res && res[0];
        return this.formatSingleProdDetailData(resData);
      },

      // 单产品处理订单详情数据
      formatSingleProdDetailData(resData) {
        const {
          CUST_INFO,
          ORDER_SALESMAN,
          ORDER_ATTR_INFO,
          AMEND_ORDER_LINE_INFO,
          MULTIMEDIA_FILES,
          ORDER_REMARK_ITEM,
          ORDER_EXPANSION,
        } = resData;

        // 客户信息
        let customerInfo = CUST_INFO?.[0];
        if (customerInfo) {
          customerInfo.CUSTOMER_NO = customerInfo.CUST_ID;
          customerInfo.CUSTOMER_NAME = customerInfo.CUST_NAME;
          // TODO 假数据
          if (customerInfo.CUST_ID == '3025032101123196') {
            customerInfo.MARKET_SEGMENT = 'CORP';
            customerInfo.LOB_NAME = 'Fixed Line';
            customerInfo.LOB = '30';
          } else {
            customerInfo.MARKET_SEGMENT = 'SME';
            customerInfo.LOB_NAME = 'Fixed Line';
            customerInfo.LOB = '30';
          }
          this.handleCustomerUpdate(customerInfo);
        }

        //销售和ASM信息
        let orderSalesAsmInfo = {};
        let orderSalesmanInfoTemp = ORDER_SALESMAN?.[0];
        if (orderSalesmanInfoTemp) {
          orderSalesAsmInfo.SALES_CODE = orderSalesmanInfoTemp.SALES_CODE;
          orderSalesAsmInfo.SALES_SEGMENT = orderSalesmanInfoTemp.SALES_SEGMENT;
          orderSalesAsmInfo.SALES_TEAM = orderSalesmanInfoTemp.SALES_TEAM;
          orderSalesAsmInfo.SALES_SM = orderSalesmanInfoTemp.SALES_SM;
          orderSalesAsmInfo.SALES_ID = orderSalesmanInfoTemp.STAFF_ID;
          orderSalesAsmInfo.SALES_NAME = orderSalesmanInfoTemp.STAFF_NAME;
          orderSalesAsmInfo.SALES_CONTACT_NO = orderSalesmanInfoTemp.CONTACT_NO;
          orderSalesAsmInfo.SALES_EMAIL = orderSalesmanInfoTemp.EMAIL;
        }
        let orderAsmInfoTemp = ORDER_SALESMAN?.[1];
        if (orderAsmInfoTemp) {
          orderSalesAsmInfo.ASM_STAFF_NO = orderAsmInfoTemp.STAFF_ID;
          orderSalesAsmInfo.ASM_NAME = orderAsmInfoTemp.STAFF_NAME;
          orderSalesAsmInfo.ASM_CONTACT_NO = orderAsmInfoTemp.CONTACT_NO;
          orderSalesAsmInfo.ASM_EMAIL = orderAsmInfoTemp.EMAIL;
        }
        // 地址区域信息
        let addressZone = {};
        if (ORDER_ATTR_INFO) {
          ORDER_ATTR_INFO.forEach(item => {
            addressZone['ADDRESS_ZONE'] = item.ATTR_VALUE;
            addressZone['ADDRESS_ZONE_NAME'] = item.VALUE_NAME;
          });
        }
        // 备注信息
        let orderRemark = {};
        if (ORDER_REMARK_ITEM) {
          ORDER_REMARK_ITEM.forEach(item => {
            orderRemark[item.ATTR_CODE] = item.ATTR_VALUE;
          });
        }

        // 获取产品地址和count信息
        const productInfos = this.getProductInfo(AMEND_ORDER_LINE_INFO);
        this.productInfos = productInfos;
        return {
          ...resData,
          customerInfo: customerInfo,
          orderSalesAsmInfo: orderSalesAsmInfo,
          addressZone: addressZone,
          multimediaFiles: MULTIMEDIA_FILES,
          orderRemark: orderRemark,
          expansion: ORDER_EXPANSION?.map(item => item.RESERVED_VALUE) || [],
          productInfos,
        };
      },

      // 获取产品信息
      getProductInfo(ORDER_LINE_INFO) {
        let productInfos = [];
        (ORDER_LINE_INFO || []).forEach(item => {
          const { ORDER_INSTALL_ADDRESS, PRODUCT_INFO } = item;
          const installInfoFirst = ORDER_INSTALL_ADDRESS?.[0] || {};
          if (PRODUCT_INFO) {
            const MainProduct = PRODUCT_INFO?.filter(item => item.PRODUCT_MODE == '00') || [];
            productInfos.push({
              addressInfo: installInfoFirst, //产品地址回显信息
              productInfo: { MainProduct: MainProduct }, //产品界面回显信息
            });
          }
        });
        return productInfos;
      },

      // 产品信息更新
      updateProductInfos(infos) {
        this.productInfos = infos;
      },

      // 客户信息更新
      handleCustomerUpdate(customerInfo) {
        // 处理客户信息更新逻辑
        console.log('客户信息已更新', customerInfo);
        // #TODO: 减少触发频率
        // 更新产品选择 - 可选状态
        this.customerLobValue = {
          LOB: customerInfo.LOB,
          LOB_NAME: customerInfo.LOB_NAME,
        };
        this.isCORP = customerInfo.MARKET_SEGMENT === 'SME' ? false : true;
        this.$store.dispatch('quotation/setIsCORP', this.isCORP);
        this.$refs.selProductBoxRef.isSelectCustomer = customerInfo.MARKET_SEGMENT !== '';
      },
      // 获取订单报文信息
      getOrderInfo(
        ORDER_ID,
        CUST_ID,
        ORDER_SALESMAN,
        addressZoneForm,
        MULTIMEDIA_FILES,
        ORDER_REMARK_ITEM,
        EXPANSION_INFO,
      ) {
        let ORDER_LINE_INFO = []; //编辑、新增或者删除的订单信息
        let KEEP_LINE_IDS = []; //没有进行编辑的产品订单行信息
        if (this.productInfos && this.productInfos.length > 0) {
          this.productInfos.forEach(item => {
            if (
              item.productInfo.mainProductTreeSelectedList &&
              item.productInfo.mainProductTreeSelectedList.length > 0
            ) {
              ORDER_LINE_INFO.push({
                NET_TYPE_CODE: '-1',
                TRADE_TYPE_CODE: '10',
                SCENE_TYPE: '10000',
                SERIAL_NUMBER: '-1',
                ORDER_INSTALL_ADDRESS: item.addressInfo,
                PRODUCT_INFO: getProductInfoList([
                  ...item.productInfo.mainProductTreeSelectedList,
                  ...item.productInfo.additionalProductTreeSelectedList,
                ]),
                PRODUCT_ATTR_INFO: getProductAttrInfoList(
                  [
                    ...item.productInfo.mainProductTreeSelectedList,
                    ...item.productInfo.additionalProductTreeSelectedList,
                  ],
                  null,
                  '1000',
                  item.tabCountList,
                  item.lineInstallNumber,
                ),
                ELEMENT_INFO: getElementInfoList([
                  ...item.productInfo.mainProductTreeSelectedList,
                  ...item.productInfo.additionalProductTreeSelectedList,
                ]),
                ELEMENT_ATTR_INFO: getElementAttrInfoList([
                  ...item.productInfo.mainProductTreeSelectedList,
                  ...item.productInfo.additionalProductTreeSelectedList,
                ]),
                // 额外补充 - 用于详情显示计算结果，减少详情的开发
                ORDER_LINE_ATTR_INFO: getOrderLineAttrInfo(
                  [...item.productInfo.mainProductTreeSelectedList],
                  {
                    totalMRCForAllLine: item.totalMRCForAllLine,
                    totalOTCForAllLine: item.totalOTCForAllLine,
                    totalMRCForAllLinePeriod: item.totalMRCForAllLinePeriod,
                    redeemablePoints: item.redeemablePoints,
                  },
                  item.lineInstallNumber,
                ),
              });
            } else {
              KEEP_LINE_IDS.push(item.productInfo?.MainProduct[0]?.ORDER_LINE_ID);
            }
          });
        }
        return {
          ORDER_ID,
          IN_MODE_CODE: '2000',
          EXT_ORDER_ID: getUniqueOrderID(), //外部订单号
          CUST_INFO: [
            {
              CUST_ID: CUST_ID,
            },
          ],
          ORDER_SALESMAN: ORDER_SALESMAN.ORDER_SALESMAN,
          ORDER_ATTR_INFO: [
            {
              ATTR_TYPE: '',
              ATTR_CODE: 'address_zone',
              ATTR_VALUE: addressZoneForm.ADDRESS_ZONE,
              ATTR_VALUE_NAME: addressZoneForm.ADDRESS_ZONE_NAME,
              MODIFY_TAG: '0',
            },
          ],
          KEEP_LINE_IDS,
          ORDER_LINE_INFO,
          MULTIMEDIA_FILES: MULTIMEDIA_FILES.map(item => {
            delete item.SERVER_TYPE_NAME;
            return item;
          }),
          ORDER_REMARK_ITEM: ORDER_REMARK_ITEM?.ORDER_REMARK_ITEM?.filter(
            item => item.ATTR_VALUE && item.ATTR_VALUE != '',
          ),
          ORDER_EXPANSION_INFO: EXPANSION_INFO.map(item => {
            return {
              RESERVED_CODE: 'RESERVED_VALUE',
              RESERVED_VALUE: item.DATA_NAME,
              MODIFY_TAG: '0',
            };
          }),
        };
      },

      // 审批提醒后确认提交
      handleConfirmSubmit() {
        this.approveVisible = false;
        // this.commitInfo();
      },
      //取消  返回上一个页面
      handleComplete() {
        this.$router.customBack();
      },
      //保存按钮
      saveInfo() {
        this.saveInfoCommit(false);
      },
      //提交按钮
      async submit() {
        //先保存
        this.saveInfoCommit(true);
      },
      // 保存信息提交 isCommit提交
      saveInfoCommit(isCommit) {
        // 表单校验不通过
        if (!this.$refs.customerInfoAndSelectCustomerRef.validate()) {
          return;
        }
        if (!this.$refs.salesASMInfomationRef.validate()) {
          return;
        }
        if (!this.$refs.selectAddressZoneRef.validate()) {
          return;
        }

        if (isCommit && !(this.productInfos && this.productInfos.length > 0)) {
          this.tipsVisible = true;
          this.tipsMessage = this.$t('macd.pleaseSelectProduct');
          return;
        }
        // 获取订单保存报文信息
        const orderInfo = this.getOrderInfo(
          this.orderId,
          this.$refs.customerInfoAndSelectCustomerRef.formData.CUST_ID,
          this.$refs.salesASMInfomationRef.getOrderInfo(),
          this.$refs.selectAddressZoneRef.formData,
          this.$refs.attachmentRef.dataList,
          this.$refs.approveRemarkRef.getOrderInfo(),
          this.$refs.approvalReason.getSelectedRows(),
        );
        console.log('orderInfo', orderInfo);

        oscaOrderReceive({ ORDER: orderInfo })
          .then(async res => {
            console.log('oscaOrderReceive', res);
            this.orderId = res.DATA[0]?.ORDER_ID || '';
            if (isCommit) {
              this.submitApproval();
            } else {
              this.tipsVisible = true;
              this.tipsMessage = this.$t('common.successMessage');
            }
          })
          .finally(() => {
            // this.additionalProductSpinning = false;
          });
      },

      // 提交审批接口
      async submitApproval() {
        this.pageLoading = true;
        this.submitApprovalSuccess = false;
        try {
          const res = await submitApproval({ ORDER_ID: this.orderId });
          this.pageLoading = false;
          this.tipsVisible = true;
          this.submitApprovalSuccess = true;
          this.tipsMessage = this.$t('common.successMessage');
        } catch (error) {
          console.log(error, '出错了！');
        } finally {
          this.pageLoading = false;
        }
      },

      // 保存成功提示
      confirmOK() {
        this.tipsVisible = false;
        if (this.tipsMessage == this.$t('common.successMessage')) {
          if (this.submitApprovalSuccess) {
            this.handleComplete();
          } else {
            this.getDetailData();
            // 更新时间轨迹
            this.$nextTick(() => {
              this.$refs.actionTimeLine.updateInfo();
            });
          }
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  @foot-height: 80px;
  .operation-quotation {
    padding-bottom: @foot-height;
  }
  .footer-tool-bar {
    height: @foot-height;
    box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .details-order-info {
    background: #e9e9e9;
    height: 40px;
    padding: 4px 20px;
    margin-bottom: 16px;
    .ant-col {
      padding: 0 !important;
    }
    .ant-form {
      padding-left: 18px;
    }
  }
  .order-status {
    background: #e3f0ff;
    border: 1px solid rgba(0, 114, 255, 1);
    border-radius: 11px;
    font-size: 14px;
    color: #0072ff;
    font-weight: 400;
    padding: 1px 15px;
  }
</style>
