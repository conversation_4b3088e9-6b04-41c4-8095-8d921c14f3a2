<template>
  <div class="approval-reference">
    <span class="approval-reference-title">
      {{ $t('quotation.ApprovalReference') }}
    </span>
    <div class="approval-reference-list">
      <div class="list-item" v-for="(item, index) in refList" :key="index">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ApprovalReference',
    props: {
      refList: {
        type: Array,
        default: () => [],
      },
    },
  };
</script>

<style lang="less" scoped>
  @item-height: 32px;
  .approval-reference {
    display: grid;
    grid-template-columns: 152px auto;
    &-title {
      font-size: 14px;
      color: #373d41;
      font-weight: 500;
      text-align: right;
      line-height: @item-height;
    }
    &-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .list-item {
        margin: 0 0 5px 9px;
        background: #ffffff;
        border: 1px solid #0072ff;
        border-radius: 20px;
        font-size: 14px;
        color: #0072ff;
        font-weight: 400;
        padding: 0 11px;
        height: @item-height;
        line-height: 30px;
      }
    }
  }
</style>
