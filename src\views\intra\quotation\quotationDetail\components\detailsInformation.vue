<template>
  <div class="details-info">
    <!-- 报价单 - 明细 -->
    <div class="details-order-info">
      <a-form-model :model="orderInfo" layout="inline">
        <a-row :gutter="24" justify="start" type="flex">
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.PreAFNo')">
              {{ orderInfo.ORDER_ID }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6" flex="flex-start">
            <a-form-model-item :label="$t('quotation.CreateDate')">
              {{ orderInfo.CREATE_DATA }}
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item :label="$t('quotation.Status')" labelAlign="left">
              <span class="order-status">{{ orderInfo.STATUS_NAME }}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    <!-- 客户 - 明细 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.CustomerInformation') }}
    </div>
    <a-form-model :model="cusInfo" layout="inline">
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.CustomerName')">
            {{ cusInfo.CUST_NAME }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.MarketSegment')">
            {{ cusInfo.LOB }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.LOB')" labelAlign="left">
            {{ cusInfo.MARKET_SEGMENT }}
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-divider />
    <!-- Sale&Asm - 明细 -->
    <div class="secondLevel-header-title">
      {{ $t('quotation.SalesASMInformation') }}
    </div>
    <a-form-model :model="salesAsmInfo" layout="inline">
      <!-- Sales Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesmanCode')">
            {{ salesAsmInfo.SALES_CODE }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesName')">
            {{ salesAsmInfo.SALES_NAME }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesSegment')">
            {{ salesAsmInfo.SALES_SEGMENT }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.SalesTeam')" labelAlign="left">
            {{ salesAsmInfo.SALES_TEAM }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesContactNo')">
            {{ salesAsmInfo.SALES_CONTACT_NO }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.SalesEmail')">
            {{ salesAsmInfo.SALES_EMAIL }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.SalesSM')" labelAlign="left">
            {{ salesAsmInfo.SALES_SM }}
          </a-form-model-item>
        </a-col>
      </a-row>
      <!-- ASM Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMStaffNo')">
            {{ salesAsmInfo.ASM_STAFF_NO }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMName')">
            {{ salesAsmInfo.ASM_NAME }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.ASMContactNo')">
            {{ salesAsmInfo.ASM_CONTACT_NO }}
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.ASMEmail')" labelAlign="left">
            {{ salesAsmInfo.ASM_EMAIL }}
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
    <a-divider v-if="addressInfo.ADDRESS_ZONE" />
    <!-- Address Zone - 明细 -->
    <div v-if="addressInfo.ADDRESS_ZONE" class="secondLevel-header-title">
      {{ $t('quotation.AddressZone') }}
    </div>
    <a-form-model :model="addressInfo" v-if="addressInfo.ADDRESS_ZONE" layout="inline">
      <!-- Sales Infomation -->
      <a-row :gutter="24" justify="start" type="flex">
        <a-col :span="6" flex="flex-start">
          <a-form-model-item :label="$t('quotation.AddressZone')">
            {{ addressInfo.ADDRESS_ZONE }}
          </a-form-model-item>
        </a-col>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  export default {
    name: 'DetailsInformation',
    data() {
      return {
        orderInfo: {},
        cusInfo: {},
        salesAsmInfo: {},
        addressInfo: {},
      };
    },
    computed: {},
    mounted() {},
    methods: {
      init({ orderInfo = {}, cusInfo = {}, salesAsmInfo = [], addressInfo = [] }) {
        this.orderInfo = Object.freeze({ ...orderInfo });
        this.cusInfo = Object.freeze({ ...cusInfo });

        // 处理
        this.salesAsmInfo = Object.freeze(this.formatSalesAsm(salesAsmInfo));
        this.addressInfo = Object.freeze(this.formatAddressZone(addressInfo));
      },
      // 格式化 - 销售 & ASM
      formatSalesAsm(arr = []) {
        let saleObj = {},
          asmObj = {};
        for (let i = 0; i < arr.length; i++) {
          const ele = arr[i];
          if (ele.SALES_TYPE === '01') {
            // 01 - 销售
            saleObj = {
              SALES_CODE: ele.SALES_CODE,
              SALES_NAME: ele.STAFF_NAME,
              SALES_SEGMENT: ele.SALES_SEGMENT,
              SALES_TEAM: ele.SALES_TEAM,
              SALES_CONTACT_NO: ele.CONTACT_NO,
              SALES_EMAIL: ele.EMAIL,
              SALES_SM: ele.SALES_SM,
            };
          } else if (ele.SALES_TYPE === '02') {
            // 02 - ASM
            asmObj = {
              ASM_STAFF_NO: ele.SALES_CODE,
              ASM_NAME: ele.STAFF_NAME,
              ASM_CONTACT_NO: ele.CONTACT_NO,
              ASM_EMAIL: ele.EMAIL,
            };
          }
        }
        return { ...saleObj, ...asmObj };
      },
      // 格式化 - 地址区域
      formatAddressZone(arr = []) {
        const result = arr.find(x => x.ATTR_CODE === 'address_zone');
        return {
          ADDRESS_ZONE: result ? result.VALUE_NAME || result.ATTR_VALUE : '',
        };
      },
    },
  };
</script>

<style lang="less" scoped>
  .details-info {
    .ant-form-item {
      font-weight: 400;
      margin-bottom: 0 !important;
    }
    /deep/ .ant-form-item-label > label {
      font-weight: 600;
    }
  }
  .details-order-info {
    background: #e9e9e9;
    height: 40px;
    padding: 4px 20px;
    margin-bottom: 16px;
    .ant-col {
      padding: 0 !important;
    }
    .ant-form {
      padding-left: 18px;
    }
  }
  .order-status {
    background: #e3f0ff;
    border: 1px solid rgba(0, 114, 255, 1);
    border-radius: 11px;
    font-size: 14px;
    color: #0072ff;
    font-weight: 400;
    padding: 1px 15px;
  }
</style>
