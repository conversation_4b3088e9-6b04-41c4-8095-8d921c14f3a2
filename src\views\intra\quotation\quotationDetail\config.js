import { queryOrderDetail } from '@/api/quotation';
import moment from 'moment';
// 获取订单详情
export const getOrderDetail = async orderId => {
  const res = await queryOrderDetail({ ORDER_ID: orderId });
  // 存储详情数据用于页面回显
  const orderDetail = res.DATA && res.DATA[0];
  // 处理回显的页面数据
  return processOrderDetailData(orderDetail);
};

// 处理订单详情数据
function processOrderDetailData(orderDetail) {
  const {
    CUST_INFO,
    ORDER_SALESMAN,
    ORDER_ATTR_INFO,
    MULTIMEDIA_FILES,
    ORDER_REMARK_ITEM,
    ORDER_EXPANSION,
  } = orderDetail;
  const orderInfo = {
    ORDER_ID: orderDetail.ORDER_ID,
    CREATE_DATA: moment(orderDetail.CREATE_DATE).format('DD/MM/YYYY'),
    STATUS: orderDetail.ORDER_STATE,
    STATUS_NAME: orderDetail.ORDER_STATE_NAME,
  };
  const cusInfo = destructureArray(CUST_INFO, ['CUST_ID', 'CUST_NAME', 'LOB', 'MARKET_SEGMENT']);
  const salesAsmInfo = destructureArray(
    ORDER_SALESMAN,
    [
      'SALES_TYPE',
      'SALES_CODE',
      'SALES_SEGMENT',
      'SALES_TEAM',
      'SALES_SM',
      'STAFF_NAME',
      'CONTACT_NO',
      'EMAIL',
    ],
    { forceArray: true },
  );
  const addressInfo = destructureArray(ORDER_ATTR_INFO, ['ATTR_CODE', 'ATTR_VALUE', 'VALUE_NAME'], {
    forceArray: true,
  });

  const attachmentInfo = destructureArray(
    MULTIMEDIA_FILES,
    [
      'FILE_TYPE',
      'FILE_URL',
      'SERVER_TYPE',
      'SERVER_TYPE_NAME',
      'FILE_NAME',
      'FILE_DESC',
      'FILE_UNIQUE_ID',
      'OPER_STAFF_NAME',
      'OPER_TIME',
    ],
    {
      forceArray: true,
    },
  );

  const remarkInfo = destructureArray(ORDER_REMARK_ITEM, ['ATTR_CODE', 'ATTR_VALUE'], {
    forceArray: true,
  });
  const expansionInfo = ORDER_EXPANSION?.map(item => item.RESERVED_VALUE) || [];

  return {
    orderInfo,
    cusInfo,
    salesAsmInfo,
    addressInfo,
    attachmentInfo,
    remarkInfo,
    amendOrderLineInfo: orderDetail.AMEND_ORDER_LINE_INFO,
    expansionInfo,
  };
}

/**
 * 从数组中解构出指定的多个属性
 * @param {Array} array - 源数组
 * @param {Array} props - 需要解构的属性名数组
 * @param {Object} [options] - 配置选项
 * @param {boolean} [options.forceArray=false] - 强制返回数组形式
 * @returns {Array|Object} - 返回解构后的数据，单条数据时返回对象
 */
function destructureArray(array, props, options = {}) {
  if (!Array.isArray(array)) {
    throw new TypeError('第一个参数必须是数组');
  }

  if (!Array.isArray(props)) {
    throw new TypeError('第二个参数必须是属性名数组');
  }

  const { forceArray = false } = options;

  // 处理空数组情况
  if (array.length === 0) {
    return forceArray ? [] : undefined;
  }

  // 解构数据
  const result = array.map(item => {
    const obj = {};
    props.forEach(prop => {
      obj[prop] = item[prop];
    });
    return obj;
  });

  // 根据条件和数据长度决定返回形式
  if (!forceArray && result.length === 1) {
    return result[0];
  }

  return result;
}

/**
 * 处理 - 产品树 - Tabs
 */
export const formatProductTabs = orderLineTemp => {
  let result = [];
  orderLineTemp.forEach((orderLineItem, i) => {
    const mainProductInfo = orderLineItem.PRODUCT_INFO?.find(x => x.PRODUCT_MODE === '00');
    if (mainProductInfo) {
      result.push({
        id: `${mainProductInfo.ORDER_LINE_ID}_${mainProductInfo.PRODUCT_ID}`,
        name: mainProductInfo.PRODUCT_NAME,
      });
    }
  });
  return result;
};

/**
 * 处理 & 统计 - 数量和费用
 * @param {Object} orderDetailTemp - 包含订单详情的临时对象。
 * @returns {Object} - 各产品下的收费数据（MRC/OTC/REBATE）。
 */
export const formatOrderChargeList = orderLineTemp => {
  let orderLineAttTemp = {};
  // 从订单详情对象中解构出订单行信息
  orderLineTemp.forEach(orderLineItem => {
    orderLineAttTemp[orderLineItem.ORDER_LINE_ID] = {};
    // 遍历订单行中的每个产品项目
    // 初始化一个空数组，用于存储临时元素信息
    const details = getProductItemByKey(orderLineItem.ORDER_LINE_ATTR_INFO, [
      'lineInstallNumber',
      'totalMRCForAllLine',
      'totalOTCForAllLine',
      'totalMRCForAllLinePeriod',
    ]);
    orderLineAttTemp[orderLineItem.ORDER_LINE_ID] = { ...details };
  });
  return orderLineAttTemp;
};

// 辅助函数 - 根据key返回订单详情中的PRODUCT_ITEM_INFO的对象
function getProductItemByKey(arr, keys) {
  let result = {};
  const filterArr = arr.filter(x => keys.includes(x.ATTR_CODE));
  for (let i = 0; i < filterArr.length; i++) {
    const ele = filterArr[i];
    result[ele.ATTR_CODE] = ele.ATTR_VALUE;
  }
  return result;
}
