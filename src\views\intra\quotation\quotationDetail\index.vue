<template>
  <div class="quotation-detail">
    <!-- RETURN栏 -->
    <PageHeader :prePageInfo="prePageInfo"></PageHeader>
    <!-- 流程节点 -->
    <FlowSteps v-if="orderStatus && orderStatus != 'S8'" :orderStatus="orderStatus"></FlowSteps>
    <!-- 详情信息 -->
    <DetailsInformation ref="detailsInformation"></DetailsInformation>
    <a-divider />
    <!-- 产品树 -->
    <DetailsProductTree
      ref="detailsProductTree"
      :orderId="orderId"
      :productTabs="productTabs"
      :chargeListMap="chargeListMap"
    ></DetailsProductTree>
    <a-divider v-if="attachmentList && attachmentList.length > 0" />
    <!-- 附件 -->
    <Attachment
      v-if="attachmentList && attachmentList.length > 0"
      :fileList="attachmentList"
      :ifShowUpload="false"
      actionBtnShowType="download"
      ref="attachmentRef"
    />
    <a-divider />
    <!-- 审批备注 -->
    <OfferPreApprovalRemark ref="offerPreApprovalRemark"></OfferPreApprovalRemark>
    <a-divider v-if="refList && refList.length > 0" />
    <!-- 批准参考 -->
    <ApprovalReference
      v-if="refList && refList.length > 0"
      ref="approvalRef"
      :refList="refList"
    ></ApprovalReference>
    <!-- 时间线 -->
    <ActionTimeLine ref="actionTimeLine" :orderId="orderId"></ActionTimeLine>
    <!-- 底部按钮组 -->
    <FooterToolBar class="footer-tool-bar">
      <a-button ghost type="primary" class="fixed-bottom-btn" @click="back">{{
        $t('common.buttonBack')
      }}</a-button>

      <a-button
        ghost
        v-if="orderStatus != 'S8'"
        type="primary"
        class="fixed-bottom-btn w-auto"
        @click="handleQuotation('cancel')"
        >{{ $t('quotation.CancelPreAF') }}</a-button
      >
      <a-button
        type="primary"
        class="fixed-bottom-btn w-auto bg-blue"
        @click="handleQuotation('copy')"
        >{{ $t('quotation.CopyQuotation') }}</a-button
      >
    </FooterToolBar>
    <!-- 校验提示 -->
    <MessageModal
      :visible="tipsVisible"
      :message="tipsMessage"
      :displayCancelBtn="displayCancelBtn"
      @cancel="tipsVisible = false"
      @confirm="confirmOK"
    />

    <!-- 复制成功 -->
    <successMessageModal
      v-if="visible"
      :visible="visible"
      :messageTitleTips="$t('quotation.NewPreApprovalFormCreate')"
      :subText="subText"
      :confirmText="$t('quotation.ViewNewForm')"
      :loading="loading"
      @cancel="handleCancel"
      @confirm="handleOk"
    >
    </successMessageModal>
  </div>
</template>

<script>
  import { oscaOrderCopy, oscaOrderCancel } from '@/api/quotation';
  import { getOrderDetail, formatOrderChargeList, formatProductTabs } from './config';

  import PageHeader from '@/views/components/pageHeader/index.vue';
  import FlowSteps from '@/views/components/flowSteps/index.vue';
  import DetailsInformation from './components/detailsInformation';
  import DetailsProductTree from './components/detailsProductTree';
  import Attachment from '@/views/components/attachment/index.vue';
  import OfferPreApprovalRemark from './components/offerPreApprovalRemark';
  import ApprovalReference from './components/approvalReference';
  import ActionTimeLine from '@/views/components/actionTimeLine';
  import FooterToolBar from '@/components/footerToolbar';
  import MessageModal from '@/components/messageModal';
  import successMessageModal from '@/views/components/successMessageModal';

  export default {
    name: 'QuotationDetail',
    components: {
      PageHeader,
      FlowSteps,
      FooterToolBar,
      DetailsInformation,
      DetailsProductTree,
      Attachment,
      ApprovalReference,
      ActionTimeLine,
      OfferPreApprovalRemark,
      MessageModal,
      successMessageModal,
    },
    data() {
      return {
        newOrderId: '',
        orderStatus: '',
        detailsInfo: {},
        productTabs: [],
        attachmentList: [],
        refList: [],
        displayCancelBtn: false,
        tipsVisible: false,
        tipsMessage: '',
        chargeListMap: {},
        visible: false,
        subText: '',
        loading: false,
      };
    },
    computed: {
      // 上一页页面信息
      prePageInfo() {
        return `${this.orderId}(${this.$t('quotation.returnHeaderView')})`;
      },
      orderId() {
        return this.$route.query.orderId || '';
      },
    },
    watch: {
      // 监听 - 复制后进行重新渲染
      '$route'(to, from) {
        if (to.query !== from.query) {
          this.initData(to.query.orderId);
          console.log('触发了吗？');
        }
      },
    },
    mounted() {
      this.initData(this.orderId);
    },
    methods: {
      async initData(orderId) {
        this.loading = true;
        this.orderStatus = this.$route.query.orderStatus;
        const {
          orderInfo,
          cusInfo,
          salesAsmInfo,
          addressInfo,
          attachmentInfo,
          remarkInfo,
          amendOrderLineInfo,
          expansionInfo,
        } = await getOrderDetail(orderId);
        this.loading = false;
        // 赋值 - 步骤条
        this.orderStatus = orderInfo.STATUS;
        // 赋值 - 基础信息
        this.$refs.detailsInformation.init({
          orderInfo,
          cusInfo,
          salesAsmInfo,
          addressInfo,
        });
        // 处理产品的Tabs
        this.productTabs = formatProductTabs(amendOrderLineInfo);
        // 处理产品树所需要的费用列表
        this.chargeListMap = formatOrderChargeList(amendOrderLineInfo);
        // 附件
        this.attachmentList = attachmentInfo;
        // 处理审批备注
        this.$refs.offerPreApprovalRemark.init(remarkInfo);

        this.refList = expansionInfo;
      },

      // 格式化 - 附件
      formatAttach(arr) {
        return arr
          .filter(i => i.FILE_TYPE == 11)
          .map((v, index) => {
            return {
              ...v,
              id: v.FILE_UNIQUE_ID || index,
              SERVER_TYPE: v.FILE_TYPE,
              OPER_STAFF_NAME: v.OPER_STAFF_ID,
            };
          });
      },

      // 操作 - 报价单
      async handleQuotation(type) {
        // 操作 - 报价单
        // copy - 复制， cancel - 取消
        if (!type || type === '') {
          return false;
        }
        const ORDER_ID = this.orderId;
        let res = null;

        try {
          if (type === 'copy') {
            res = await oscaOrderCopy({
              COPY_ORDER_ID: ORDER_ID,
            });
            this.newOrderId = res.DATA[0]?.NEW_ORDER_ID;
            this.subText = this.$t('quotation.FormNo') + this.newOrderId;
            this.visible = true;
          } else if (type === 'cancel') {
            this.displayCancelBtn = true;
            this.showTipModal(this.$t('quotation.CancelPreFormInfo'));
          }
          console.log(res, 'handleQuotation');
        } catch (error) {
          console.log(error, 'error');
        }
      },
      // 弹窗提示
      showTipModal(msg) {
        this.tipsMessage = msg;
        this.tipsVisible = true;
      },
      // 返回
      back() {
        this.$router.customBack();
      },
      // 复制报价单成功弹窗取消
      handleCancel() {
        this.newOrderId = '';
        this.visible = false;
      },
      // 复制报价单成功弹窗 查看新报价单
      handleOk() {
        this.visible = false;
        this.$store.dispatch('quotation/setCleanState');
        this.$router.push({
          name: 'quotationDetail',
          query: { orderId: this.newOrderId, orderStatus: 'S0' },
        });
      },
      // 取消报价单确认
      async confirmOK() {
        if (this.displayCancelBtn == true) {
          const res = await oscaOrderCancel({
            ORDER_ID: this.orderId,
          });
          this.displayCancelBtn = false;
          this.showTipModal(this.$t('common.successMessage'));
        } else {
          this.tipsVisible = false;
          this.back();
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  @foot-height: 80px;
  .quotation-detail {
    padding-bottom: @foot-height;
  }
  .footer-tool-bar {
    height: @foot-height;
    box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>
