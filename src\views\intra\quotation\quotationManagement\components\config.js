import that from '@/main';
import moment from 'moment';
export default {
  columns: [
    {
      title: that.$t('quotation.PreAFNo'),
      dataIndex: 'ORDER_ID',
      key: 'ORDER_ID',
      width: 126,
    },
    {
      title: that.$t('quotation.CustomerName'),
      dataIndex: 'CUST_NAME',
      key: 'CUST_NAME',
      width: 138,
    },
    {
      title: that.$t('quotation.MarketSegment'),
      dataIndex: 'orderType',
      key: 'marketSegment',
      width: 140,
    },
    {
      title: that.$t('quotation.SalesSegment'),
      dataIndex: 'SALES_SEGMENT',
      key: 'SALES_SEGMENT',
      width: 129,
    },
    {
      title: that.$t('quotation.SalesmanCode'),
      dataIndex: 'SALES_CODE',
      key: 'SALES_CODE',
      width: 134,
    },
    {
      title: that.$t('quotation.SalesName'),
      dataIndex: 'SALES_NAME',
      key: 'SALES_NAME',
      width: 109,
    },
    {
      title: that.$t('quotation.ASM'),
      dataIndex: 'ASM_NAME',
      key: 'ASM_NAME',
      width: 109,
    },
    {
      title: that.$t('quotation.NextApprover'),
      dataIndex: 'APPROVER_NAME',
      key: 'APPROVER_NAME',
      width: 146,
      ellipsis: true,
    },
    {
      title: that.$t('quotation.CreateDate'),
      dataIndex: 'CREATE_DATE',
      key: 'CREATE_DATE',
      //年月日时分秒
      customRender: (text, record) => {
        return moment(text).format('YYYY-MM-DD');
      },
      width: 110,
    },
    {
      title: that.$t('quotation.FormStatus'),
      scopedSlots: { customRender: 'status' },
      dataIndex: 'STATUS',
      key: 'STATUS_NAME',
      width: 125,
      ellipsis: true,
    },
    {
      title: that.$t('common.action'),
      scopedSlots: { customRender: 'action' },
      dataIndex: 'action',
      key: 'action',
      fixed: 'right',
      className: 'action-column',
      width: 140,
    },
  ],
};
