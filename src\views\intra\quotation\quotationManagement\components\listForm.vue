<template>
  <div>
    <a-form-model :model="orderFormData" :colon="false" ref="Form" :rules="rules">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.CustomerName')" prop="CUST_NAME">
            <a-input
              name="CUST_NAME"
              v-model.trim="orderFormData.CUST_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.PreAFNo')" prop="ORDER_ID" labelAlign="left">
            <a-input
              v-model="orderFormData.ORDER_ID"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('quotation.SalesmanCode')"
            prop="SALES_CODE"
            labelAlign="left"
          >
            <a-input
              v-model="orderFormData.SALES_CODE"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('quotation.SalesmanName')"
            prop="SALES_NAME"
            labelAlign="left"
          >
            <a-input
              v-model="orderFormData.SALES_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :gutter="24">
        <a-col :span="6">
          <a-form-model-item
            :label="$t('quotation.SalesSegment')"
            prop="SALES_SEGMENT"
            labelAlign="left"
          >
            <a-input
              v-model="orderFormData.SALES_SEGMENT"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('quotation.NextApprover')"
            prop="APPROVER_NAME"
            labelAlign="left"
          >
            <a-input
              v-model="orderFormData.APPROVER_NAME"
              :placeholder="$t('common.inputPlaceholder')"
              allowClear
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item :label="$t('quotation.FormStatus')" prop="STATUS" labelAlign="left">
            <a-select
              v-model="orderFormData.STATUS"
              :placeholder="$t('common.selectPlaceholder')"
              allowClear
            >
              <a-select-option v-for="item in statusData" :value="item.value" :key="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="6">
          <a-form-model-item
            :label="$t('quotation.CreateDate')"
            prop="orderCreationTime"
            labelAlign="left"
          >
            <a-range-picker
              v-model="orderFormData.orderCreationTime"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
              style="width: 100%; text-align: start"
            >
              <template #suffixIcon>
                <span class="iconfont icon-rili"></span>
              </template>
            </a-range-picker>
          </a-form-model-item>
        </a-col>
        <a-row justify="end" type="flex" class="padding form-item-btn">
          <a-col>
            <a-form-model-item class="textAlignRight">
              <a-button ghost type="primary" class="reset-button" @click="resetForm">
                {{ $t('common.buttonReset') }}
              </a-button>
            </a-form-model-item>
          </a-col>
          <a-col>
            <a-form-model-item class="textAlignRight">
              <a-button type="primary" @click="filterBtn" class="search-button">
                {{ $t('common.buttonInquiry') }}
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-row>
    </a-form-model>
  </div>
</template>

<script>
  import { queryParamList } from '@/api/common';
  // import moment from 'moment';
  export default {
    name: 'listForm',
    data() {
      return {
        statusData: [],
        orderFormData: {
          CUST_NAME: '',
          ORDER_ID: '', // 订单ID - Pre-AF.NO
          SALES_CODE: '',
          SALES_NAME: '',
          SALES_SEGMENT: '', // 销售员细分市场
          APPROVER_NAME: '',
          STATUS: undefined,
          orderCreationTime: [
            // moment().startOf('month').format('YYYY-MM-DD'),
            // moment().format('YYYY-MM-DD'),
          ],
        },
      };
    },
    computed: {
      rules() {
        return {
          orderCreationTime: [
            {
              message: this.$t('quotation.CreateDateTimeLimit'),
              trigger: 'change',
              validator: (rule, value, callback) => {
                // 限定时间范围在三个月以内
                const [startDate, endDate] = value;

                const start = new Date(startDate);
                const end = new Date(endDate);

                // 计算月份差
                const months = (end.getFullYear() - start.getFullYear()) * 12;
                const monthDiff = months + end.getMonth() - start.getMonth();

                if (monthDiff > 3) {
                  console.log(monthDiff);
                  callback(new Error(this.$t('quotation.CreateDateTimeLimit')));
                } else {
                  callback();
                }
              },
            },
          ],
        };
      },
    },
    mounted() {
      this.getStatusData();
    },
    methods: {
      async getStatusData() {
        const params = {
          TYPE_ID: 'ORDER_STATUS_TYPE',
        };

        try {
          const res = await queryParamList(params);
          const arr = res.DATA.map(item => {
            return {
              value: item.DATA_ID,
              label: item.DATA_NAME,
            };
          });
          this.$nextTick(() => {
            this.statusData = arr;
          });
        } catch (error) {
          console.log(error, 'error');
        }
      },
      //点击重置按钮
      resetForm() {
        this.$refs.Form.resetFields();
        Object.assign(this.$data.orderFormData, this.$options.data().formData);
      },
      // 点击filter按钮
      filterBtn() {
        let _this = this;
        this.$refs.Form.validate(valid => {
          if (valid) {
            _this.$emit('filterBtn', _this.orderFormData);
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
    },
  };
</script>

<style lang="less" scoped>
  .padding {
    padding: 0 12px;
  }
  .icon-rili {
    font-size: 14px;
    color: #9e9e9e;
  }
</style>
