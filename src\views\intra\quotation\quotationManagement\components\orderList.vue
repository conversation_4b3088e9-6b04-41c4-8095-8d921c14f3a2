/** 报价单列表 表格数据 */
<template>
  <div class="tableList">
    <div class="common-custom-header">
      <!-- 标题 -->
      <div class="common-custom-title">{{ $t('quotation.PreAFList') }}</div>
      <a-button type="primary" @click="addOpen" style="margin-right: 0px !important">{{
        $t('quotation.NewPreAF')
      }}</a-button>
    </div>
    <!-- 主列表 -->
    <a-table
      :columns="columns"
      :data-source="dataList"
      :loading="loading"
      :rowKey="(record, index) => `${record.ORDER_ID}`"
      :scroll="{ x: 1800 }"
      :pagination="{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        class: 'common-custom-pagination',
        showQuickJumper: true,
        showSizeChanger: true,
        hideOnSinglePage: true, //只有一页时是否隐藏分页器
        showTotal: total => $t('common.tableTotalCount', { total }),
        onChange: page => {
          this.paginationChange(page);
        },
        onShowSizeChange: (_, size) => {
          this.paginationChange(1, size);
        },
      }"
    >
      <template slot="status" slot-scope="value, record">
        <span :class="['status-icon', `status-icon-${record.STATUS}`]">{{
          record.STATUS_NAME
        }}</span>
      </template>
      <template slot="action" slot-scope="value, record">
        <!-- #TODO 后面补充权限 -->
        <div class="action-cell">
          <a
            class="iconfont icon-xiangqingmingxi"
            @click.stop="pageLink('detail', record, $event)"
          ></a>
          <!-- 编辑 - Save as draft || Offer Rejected -->
          <a
            class="iconfont icon-xiugai"
            @click.stop="pageLink('edit', record, $event)"
            v-if="record.STATUS === 'S0' || record.STATUS === 'S3'"
          ></a>
          <a
            class="iconfont icon-ico_kaobeijiankongrenwu"
            v-debounceClick="{ fn: handleQuotation, args: ['copy', record] }"
          ></a>
          <a
            class="iconfont icon-baocuo"
            v-debounceClick="{ fn: handleQuotation, args: ['cancel', record] }"
            v-if="record.STATUS !== 'S8'"
          ></a>
        </div>
      </template>
    </a-table>
    <TipsPopWindow
      v-if="tipsVisible"
      :text="tipText"
      :visible="tipsVisible"
      :cancelBtn="displayCancelBtn"
      @cancel="tipsVisible = false"
      @Ok="confirmOk"
    />
  </div>
</template>

<script>
  import config from './config';
  import TipsPopWindow from '@/components/tipsPopWindow/index.vue';
  import { queryQuotationList, oscaOrderCopy, oscaOrderCancel } from '@/api/quotation';
  export default {
    name: 'OrderList',
    components: {
      TipsPopWindow,
    },
    props: {
      form: { type: Object, default: () => {} },
    },
    data() {
      return {
        tipText: '',
        tipsVisible: false,
        displayCancelBtn: false,
        orderId: '',
        columns: config.columns,
        loading: false,
        dataList: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
        },
      };
    },
    activated() {
      if (this.dataList && this.dataList.length > 0) {
        this.getOrderList();
      }
    },
    computed: {},
    mounted() {
      // this.getOrderList();
    },
    methods: {
      paginationChange(page, pageSize) {
        this.pagination.current = page || 1;
        this.pagination.pageSize = pageSize || this.pagination.pageSize;
        this.getOrderList();
      },
      // 页面赋值
      setOrderList(obj) {
        this.dataList = [...obj.QUERY_RESPONSE] || [];
        this.pagination.total = obj.TOTAL_COUNT || 0;
      },
      // 获取订单列表
      getOrderList() {
        this.loading = true;
        const reqParams = { ...this.form };

        const params = this.formatParams(reqParams);
        const pageNum = this.pagination.current;
        const pageSize = this.pagination.pageSize;
        queryQuotationList({
          ...params,
          PAGE_NO: pageNum,
          PAGE_SIZE: pageSize,
        })
          .then(res => {
            const data = res.DATA[0] || {};
            this.setOrderList(data);
          })
          .catch(error => {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // 规范处理参数
      formatParams(params) {
        const staticCol = {
          CUST_NAME: params.CUST_NAME || '',
          ORDER_ID: params.ORDER_ID || '', // 订单ID - Pre-AF.NO
          SALES_CODE: params.SALES_CODE || '',
          SALES_NAME: params.SALES_NAME || '',
          SALES_SEGMENT: params.SALES_SEGMENT || '', // 销售员细分市场
          APPROVER_NAME: params.APPROVER_NAME || '',
          STATUS: params.STATUS || '',
        };
        if (params.orderCreationTime && params.orderCreationTime.length === 2) {
          staticCol.START_DATE = params.orderCreationTime[0] + ' 00:00:00';
          staticCol.END_DATE = params.orderCreationTime[1] + ' 23:59:59';
        }
        return staticCol;
      },
      // 新建报价单
      addOpen() {
        console.log('addOpen');
        // 清空存储数据
        this.$store.dispatch('quotation/setCleanState');
        this.$router.push({ name: 'addQuotation', query: {} });
      },
      // 页面跳转
      pageLink(type, orderItem, e) {
        // 手动阻止事件冒泡
        e.stopPropagation();
        let routerName = '';
        switch (type) {
          case 'edit':
            routerName = 'addQuotation';
            break;
          default:
            routerName = 'quotationDetail';
            break;
        }
        this.$store.dispatch('quotation/setCleanState');
        this.$router.push({
          name: routerName,
          query: { orderId: orderItem.ORDER_ID, orderStatus: orderItem.STATUS },
        });
      },
      // 操作 - 报价单
      async handleQuotation(type, record) {
        // 操作 - 报价单
        // copy - 复制， cancel - 取消
        if (!type || type === '') {
          return false;
        }
        let res = null;
        try {
          if (type === 'copy') {
            res = await oscaOrderCopy({
              COPY_ORDER_ID: record.ORDER_ID,
            });
            this.displayCancelBtn = false;
            this.tipText = this.$t('common.successMessage');
            this.tipsVisible = true;
          } else if (type === 'cancel') {
            this.orderId = record.ORDER_ID;
            this.displayCancelBtn = true;
            this.tipText = this.$t('quotation.CancelPreFormInfo');
            this.tipsVisible = true;
          }
          console.log(res, 'handleQuotation');
          // this.$emit('handleTips', { type: 'success', text: this.$t('common.successMessage') });
        } catch (error) {
          console.log(error, 'error');
        }
      },
      // 消息确认弹窗 确定按钮
      async confirmOk() {
        if (this.displayCancelBtn == true) {
          const res = await oscaOrderCancel({
            ORDER_ID: this.orderId,
          });
          this.displayCancelBtn = false;
          this.tipText = this.$t('common.successMessage');
          this.tipsVisible = true;
        } else {
          this.tipsVisible = false;
          this.$emit('listFormFresh');
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  // 生成颜色 - 列表状态标记
  @status-colors: #0072ff, #ffb100, #2dcb31, #e60017;
  .generate-status(@i) when (@i >= 0) {
    @item: extract(@status-colors, @i);
    @name: @i - 1;
    .status-icon-S@{name} {
      &::before {
        background: @item;
      }
    }
    .generate-status(@i - 1);
  }
  .generate-status(length(@status-colors));

  /deep/ .ant-table-tbody > tr > td:last-child.action-column {
    text-align: left !important;
  }
  .common-custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    height: 30px;
  }
  .status-icon {
    position: relative;
    &::before {
      content: '';
      width: 10px;
      height: 10px;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: -14.5px;
    }
  }
  .action-cell {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    a {
      margin-right: 21px;
      font-size: 12px;
      line-height: 12px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
</style>
