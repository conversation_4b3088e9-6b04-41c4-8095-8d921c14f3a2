<template>
  <div class="orderList">
    <!-- 查询区域 -->
    <ListForm ref="listForm" @filterBtn="filterBtn" />

    <a-divider />

    <!-- 订单列表 -->
    <OrderList ref="orderList" :form="orderFormData" @listFormFresh="listFormFresh" />
  </div>
</template>

<script>
  import ListForm from './components/listForm.vue';
  import OrderList from './components/orderList.vue';
  // import { hasAnyValue } from '@/utils/utils';
  import i18n from '@/i18n/index';
  export default {
    name: 'customerQuotation',
    components: {
      ListForm,
      OrderList,
    },
    data() {
      return {
        orderFormData: {
          CUST_NAME: '',
          ORDER_ID: '', // 订单ID - Pre-AF.NO
          SALES_CODE: '',
          SALES_NAME: '',
          SALES_SEGMENT: '', // 销售员细分市场
          APPROVER_NAME: '',
          STATUS: undefined,
          orderCreationTime: [],
        },
      };
    },
    computed: {},
    mounted() {},
    methods: {
      // 列表数据查询查询
      getOrderList() {
        this.$nextTick(() => {
          // 转成用分页的方法触发查询 - 重回第一页
          this.$refs.orderList && this.$refs.orderList.paginationChange();
        });
      },

      // 点击filter按钮
      filterBtn(form) {
        // 传值回来
        this.orderFormData = form;
        this.getOrderList();
      },

      // 刷新列表数据
      listFormFresh() {
        this.$refs.listForm.filterBtn();
      },
    },
  };
</script>

<style lang="less" scoped></style>
