const { defineConfig } = require('@vue/cli-service');
const path = require('path');
const webpack = require('webpack');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
require('events').EventEmitter.defaultMaxListeners = 0;

const isOuter = process.env.VUE_APP_MODE === 'outer';
const isProd = process.env.NODE_ENV === 'production';

module.exports = defineConfig({
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      fallback: {
        path: require.resolve('path-browserify'),
      },
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    plugins: [
      // 在生产环境使用 IgnorePlugin 完全排除不需要的模块
      ...(isProd
        ? [
            new CleanWebpackPlugin(),
            new webpack.IgnorePlugin({
              resourceRegExp: isOuter ? /[\\/]views[\\/]intra[\\/]/ : /[\\/]views[\\/]outer[\\/]/,
              contextRegExp: /src/,
            }),
          ]
        : []),
    ],
    optimization: isProd
      ? {
          splitChunks: {
            chunks: 'all',
            cacheGroups: {
              vendors: {
                name: 'chunk-vendors',
                test: /[\\/]node_modules[\\/]/,
                priority: -10,
                chunks: 'initial',
              },
              common: {
                name: 'chunk-common',
                minChunks: 2,
                priority: -20,
                chunks: 'initial',
                reuseExistingChunk: true,
              },
            },
          },
          usedExports: true,
          sideEffects: true,
          minimize: true,
          minimizer: [
            new TerserPlugin({
              terserOptions: {
                compress: {
                  warnings: false,
                  drop_console: true, // 移除 console
                  drop_debugger: true, // 移除 debugger
                },
              },
            }),
          ],
        }
      : {},
  },
  css: {
    loaderOptions: {
      less: {
        // 引入 Less 变量
        prependData: `@import "@/styles/_variable.less";`,
        lessOptions: {
          modifyVars: {
            hack: `true; @import "${path.resolve(__dirname, './src/styles/theme.less')}";`,
          },
          javascriptEnabled: true,
        },
      },
    },
  },

  devServer: {
    open: true, //设置自动打开
    port: 8094,
    proxy: {
      '/osca/order': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/osca/order': '/osca/order',
        },
      },
      '/osca/file': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/osca/file': '/osca/file',
        },
      },
      '/cim/account': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/cim/account': '/cim/account',
        },
      },
      '/cim/user': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/cim/user': '/cim/user',
        },
      },
      // 账户
      '/cim': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/cim': '/cim',
        },
      },
      '/cim/customer': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/cim/customer': '/cim/customer',
        },
      },
      '/prods/light': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/prods/light': '/prods/light',
        },
      },
      '/prods/defprodandmutex': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/prods/defprodandmutex': '/prods/defprodandmutex',
        },
      },
      '/prods/highsearch': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/prods/highsearch': '/prods/highsearch',
        },
      },
      '/prods': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/prods': '/prods',
        },
      },
      '/order/receive': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/order/receive': '/order/receive',
        },
      },
      // 权限
      '/auths/query/': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/auths/query/': '/auths/query/',
        },
      },
      '/users': {
        target: 'https://asccrm-sit-a.hkt.com',
        changeOrigin: true,
        pathRewrite: {
          '^/users': '/users',
        },
      },
    },
  },
  productionSourceMap: false,
  // 根据环境配置不同的 publicPath
  publicPath: isOuter ? '/osca-outer' : '/osca',
  // 根据环境配置不同的输出目录
  outputDir: isOuter ? 'dist-outer' : 'dist',
});
